#!/usr/bin/env node

import dotenv from 'dotenv'
import axios from 'axios'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 加载 .env 文件
dotenv.config({ path: path.join(__dirname, '../.env') })

// 从配置文件读取 cannyId 数组和语言映射
let cannyIds = []
let langConfigMap = new Map() // cannyId -> 语言信息的映射

try {
  const configPath = path.join(__dirname, '../src/config/default/setting.config.ts')
  const configContent = fs.readFileSync(configPath, 'utf8')
  
  // 提取 langRouting 数组
  const langRoutingMatch = configContent.match(/export const langRouting = \[([\s\S]*?)\]/m)
  
  if (langRoutingMatch) {
    // 解析每个语言配置项
    const langEntries = langRoutingMatch[1].match(/\{[^}]*\}/g) || []
    
    langEntries.forEach(entry => {
      const langMatch = entry.match(/lang:\s*'([^']*)'/)
      const labelMatch = entry.match(/label:\s*'([^']*)'/)
      const cannyIdMatch = entry.match(/cannyId:\s*'([^']*)'/)
      
      if (cannyIdMatch && labelMatch && langMatch) {
        const cannyId = cannyIdMatch[1]
        const label = labelMatch[1]
        const lang = langMatch[1]
        
        cannyIds.push(cannyId)
        langConfigMap.set(cannyId, { label, lang })
      }
    })
  }
  
  console.log(`✅ 成功加载 ${cannyIds.length} 个多语言配置`)
} catch (error) {
  console.error('❌ 读取配置文件失败:', error.message)
  process.exit(1)
}

// Canny API 配置
const CANNY_API_URL = 'https://canny.io/api/v1/entries/list'
const API_KEY = process.env.VITE_CANNY_API_KEY || '' // 从环境变量获取 API Key

console.log('API Key 状态:', API_KEY ? '✅ 已设置' : '❌ 未设置')

// 目标文件路径
const DATA_FILE_PATH = path.join(__dirname, '../src/components/CannyModal/data.json')

async function fetchCannyData() {
  try {
    console.log('正在获取 Canny 数据...')
    
    const response = await axios.post(CANNY_API_URL, {
      apiKey: API_KEY,
      type: 'new'
    }, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })

    const data = response.data
    console.log(`获取到 ${data.entries.length} 条数据`)

    // 筛选数据：获取标签 ID 在 cannyIds 列表中的条目
    const filteredEntries = []
    const rejectedEntries = []
    
    data.entries.forEach(entry => {
      const hasMatchingLabel = entry.labels.some(label => cannyIds.includes(label.id))
      
      if (hasMatchingLabel) {
        filteredEntries.push(entry)
      } else {
        rejectedEntries.push(entry)
      }
    })

    console.log(`筛选后有 ${filteredEntries.length} 条符合条件的数据`)
    
    // 输出被拒绝的条目详情
    if (rejectedEntries.length > 0) {
      console.log(`\n❌ ${rejectedEntries.length} 条数据不符合筛选条件:`)
      rejectedEntries.forEach((entry, index) => {
        console.log(`   ${index + 1}. "${entry.title}"`)
        console.log(`      - 创建时间: ${new Date(entry.created).toLocaleString()}`)
        console.log(`      - 标签: ${entry.labels.map(label => `${label.name}(${label.id})`).join(', ')}`)
        console.log(`      - 拒绝原因: 标签ID不在配置的cannyIds列表中`)
        console.log(`      - 配置的cannyIds: [${cannyIds.join(', ')}]`)
        console.log()
      })
    }

    // 获取今天的日期
    const today = new Date()
    const todayDateString = today.toDateString()
    
    // 筛选出今天创建的所有条目
    const todayEntries = filteredEntries.filter(entry => {
      const entryDate = new Date(entry.created)
      return entryDate.toDateString() === todayDateString
    })

    // 如果今天没有数据，则获取最近一天的所有数据
    let finalEntries = todayEntries
    if (todayEntries.length === 0) {
      // 按日期分组
      const groupedByDate = filteredEntries.reduce((acc, entry) => {
        const date = new Date(entry.created).toDateString()
        if (!acc[date]) {
          acc[date] = []
        }
        acc[date].push(entry)
        return acc
      }, {})
      
      // 获取最新日期的所有条目
      const sortedDates = Object.keys(groupedByDate).sort((a, b) => new Date(b) - new Date(a))
      if (sortedDates.length > 0) {
        finalEntries = groupedByDate[sortedDates[0]]
      }
    }

    // 按创建时间排序（最新的在前）
    finalEntries = finalEntries.sort((a, b) => new Date(b.created) - new Date(a.created))

    console.log(`最终筛选出 ${finalEntries.length} 条当天数据`)

    // 为每个条目添加语言字段
    const enhancedEntries = finalEntries.map(entry => {
      // 找到匹配 cannyId 的标签
      const matchedLabel = entry.labels.find(label => cannyIds.includes(label.id))
      
      let langId = ''
      let langName = ''
      
      if (matchedLabel) {
        langId = matchedLabel.id
        langName = matchedLabel.name
      }
      
      return {
        ...entry,
        langId,
        langName
      }
    })

    console.log('已添加语言字段:', enhancedEntries.map(e => `${e.title} (${e.langId})`).join(', '))

    // 构造结果数据
    const result = {
      hasMore: data.hasMore,
      entries: enhancedEntries
    }

    // 写入文件
    fs.writeFileSync(DATA_FILE_PATH, JSON.stringify(result, null, 2), 'utf8')
    console.log(`🏘 数据已成功写入: ${DATA_FILE_PATH}`)

    // 输出更新的条目标题
    enhancedEntries.forEach(entry => {
      console.log(`- ${entry.title} [${entry.langId}] (${new Date(entry.created).toLocaleDateString()})`)
    })

    // 检查缺失的语言条目
    console.log('\n📋 多语言完整性检查:')
    const existingCannyIds = new Set(enhancedEntries.map(entry => entry.langId))
    const missingCannyIds = cannyIds.filter(id => !existingCannyIds.has(id))
    
    if (missingCannyIds.length > 0) {
      console.log(`⚠️  缺少 ${missingCannyIds.length} 个语言条目:`)
      missingCannyIds.forEach(cannyId => {
        const langInfo = langConfigMap.get(cannyId)
        if (langInfo) {
          console.log(`   - 缺少 ${langInfo.label} (${langInfo.lang}, cannyId: ${cannyId})`)
        } else {
          console.log(`   - 缺少 cannyId: ${cannyId}`)
        }
      })
    } else {
      console.log('✅ 所有语言条目完整')
    }
    
    console.log(`📊 统计: 配置 ${cannyIds.length} 个语言，实际获取 ${enhancedEntries.length} 个条目`)

  } catch (error) {
    console.error('获取 Canny 数据失败:', error.message)
    
    if (error.response) {
      console.error('响应状态:', error.response.status)
      console.error('响应数据:', error.response.data)
    }
    
    process.exit(1)
  }
}

// 检查 API Key
if (!API_KEY) {
  console.error('错误: 请设置 VITE_CANNY_API_KEY 环境变量')
  console.log('使用方法: VITE_CANNY_API_KEY=your_api_key yarn update-canny')
  process.exit(1)
}

fetchCannyData()