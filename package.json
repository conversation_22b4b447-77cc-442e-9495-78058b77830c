{"name": "pumpkin-dapp-web", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": "22.14.0", "yarn": "1.22.19"}, "scripts": {"=========== 启动命令 ===========": "", "serve:dev": "vite --mode dev", "serve:test": "vite --mode test", "serve:main": "vite --mode main", "=========== 打包命令 ===========": "", "build:dev": "tsc & vite build --mode dev", "build:test": "tsc & vite build --mode test", "build:main": "tsc & vite build --mode main", "=========== 配置检查 ===========": "", "lint": "eslint 'src/**/*.{js,jsx,ts,tsx,json}' --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "=========== 自定义命令(start) ===========": "", "update-canny": "node tools/update-canny-data.js", "add-commit-msg": "if ! grep -q 'yarn commitlint' .husky/commit-msg; then echo 'yarn commitlint --edit \"$1\"' > .husky/commit-msg; fi", "add-pre-commit": "if ! grep -q 'yarn lint-staged' .husky/pre-commit; then echo 'yarn lint-staged' > .husky/pre-commit; fi", "prepare": "yarn run husky && yarn run add-commit-msg && yarn run add-pre-commit", "=========== 自定义命令(end) =============": ""}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --report-unused-disable-directives"], "src/**/*.{css,scss}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "dependencies": {"@ahooksjs/use-url-state": "3.5.1", "@nktkas/hyperliquid": "0.17.2", "@okxconnect/solana-provider": "1.5.5", "@okxconnect/ui": "1.5.5", "@okxweb3/coin-solana": "1.0.8", "@privy-io/react-auth": "2.14.1", "@radix-ui/react-slider": "1.3.2", "@rongcloud/engine": "5.12.1", "@rongcloud/imlib-next": "5.12.1", "@rongcloud/plugin-rtc": "5.7.4", "@solana/spl-token": "0.4.13", "@solana/web3.js": "1.98.2", "@typescript-eslint/eslint-plugin": "8.18.2", "@typescript-eslint/parser": "8.18.2", "@vitejs/plugin-basic-ssl": "1.1.0", "ahooks": "3.8.1", "antd": "5.22.6", "antd-mobile": "5.38.1", "axios": "1.7.7", "clsx": "2.1.1", "crypto-browserify": "3.12.1", "danmu": "0.15.1", "dayjs": "1.11.13", "decimal.js": "10.4.3", "ethers": "6.13.5", "i18next": "23.16.2", "i18next-browser-languagedetector": "8.0.0", "input-otp": "1.4.2", "js-cookie": "3.0.5", "lodash": "4.17.21", "mitt": "3.0.1", "mobx": "6.13.3", "mobx-react-lite": "4.0.7", "numeral": "2.0.6", "qrcode.react": "4.2.0", "qs": "6.13.0", "react": "19.0.0", "react-copy-to-clipboard": "5.1.0", "react-device-detect": "2.2.3", "react-dom": "19.0.0", "react-i18next": "15.1.0", "react-infinite-scroll-component": "6.1.0", "react-intersection-observer": "9.13.1", "react-joyride": "2.9.3", "react-responsive": "10.0.0", "react-router-dom": "6.26.2", "react-virtualized-auto-sizer": "1.0.26", "react-window": "1.8.11", "rxjs": "7.8.1", "store2": "2.14.4", "stream-browserify": "3.0.0", "twin.macro": "3.4.1", "util": "0.12.5", "vconsole": "3.15.1", "viem": "2.23.5", "webview-javascript-bridge": "2.0.0"}, "devDependencies": {"@ant-design/v5-patch-for-react-19": "1.0.3", "@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@eslint/js": "9.9.0", "@stylistic/eslint-plugin": "4.2.0", "@svgr/rollup": "8.1.0", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@types/react-window": "1.8.8", "@vitejs/plugin-react-swc": "3.5.0", "autoprefixer": "10.4.20", "buffer": "6.0.3", "code-inspector-plugin": "0.17.2", "dotenv": "16.5.0", "eslint": "9.17.0", "eslint-config-prettier": "9.1.0", "eslint-config-react-app": "7.0.1", "eslint-plugin-jest": "28.8.3", "eslint-plugin-n": "17.10.3", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-promise": "7.1.0", "eslint-plugin-react": "7.36.1", "eslint-plugin-react-hooks": "5.1.0", "eslint-plugin-react-refresh": "0.4.12", "globals": "15.9.0", "husky": "9.1.6", "lint-staged": "15.2.10", "postcss": "8.4.47", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "0.6.8", "react-markdown": "10.1.0", "remark-gfm": "4.0.1", "snazzy": "9.0.0", "strip-ansi": "7.1.0", "styled-components": "6.1.13", "tailwindcss": "3.4.13", "typescript": "5.5.3", "typescript-eslint": "8.0.1", "vite": "5.4.1", "vite-plugin-babel-macros": "1.0.6", "vite-plugin-node-polyfills": "0.23.0"}}