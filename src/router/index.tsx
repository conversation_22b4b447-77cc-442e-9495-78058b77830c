/**
 * @external react-router-dom
 * */
import React from 'react'
import { createBrowserRouter } from 'react-router-dom'

import Layout from '@/Layout'
import NotFoundPage from '@/pages/NotFoundPage'

const withAuth = (LazyComponent: React.ComponentType) => {
  const AuthWrapper = (props: any) => {
    const RequireAuth = React.lazy(() => import('@/router/RequireAuth'))
    return (
      <React.Suspense fallback={<div>加载中...</div>}>
        <RequireAuth>
          <LazyComponent {...props} />
        </RequireAuth>
      </React.Suspense>
    )
  }
  return AuthWrapper
}

// 开发环境路由配置
const devRoutes = import.meta.env.DEV
  ? [
      {
        path: '/demos/*',
        async lazy() {
          const { Component } = await import('@/pages/Demos')
          return { Component }
        },
      },
    ]
  : []

const router = createBrowserRouter([
  {
    element: <Layout />,
    children: [
      {
        path: '/',
        lazy: () => import('@/pages/Home/HomePage'),
      },
      {
        path: '/msgs',
        lazy: () => import('@/pages/Msgs/MsgsPage'),
      },
      {
        path: '/apps',
        lazy: () => import('@/pages/Apps/AppsPage'),
      },
      {
        path: '/perps',
        lazy: () => import('@/pages/Perp/PerpPage'),
      },
      {
        path: '/profile',
        children: [
          {
            path: '',
            lazy: () => import('@/pages/Profile/ProfilePage'),
          },
          {
            path: 'twitter-bind',
            lazy: () => import('@/pages/Profile/TwitterBindPage'),
          },
          {
            path: 'twitter-unbind',
            lazy: () => import('@/pages/Profile/TwitterUnbindPage'),
          },
          {
            path: 'friends',
            lazy: () => import('@/pages/Profile/FriendsPage'),
          },
          {
            path: 'followings',
            lazy: () => import('@/pages/Profile/FollowingsPage'),
          },
          {
            path: 'followers',
            lazy: () => import('@/pages/Profile/FollowersPage'),
          },
          {
            path: 'followers',
            lazy: () => import('@/pages/Profile/FollowersPage'),
          },
          {
            path: 'edit',
            lazy: () => import('@/pages/Profile/EditProfilePage'),
          },
          {
            path: 'referral',
            lazy: () => import('@/pages/Profile/ReferralPage'),
          },
        ],
      },
      {
        path: '/chatroom',
        lazy: async () => {
          const module = await import('@/pages/ChatRoom/ChatRoomPage')
          return {
            Component: withAuth(module.Component),
          }
        },
      },
      {
        path: '/trade',
        lazy: async () => {
          const module = await import('@/pages/TradePage/TradePage')
          return {
            Component: withAuth(module.Component),
          }
        },
      },
      {
        path: '/wallet',
        lazy: () => import('@/pages/Wallet/WalletPage'),
      },
      {
        path: '/investment',
        lazy: () => import('@/pages/Wallet/WalletPage'),
      },
      {
        path: '/settings',
        lazy: () => import('@/pages/Settings/SettingsPage'),
      },
      {
        path: '/points',
        lazy: () => import('@/pages/Points/PointsPage'),
      },
      {
        path: '/search',
        lazy: () => import('@/pages/Search/SearchPage'),
      },
      {
        path: '/user',
        children: [
          {
            path: '',
            lazy: () => import('@/pages/User/UserPage'),
          },
          {
            path: ':id',
            lazy: () => import('@/pages/User/UserPage'),
          },
        ],
      },
      {
        path: '/guide',
        lazy: () => import('@/pages/Guide/GuidePage'),
      },
      {
        path: '/mguide',
        lazy: () => import('@/pages/Guide/GuideMobilePage'),
      },
      {
        path: '/terms',
        lazy: () => import('@/pages/Terms/TermsPage'),
      },
      {
        path: '/privacy',
        lazy: () => import('@/pages/Terms/PrivacyPage'),
      },
      {
        path: '/policy',
        lazy: () => import('@/pages/Terms/PolicyPage'),
      },
      ...devRoutes,
    ],
  },
  {
    path: '/403',
    lazy: () => import('@/pages/NotAuthPage'),
  },
  {
    path: '*',
    element: <NotFoundPage />,
  },
])

export { router }
