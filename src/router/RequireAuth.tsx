import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { userStore } from '@/store'
import { StorageUtils } from '@/utils/STORAGE_REG'

interface RequireAuthProps {
  children: React.ReactNode
}

const RequireAuth: React.FC<RequireAuthProps> = ({ children }) => {
  const location = useLocation()

  if (!userStore.IS_LOGIN()) {
    // 被邀请用户视角注入邀请码
    if (location?.search) {
      const cid = new URLSearchParams(location.search).get('cid')
      if (cid) StorageUtils.setReferralCid(cid)
    }

    // 如果用户未登录，重定向到登录页面，并记录当前路径以便登录后返回
    return <Navigate to="/" state={{ from: location }} replace />
  }

  return <>{children}</>
}

export default RequireAuth
