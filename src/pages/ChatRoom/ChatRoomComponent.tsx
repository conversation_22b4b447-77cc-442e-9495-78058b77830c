import {
  ChatroomErrorCode,
  Mode,
  chatroomManager,
} from '@/managers/chatroomManager'
import { Flex, Modal } from 'antd'
import {
  createContext,
  useContext,
  useEffect,
  useLayoutEffect,
  useRef,
} from 'react'
import AudioRoomHeader from '@/pages/ChatRoom/module/AudioRoomHeader'
import { observer } from 'mobx-react-lite'
import { ChatRoomPageStore } from '@/pages/ChatRoom/store/ChatRoomPageStore'
import { useRoute } from '@/hooks/useRoute'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import i18n from '@/i18n'
import { BuildChatModeAndScreenShareing } from '@/pages/ChatRoom/module/BuildChatModeAndScreenShareing'
import { BuildChatModeOnly } from '@/pages/ChatRoom/module/BuildChatModeOnly'
import { BuildTradeModeAndScreenShareing } from '@/pages/ChatRoom/module/BuildTradeModeAndScreenShareing'
import { BuildTradeModeOnly } from '@/pages/ChatRoom/module/BuildTradeModeOnly'
import { BuildPerpModeAndScreenShareing } from '@/pages/ChatRoom/module/BuildPerpModeAndScreenShareing'
import { BuildPerpModeOnly } from '@/pages/ChatRoom/module/BuildPerpModeOnly'
import { IS_OPEN_ROOM_LOG } from '@/config'
import { ButtonBlack } from '@/components/Customize/Button'
import JoinRoomContent from '@/pages/ChatRoom/module/JoinLockRoomContent'
import { useUserInteracted } from '@/pages/ChatRoom/hooks/useUserInteracted'
import Danmu from '@/pages/ChatRoom/module/Danmu'
import { QUERY_ROOM_DETIAL_BY_ID } from '@/api/interface/QUERY_ROOM_DETIAL_BY_ID'

const store = new ChatRoomPageStore()
export const StoreContext = createContext<ChatRoomPageStore>(store)
export const useStore = () => useContext(StoreContext)

const ChatRoomComponent = observer(() => {
  const { hasUserInteracted } = useUserInteracted()
  const { path } = useRoute()
  const { addQuery, query } = useRoute()
  const { toastErr, toastInfo } = useToastMessage()
  const { roomMode, isScreenShareing } = store

  const hasJoined = useRef(false)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible' && store.roomId) {
        try {
          const roomInfo = await chatroomManager.fetchRoomDetail(store.roomId)
          if (
            roomInfo &&
            roomInfo.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST
          ) {
            handleRoomClosedOrKicked()
          } else {
            console.log('Room status OK.')
          }
        } catch (error: any) {
          console.error('Error checking room status:', error)
          // 根据 SDK 返回的错误码判断
          if (error.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST) {
            handleRoomClosedOrKicked()
          }
        }
      }
    }

    // 房间关闭或用户被踢出房间
    const handleRoomClosedOrKicked = () => {
      toastInfo(i18n.t('this_room_is_closed'))
      store.leaveRoom()
      path('/')
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [store.roomId])

  const joinRoom = (roomId: string, password: string) => {
    store
      .joinRoom(roomId as string, password, true)
      .then((res) => {
        if (res.code === ChatroomErrorCode.PK_SUCCESS) {
          console.log(`refresh`)
        }
      })
      .catch((error) => {
        // 用户被踢出房间
        if (error && error.code === ChatroomErrorCode.RC_CHATROOM_USER_KICKED) {
          toastErr(i18n.t('you_have_been_blocked_and_cannot_join_the_room'))
          store.roomDetial.roomId = ''
          path('/')
          return
        }
        // 房间不存在
        if (error && error.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST) {
          toastErr(i18n.t('this_room_is_closed'))
          path('/') // 房间不存在也要离开
          return
        }
        // 密码错误
        if (
          error &&
          error.code === ChatroomErrorCode.PK_PASSWORD_IS_INCORRECT
        ) {
          toastErr(i18n.t('password_incorrect'))
          path('/')
          return
        }
        toastErr(`${error.message}(${error.code})`)
        path('/') // 其他加入失败也离开
      })
  }

  // 加入房间
  useEffect(() => {
    if (!hasJoined.current) {
      const curRoomId = query?.roomId
      if (curRoomId) {
        if (!hasUserInteracted) {
          const curRoomIdFromLocal = store.getCurRoomId()
          if (curRoomIdFromLocal === curRoomId) {
            // 已经在房间内
            Modal.confirm({
              title: null,
              icon: null,
              content: (
                <Flex vertical className="items-center justify-center">
                  <Flex className="mb-4 text-center text-[20px] font-bold text-[#1A1B1E]">
                    {i18n.t('need_interaction')}
                  </Flex>
                  <Flex className="mb-4 text-center text-[14px] font-normal text-[#1A1B1E]">
                    {i18n.t('browsers_require_user_interaction')}
                  </Flex>
                  <ButtonBlack
                    onClick={() => {
                      const curPassword = store.getCurPassword()
                      joinRoom(curRoomId, curPassword ?? '')
                      Modal.destroyAll()
                    }}
                  >
                    {i18n.t('user_interaction_confirm')}
                  </ButtonBlack>
                </Flex>
              ),
              width: 500,
              okButtonProps: { style: { display: 'none' } },
              cancelButtonProps: { style: { display: 'none' } },
              footer: null,
              maskClosable: false,
              closable: false,
            })
          } else {
            Modal.confirm({
              title: null,
              icon: null,
              content: (
                <JoinRoomContent
                  roomId={curRoomId}
                  onJoinRoom={(password) => {
                    joinRoom(curRoomId, password)
                    Modal.destroyAll()
                  }}
                  onReturnHome={() => {
                    path('/')
                    Modal.destroyAll()
                  }}
                />
              ),
              width: 500,
              okButtonProps: { style: { display: 'none' } },
              cancelButtonProps: { style: { display: 'none' } },
              footer: null,
              maskClosable: false,
              closable: false,
            })
          }
        }
        IS_OPEN_ROOM_LOG &&
          console.log(`****** AudioRoomHeader curRoomId:${curRoomId} ******`)
      }
      hasJoined.current = true
    }
  }, [query?.roomId]) // 确保 query?.roomId 作为依赖

  // 更新 query 参数
  useEffect(() => {
    // 邀请用户视角注入邀请码
    addQuery({ roomMode })
  }, [roomMode])

  useEffect(() => {
    const roomId = query?.roomId
    const pwd = store.getCurPassword() || ''
    QUERY_ROOM_DETIAL_BY_ID({ roomId, pwd }).then((res) => {
      // 用户必须是房主才能注入邀请码
      if (res.data?.conciseUserId) {
        addQuery({ cid: res.data?.conciseUserId, roomMode })
      }
    })
  }, [])

  useLayoutEffect(() => {
    if (query?.roomMode) {
      store.handleModeChanged(query.roomMode as Mode)
    }
    if (query?.roomId) {
      store.roomDetial.roomId = query.roomId
    }
  }, [])

  return (
    <StoreContext.Provider value={store}>
      <Danmu />
      <Flex
        ref={containerRef}
        className="h-dvh w-full bg-[##313131] text-white"
        vertical={true}
      >
        <Flex className="mb-4 w-full border-[#313131] py-2 md:mb-0 md:border-b">
          <AudioRoomHeader />
        </Flex>
        <div className="h-[calc(100dvh-64px)] w-full overflow-hidden">
          {/* 聊天模式 - 共享 */}
          {roomMode === Mode.Chat && isScreenShareing && (
            <BuildChatModeAndScreenShareing />
          )}
          {/* 聊天模式 - 非共享 */}
          {roomMode === Mode.Chat && !isScreenShareing && <BuildChatModeOnly />}

          {/* 现货交易模式 - 共享 */}
          {roomMode === Mode.Trade && isScreenShareing && (
            <BuildTradeModeAndScreenShareing />
          )}
          {/* 现货交易模式 - 非共享 */}
          {roomMode === Mode.Trade && !isScreenShareing && (
            <BuildTradeModeOnly />
          )}

          {/* 永续合约交易模式 - 共享 */}
          {store.roomMode === Mode.Perp && store.isScreenShareing && (
            <BuildPerpModeAndScreenShareing />
          )}
          {/* 永续合约交易模式 - 非共享 */}
          {store.roomMode === Mode.Perp && !store.isScreenShareing && (
            <BuildPerpModeOnly />
          )}
        </div>
      </Flex>
    </StoreContext.Provider>
  )
})

export default ChatRoomComponent
