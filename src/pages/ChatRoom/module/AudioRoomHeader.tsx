import {
  IconRoomFollowRoom,
  IconRoomIsLocked,
  IconRoomBack,
} from '@/imgs/icons'
import {
  chatroomManager,
  ChatroomManagerEvents,
  RoomRole,
  IOnlineUserItem,
  MicPosIndex,
} from '@/managers/chatroomManager'
import { Divider, Flex } from 'antd'
import { useEffect, useLayoutEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import InviteJoin from '@/pages/ChatRoom/module/InviteJoin'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { useRoute } from '@/hooks/useRoute'
import { Trans, useI18n } from '@/hooks/useI18n'
import { userStore } from '@/store'
import { IS_OPEN_ROOM_LOG } from '@/config'
import { ViewButton } from '@/pages/ChatRoom/module/ViewButton'
import { useDevice } from '@/hooks/useDevice'
import { ModHot } from '@/pages/ChatRoom/module/ModHot'
import clsx from 'clsx'
import { ButtonExitScreen } from '@/pages/ChatRoom/module/button/ButtonExitScreen'
import { RoomModalProvider } from '@/pages/ChatRoom/module/button/provider'
import {
  ButtonNotice,
  ButtonNoticeModal,
} from '@/pages/ChatRoom/module/button/ButtonNotice'
import {
  ButtonLock,
  ButtonLockModal,
} from '@/pages/ChatRoom/module/button/ButtonLock'
import {
  ButtonAdmin,
  ButtonAdminModal,
} from '@/pages/ChatRoom/module/button/ButtonAdmin'
import { ButtonSettingModal } from '@/pages/ChatRoom/module/button/ButtonSetting'
import { ButtonColoseModal } from '@/pages/ChatRoom/module/button/ButtonColose'
import {
  ButtonMoreDesktop,
  ButtonMoreMobile,
} from '@/pages/ChatRoom/module/button/ButtonMore'
import CopyToClipboard from '@/components/CopyToClipboard'
import Monitor from '@/pages/Home/module/Monitor'
import { ButtonBlack, ButtonSecondary } from '@/components/Customize/Button'
import { Modal } from '@/components/Customize/Modal'
import { useRoleBasedPermission } from '../hooks/useRoleBasedPermission'
import ButtonDanmu from './button/ButtonDanmu'
import ButtonClearMsg from './button/ButtonClearMsg'
import { SvgIconOnlineUsers } from '@/imgs/other'
import { PopupDown } from '@/components/PopupDown'
import OnlineUserList from './OnlineUserList'

const AudioRoomHeader = observer(() => {
  const eventsRegistered = useRef(false)
  const { t } = useI18n()
  const store = useStore()
  const { path } = useRoute()
  const { isMobile } = useDevice()
  const { toastSuc, toastErr, toastInfo } = useToastMessage()
  const { roleCheck } = useRoleBasedPermission()
  const [visibleOnlineUserList, setVisibleOnlineUserList] = useState(false)

  useLayoutEffect(() => {
    return () => {
      document.title = 'Pumpkin - Where Live Streaming Meets Trading'
    }
  }, [])

  useEffect(() => {
    if (store?.roomDetial?.roomTitle) {
      document.title = `Pumpkin - ${store.roomDetial.roomTitle}`
    }
  }, [store?.roomDetial?.roomTitle])

  const handleRejectInviteJoinMicPos = (event: {
    // 事件类型
    contentType: number
    // 邀请者昵称
    fromNickname: string
    // 邀请者ID
    fromUserId: string
    // 房间ID
    roomId: string
    // 被邀请者ID
    toUserId: string
  }) => {
    toastInfo(
      t('user_declines_your_invitation', { nickname: event?.fromNickname }),
    )
  }

  const handleAddedAdminMsg = () => {
    store
      .updateRoomDetial()
      .then(() => {
        store.updateOnlineUserList(store.roomId, userStore.info.id)
        toastInfo(t('you_have_been_set_as_an_administrator'))
      })
      .catch((err) => {
        console.error('handleRemovedAdminMsg', err)
      })
  }

  const handleRemovedAdminMsg = async () => {
    // 自己在麦上，且是主持人位。（说明被房主取消了管理员权限）
    const selfMicPosInfo = store.micPosInfos.find(
      (item) => item.userId === userStore.info.id,
    )
    if (selfMicPosInfo && selfMicPosInfo.micPosIndex === MicPosIndex.compere) {
      await chatroomManager.pkLeaveMic(store.roomId, MicPosIndex.compere)
    }

    store
      .updateRoomDetial()
      .then(() => {
        store.updateOnlineUserList(store.roomId, userStore.info.id)
        toastInfo(t('your_administrative_privileges_have_been_removed'))
      })
      .catch((err) => {
        console.error('handleRemovedAdminMsg', err)
      })
  }

  const handleAddedBlackMSG = async () => {
    toastInfo(t('you_have_been_kicked_out_of_the_room'))
    await store.leaveRoom()
    store.roomDetial.roomId = ''
    store.isFloating = false
    path('/')
  }

  const handleSelfMutedByAdminMSG = () => {
    toastInfo(`Mic has been muted.`)
  }

  const handleRoomDestroyed = async () => {
    toastInfo(t('this_room_is_closed'))
    await store.leaveRoom()
    store.isFloating = false
    path('/')
  }

  const handleMicUnauthorized = () => {
    toastInfo(t('you_are_not_authorized_for_the_microphone'))
  }

  const handleMutiClientKickEachOther = async (event: any) => {
    try {
      const { quitRoomId } = event
      if (quitRoomId === store.roomId) {
        toastInfo(t('You have already logged in on another device'))
        await store.leaveRoom()
        path('/')
      }
    } catch (error) {
      console.error(`handleMutiClientKickEachOther`, error)
    }
  }

  useEffect(() => {
    if (!eventsRegistered.current) {
      IS_OPEN_ROOM_LOG && console.log(`****** AudioRoomHeader useEffect ******`)
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKMicUnauthorized,
        handleMicUnauthorized,
      )
      chatroomManager.addEventListener(
        ChatroomManagerEvents.PKMicUnauthorized,
        handleMicUnauthorized,
      )

      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKRoomDestroyed,
        handleRoomDestroyed,
      )
      chatroomManager.addEventListener(
        ChatroomManagerEvents.PKRoomDestroyed,
        handleRoomDestroyed,
      )

      // MSG:
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKMutiClientKickEachOther,
        handleMutiClientKickEachOther,
      )
      chatroomManager.addEventListener(
        ChatroomManagerEvents.PKMutiClientKickEachOther,
        handleMutiClientKickEachOther,
      )
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKSelfMutedByAdminMSG,
        handleSelfMutedByAdminMSG,
      )
      chatroomManager.addEventListener(
        ChatroomManagerEvents.PKSelfMutedByAdminMSG,
        handleSelfMutedByAdminMSG,
      )

      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKAddedBlackMSG,
        handleAddedBlackMSG,
      )
      chatroomManager.addEventListener(
        ChatroomManagerEvents.PKAddedBlackMSG,
        handleAddedBlackMSG,
      )

      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKAddedAdminMSG,
        handleAddedAdminMsg,
      )
      chatroomManager.addEventListener(
        ChatroomManagerEvents.PKAddedAdminMSG,
        handleAddedAdminMsg,
      )

      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKRemovedAdminMSG,
        handleRemovedAdminMsg,
      )
      chatroomManager.addEventListener(
        ChatroomManagerEvents.PKRemovedAdminMSG,
        handleRemovedAdminMsg,
      )

      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKRejectInviteJoinMicPosMSG,
        handleRejectInviteJoinMicPos,
      )
      chatroomManager.addEventListener(
        ChatroomManagerEvents.PKRejectInviteJoinMicPosMSG,
        handleRejectInviteJoinMicPos,
      )

      eventsRegistered.current = true
    }
    return () => {
      IS_OPEN_ROOM_LOG && console.log(`****** AudioRoomHeader unmount ******`)
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKRoomDestroyed,
        handleRoomDestroyed,
      )
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKMicUnauthorized,
        handleMicUnauthorized,
      )

      // MSG:
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKMutiClientKickEachOther,
        handleMutiClientKickEachOther,
      )
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKSelfMutedByAdminMSG,
        handleSelfMutedByAdminMSG,
      )
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKAddedBlackMSG,
        handleAddedBlackMSG,
      )
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKAddedAdminMSG,
        handleAddedAdminMsg,
      )
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKRemovedAdminMSG,
        handleRemovedAdminMsg,
      )
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKRejectInviteJoinMicPosMSG,
        handleRejectInviteJoinMicPos,
      )
      eventsRegistered.current = false
    }
  }, [])

  const buildKickOutUser = () => {
    const durations = [
      { label: t('10_minutes'), minutes: 10 },
      { label: t('1_hour'), minutes: 60 },
      { label: t('1_day'), minutes: 24 * 60 },
      { label: t('1_week'), minutes: 7 * 24 * 60 },
      { label: t('1_month'), minutes: 30 * 24 * 60 },
    ]

    // 封装调用 addBlack 的公共逻辑
    const handleAddBlack = (minutes: number) => {
      if (store.clickedNeedBlackUserId) {
        if (store.roomDetial.roomRole === RoomRole.Master) {
          if (store.isScreenShareing) {
            store.isScreenShareing = false
            chatroomManager
              .updateRoomSetting(store.roomId, { isScreenShareing: false })
              .then(() => {
                IS_OPEN_ROOM_LOG && console.log(`updateRoomSetting bingo`)
              })
              .catch((err) => {
                console.error(`updateRoomSetting error`, err)
              })
          }
        }
        chatroomManager
          .addBlack(store.roomId, store.clickedNeedBlackUserId, minutes, true)
          .then((res) => {
            if (res.code !== 200) {
              toastErr(`${res.message}(${res.code})`)
              return
            }
            toastSuc(t('success'))
            store.fetchBlockList()
            store.setOnlineUsers((prevUsers: IOnlineUserItem[]) => {
              return prevUsers.filter(
                (item) => item.userId !== store.clickedNeedBlackUserId,
              )
            })
            store.clickedNeedBlackUserId = null
            store.isOpenAddBlackListModel = false
          })
          .catch((err) => {
            console.error('addBlack', err)
          })
      }
    }
    return (
      <Modal
        open={store.isOpenAddBlackListModel}
        onCancel={() => {
          store.clickedNeedBlackUserId = null
          store.isOpenAddBlackListModel = false
        }}
        afterClose={() => {
          store.clickedNeedBlackUserId = null
          store.isOpenAddBlackListModel = false
        }}
      >
        <Flex className="flex-col items-center justify-center space-y-2">
          {durations.map((item) => (
            <div
              key={item.label}
              className="cursor-pointer text-[16px] font-normal text-black"
              onClick={() => handleAddBlack(item.minutes)}
            >
              {item.label}
            </div>
          ))}
        </Flex>
      </Modal>
    )
  }

  const buildInviteJoinMicPosModel = () => {
    return (
      <Modal
        open={store.isOpenInviteJoinMicPosModel}
        onCancel={() => {
          store.isOpenInviteJoinMicPosModel = false
        }}
        afterClose={() => {
          store.isOpenInviteJoinMicPosModel = false
        }}
        centered
        footer={null}
      >
        <Flex vertical className="w-full items-center justify-center">
          <Trans
            i18nKey="invites_you_to_be_on_stage_do_you_accept"
            className="mt-[20px] text-[18px] font-normal text-[#1A1B1E]"
            values={{ name: store.recivedInviteJoinMicPosObj?.fromNickname }}
          />
          <div className="mb-[30px] mt-10 grid w-full grid-cols-2 gap-2 md:gap-4">
            <ButtonSecondary
              onClick={() => {
                const { roomId, fromUserId } = store.recivedInviteJoinMicPosObj
                chatroomManager
                  .rejectInviteJoin(roomId, fromUserId)
                  .then(() => {
                    toastSuc(t('success'))
                    store.recivedInviteJoinMicPosObj = null
                    store.isOpenInviteJoinMicPosModel = false
                  })
                  .catch(() => {
                    store.recivedInviteJoinMicPosObj = null
                    store.isOpenInviteJoinMicPosModel = false
                  })
              }}
            >
              {t('reject')}
            </ButtonSecondary>
            <ButtonBlack
              className="h-10"
              onClick={() => {
                try {
                  const { roomId, toUserId, fromUserId } =
                    store.recivedInviteJoinMicPosObj
                  chatroomManager
                    .agreeInviteJoin(roomId, toUserId, fromUserId)
                    .then(() => {
                      toastSuc(t('success'))
                      store.recivedInviteJoinMicPosObj = null
                      store.isOpenInviteJoinMicPosModel = false
                    })
                    .catch(() => {
                      store.recivedInviteJoinMicPosObj = null
                      store.isOpenInviteJoinMicPosModel = false
                    })
                } catch {
                  console.error('header accept error')
                }
              }}
            >
              {t('accept')}
            </ButtonBlack>
          </div>
        </Flex>
      </Modal>
    )
  }

  const handleGoback = () => {
    path('/')
    // store.leaveRoom()
    // store.isFloating = false
    // if (window.history.length > 1) {
    //   window.history.back()
    // } else {
    //   path('/')
    // }
  }

  const handleFoloow = () => {
    if (store.roomDetial.roomId) {
      chatroomManager
        .followRoom(store.roomDetial.roomId)
        .then((res) => {
          if (res.code !== 200) {
            toastSuc(`${res.message}(${res.code})`)
            return
          }
          toastSuc(t('success'))
          chatroomManager
            .fetchRoomDetail(store.roomId, store.roomPassword)
            .then((res) => {
              if (res?.data && res?.data.roomId === store.roomId) {
                store.roomDetial = res?.data
              }
            })
            .catch((err) => {
              console.error('fetchRoomDetail error', err)
            })
        })
        .catch((err) => {
          console.error('followRoom error', err)
        })
    }
  }

  return (
    <main className="h-full w-full px-4 md:px-0">
      <Flex className="w-full items-center justify-between">
        <RoomModalProvider>
          <Flex className="relative h-full items-center">
            {/* 回退按钮 */}
            {!isMobile && (
              <IconRoomBack className="mr-2 size-10" onClick={handleGoback} />
            )}
            <Flex className={clsx('relative items-center justify-center')}>
              <img
                src={store.roomDetial.roomPic}
                className="size-10 rounded-full"
              />
              {store.isLocked ? (
                <IconRoomIsLocked className="absolute z-[10] size-10" />
              ) : null}
            </Flex>
            {/* 房名和ID */}
            <Flex vertical={true} className="ml-2 items-start">
              <Flex className="items-center font-lexend text-[14px] font-medium">
                {/* 限制 roomTitle 的最大长度为 200px */}
                <div
                  className={clsx(
                    'overflow-hidden text-ellipsis whitespace-nowrap',
                    isMobile ? 'max-w-[90px]' : 'max-w-48',
                  )}
                  title={store.roomDetial.roomTitle} // 添加 title 属性，鼠标悬停时显示完整标题
                >
                  {store.roomDetial.roomTitle}
                </div>
                {/* 显示关注图标 */}
                {store.roomDetial.isFollow !== true &&
                store.roomDetial.roomRole !== RoomRole.Master ? (
                  <IconRoomFollowRoom
                    className="ml-1 size-5 cursor-pointer md:ml-2"
                    onClick={handleFoloow}
                  />
                ) : null}
              </Flex>
              <Flex className="items-center justify-center text-10 font-medium text-gray-400">
                <CopyToClipboard
                  text={store.roomDetial.roomId || ''}
                  onCopy={() => {
                    toastSuc(t('success'))
                  }}
                >
                  ID:{store.roomDetial.roomId}
                </CopyToClipboard>
              </Flex>
            </Flex>
            {/* 热度和标签 */}
            {!isMobile && <ModHot type="icon" />}
            {!isMobile && store.isShowMonitor() && <Monitor />}
          </Flex>
          <Flex className="h-full items-center">
            {isMobile ? (
              <div className="flex items-center gap-2.5">
                <ButtonNotice />
                <ButtonMoreMobile />
              </div>
            ) : (
              <>
                <Flex className="ml-auto gap-2">
                  <ViewButton />
                  {/* 投屏按钮 */}
                  <ButtonExitScreen />
                  <section className="flex items-center gap-2">
                    {/* 公告按钮 */}
                    <ButtonNotice />

                    {/* 弹幕开关，所有角色都有 */}
                    <ButtonDanmu />

                    <Divider type="vertical" className="h-10 bg-[#303030]" />
                  </section>
                  <section className="flex items-center gap-2">
                    <ButtonClearMsg />
                    {/* 上锁按钮 */}
                    <ButtonLock />
                    {/* 黑名单按钮 */}
                    <ButtonAdmin type="blacklist" />
                    {/* 管理员按钮 */}
                    <ButtonAdmin type="adminlist" />

                    {/* 普通用户不显示 */}
                    {!roleCheck.isCivilian && (
                      <Divider type="vertical" className="h-10 bg-[#303030]" />
                    )}
                  </section>
                  {/* 设置按钮 */}
                  {/* <ButtonSetting /> */}
                  {/* 更多按钮 */}
                  <ButtonMoreDesktop />
                </Flex>
              </>
            )}
            <InviteJoin open={store.isOpenInviteJoinMicPos} />
          </Flex>
          {buildInviteJoinMicPosModel()}
          {buildKickOutUser()}
          <Flex className="absolute right-0 top-0 z-10">
            <ButtonNoticeModal />
            <ButtonLockModal />
            <ButtonAdminModal />
            <ButtonSettingModal />
            <ButtonColoseModal />
          </Flex>
        </RoomModalProvider>
      </Flex>
      <section className="mt-2 flex items-center justify-between md:hidden">
        <div className="-ml-6 inline-flex">{<ModHot type="icon" />}</div>
        <div className="flex items-center justify-end gap-2">
          <div>
            <ViewButton />
          </div>
          <div
            className="flex cursor-pointer items-center gap-1 rounded-16 bg-white/20 px-1.5"
            onClick={() => setVisibleOnlineUserList(true)}
          >
            <SvgIconOnlineUsers className="size-3" />
            {store.roomOnlineUserCount}
          </div>
        </div>

        {/* 用户列表 */}
        <PopupDown
          visible={visibleOnlineUserList}
          onClose={() => setVisibleOnlineUserList(false)}
        >
          <div className="h-96 px-3">
            <OnlineUserList type="detailed" />
          </div>
        </PopupDown>
      </section>
    </main>
  )
})

export default AudioRoomHeader
