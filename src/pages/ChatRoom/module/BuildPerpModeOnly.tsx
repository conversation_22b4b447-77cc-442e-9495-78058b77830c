import { Flex } from 'antd'
import { observer } from 'mobx-react-lite'
import AudioRoom from '@/pages/ChatRoom/module/AduioRoom'
import ChatRoom from '@/pages/ChatRoom/module/ChatRoom'
import { ModeSwitch } from '@/pages/ChatRoom/module/ModeSwitch'
import OnlineUserList from '@/pages/ChatRoom/module/OnlineUserList'
import { useDevice } from '@/hooks/useDevice'
import PerpPage from '@/pages/Perp/PerpPage'

export const BuildPerpModeOnly = observer(() => {
  const { isMobile } = useDevice()

  if (isMobile) {
    return (
      <div className="flex h-full flex-col">
        <div className="h-[40px] w-full">
          <ModeSwitch type="mobile" />
        </div>
        <PerpPage />
      </div>
    )
  }

  return (
    <Flex className="h-full flex-grow">
      <Flex className="h-full w-16">
        <OnlineUserList type="simple" />
      </Flex>

      {/* 麦克位与聊天部分 */}
      <Flex vertical={true} className="w-[360px]">
        <Flex className="h-[360px] w-full border-r border-[#313131]">
          <AudioRoom />
        </Flex>

        <Flex className="h-[calc(100vh-64px-360px)] flex-grow overflow-hidden">
          <ChatRoom />
        </Flex>
      </Flex>

      {/* 交易面板 */}
      <Flex className="h-[calc(100vh-64px)] flex-grow">
        <PerpPage />
      </Flex>

      <Flex vertical={true} className="w-16 border-l border-[#313131]">
        <ModeSwitch type="desktop" />
      </Flex>
    </Flex>
  )
})
