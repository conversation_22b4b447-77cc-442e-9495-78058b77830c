/**
 * @file 弹幕组件
 * @description 展示弹幕功能的组件，支持交易消息和热门币种消息
 */

import { type Manager, type Danmaku, create } from 'danmu'
import { useEffect, useRef, useCallback } from 'react'
import { observer } from 'mobx-react-lite'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { ChatroomManagerEvents, Mode } from '@/managers/chatroomManager'
import {
  ChatRoomPageStore,
  type IStatuses,
} from '@/pages/ChatRoom/store/ChatRoomPageStore'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import ReactDOM from 'react-dom/client'
import { useTranslation } from 'react-i18next'
import SymbolIcon from '@/pages/Trade/module/SymbolIcon'
import Image from '@/components/Customize/Image'
import emitter from '@/utils/emitter'
import { useDevice } from '@/hooks/useDevice'
import { useRoute } from '@/hooks/useRoute'
import CombinedUserPopover from './CombinedUserPopover'
import { MemoryRouter } from 'react-router-dom'
import { MessageProvider } from '@/components/ToastMessage/MessageProvider'
import { useClickAway } from 'ahooks'
import TranslatedText from '@/components/TranslatedText'

/** 链ID类型定义 */
type IChainId = 'solana' | 'ethereum' | 'bsc' | 'base'

/** 弹幕交易消息接口 */
interface IDanmuTradeMessage {
  roomId: string
  role: string | null
  userId: string
  userThumbnail: string
  nickname: string
  fromAmount: string
  fromCurrency: string
  buyOrSell: boolean
  currencyThumbnail: string
  toCurrency: string
  chainId: IChainId
  fromContractAddress: string
  toContractAddress: string
  messageType?: MessageType
  createTime?: number | string
}

/** 弹幕热门交易币种消息接口 */
interface IDanmuHotMessage {
  currencyIcon: string
  currency: string
  liveNum: number
  chainId: IChainId
  contractAddress: string
  messageType?: MessageType
  createTime?: number | string
}

/** 弹幕消息类型枚举 */
enum MessageType {
  /** 直播热聊消息 */
  LiveChat = 105,
  /** 交易消息 */
  Trade = 107,
}

export type IDanmuMessage = IDanmuTradeMessage | IDanmuHotMessage

/** 弹幕消息数据接口 */
export interface IMessageData {
  type: MessageType
  data: IDanmuMessage
  createTime?: number
}

// 添加一个全局变量来记录最后一次页面获得焦点的时间
let lastVisibleTime = Date.now()

// 添加一个标记变量，用于标记页面是否刚刚变为可见状态
let isFirstVisibleAfterHidden = false

/**
 * @description 判断用户角色
 * @param {IDanmuTradeMessage} data - 交易消息数据
 * @param {string | undefined} currentRoomId - 当前房间ID
 * @returns {string} 用户角色
 */
const getDanmuUserRole = (
  data: IDanmuTradeMessage,
  currentRoomId?: string,
): string => {
  // 所有值转为字符串再比较
  const roomId = String(currentRoomId || '').trim()
  const dataRoomId = String(data.roomId || '').trim()

  // 如果不是当前房间的消息，返回user
  if (roomId !== dataRoomId) {
    return 'user'
  }

  // 在当前房间内的角色判断
  if (data.role === 'compere') return 'role_type_host'
  if (data.role?.startsWith('microphone_owner')) return 'guest'
  if (data.role === null) return 'role_type_audience'

  return 'role_type_audience'
}

/**
 * @description 处理弹幕消息
 * @param {Manager<IMessageData, IStatuses>} manager - 弹幕管理器实例
 * @param {number} type - 消息类型
 * @param {IDanmuHotMessage | IDanmuTradeMessage} data - 从服务器接收的原始数据
 */
const handleDanmuMessage = (
  manager: Manager<IMessageData, IStatuses>,
  type: number,
  data?: IDanmuHotMessage | IDanmuTradeMessage,
) => {
  if (data) {
    // 获取消息创建时间
    const createTime = Number(data.createTime)

    // 如果当前处于"刚刚可见"的窗口期，或消息创建时间晚于页面可见时间，或未指定创建时间
    if (
      isFirstVisibleAfterHidden ||
      createTime === 0 ||
      createTime > lastVisibleTime
    ) {
      manager.push({
        type,
        data,
        createTime: createTime || Date.now(),
      })
    }
  }
}

/**
 * @description 初始化弹幕管理器
 * @param {ChatRoomPageStore} store - 聊天室页面状态存储
 * @param {boolean} isMobile - 是否为移动端
 * @returns {Manager<IMessageData, IStatuses>} 弹幕管理器实例
 */
const initDanmuManager = (
  store: ChatRoomPageStore,
  isMobile?: boolean,
  onNavigate?: (path: string, params?: any) => void,
  currentRoomId?: string,
) => {
  const manager = create<IMessageData, IStatuses>({
    trackHeight: isMobile ? 40 : 48,
    rate: isMobile ? 0.5 : 0.3,
    durationRange: !isMobile ? [4000, 6000] : [6000, 6000],
    plugin: {
      init(manager) {
        'bg-transparent'.split(' ').forEach((c) => {
          manager.container.node.classList.add(c)
        })
      },
      $createNode: (dm) => {
        if (!dm.node) return
        ReactDOM.createRoot(dm.node).render(
          <MemoryRouter>
            <MessageProvider>
              <DanmakuItem
                manager={manager}
                danmaku={dm}
                store={store}
                onNavigate={onNavigate}
                currentRoomId={currentRoomId}
              />
            </MessageProvider>
          </MemoryRouter>,
        )
      },
    },
  })
  return manager
}

/**
 * @interface I18nCryptoIconProps
 * @description 加密货币图标组件属性接口
 */
interface I18nCryptoIconProps {
  /** 链ID */
  chainId: IChainId
  /** 图片URL */
  imageUrl: string
  /** 货币名称 */
  name: string
  /** 图标大小 */
  size?: number
  /** 附加类名 */
  className?: string
  /** 是否显示点击事件 */
  onClick?: () => void
}

/**
 * @component I18nCryptoIcon
 * @description 加密货币图标组件，用于在文本中渲染加密货币图标
 */
const I18nCryptoIcon: React.FC<I18nCryptoIconProps> = ({
  chainId,
  imageUrl,
  name,
  size = 18,
  className = '',
  onClick,
}) => {
  return (
    <div
      className={`inline-flex items-center text-status-warning ${className}`}
      onClick={onClick}
    >
      <SymbolIcon
        chainId={chainId}
        image_url={imageUrl}
        name={name}
        size={size}
      />
      <span className="mx-0.5">${name}</span>
    </div>
  )
}

/**
 * @component DanmuTradeMessage
 * @description 弹幕交易消息组件
 */
const DanmuTradeMessage: React.FC<
  IDanmuTradeMessage & {
    currentRoomId?: string
    onSymbolClick?: (data: IDanmuTradeMessage) => void
    onNavigate?: (path: string, params?: any) => void
  }
> = (props) => {
  const { t } = useTranslation()
  const role = getDanmuUserRole(
    props as IDanmuTradeMessage,
    props.currentRoomId,
  )

  const handleSymbolClick = () => {
    props.onSymbolClick?.(props as IDanmuTradeMessage)
  }

  return (
    <div>
      <TranslatedText
        className="pointer-events-auto box-border inline-flex h-9 cursor-pointer items-center space-x-1 rounded-4 bg-theme-primary/80 p-2 py-1.5 text-14 font-400 text-white md:hover:bg-[#232429]"
        i18nKey={props.buyOrSell ? 'trade_message_buy' : 'trade_message_sell'}
        values={{
          role: t(role),
          avatar: props.userThumbnail,
          nickname: props.nickname,
          amount: props.fromAmount,
          unit: props.buyOrSell ? props.fromCurrency : props.toCurrency,
          token: props.buyOrSell ? props.toCurrency : props.fromCurrency,
        }}
        components={{
          role: () => <span className="mr-1 text-white">{t(role)}</span>,
          avatar: () => {
            return (
              <Image
                src={props.userThumbnail}
                size={18}
                alt="User"
                className="rounded-full"
              />
            )
          },
          nickname: () => (
            <CombinedUserPopover
              userId={props.userId}
              trigger={['hover', 'click']}
              onNavigate={props.onNavigate}
            >
              <span>{props.nickname}</span>
            </CombinedUserPopover>
          ),
          amount: <span className="text-status-warning" />,
          unit: <span className="mr-1 text-white" />,
          token: () => (
            <I18nCryptoIcon
              onClick={handleSymbolClick}
              chainId={props.chainId}
              imageUrl={props.currencyThumbnail}
              name={props.buyOrSell ? props.toCurrency : props.fromCurrency}
            />
          ),
        }}
      />
    </div>
  )
}

/**
 * @component DanmuHotMessage
 * @description 弹幕热门币种消息组件
 */
const DanmuHotMessage: React.FC<
  IDanmuHotMessage & {
    onSymbolClick?: (data: IDanmuHotMessage) => void
  }
> = (props) => {
  const handleSymbolClick = () => {
    props.onSymbolClick?.(props)
  }

  return (
    <TranslatedText
      className="pointer-events-auto box-border inline-flex h-9 cursor-pointer items-center space-x-2 rounded-4 bg-theme-primary/80 p-2 py-1.5 text-14 font-400 text-white md:hover:bg-[#232429]"
      i18nKey="hot_chat_rooms"
      values={{
        count: props.liveNum,
        token: props.currency,
        icon: '🔥',
      }}
      components={{
        icon: <span className="-mr-1 text-16" />,
        count: (values) => <span className="text-white">{values.count}</span>,
        token: () => (
          <I18nCryptoIcon
            onClick={handleSymbolClick}
            chainId={props.chainId}
            imageUrl={props.currencyIcon}
            name={props.currency}
          />
        ),
      }}
    />
  )
}

/**
 * @component DanmakuItem
 * @description 单个弹幕的渲染组件
 */
const DanmakuItem = ({
  manager,
  danmaku,
  store,
  onNavigate,
  currentRoomId,
}: {
  manager: Manager<IMessageData, IStatuses>
  danmaku: Danmaku<IMessageData>
  store: ChatRoomPageStore
  onNavigate?: (path: string, params?: any) => void
  currentRoomId?: string
}) => {
  const { isMobile } = useDevice()
  const messageData = danmaku.data
  const danmakuRef = useRef<HTMLDivElement>(null)

  const handleSymbolClick = useCallback(
    (data: IDanmuTradeMessage | IDanmuHotMessage) => {
      console.log('🚀🚀🚀🚀点击弹幕🚀🚀🚀', data)
      // 如果当前是聊天模式，先切换到交易模式
      if (store.roomMode !== Mode.Trade) {
        store.handleModeChanged(Mode.Trade)
      } else {
        // 在交易模式下显示闪光效果
        store.showFlash()
      }

      // 无论是哪种模式，都需要设置当前加密货币
      // 根据消息类型不同设置不同的加密货币数据
      const messageType = messageData.type

      if (messageType === MessageType.Trade) {
        const tradeData = data as IDanmuTradeMessage
        tradeStore.setCurrCrypto({
          chainId: tradeData.chainId,
          contract_address: tradeData.buyOrSell
            ? tradeData.toContractAddress
            : tradeData.fromContractAddress,
          name: tradeData.buyOrSell
            ? tradeData.toCurrency
            : tradeData.fromCurrency,
        } as any)
      } else {
        const hotData = data as IDanmuHotMessage
        tradeStore.setCurrCrypto({
          chainId: hotData.chainId,
          contract_address: hotData.contractAddress,
          name: hotData.currency,
        } as any)
      }
    },
    [store, messageData.type, onNavigate],
  )

  // 暂停弹幕移动
  const pauseDanmaku = useCallback(() => {
    danmaku.pause()
  }, [danmaku])

  // 恢复弹幕移动
  const resumeDanmaku = useCallback(() => {
    if (!manager.isFreeze()) {
      danmaku.resume()
    }
  }, [danmaku, manager])

  // 使用 useClickAway 监听点击外部事件
  useClickAway(() => {
    if (isMobile) {
      resumeDanmaku()
    }
  }, danmakuRef)

  return (
    <div
      ref={danmakuRef}
      className="pointer-events-auto cursor-pointer"
      onMouseEnter={!isMobile ? pauseDanmaku : undefined}
      onMouseLeave={!isMobile ? resumeDanmaku : undefined}
      onClick={isMobile ? pauseDanmaku : undefined}
    >
      {messageData.type === MessageType.Trade ? (
        <DanmuTradeMessage
          {...(messageData.data as IDanmuTradeMessage)}
          onSymbolClick={handleSymbolClick}
          onNavigate={onNavigate}
          currentRoomId={currentRoomId}
        />
      ) : (
        <DanmuHotMessage
          {...(messageData.data as IDanmuHotMessage)}
          onSymbolClick={handleSymbolClick}
        />
      )}
    </div>
  )
}

/**
 * @description 自动调整弹幕格式
 * @param {Manager<IMessageData>} manager - 弹幕管理器实例
 */
const autoFormat = (manager: Manager<IMessageData, IStatuses>) => {
  const resizeObserver = new ResizeObserver(() => manager.format())
  resizeObserver.observe(document.body)
}

/**
 * @component Danmu
 * @description 弹幕主组件
 */
const Danmu = observer(() => {
  const store = useStore()
  const danmuContainerRef = useRef<HTMLDivElement>(null)
  const { isMobile } = useDevice()
  const { path, query } = useRoute()

  // 在组件挂载时初始化弹幕
  useEffect(() => {
    if (!danmuContainerRef.current) return

    // 初始化弹幕管理器，传入 isMobile 参数
    const manager = initDanmuManager(store, isMobile, path, query.roomId)
    store.setDanmuManager(manager)

    // 定义页面可见性变化处理函数
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // 标记为刚刚从隐藏状态变为可见状态
        isFirstVisibleAfterHidden = true

        // 更新最后一次页面可见时间
        lastVisibleTime = Date.now()
        console.log('页面重新获得焦点，更新时间戳:', lastVisibleTime)

        // 清空当前所有弹幕
        manager.clear()

        // 设置一个短暂的延时，之后重置标记
        setTimeout(() => {
          isFirstVisibleAfterHidden = false
        }, 100)
      }
    }

    // 定义事件处理函数
    const handleLiveChatMessage = (serverData?: IMessageData) => {
      // if (!serverData || !serverData.data) return

      console.log('🚀🚀🚀🚀处理直播热聊消息🚀🚀🚀🚀:', serverData)

      // 提取createTime并转发给handleDanmuMessage
      const messageData = serverData?.data
      if (messageData) {
        messageData.createTime = serverData.createTime
        handleDanmuMessage(manager, MessageType.LiveChat, messageData)
      }
    }

    const handleTradeMessage = (serverData?: IMessageData) => {
      // if (!serverData || !serverData.data) return

      console.log('🚀🚀🚀🚀处理交易消息🚀🚀🚀🚀:', serverData)

      // 提取createTime并转发给handleDanmuMessage
      const messageData = serverData?.data
      if (messageData) {
        messageData.createTime = serverData.createTime
        handleDanmuMessage(manager, MessageType.Trade, messageData)
      }
    }

    // 注册事件监听
    emitter.on(ChatroomManagerEvents.PKDanmuMsgLiveChat, handleLiveChatMessage)
    emitter.on(ChatroomManagerEvents.PKDanmuMsgTrade, handleTradeMessage)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 设置自动格式化
    autoFormat(manager)

    // 设置全屏变化格式化处理
    const handleFullscreenChange = () => {
      manager.nextFrame(() => {
        if (danmuContainerRef.current) {
          manager.mount(danmuContainerRef.current)
        }
      })
    }

    // 挂载弹幕容器并开始播放
    manager.mount(danmuContainerRef.current)
    manager.startPlaying()
    document.addEventListener('fullscreenchange', handleFullscreenChange)

    // 根据当前状态显示或隐藏弹幕
    if (!store.isDanmuVisible) {
      manager.hide()
    } else {
      manager.show()
    }

    // 清理函数
    return () => {
      // 移除事件监听
      emitter.off(
        ChatroomManagerEvents.PKDanmuMsgLiveChat,
        handleLiveChatMessage,
      )
      emitter.off(ChatroomManagerEvents.PKDanmuMsgTrade, handleTradeMessage)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      store.clearDanmuManager()
    }
  }, [store, isMobile])

  return (
    <div
      ref={danmuContainerRef}
      id="DanmuRenderContainer"
      className="pointer-events-none fixed top-[72px] z-[100] h-12 w-full md:top-20 md:h-24"
    ></div>
  )
})

export default Danmu
