import { FOLLOW } from '@/api/interface/FOLLOW'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { ButtonBlack } from '@/components/Customize/Button'
import { useRoute } from '@/hooks/useRoute'
import { userStore } from '@/store'
import { usePrivy } from '@privy-io/react-auth'
import { Divider } from 'antd'
import { observer } from 'mobx-react-lite'
import { useEffect, useState } from 'react'
import { PlusOutlined } from '@ant-design/icons'
import { SvgIconTwitterXAuthed } from '@/imgs/icons'
export const ModUser = observer(({ item, ...rest }: { item: any }) => {
  const { path } = useRoute()
  const { toastErr, toastSuc } = useToastMessage()
  const { authenticated, login, ready } = usePrivy()
  const [loadingStates, setLoadingStates] = useState<{
    [key: string]: boolean
  }>({})
  const [isFollow, setIsFollow] = useState<{
    [key: string]: boolean
  }>({})

  useEffect(() => {
    setIsFollow((prev) => ({ ...prev, [item?.id]: item?.isFollow }))
  }, [item])

  function onProfile(item: any) {
    if (userStore.IS_LOGIN() === false) {
      if (ready && authenticated === false) {
        login()
        return
      }
    }
    path(`/user/${item?.id}`)
  }

  function onFollow(item: any) {
    if (userStore.IS_LOGIN() === false) {
      if (ready && authenticated === false) {
        login()
        return
      }
    }
    if (loadingStates[item?.id]) return
    setLoadingStates((prev) => ({ ...prev, [item?.id]: true }))
    FOLLOW(item?.id)
      .then((res) => {
        if (res.code !== 200) {
          toastErr('Failed, please try again later')
        } else {
          toastSuc('Success')
          setIsFollow((prev) => ({ ...prev, [item?.id]: true }))
        }
      })
      .catch((error) => {
        console.error(`error: ${error}`)
        toastErr('Failed, please try again later')
      })
      .finally(() => {
        setLoadingStates((prev) => ({ ...prev, [item?.id]: false }))
      })
  }

  return (
    <div className="w-full" {...rest}>
      <div className="flex w-full items-center justify-between">
        <div
          className="flex cursor-pointer items-center gap-2"
          onClick={() => onProfile(item)}
        >
          <div
            className="relative h-[50px] w-[50px] rounded-full bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url(${item?.avatar})`,
            }}
          >
            {item?.onlineStatus === 0 && (
              <div className="absolute bottom-0 right-0 h-3.5 w-3.5 rounded-full border border-gray-50 bg-[#14b855]" />
            )}
          </div>
          <div className="inline-flex h-12 flex-col items-start justify-center gap-1">
            <div className="inline-flex items-center justify-start gap-2">
              <div className="text-base font-normal leading-normal text-[#1a1b1e]">
                {item?.nickname}
              </div>
              {item?.twitterInfo?.id && (
                <SvgIconTwitterXAuthed className="h-4 w-4" />
              )}
            </div>
            <div className="text-sm font-normal leading-tight text-[#aaacbb]">
              ID:{item?.conciseUserId}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <ButtonBlack
            disabled={isFollow[item?.id]}
            loading={loadingStates[item?.id]}
            onClick={() => onFollow(item)}
            className="h-9 w-28 leading-7"
          >
            <PlusOutlined />
          </ButtonBlack>
        </div>
      </div>
      <Divider className="my-3" />
    </div>
  )
})
