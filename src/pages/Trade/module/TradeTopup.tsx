import { useEffect, useState } from 'react'
import { DepositModal } from '@/pages/Wallet/DepositModal'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import { ChainIdName } from '@/api/interface/GET_ALL_TOKEN_BALANCE'
import { usePrivy } from '@privy-io/react-auth'
import { useTrade } from '@/pages/Trade/provider/trade.hooks'

export const TradeTopup = () => {
  const { user } = usePrivy()
  const { spotTopupModal, setSpotTopupModal } = useTrade()

  const getChainIdName = (network: string): ChainIdName => {
    const networkMap: Record<string, ChainIdName> = {
      ethereum: 'ETH',
      bsc: 'BSC',
      base: 'Base',
      solana: 'Solana',
    }
    return networkMap[network] || 'solana'
  }

  const [selectedNetwork, setSelectedNetwork] = useState<string>(() => {
    return getChainIdName(tradeStore.currCrypto?.chainId)
  })
  const [withdrawalAddress, setWithdrawalAddress] = useState<string>('Solana')

  // 配置充值地址
  useEffect(() => {
    const chainType = ['ETH', 'BSC', 'Base'].includes(selectedNetwork || '')
      ? 'ethereum'
      : 'solana'
    const account: any = user?.linkedAccounts.find(
      (account: any) => account.chainType === chainType.toLowerCase(),
    )
    setWithdrawalAddress(account?.address)
  }, [user, selectedNetwork])

  useEffect(() => {
    setSelectedNetwork(getChainIdName(tradeStore.currCrypto?.chainId))
  }, [tradeStore.currCrypto?.chainId])

  return (
    <DepositModal
      visible={spotTopupModal}
      onClose={() => setSpotTopupModal(false)}
      currentNetwork={selectedNetwork}
      address={withdrawalAddress}
      onNetworkChange={setSelectedNetwork}
      type="spot"
    />
  )
}
