import {
  ButtonPrimaryDown,
  ButtonPrimaryStatus,
  ButtonPrimaryUp,
} from '@/components/Customize/Button'
import { observer } from 'mobx-react-lite'
import { $ten, milliFormat } from '@/utils'
import { useEffect, useState, useRef, useMemo } from 'react'
import clsx from 'clsx'
import i18n from '@/i18n'
import { InputNumberTarde } from '@/components/Customize/InputNumber'
import { usePrivy } from '@privy-io/react-auth'
import { useChainSwap } from '@/pages/Trade/hooks/useChainSwap'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import { DEXTOKEN_DATA_RETURN } from '@/api/interface/NAME_DEXTOKEN_DATA'
import { CHAIN_SWAP_LIST } from '@/pages/Trade/hooks/config'
import { message } from 'antd'
import { BUTTON_TAG } from '@/pages/Trade/module/BUTTON_TAG'
import { useTradeExecution } from '@/pages/Trade/hooks/useTradeExecution'
import { ModSlippage } from '@/pages/Trade/module/ModSlippage'
import { isMobile } from 'react-device-detect'
import styled, { keyframes } from 'styled-components'
import { WalletColumn } from './WalletColumn'
import { useTrade } from '@/pages/Trade/provider/trade.hooks'

export const ModPlaceOrder = observer(
  ({
    roomId,
    data,
  }: {
    roomId: string
    data: DEXTOKEN_DATA_RETURN['data']
  }) => {
    // 最小下单金额
    const defaultMin = 0.001
    // 最大下单金额
    const defaultMax = 1000000000000000000
    // 下单金额精度
    const precision: number = 4
    // 下单金额
    const [amount, setAmount] = useState<number | null>(null)
    // 是否禁用
    const [disabled] = useState(false)
    const [min, setMin] = useState(defaultMin)
    const [max] = useState(defaultMax)
    const [balance, setBalance] = useState('0')
    const [status, setStatus] = useState<'BUY' | 'SELL'>('BUY')
    const [isBuy, setIsBuy] = useState(true)
    const { authenticated, login } = usePrivy()
    const { getTokenBalance, evmWallet, solanaWallet, connectChainWallet } =
      useChainSwap()
    const { setSpotTopupModal } = useTrade()

    // 保存原始body样式的引用
    const originalBodyStyleRef = useRef<{
      overflow?: string
      position?: string
      top?: string
      width?: string
    }>({})
    // 是否展示键盘
    const [isKeyboardShown, setIsKeyboardShown] = useState(false)

    const { loadingUp, loadingDown, handleBet, NotificationComponent } =
      useTradeExecution()

    const [decimals, setDecimals] = useState(0)
    const [tokenName, setTokenName] = useState('')

    const isReady = useMemo(() => {
      return authenticated
    }, [authenticated])

    useEffect(() => {
      setMin(isBuy ? defaultMin : 0)
    }, [isBuy])

    // 添加键盘事件处理
    useEffect(() => {
      if (isMobile) {
        // 保存原始body样式
        const bodyStyle = document.body.style
        originalBodyStyleRef.current = {
          overflow: bodyStyle.overflow,
          position: bodyStyle.position,
          top: bodyStyle.top,
          width: bodyStyle.width,
        }

        // 添加键盘事件监听
        const handleKeyboardShow = () => {
          // 检测是否为键盘弹出（可通过视口高度变化判断）
          const isKeyboard = window.innerHeight < window.outerHeight * 0.8
          if (isKeyboard && !isKeyboardShown) {
            setIsKeyboardShown(true)
            // 防止页面滚动
            document.body.style.overflow = 'hidden'
            // 固定页面位置
            document.body.style.position = 'fixed'
            document.body.style.width = '100%'
            // 保存当前滚动位置
            document.body.style.top = `-${window.scrollY}px`
          }
        }

        // 键盘隐藏事件处理
        const handleKeyboardHide = () => {
          if (isKeyboardShown) {
            setIsKeyboardShown(false)
            // 恢复原始样式
            const scrollY = parseInt(document.body.style.top || '0') * -1
            document.body.style.overflow =
              originalBodyStyleRef.current.overflow || ''
            document.body.style.position =
              originalBodyStyleRef.current.position || ''
            document.body.style.width = originalBodyStyleRef.current.width || ''
            document.body.style.top = ''

            // 恢复滚动位置
            window.scrollTo(0, scrollY)
          }
        }

        // 监听输入框焦点事件
        const handleInputFocus = () => {
          handleKeyboardShow()
        }

        // 监听输入框失焦事件
        const handleInputBlur = () => {
          // 延迟处理，避免键盘隐藏后立即执行可能导致的布局问题
          setTimeout(() => {
            handleKeyboardHide()
          }, 100)
        }

        // 添加事件监听器
        window.addEventListener('resize', handleKeyboardShow)
        window.addEventListener('blur', handleKeyboardHide)
        window.addEventListener('orientationchange', handleKeyboardHide)

        return () => {
          // 移除事件监听器
          window.removeEventListener('resize', handleKeyboardShow)
          window.removeEventListener('blur', handleKeyboardHide)
          window.removeEventListener('orientationchange', handleKeyboardHide)

          // 恢复原始样式
          document.body.style.overflow =
            originalBodyStyleRef.current.overflow || ''
          document.body.style.position =
            originalBodyStyleRef.current.position || ''
          document.body.style.width = originalBodyStyleRef.current.width || ''
          document.body.style.top = ''
        }
      }
    }, [isMobile, isKeyboardShown])

    useEffect(() => {
      const currAddress = isBuy
        ? tradeStore.currCrypto.chainId
        : tradeStore.currCrypto.contract_address

      const foundBalance =
        tradeStore.balances.find(
          (item) =>
            item.symbolAddress === currAddress &&
            [evmWallet?.address, solanaWallet?.address].includes(item.address),
        )?.balance || '0'

      setBalance(foundBalance)
    }, [
      tradeStore.balances,
      tradeStore.currCrypto.chainId,
      isBuy,
      evmWallet,
      solanaWallet,
    ])

    useEffect(() => {
      // 设置token名称
      setTokenName(
        isBuy
          ? CHAIN_SWAP_LIST[tradeStore.currCrypto.chainId]?.symbol
          : data?.tokenInfo?.symbol,
      )
      // 设置token精度
      setDecimals(
        isBuy
          ? CHAIN_SWAP_LIST[tradeStore.currCrypto.chainId]?.decimals
          : data?.tokenInfo?.decimals,
      )
      getBalance()
    }, [isBuy, data, tradeStore.currCrypto.chainId, evmWallet, solanaWallet])

    useEffect(() => {
      setIsBuy(status === 'BUY')
      setAmount(null)
    }, [status])

    function onSubAmount(amount: string) {
      if (Number(amount) <= min) {
        setAmount(min)
      } else if (Number(amount) >= max) {
        setAmount(max)
      } else {
        setAmount(Number(amount))
      }
    }

    function onAddAmount(append: 'first' | 'second' | 'third' | 'last') {
      let sumAmount = '0'

      if (append === 'first') {
        sumAmount = isBuy
          ? BUTTON_TAG[tradeStore.currCrypto.chainId]?.[
              isBuy ? 'number_1' : 'percent_25'
            ]
          : $ten.mul((balance || 0) as number, 0.25)
      } else if (append === 'second') {
        sumAmount = isBuy
          ? BUTTON_TAG[tradeStore.currCrypto.chainId]?.[
              isBuy ? 'number_2' : 'percent_50'
            ]
          : $ten.mul((balance || 0) as number, 0.5)
      } else if (append === 'third') {
        sumAmount = isBuy
          ? BUTTON_TAG[tradeStore.currCrypto.chainId]?.[
              isBuy ? 'number_3' : 'percent_75'
            ]
          : $ten.mul((balance || 0) as number, 0.75)
      } else if (append === 'last') {
        sumAmount = isBuy
          ? BUTTON_TAG[tradeStore.currCrypto.chainId]?.[
              isBuy ? 'number_4' : 'percent_100'
            ]
          : balance
      }

      onSubAmount($ten.USDT(sumAmount))
    }

    async function onUserBet(direction: 'BUY' | 'SELL' | null) {
      // 验证是否登录
      if (!authenticated) {
        login({
          disableSignup: true,
        })
        return
      }

      // 验证钱包是否连接
      if (!isReady) {
        connectChainWallet(tradeStore.currCrypto.chainId)
        return
      }
      // 验证金额是否为空
      if (!amount) {
        message.error(i18n.t('please_enter_the_transaction_amount'))
        return
      }
      // 验证余额是否充足
      const actualBalance = await getBalance()
      const balanceNum = Number(actualBalance) || Number(balance)
      if (balanceNum < (amount || 0)) {
        message.error(i18n.t('token_balance_is_not_enough', { tokenName }))
        return
      }

      handleBet({
        direction,
        amount,
        decimals,
        roomId,
        getBalance,
      })
    }

    function getBalance(type?: 'cycle') {
      if (type === 'cycle') {
        tradeStore.setPollingBalanceLoading(true)
      }
      return getTokenBalance({
        chainId: tradeStore.currCrypto.chainId,
        decimals: data?.tokenInfo?.decimals,
        tokenAddress: isBuy
          ? tradeStore.currCrypto.chainId
          : tradeStore.currCrypto.contract_address,
      })
    }

    // 判断是否余额不足
    const isInsufficientBalance = useMemo(() => {
      const balanceNum = Number($ten.USDT(balance))
      // 余额为0时显示余额不足
      if (!balanceNum) return true
      // 没有输入金额时不显示余额不足
      if (!amount) return false

      return Number($ten.USDT(balance)) < Number(amount)
    }, [balance, amount])

    return (
      <div className="mb-2 w-full">
        <NotificationComponent />
        <WalletColumn />
        <div className="mb-2 flex h-8 items-center justify-between gap-2">
          <div
            onClick={() => setStatus('BUY')}
            className={clsx(
              'flex h-full flex-1 cursor-pointer items-center justify-center rounded',
              status === 'BUY' ? 'bg-[#0F704B]' : 'border border-[#5f606d]',
            )}
          >
            {i18n.t('buy').toUpperCase()}
          </div>
          <div
            onClick={() => setStatus('SELL')}
            className={clsx(
              'flex h-full flex-1 cursor-pointer items-center justify-center rounded',
              status === 'SELL' ? 'bg-[#891A1B]' : 'border border-[#5f606d]',
            )}
          >
            {i18n.t('sell').toUpperCase()}
          </div>
        </div>
        <div className="mb-2">
          <InputNumberTarde
            value={amount}
            onChange={(value) => setAmount(value as number)}
            className="w-full"
            max={max}
            min={min}
            precision={precision}
            placeholder={i18n.t('enter_amount')}
            onFocus={() => isMobile && setIsKeyboardShown(true)}
            onBlur={() => {
              if (isMobile) {
                setTimeout(() => {
                  setIsKeyboardShown(false)
                }, 100)
              }
            }}
            addonAfter={
              <div>
                <div className="text-right text-sm font-normal leading-tight text-gray-50">
                  {tokenName}
                </div>
                <div className="text-right text-[10px] font-normal leading-3 tracking-tight text-[#9293a0]">
                  {i18n.t('balance')}：
                  {tradeStore.pollingBalanceLoading ? (
                    <ShimmerSpan>{milliFormat($ten.USDT(balance))}</ShimmerSpan>
                  ) : (
                    milliFormat($ten.USDT(balance))
                  )}
                </div>
              </div>
            }
          />
        </div>
        <div className="mb-2 grid w-full grid-cols-4 gap-1">
          <ButtonTag onClick={() => onAddAmount('first')} disabled={disabled}>
            {
              BUTTON_TAG[tradeStore.currCrypto.chainId]?.[
                isBuy ? 'number_1' : 'percent_25'
              ]
            }
          </ButtonTag>
          <ButtonTag onClick={() => onAddAmount('second')} disabled={disabled}>
            {
              BUTTON_TAG[tradeStore.currCrypto.chainId]?.[
                isBuy ? 'number_2' : 'percent_50'
              ]
            }
          </ButtonTag>
          <ButtonTag onClick={() => onAddAmount('third')} disabled={disabled}>
            {
              BUTTON_TAG[tradeStore.currCrypto.chainId]?.[
                isBuy ? 'number_3' : 'percent_75'
              ]
            }
          </ButtonTag>
          <ButtonTag onClick={() => onAddAmount('last')} disabled={disabled}>
            {
              BUTTON_TAG[tradeStore.currCrypto.chainId]?.[
                isBuy ? 'number_4' : 'percent_100'
              ]
            }
          </ButtonTag>
        </div>
        {/* 滑点模块 */}
        {isMobile && (
          <div className="mb-2">
            <ModSlippage />
          </div>
        )}
        <div className="">
          {!authenticated && (
            <ButtonPrimaryStatus
              onClick={() => onUserBet(null)}
              className="h-11 w-full !rounded bg-[#28292f] text-white"
            >
              <div className="text-sm font-normal">
                {i18n.t('connect_wallet')}
              </div>
            </ButtonPrimaryStatus>
          )}
          {status === 'BUY' && authenticated && (
            <ButtonPrimaryUp
              onClick={() =>
                isInsufficientBalance
                  ? setSpotTopupModal(true)
                  : onUserBet('BUY')
              }
              disabled={disabled}
              loading={loadingUp}
              className="h-11 w-full !rounded text-white"
            >
              <div className="text-sm font-normal">
                {isInsufficientBalance
                  ? i18n.t('top_up')
                  : i18n.t('up').toUpperCase()}
              </div>
            </ButtonPrimaryUp>
          )}
          {status === 'SELL' && authenticated && (
            <ButtonPrimaryDown
              onClick={() => onUserBet('SELL')}
              disabled={disabled || isInsufficientBalance}
              loading={loadingDown}
              className="h-11 w-full !rounded text-white"
            >
              <div className="text-sm font-normal">
                {i18n.t('down').toUpperCase()}
              </div>
            </ButtonPrimaryDown>
          )}
        </div>
      </div>
    )
  },
)

function ButtonTag({
  children,
  onClick,
  disabled,
}: {
  children: React.ReactNode
  onClick: () => void
  disabled: boolean
}) {
  return (
    <div
      className={clsx(
        `flex h-8 items-center justify-center rounded border border-[#5f606d] px-4 text-center text-xs font-medium`,
        disabled
          ? 'cursor-not-allowed text-black/50'
          : 'cursor-pointer text-sm text-gray-50',
      )}
      onClick={onClick}
    >
      {children}
    </div>
  )
}

const shimmerAnimation = keyframes`
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
`

// 优化后的 ShimmerSpan，光效只在文字上
const ShimmerSpan = styled.span`
  display: inline-block;
  position: relative;
  color: transparent; /* 使原文字透明 */
  background: linear-gradient(
    90deg,
    #9293a0 0%,
    /* 文字原色 */ #c5c6d0 50%,
    /* 光效亮色 */ #9293a0 100% /* 文字原色 */
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  animation: ${shimmerAnimation} 2s infinite linear;
`

export default ModPlaceOrder
