import { cutToken } from '@/utils'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import CopyToClipboard from '@/components/CopyToClipboard'
import { SymbolChainIcon } from '@/pages/Trade/module/SymbolIcon'
import { observer } from 'mobx-react-lite'
import { message } from 'antd'
import i18n from '@/i18n'

import { useMemo } from 'react'
import { LoadingOutlined } from '@ant-design/icons'
import { usePrivy } from '@privy-io/react-auth'
import { useMedia } from '@/hooks/useMedia'
import { IconRoomFollowRoom } from '@/imgs/icons'
import { useTrade } from '@/pages/Trade/provider/trade.hooks'

export const WalletColumn = observer(() => {
  const { user } = usePrivy()
  const { isMd } = useMedia()
  const { setSpotTopupModal } = useTrade()

  // 获取钱包地址
  const walletAddress = useMemo(() => {
    const chainType = ['ethereum', 'bsc', 'base'].includes(
      tradeStore.currCrypto?.chainId,
    )
      ? 'ethereum'
      : 'solana'
    const account: any = user?.linkedAccounts.find(
      (account: any) => account.chainType === chainType,
    )

    return account?.address
  }, [user, tradeStore.currCrypto?.chainId])

  if (!walletAddress) {
    return (
      <div className="flex w-full items-center justify-center py-2">
        <LoadingOutlined />
      </div>
    )
  }

  return (
    <div className="mb-2 hidden items-center justify-between gap-2 rounded-lg bg-[#1a1b1e] p-2 md:flex">
      <div className="flex items-center gap-2">
        <SymbolChainIcon
          chainId={tradeStore.currCrypto?.chainId}
          className="size-5 rounded-full object-cover"
        />
        <div>
          <CopyToClipboard
            text={walletAddress}
            onCopy={() => {
              message.success(i18n.t('copy_success'))
            }}
          >
            <div>{cutToken(walletAddress || '', '...', isMd ? 12 : 16)}</div>
          </CopyToClipboard>
        </div>
      </div>
      <div className="flex cursor-pointer items-center gap-2">
        <IconRoomFollowRoom
          className="size-3 md:size-4"
          onClick={() => setSpotTopupModal(true)}
        />
      </div>
    </div>
  )
})
