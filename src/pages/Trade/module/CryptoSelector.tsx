/**
 * @file CryptoSelector.tsx
 * @description 加密货币选择器组件，支持PC端和移动端两种模式，用于搜索和选择加密货币
 */

import { ConfigProvider, Divider, Input, Spin } from 'antd'
import {
  useEffect,
  useState,
  useRef,
  useReducer,
  useCallback,
  useMemo,
} from 'react'
import { SvgIconDelete, SvgIconZoomOut } from '@/imgs/icons'
import { SEARCH_TOKEN_LIST } from '@/api/interface/SEARCH_TOKEN_LIST'
import { useLocalStorageState, useDebounceFn, useClickAway } from 'ahooks'
import { STORAGE_TRADE_CRYPTO_LIST } from '@/utils/STORAGE_REG'
import { cutToken } from '@/utils'
import numeral from 'numeral'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import { LoadingOutlined } from '@ant-design/icons'
import { SEARCH_TOKEN, SEARCH_TOKEN_RETURN } from '@/api/interface/SEARCH_TOKEN'
import { SvgIconNoResults } from '@/imgs/other'
import SymbolIcon, { DefaultSymbolIcon } from '@/pages/Trade/module/SymbolIcon'
import i18n from '@/i18n'
import FormatIconAmount from '@/components/FormatIconAmount'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'

/**
 * 全局配置常量
 * MAX_RECENT_TOKENS: 历史记录最大保存数量
 * PLACEHOLDER: 数据为空时显示的占位符
 * DEBOUNCE_WAIT: 搜索防抖等待时间(毫秒)
 * THEME: Ant Design组件主题配置
 */
const CONFIG = {
  MAX_RECENT_TOKENS: 9,
  PLACEHOLDER: '-',
  DEBOUNCE_WAIT: 300,
  THEME: {
    token: {
      colorBgElevated: '#28292f',
      colorBorder: 'transparent',
      colorText: '#ffffff',
      colorTextPlaceholder: '#5f606d',
      borderRadius: 4,
      controlHeight: 40,
      controlPaddingHorizontal: 20,
    },
    components: {
      Input: {
        activeBorderColor: '#F9FAFB',
        hoverBorderColor: '#F9FAFB',
        activeBg: '#1a1b1e',
        addonBg: '#1a1b1e',
        hoverBg: '#1a1b1e',
      },
    },
  },
} as const

// ==================== 类型定义 ====================

/**
 * 加密货币数据项类型
 * 从API接口获取的单个加密货币数据结构
 */
type ICryptoItem = SEARCH_TOKEN_RETURN['data']

/**
 * 通用组件属性接口
 * 定义了所有选择器相关组件共享的基本属性
 */
interface ICommonProps {
  /** 选择加密货币时的回调函数 */
  onSelect?: (item: ICryptoItem) => void
  /** 可选的自定义类名 */
  className?: string
}

/**
 * 加密货币选择器组件属性接口
 * 扩展了通用属性，添加了特定于选择器的配置选项
 */
interface ICryptoSelectorProps extends ICommonProps {
  /** 组件显示类型，'mobile'为移动端样式，'desktop'为桌面端样式 */
  type?: 'mobile' | 'desktop'
  /** 当选择变化时的回调函数，主要用于移动端 */
  onChange?: (crypto: ICryptoItem) => void
  /** 当下拉框可见性变化时的回调函数 */
  onVisibleChange?: (visible: boolean) => void
  /** 组件宽度，单位像素，默认为316 */
  width?: number
}

/**
 * 加密货币列表属性接口
 * 用于渲染加密货币列表的组件属性
 */
interface ICryptoListProps {
  /** 要显示的加密货币数据项数组 */
  items: ICryptoItem[]
  /** 选择加密货币时的回调函数 */
  onSelect: (item: ICryptoItem) => void
}

/**
 * 搜索状态接口
 * 定义搜索相关的状态数据结构
 */
interface ISearchState {
  /** 当前搜索输入值 */
  value: string
  /** 搜索加载状态 */
  loading: boolean
  /** 搜索结果项 */
  items: ICryptoItem[]
}

/**
 * 搜索操作类型
 * 定义了所有可能的搜索状态更新操作
 */
type ISearchAction =
  | { type: 'SET_VALUE'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ITEMS'; payload: ICryptoItem[] }
  | { type: 'RESET' }

/**
 * 热门列表状态接口
 * 定义热门加密货币列表相关的状态数据结构
 */
interface IHotListState {
  /** 热门列表加载状态 */
  loading: boolean
  /** 热门列表数据项 */
  items: ICryptoItem[]
}

/**
 * 热门列表操作类型
 * 定义了所有可能的热门列表状态更新操作
 */
type IHotListAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ITEMS'; payload: ICryptoItem[] }
  | { type: 'RESET' }

/**
 * 搜索状态处理器
 * 处理搜索相关的状态更新，遵循Redux模式
 * @param state 当前搜索状态
 * @param action 要执行的操作
 * @returns 更新后的搜索状态
 */
const searchReducer = (
  state: ISearchState,
  action: ISearchAction,
): ISearchState => {
  switch (action.type) {
    case 'SET_VALUE':
      return { ...state, value: action.payload }
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    case 'SET_ITEMS':
      return { ...state, items: action.payload }
    case 'RESET':
      return { value: '', loading: false, items: [] }
    default:
      return state
  }
}

const hotListReducer = (
  state: IHotListState,
  action: IHotListAction,
): IHotListState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    case 'SET_ITEMS':
      return { ...state, items: action.payload }
    case 'RESET':
      return { loading: false, items: [] }
    default:
      return state
  }
}

// ==================== 工具函数 ====================

/**
 * 生成加密货币唯一键值
 * 为列表项创建稳定、唯一的key
 */
const getCryptoKey = (item: ICryptoItem): string => {
  return `${item.chainId || ''}_${item.contract_address}`
}

/**
 * 格式化交易量显示
 * 将数字转换为格式化的交易量显示，如1200000 -> 1.20M
 */
const formatVolumeDisplay = (value: number | null): string => {
  if (value === null || value === undefined) return CONFIG.PLACEHOLDER
  return numeral(value).format('0.00a').toUpperCase()
}

// ==================== 共用组件 ====================

/**
 * 清除按钮组件
 */
const ClearButton = ({
  onClick,
}: {
  onClick: (e: React.MouseEvent) => void
}) => (
  <button
    className="flex cursor-pointer items-center space-x-1 text-gray-450"
    onClick={onClick}
  >
    <SvgIconDelete className="size-3" />
    <span>{i18n.t('clear')}</span>
  </button>
)

/**
 * 搜索图标组件
 */
const SearchIcon = () => <SvgIconZoomOut className="size-3.5 text-gray-500" />

/**
 * 清除图标组件
 */
const ClearIcon = ({ className = '' }: { className?: string }) => (
  <span
    className={`flex size-4 items-center justify-center rounded-full bg-gray-400 ${className}`}
  >
    <span className="-mt-0.5 text-14 text-black">x</span>
  </span>
)

/**
 * 搜索输入框组件
 */
interface ISearchInputProps {
  /** 搜索值 */
  value: string
  /** 值变化回调 */
  onChange: (value: string) => void
  /** 额外的类名 */
  className?: string
  /** 引用对象 */
  inputRef?: React.RefObject<any>
  /** 聚焦回调 */
  onFocus?: () => void
  /** 失焦回调 */
  onBlur?: () => void
  /** 点击回调 */
  onClick?: () => void
  /** 清除图标的额外类名 */
  clearIconClassName?: string
}

const SearchInput = ({
  value,
  onChange,
  className = '',
  inputRef,
  onFocus,
  onBlur,
  onClick,
  clearIconClassName = '',
}: ISearchInputProps) => (
  <ConfigProvider theme={CONFIG.THEME}>
    <Input
      className={`w-full ${className}`}
      allowClear={{
        clearIcon: <ClearIcon className={clearIconClassName} />,
      }}
      ref={inputRef}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      onFocus={onFocus}
      onBlur={onBlur}
      onClick={onClick}
      placeholder={i18n.t('search_crypto')}
      prefix={<SearchIcon />}
      autoComplete="off"
    />
  </ConfigProvider>
)

// ==================== 基础组件 ====================

/**
 * 无结果视图组件
 */
const NoResultsView = () => (
  <section className="flex min-h-[200px] flex-col items-center justify-center gap-4 py-14">
    <SvgIconNoResults className="w-32" />
    <p className="text-sm font-normal leading-tight text-gray-450">
      {i18n.t('no_match_result')}
    </p>
  </section>
)

/**
 * 加载视图组件
 */
const LoadingView = ({ className = '' }: { className?: string }) => (
  <div
    className={`flex min-h-[200px] items-center justify-center ${className}`}
  >
    <Spin
      size="small"
      className="text-white"
      indicator={<LoadingOutlined spin />}
    />
  </div>
)

/**
 * 加密货币列表项组件
 */
const CryptoListItem = ({
  item,
  onClick,
}: {
  item: ICryptoItem
  onClick: () => void
}) => (
  <article
    key={getCryptoKey(item)}
    className="flex w-full cursor-pointer items-center justify-between gap-2 p-1 hover:bg-neutral-900"
    onClick={onClick}
  >
    <div className="flex items-center gap-2">
      <SymbolIcon
        chainId={item.chainId}
        image_url={item.image_url}
        name={item.name}
      />
      <div className="font-400">
        <h4 className="text-12 text-gray-50">{item.symbol}</h4>
        <p className="mt-1 text-10 text-gray-450">
          {cutToken(item.contract_address, '...')}
        </p>
      </div>
    </div>
    <div className="text-12 font-700">
      <p className="text-right text-12 text-gray-50">
        <FormatIconAmount value={item.usd} />
      </p>
      <p className="mt-1 text-right font-400 text-gray-450">
        {i18n.t('24h_vol')} ${formatVolumeDisplay(item.h24_volume_usd)}
      </p>
    </div>
  </article>
)

/**
 * 加密货币列表组件
 * 渲染多个加密货币列表项
 *
 * @param items 加密货币数据项列表
 * @param onSelect 选择处理函数
 */
const CryptoList = ({ items, onSelect }: ICryptoListProps) => (
  <nav className="flex flex-col gap-2">
    {items.map((item) => (
      <CryptoListItem
        key={getCryptoKey(item)}
        item={item}
        onClick={() => onSelect(item)}
      />
    ))}
  </nav>
)

// ==================== 自定义Hook ====================

/**
 * 加密货币搜索Hook
 * 管理代币搜索的状态和操作
 */
const useTokenSearch = () => {
  const [state, dispatch] = useReducer(searchReducer, {
    value: '',
    loading: false,
    items: [],
  })

  /**
   * 搜索代币API调用
   */
  const searchToken = async (value: string) => {
    return await SEARCH_TOKEN({
      address: value,
    })
  }

  /**
   * 防抖处理的搜索函数
   */
  const { run: debouncedSearch } = useDebounceFn(
    async (value: string) => {
      try {
        dispatch({ type: 'SET_ITEMS', payload: [] })
        if (value) {
          const res = await searchToken(value)
          if (res?.code === 200) {
            dispatch({ type: 'SET_ITEMS', payload: [{ ...(res.data || {}) }] })
          }
        }
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    },
    { wait: CONFIG.DEBOUNCE_WAIT },
  )

  /**
   * 处理搜索输入变化
   */
  const handleSearch = useCallback(
    (value: string) => {
      const trimmedValue = value.trim()
      dispatch({ type: 'SET_VALUE', payload: trimmedValue })

      if (!trimmedValue) {
        dispatch({ type: 'RESET' })
        return
      }

      dispatch({ type: 'SET_LOADING', payload: true })
      debouncedSearch(trimmedValue)
    },
    [debouncedSearch],
  )

  return {
    state,
    handleSearch,
    resetSearch: () => dispatch({ type: 'RESET' }),
  }
}

/**
 * 热门列表Hook
 * 管理热门代币列表的获取和状态
 */
const useHotList = () => {
  const [state, dispatch] = useReducer(hotListReducer, {
    loading: false,
    items: [],
  })

  /**
   * 获取热门代币列表
   */
  const fetchHotList = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true })
    try {
      const res = await SEARCH_TOKEN_LIST()
      if (res?.code === 200) {
        // 使用明确的类型，避免使用any
        const hotTokenList = Array.isArray(res.data) ? res.data : []
        dispatch({ type: 'SET_ITEMS', payload: hotTokenList })
      }
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [])

  return {
    state,
    fetchHotList,
  }
}

/**
 * 最近使用代币记录Hook
 * 管理用户最近选择的代币历史记录
 */
const useRecentTokens = () => {
  const { toastSuc } = useToastMessage()
  const [recentTokens, setRecentTokens] = useLocalStorageState<ICryptoItem[]>(
    STORAGE_TRADE_CRYPTO_LIST,
    { defaultValue: [] },
  )

  /**
   * 添加代币到最近记录
   */
  const addToRecent = useCallback(
    (crypto: ICryptoItem) => {
      const newList = (recentTokens || []).filter(
        (item) => item.contract_address !== crypto.contract_address,
      )
      setRecentTokens([
        crypto,
        ...newList.slice(0, CONFIG.MAX_RECENT_TOKENS - 1),
      ])
    },
    [recentTokens, setRecentTokens],
  )

  /**
   * 清空历史记录
   */
  const clearHistory = useCallback(() => {
    setRecentTokens([])
    toastSuc(i18n.t('clear_history_success'))
  }, [setRecentTokens, toastSuc])

  return {
    recentTokens: recentTokens || [],
    addToRecent,
    clearHistory,
  }
}

/**
 * 桌面端下拉框管理Hook
 * 管理下拉框的显示/隐藏状态和相关交互
 */
const useDesktopDropdown = (
  isTypeMobile: boolean,
  onVisibleChange?: (visible: boolean) => void,
) => {
  const [inputFocused, setInputFocused] = useState(false)
  const [dropdownVisible, setDropdownVisible] = useState(false)
  const selectorRef = useRef<HTMLDivElement>(null)

  // 通知外部组件下拉框状态变化
  useEffect(() => {
    if (!isTypeMobile) {
      onVisibleChange?.(dropdownVisible)
    }
  }, [dropdownVisible, isTypeMobile, onVisibleChange])

  // 使用ahooks的useClickAway处理点击外部关闭下拉框
  useClickAway(() => {
    if (!isTypeMobile && dropdownVisible) {
      setDropdownVisible(false)
    }
  }, selectorRef)

  const handleInputFocus = useCallback(() => {
    setInputFocused(true)
    setDropdownVisible(true)
  }, [])

  const handleInputBlur = useCallback(() => {
    setInputFocused(false)
  }, [])

  const closeDropdown = useCallback(() => {
    setDropdownVisible(false)
    setInputFocused(false)
  }, [])

  return {
    inputFocused,
    dropdownVisible,
    selectorRef,
    handleInputFocus,
    handleInputBlur,
    closeDropdown,
  }
}

// ==================== 组件 ====================

/**
 * 最近搜索记录区域组件
 */
const RecentSearchSection = ({
  cryptoList,
  onClearHistory,
  onSelect,
}: {
  cryptoList: ICryptoItem[]
  onClearHistory: () => void
  onSelect: (item: ICryptoItem) => void
}) => (
  <section className="py-2">
    <header className="mb-4 flex items-center justify-between">
      <h3 className="text-sm font-medium leading-tight text-white">
        {i18n.t('recent_search')}
      </h3>
      <ClearButton
        onClick={(e) => {
          e.stopPropagation()
          onClearHistory()
        }}
      />
    </header>
    <nav className="flex flex-wrap gap-3">
      {cryptoList.map((item) => (
        <button
          key={getCryptoKey(item)}
          className="mb-2 flex cursor-pointer items-center gap-2"
          onClick={(e) => {
            e.stopPropagation()
            onSelect(item)
          }}
        >
          {item.image_url ? (
            <img
              src={item.image_url}
              alt={`${item.symbol} logo`}
              className="size-5 rounded-full object-cover"
            />
          ) : (
            <DefaultSymbolIcon name={item.name} size={20} />
          )}
          <span className="text-14 font-500 text-gray-50">{item.symbol}</span>
        </button>
      ))}
    </nav>
  </section>
)

/**
 * 热门列表区域组件
 */
const HotListSection = ({
  isLoading,
  items,
  onSelect,
  containerClassName = '',
  children,
}: {
  isLoading: boolean
  items: ICryptoItem[]
  onSelect: (item: ICryptoItem) => void
  containerClassName?: string
  children?: React.ReactNode
}) => (
  <section className={containerClassName}>
    <h3 className="mb-4 text-sm font-medium leading-tight text-white">
      {i18n.t('hot')}
    </h3>
    {isLoading ? (
      <LoadingView className="py-4" />
    ) : children ? (
      children
    ) : (
      <CryptoList items={items} onSelect={onSelect} />
    )}
  </section>
)

/**
 * 搜索结果区域组件
 */
const SearchResultsSection = ({
  isLoading,
  searchValue,
  items,
  onSelect,
}: {
  isLoading: boolean
  searchValue: string
  items: ICryptoItem[]
  onSelect: (item: ICryptoItem) => void
}) => {
  // 早期返回模式，避免嵌套条件渲染
  if (!searchValue) return null
  if (isLoading) return <LoadingView className="py-10" />
  if (items.length === 0) return <NoResultsView />

  // 只有在有搜索结果时才渲染列表
  return (
    <section className="flex min-h-[200px] flex-col gap-2">
      <CryptoList items={items} onSelect={onSelect} />
    </section>
  )
}

/**
 * 共享内容容器接口
 * 定义共享内容组件所需的属性
 */
interface IContentContainerProps {
  searchState: ISearchState
  hotListState: IHotListState
  recentTokens: ICryptoItem[]
  clearHistory: () => void
  onSelectCrypto: (crypto: ICryptoItem) => void
  maxHotListHeight?: number
}

/**
 * 共享内容容器组件
 * 减少移动端和桌面端的代码重复，提高可维护性
 * 显示搜索结果、最近使用的代币和热门代币列表
 */
const ContentContainer = ({
  searchState,
  hotListState,
  recentTokens,
  clearHistory,
  onSelectCrypto,
  maxHotListHeight = 360,
}: IContentContainerProps) => (
  <>
    <SearchResultsSection
      isLoading={searchState.loading}
      searchValue={searchState.value}
      items={searchState.items}
      onSelect={onSelectCrypto}
    />

    {!searchState.value && (
      <>
        {recentTokens.length > 0 && (
          <>
            <RecentSearchSection
              cryptoList={recentTokens}
              onClearHistory={clearHistory}
              onSelect={onSelectCrypto}
            />
            <Divider className="my-0 mb-3 bg-theme-darker" />
          </>
        )}
        <HotListSection
          isLoading={hotListState.loading}
          items={hotListState.items}
          onSelect={onSelectCrypto}
          containerClassName={`max-h-[${maxHotListHeight}px] no-scroll`}
        />
      </>
    )}
  </>
)

/**
 * 桌面端视图渲染函数
 */
const renderDesktopView = ({
  width,
  searchState,
  handleSearch,
  hotListState,
  recentTokens,
  clearHistory,
  onSelectCrypto,
  inputRef,
  dropdown,
}: {
  width: number
  searchState: ISearchState
  handleSearch: (value: string) => void
  hotListState: IHotListState
  recentTokens: ICryptoItem[]
  clearHistory: () => void
  onSelectCrypto: (crypto: ICryptoItem) => void
  inputRef: React.RefObject<any>
  dropdown: ReturnType<typeof useDesktopDropdown>
}) => (
  <main
    ref={dropdown.selectorRef}
    className="relative w-full"
    style={{ width }}
  >
    <div className="cursor-pointer">
      <SearchInput
        value={searchState.value}
        onChange={handleSearch}
        inputRef={inputRef}
        onFocus={dropdown.handleInputFocus}
        onBlur={dropdown.handleInputBlur}
        className="bg-[#1a1b1e]"
      />
    </div>
    {(dropdown.dropdownVisible || searchState.value) && (
      <div
        className="absolute left-0 top-[45px] z-10 rounded-lg bg-theme-primary px-4 py-3 shadow-lg"
        style={{ maxHeight: '80vh', overflowY: 'auto', width }}
      >
        <ContentContainer
          searchState={searchState}
          hotListState={hotListState}
          recentTokens={recentTokens}
          clearHistory={clearHistory}
          onSelectCrypto={onSelectCrypto}
          maxHotListHeight={360}
        />
      </div>
    )}
  </main>
)

/**
 * 移动端视图渲染函数
 */
const renderMobileView = ({
  searchState,
  handleSearch,
  hotListState,
  recentTokens,
  clearHistory,
  onSelectCrypto,
  contentRef,
  contentMaxHeight,
}: {
  searchState: ISearchState
  handleSearch: (value: string) => void
  hotListState: IHotListState
  recentTokens: ICryptoItem[]
  clearHistory: () => void
  onSelectCrypto: (crypto: ICryptoItem) => void
  contentRef: React.RefObject<HTMLDivElement | null>
  contentMaxHeight: number
}) => (
  <main className="flex flex-col gap-2">
    <div className="relative w-full px-2">
      <SearchInput
        value={searchState.value}
        onChange={handleSearch}
        className="bg-theme-darker"
        clearIconClassName="-translate-x-1 -translate-y-0.5"
      />
    </div>
    <div className="flex w-full flex-col gap-2 rounded-lg bg-theme-primary p-2.5 text-gray-50">
      <div
        ref={contentRef}
        style={{
          minHeight: contentMaxHeight > 0 ? `${contentMaxHeight}px` : 'auto',
        }}
      >
        <ContentContainer
          searchState={searchState}
          hotListState={hotListState}
          recentTokens={recentTokens}
          clearHistory={clearHistory}
          onSelectCrypto={onSelectCrypto}
          maxHotListHeight={300}
        />
      </div>
    </div>
  </main>
)

/**
 * CryptoSelector 主组件
 * 加密货币选择器，支持PC端和移动端两种模式
 */
const CryptoSelector = ({
  type = 'desktop',
  onChange,
  onVisibleChange,
  width = 316,
}: ICryptoSelectorProps) => {
  // 基本状态
  const isTypeMobile = type === 'mobile'
  const [visible, setVisible] = useState(isTypeMobile)
  const [contentMaxHeight, setContentMaxHeight] = useState<number>(0)

  // 使用自定义hook管理不同功能模块
  const { state: searchState, handleSearch, resetSearch } = useTokenSearch()
  const { state: hotListState, fetchHotList } = useHotList()
  const { recentTokens, addToRecent, clearHistory } = useRecentTokens()
  const dropdown = useDesktopDropdown(isTypeMobile, onVisibleChange)

  // 引用
  const contentRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<any>(null)

  /**
   * 监听内容高度变化（仅移动端）
   */
  useEffect(() => {
    if (isTypeMobile && contentRef.current) {
      const currentHeight = contentRef.current.offsetHeight
      if (currentHeight > contentMaxHeight) {
        setContentMaxHeight(currentHeight)
      }
    }
  }, [
    searchState.value,
    searchState.items,
    hotListState.items,
    recentTokens,
    searchState.loading,
    hotListState.loading,
    isTypeMobile,
    contentMaxHeight,
  ])

  /**
   * 加载热门列表
   */
  useEffect(() => {
    if ((isTypeMobile && visible) || (!isTypeMobile && dropdown.inputFocused)) {
      fetchHotList()
    }
  }, [visible, isTypeMobile, dropdown.inputFocused, fetchHotList])

  /**
   * 处理代币选择
   */
  const onSelectCrypto = useCallback(
    (crypto: ICryptoItem) => {
      // 更新全局状态
      console.log(1111)
      console.log(crypto)
      tradeStore.setCurrCrypto(crypto)
      // 添加到最近使用列表
      addToRecent(crypto)

      // 重置状态
      setVisible(false)
      resetSearch()
      dropdown.closeDropdown()

      // 输入框失焦
      if (inputRef.current) {
        inputRef.current.blur()
      }

      // 通知父组件
      onChange?.(crypto)
    },
    [addToRecent, dropdown, onChange, resetSearch],
  )

  // 优化渲染属性，使用useMemo缓存
  const desktopProps = useMemo(
    () => ({
      width,
      searchState,
      handleSearch,
      hotListState,
      recentTokens,
      clearHistory,
      onSelectCrypto,
      inputRef,
      dropdown,
    }),
    [
      width,
      searchState,
      handleSearch,
      hotListState,
      recentTokens,
      clearHistory,
      onSelectCrypto,
      dropdown,
    ],
  )

  const mobileProps = useMemo(
    () => ({
      searchState,
      handleSearch,
      hotListState,
      recentTokens,
      clearHistory,
      onSelectCrypto,
      contentRef,
      contentMaxHeight,
    }),
    [
      searchState,
      handleSearch,
      hotListState,
      recentTokens,
      clearHistory,
      onSelectCrypto,
      contentMaxHeight,
    ],
  )

  // 根据设备类型渲染对应的视图
  return isTypeMobile
    ? renderMobileView(mobileProps)
    : renderDesktopView(desktopProps)
}

export default CryptoSelector
