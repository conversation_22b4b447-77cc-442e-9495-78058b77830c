import { action, computed, makeObservable, observable } from 'mobx'
import {
  setupLocalStorageSync,
  setupSessionStorageSync,
} from '@/store/_setupSync'
import { SEARCH_TOKEN_RETURN } from '@/api/interface/SEARCH_TOKEN'
import { DEFAULT_CHAIN_LIST } from '@/pages/Trade/hooks/config'
import { User } from '@privy-io/react-auth'

class TradeStore {
  // 当前活跃的币种信息
  currCryptoToken: SEARCH_TOKEN_RETURN['data'] =
    {} as SEARCH_TOKEN_RETURN['data']
  // 默认币种信息
  defaultCryptoInfo: SEARCH_TOKEN_RETURN['data'] =
    {} as SEARCH_TOKEN_RETURN['data']
  // 上一次操作的币种信息（不持久化，每次刷新都重置为null）
  preCryptoToken: SEARCH_TOKEN_RETURN['data'] | null = null

  // 是否是首次设置币种
  private isFirstSet: boolean = true

  // 滑点
  slippage: string = '15'
  // 余额
  balances: {
    // 地址
    address: string
    // 符号地址
    symbolAddress: string
    // 余额
    balance: string
    // 最后更新时间
    lastUpdateTime: number
  }[] = []
  // 轮询余额 loading
  pollingBalanceLoading: boolean = false

  constructor() {
    // 确保preCryptoToken初始化为null
    this.preCryptoToken = null
    this.isFirstSet = true

    makeObservable(this, {
      currCrypto: computed,
      preCrypto: computed,
      currCryptoToken: observable,
      preCryptoToken: observable,
      slippage: observable,
      setCurrCrypto: action,
      setSlippage: action,
      balances: observable,
      pollingBalanceLoading: observable,
      setPollingBalanceLoading: action,
      resetPreCryptoState: action,
    })

    setupLocalStorageSync(this, 'trade.pumpkin', [
      'currCryptoToken',
      'defaultCryptoInfo',
      'slippage',
    ])
    setupSessionStorageSync(this, 'trade.pumpkin', ['balances'])
  }

  get currCrypto() {
    return this.currCryptoToken?.contract_address
      ? this.currCryptoToken
      : this.defaultCryptoInfo
  }

  get preCrypto() {
    return this.preCryptoToken
  }

  setCurrCrypto(crypto: SEARCH_TOKEN_RETURN['data']) {
    console.log(11111)
    console.log(this.isFirstSet)
    console.log(this.currCrypto)
    if (!this.isFirstSet) {
      // 只有当新选择的币种与当前币种不同时，才更新preCryptoToken
      if (this.currCrypto?.contract_address !== crypto?.contract_address) {
        this.preCryptoToken = this.currCrypto
      }
    } else {
      this.isFirstSet = false
    }
    // 设置新的当前值
    this.currCryptoToken = crypto
  }

  setSlippage(slippage: string) {
    this.slippage = slippage
  }

  // 重置前置币种状态，用于组件重新加载时调用
  // 确保preCryptoToken和isFirstSet状态被正确重置，防止UPD_ROOM_LIVE_HOT_CHAT接口发送错误数据
  resetPreCryptoState() {
    this.preCryptoToken = null
    this.isFirstSet = true
  }

  // 默认获取币种信息
  defaultGetCryptoInfo(
    privyUser: User | null,
  ): unknown | SEARCH_TOKEN_RETURN['data'] {
    const wallet = privyUser?.wallet || null

    if (!wallet) {
      return null
    }

    switch (wallet?.chainType) {
      case 'ethereum':
        return DEFAULT_CHAIN_LIST['ethereum']
      default:
        return DEFAULT_CHAIN_LIST['solana']
    }
  }

  setPollingBalanceLoading(loading: boolean) {
    this.pollingBalanceLoading = loading
  }

  setBalances(newBalance: {
    // 地址
    address: string
    // 符号地址
    symbolAddress: string
    // 余额
    balance: string
    // 最后更新时间
    lastUpdateTime: number
  }) {
    const existingIndex = this.balances.findIndex(
      (balance) => balance.symbolAddress === newBalance.symbolAddress,
    )
    if (existingIndex !== -1) {
      this.balances = [
        ...this.balances.slice(0, existingIndex),
        newBalance,
        ...this.balances.slice(existingIndex + 1),
      ]
    } else {
      this.balances = [...this.balances, newBalance]
    }
  }
}

const tradeStore = new TradeStore()

export { tradeStore }
