import { AGGREGATOR_SENDTRANSACTION } from '@/api/interface/AGGREGATOR_SENDTRANSACTION'
import { AGGREGATOR_SWAP_PARAMS } from '@/api/interface/AGGREGATOR_SWAP'
import { ConnectedSolanaWallet } from '@privy-io/react-auth'
import { useCallback } from 'react'
import { Connection, Transaction, VersionedTransaction } from '@solana/web3.js'
import bs58 from 'bs58'
import { SwapReturn } from '@/pages/Trade/hooks/transfer/useUnifyData'
import { useTransfer } from '@/pages/Trade/hooks/transfer'
import {
  mapExplainSolError,
  mapSetError,
  SwapError,
  SwapErrorType,
} from '@/pages/Trade/hooks/method/mapError'
import { useSolEvent } from '@/pages/Trade/hooks/method/useSolEvent'
import { isValidBase58 } from '@/utils'

// 定义回调函数接口
export interface SwapStatusSolCallbacks {
  // 开始执行
  onStart?: () => void
  // 交易已发送
  onTxSent?: (txHash: string) => void
  // 开始等待确认 (UI 在此启动倒计时)
  onWaitingConfirmation?: (txHash: string, countdown: number) => void
  // 交易成功
  onSuccess?: (result: { txHash: string; receipt?: any }) => void
  // 交易失败
  onError?: (error: SwapError) => void
  // 无论成功失败，最终执行
  onFinally?: () => void
}

type SwapParams = Omit<AGGREGATOR_SWAP_PARAMS, 'userWalletAddress'>

export const useSol = (wallet: ConnectedSolanaWallet | null) => {
  // 交易确认超时时间
  const CONFIRM_TIMEOUT = 20000

  const { getSwapData } = useTransfer(wallet)
  const { confirmSolTransaction } = useSolEvent()

  /**
   * 执行 Solana 交易
   * @param swapData 兑换数据
   * @returns {}
   */
  const onSolanaTransaction = useCallback(
    async (params: SwapParams, callbacks?: SwapStatusSolCallbacks) => {
      const connection = new Connection(
        import.meta.env.VITE_CUSTOM_SOLANA_RPC_URL,
        {
          commitment: 'confirmed',
        },
      )

      callbacks?.onStart?.()

      try {
        if (!wallet) {
          throw new Error('未获取到钱包')
        }

        const {
          chainId,
          roomId,
          amount,
          fromTokenAddress,
          toTokenAddress,
          slippage,
        } = params

        // 获取兑换数据
        let swapData: SwapReturn

        try {
          swapData = await getSwapData({
            chainId,
            roomId,
            amount,
            fromTokenAddress,
            toTokenAddress,
            slippage,
            userWalletAddress: wallet.address,
          })
          if (!swapData?.dexSwapTx?.data || !swapData?.id) {
            throw new Error('Invalid swap data received')
          }
        } catch (fetchError: any) {
          const finalError = mapSetError(SwapErrorType.SWAP_FAILED, fetchError)
          callbacks?.onError?.(finalError)
          return
        }

        const { dexSwapTx, id } = swapData

        // 解码交易数据
        let transaction

        // 检测是否为 isBase58
        const isBase58 = isValidBase58(dexSwapTx.data)

        if (isBase58) {
          // Base58 解码
          transaction = bs58.decode(dexSwapTx.data)
        } else {
          // Base64 数据
          transaction = Buffer.from(dexSwapTx.data, 'base64')
        }

        // 反序列化交易
        const tx = VersionedTransaction.deserialize(transaction)

        let txHash: string
        let blockhash: string

        try {
          // 获取最新区块哈希
          ;({ blockhash } = await connection.getLatestBlockhash('confirmed'))

          // 替换交易中的区块哈希
          tx.message.recentBlockhash = blockhash

          console.log(
            'wallet.walletClientType',
            wallet.walletClientType,
            'isPhantom',
            (window as any).phantom?.solana?.isPhantom,
          )
          // 检查钱包类型是否为 Phantom
          // if (
          //   wallet.walletClientType === 'phantom' &&
          //   (window as any).phantom?.solana?.isPhantom
          // ) {
          //   // 使用 Phantom 的 signAndSendTransaction 方法直接签名并发送交易
          //   const phantomProvider = (window as any).phantom?.solana
          //   // 创建未签名的交易（确保有足够空间给 Lighthouse guard instructions）
          //   const { signature } =
          //     await phantomProvider.signAndSendTransaction(transaction)
          //   txHash = signature
          // } else {
          //   // 使用 Privy 钱包重新签名
          //   const signedTx = await wallet.signTransaction(tx)

          //   // 立即发送交易
          //   txHash = await connection.sendRawTransaction(signedTx.serialize(), {
          //     skipPreflight: false,
          //   })
          // }

          // 使用 Phantom 的 signAndSendTransaction 方法直接签名并发送交易
          const phantomProvider = (window as any).phantom?.solana
          // 创建未签名的交易（确保有足够空间给 Lighthouse guard instructions）
          const { signature } = await phantomProvider.signAndSendTransaction(tx)
          txHash = signature

          callbacks?.onTxSent?.(txHash)
        } catch (sendError: any) {
          const finalError = mapExplainSolError(sendError)
          callbacks?.onError?.(finalError)
          return
        }

        // 通知后端
        try {
          await AGGREGATOR_SENDTRANSACTION({ id, txHash })
        } catch (backendError: any) {
          const finalError = mapSetError(
            SwapErrorType.BACKEND_ERROR,
            backendError,
          )
          callbacks?.onError?.(finalError)
          return
        }

        callbacks?.onWaitingConfirmation?.(txHash, CONFIRM_TIMEOUT / 1000)

        // 确认交易
        const confirmationPromise = confirmSolTransaction(
          txHash,
          connection,
          'confirmed',
        )

        const timeoutPromise = new Promise<null>((_, reject) =>
          setTimeout(() => reject(new Error('Timeout')), CONFIRM_TIMEOUT),
        )

        try {
          const confirmationResult = await Promise.race([
            confirmationPromise,
            timeoutPromise,
          ])

          if (confirmationResult?.value?.err) {
            // 链上失败
            const finalError = mapSetError(
              SwapErrorType.TRANSACTION_FAILED,
              confirmationResult.value.err,
            )
            callbacks?.onError?.(finalError)
          } else if (confirmationResult && !confirmationResult.value.err) {
            // 成功确认
            setTimeout(() => {
              callbacks?.onSuccess?.({ txHash, receipt: confirmationResult })
            }, 3000)
          } else {
            // 不确定状态
            const finalError = mapSetError(
              SwapErrorType.UNKNOWN,
              confirmationResult,
            )
            callbacks?.onError?.(finalError)
          }
        } catch (confirmationError: any) {
          // 处理超时或确认过程中的其他错误
          let finalError: SwapError
          if (confirmationError.message === 'Timeout') {
            finalError = mapSetError(
              SwapErrorType.CONFIRM_TIMEOUT,
              confirmationError,
            )
          } else {
            finalError = mapExplainSolError(confirmationError)
          }
          callbacks?.onError?.(finalError)
        }
      } catch (setupError: any) {
        const finalError = mapExplainSolError(setupError)
        callbacks?.onError?.(finalError)
      } finally {
        callbacks?.onFinally?.()
      }
    },
    [wallet],
  )

  return {
    onSolanaTransaction,
  }
}
