import { ConnectedSolanaWallet, ConnectedWallet } from '@privy-io/react-auth'
import { getAssociatedTokenAddress } from '@solana/spl-token'
import {
  AGGREGATOR_SWAP_PARAMS,
  AGGREGATOR_SWAP_RETURN,
} from '@/api/interface/AGGREGATOR_SWAP'
import { Connection, LAMPORTS_PER_SOL, PublicKey } from '@solana/web3.js'
import { switchToTargetChain } from '@/pages/Trade/hooks/method/setChain'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import { CHAIN_SWAP_LIST } from '@/pages/Trade/hooks/config'
import { SwapReturn } from '@/pages/Trade/hooks/transfer/useUnifyData'

type SwapParams = Omit<AGGREGATOR_SWAP_PARAMS, 'userWalletAddress'>

interface GetEvmTokenBalanceParams {
  // 钱包
  wallet: ConnectedWallet | null
  // 链ID
  chainId: SwapParams['chainId']
  // 查询代币地址
  tokenAddress: string | SwapParams['chainId']
  // 币种精度
  decimals?: number
  // 返回原始值
  original?: boolean
}

interface GetSolTokenBalanceParams {
  // 钱包
  wallet: ConnectedSolanaWallet | null
  // 查询代币地址
  tokenAddress: string | 'solana'
  // 币种精度
  decimals?: number
  // 返回原始值
  original?: boolean
}

/**
 * 获取 evm 代币余额
 * @param wallet 钱包
 * @param chainId 链ID
 * @param tokenAddress 查询代币地址
 * @param decimals 币种精度
 * @returns Promise<string> 代币余额
 */
export const getEvmTokenBalance = async ({
  chainId,
  tokenAddress,
  decimals,
  wallet,
  original,
}: GetEvmTokenBalanceParams) => {
  try {
    if (!wallet) return '-'

    // EVM 链处理
    const provider = await (wallet as ConnectedWallet).getEthereumProvider()

    if (tokenAddress === chainId) {
      try {
        // 检查并切换到目标网络
        await switchToTargetChain({ wallet, chainId })

        // 获取余额
        const balance = await provider.request({
          method: 'eth_getBalance',
          params: [wallet.address, 'latest'],
        })

        // 计算 ETH 余额
        const wei = BigInt(balance)
        const ethBalance = Number(wei) / 10 ** 18
        return original ? wei.toString() : ethBalance.toString()
      } catch (error) {
        console.error('Chain operation error:', error)
        return '0'
      }
    } else {
      // ERC20 代币
      try {
        const formattedAddress = wallet.address
          .slice(2)
          .toLowerCase()
          .padStart(40, '0')

        const data = `0x70a08231000000000000000000000000${formattedAddress}`

        const balance = await provider.request({
          method: 'eth_call',
          params: [
            {
              to: tokenAddress as string,
              data,
            },
            'latest',
          ],
        })

        // 转换余额
        const rawBalance = BigInt(balance)
        const divisor = BigInt(10 ** (decimals || 18))
        const integerPart = rawBalance / divisor
        const fractionalPart = rawBalance % divisor

        // 格式化小数部分
        const fractionalStr = fractionalPart
          .toString()
          .padStart(decimals || 18, '0')

        return original
          ? rawBalance.toString()
          : `${integerPart}.${fractionalStr.slice(0, 6)}`
      } catch (error) {
        console.error('获取余额失败:', error)
        return '0'
      }
    }
  } catch (error) {
    console.error('获取余额失败:', error)
    return '0'
  }
}

/**
 * 获取 solana 代币余额
 * @param wallet 钱包
 * @param tokenAddress 查询代币地址
 * @param decimals 币种精度
 * @returns Promise<{ balance: string; publicKey: string }> 代币余额
 */
export const getSolTokenBalance = async ({
  tokenAddress,
  decimals,
  wallet,
  original,
}: GetSolTokenBalanceParams) => {
  try {
    if (!wallet) return '-'

    // 添加重试逻辑
    const executeWithRetry = async <T>(fn: () => Promise<T>): Promise<T> => {
      let retries = 3
      while (retries > 0) {
        try {
          return await fn()
        } catch (error: any) {
          if (retries === 1 || !error.message?.includes('Objectifultiplex')) {
            throw error
          }
          console.warn('Solana钱包请求出错，正在重试...', error)
          await new Promise((resolve) => setTimeout(resolve, 1000))
          retries--
        }
      }
      throw new Error('重试失败')
    }

    const connection = new Connection(
      import.meta.env.VITE_CUSTOM_SOLANA_RPC_URL,
    )
    const publicKey = new PublicKey(wallet.address)

    if (tokenAddress === 'solana') {
      // 如果是原生 SOL
      const balance = await executeWithRetry(() =>
        connection.getBalance(publicKey),
      )

      return original
        ? (balance || 0).toString()
        : (balance / LAMPORTS_PER_SOL).toString()
    } else {
      // SPL 代币
      const tokenAccount = await executeWithRetry(() =>
        connection.getTokenAccountsByOwner(publicKey, {
          mint: new PublicKey(tokenAddress as string),
        }),
      )

      // 如果找到账户，使用第一个匹配的
      if (tokenAccount.value.length > 0) {
        const LinkedAccounts = tokenAccount.value[0].pubkey

        const accountInfo = await executeWithRetry(() =>
          connection.getTokenAccountBalance(LinkedAccounts),
        )

        return original
          ? (accountInfo?.value?.amount || 0).toString()
          : (
              Number(accountInfo.value.amount) /
              10 ** (decimals as number)
            ).toString()
      } else {
        // 账户不存在的处理
        return '0'
      }
    }
  } catch (error) {
    console.error('获取Solana余额失败:', error)
    return '0'
  }
}

/**
 * 轮询余额
 * @param wallet 钱包
 * @param params 交易参数
 * @param txData 交易数据
 * @returns
 */
export const pollingBalance = async (
  wallet: ConnectedSolanaWallet | ConnectedWallet,
  params: SwapParams,
  txData: SwapReturn,
  timeoutMs: number = 20000,
) => {
  // 开始时间
  const startTime = Date.now()

  // 轮询间隔
  const POLL_INTERVAL = 2000

  const { chainId, fromTokenAddress } = params
  const { dexSwapTx } = txData

  const defaultAddress = CHAIN_SWAP_LIST[chainId].tokenAddress
  const isBuys = fromTokenAddress === defaultAddress

  const tokenAddress = isBuys
    ? tradeStore.currCrypto.chainId
    : tradeStore.currCrypto.contract_address

  const decimals = dexSwapTx?.from?.decimals

  const existingBalance = tradeStore.balances.find(
    (item) =>
      item.symbolAddress === tokenAddress && item.address === wallet.address,
  )

  while (Date.now() - startTime < timeoutMs) {
    try {
      let balance: string = existingBalance?.balance || '0'

      if (chainId === 'solana') {
        balance = await getSolTokenBalance({
          wallet: wallet as ConnectedSolanaWallet,
          tokenAddress: tokenAddress as string,
          decimals,
        })
      } else {
        // EVM 链处理
        balance = await getEvmTokenBalance({
          wallet: wallet as ConnectedWallet,
          tokenAddress,
          decimals,
          chainId: chainId as SwapParams['chainId'],
        })
      }

      // 检查余额是否变化
      const balanceChanged = existingBalance?.balance !== balance

      // 余额变化时停止轮询
      if (balanceChanged) {
        return {
          success: true,
          existingBalance: existingBalance?.balance,
          balance,
        }
      }

      // 继续轮询
      await new Promise((resolve) => setTimeout(resolve, POLL_INTERVAL))
    } catch (error) {
      console.error('余额调取报错', error)
      // todo...
    }
  }

  // 超时停止
  return { success: false, balance: existingBalance?.balance || '0' }
}
