import { observer } from 'mobx-react-lite'
import { TradeContext } from '@/pages/Trade/provider/trade.context'
import { useState } from 'react'
import { TradeTopup } from '@/pages/Trade/module/TradeTopup'

export const TradeProvider = observer(
  ({ children }: { children: React.ReactNode }) => {
    // 是否显示充值弹窗
    const [spotTopupModal, setSpotTopupModal] = useState(false)

    return (
      <TradeContext.Provider
        value={{
          spotTopupModal,
          setSpotTopupModal,
        }}
      >
        {children}
        {/* 充值弹窗 */}
        <TradeTopup />
      </TradeContext.Provider>
    )
  },
)
