import { observer } from 'mobx-react-lite'
import ModDiaplay from '@/pages/Trade/module/ModDiaplay'
import ModPandL from '@/pages/Trade/module/ModPandL'
import ModPlaceOrder from '@/pages/Trade/module/ModPlaceOrder'
import ModTitle from '@/pages/Trade/module/ModTitle'
import { useState, useEffect, useRef } from 'react'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import { DEXTOKEN_DATA } from '@/api/interface/NAME_DEXTOKEN_DATA'
import { ModSlippage } from '@/pages/Trade/module/ModSlippage'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { UPD_ROOM_SETTING } from '@/api/interface/UPD_ROOM_SETTING'
import {
  UPD_ROOM_TAG,
  UPD_ROOM_LIVE_HOT_CHAT,
} from '@/api/interface/UPD_ROOM_TAG'
import { userStore } from '@/store'
import { useDevice } from '@/hooks/useDevice'
import { usePrivy } from '@privy-io/react-auth'
import { SEARCH_TOKEN_RETURN } from '@/api/interface/SEARCH_TOKEN'
import {
  FullscreenToggleButton,
  LocalFullscreen,
} from '@/components/LocalFullsreen'
import { HTTP_CODE } from '@/api/_http'
import { TradeProvider } from '@/pages/Trade/provider/trade.provider'

// 轮询时间
const POLL_TIME = 1000 * 10

const TradePlate = observer(({ roomId }: { roomId: string }) => {
  const store = useStore()
  const { isMobile } = useDevice()
  const { user } = usePrivy()

  const [tokenData, setTokenData] = useState<any>(null)
  // 轮询定时器ID
  const pollTimerRef = useRef<NodeJS.Timeout | undefined>(undefined)

  // 组件挂载时重置preCryptoToken，确保每次组件重新加载时都能正确记录前一次操作的币种
  // useEffect(() => {
  //   // 重置前置币种状态，避免组件重新加载时出现preCrypto与currCrypto相同的情况
  //   tradeStore.resetPreCryptoState()

  //   return () => {
  //     tradeStore.resetPreCryptoState()
  //     // 组件卸载时清理
  //   }
  // }, [])

  /** 获取代币数据 */
  const fetchTokenData = async () => {
    const res = await DEXTOKEN_DATA({
      chainId: tradeStore.currCrypto?.chainId,
      address: tradeStore.currCrypto?.contract_address,
    })

    setTokenData(res?.data)
    if (res?.data?.tokenInfo) {
      const updatedCrypto = {
        ...tradeStore.currCrypto,
        ...res.data.tokenInfo,
      }
      tradeStore.setCurrCrypto(updatedCrypto as SEARCH_TOKEN_RETURN['data'])
    }
  }

  const isHost = store.isHost(userStore.info.id)

  useEffect(() => {
    // 初始化默认币种信息
    if (tradeStore.currCrypto?.contract_address) {
      return
    }
    tradeStore.setCurrCrypto(
      tradeStore.defaultGetCryptoInfo(user) as SEARCH_TOKEN_RETURN['data'],
    )
  }, [user])

  useEffect(() => {
    // 立即获取一次数据
    fetchTokenData()

    // 设置10秒轮询
    pollTimerRef.current = setInterval(fetchTokenData, POLL_TIME)

    // 清理函数
    return () => {
      if (pollTimerRef.current) {
        clearInterval(pollTimerRef.current)
      }
    }
  }, [tradeStore.currCrypto?.contract_address])

  useEffect(() => {
    if (isHost) {
      // 房主如果修改了交易信息，则更新语音房标签
      UPD_ROOM_TAG({
        roomId,
        tagType: 1,
        tagContent: tradeStore.currCrypto?.symbol,
        tagIcon: tradeStore.currCrypto?.image_url,
      }).then((res) => {
        if (res.code === HTTP_CODE.SUCCESS_CODE) {
          // 构造beforeCurrency和nowCurrency
          const beforeCurrency = tradeStore.preCrypto
            ? `${tradeStore.preCrypto.symbol}-${tradeStore.preCrypto.chainId}-${tradeStore.preCrypto.contract_address}`
            : ''
          const nowCurrency = `${tradeStore.currCrypto?.symbol}-${tradeStore.currCrypto?.chainId}-${tradeStore.currCrypto?.contract_address || ''}`

          // 确保beforeCurrency和nowCurrency不相同时才发送请求
          if (beforeCurrency !== nowCurrency) {
            UPD_ROOM_LIVE_HOT_CHAT({
              roomId,
              beforeCurrency,
              nowCurrency,
            })
          }
        }
      })

      // 将房主的交易信息传递给后端
      UPD_ROOM_SETTING({
        roomId,
        settingMap: {
          currCrypto: JSON.stringify(tradeStore.currCrypto),
        },
      })
    }
  }, [tradeStore.currCrypto.contract_address, isHost])

  if (isMobile) {
    return (
      // 移动端交易面板
      <TradeProvider>
        <div className="trade-flash-container flex h-full flex-col">
          <div className="shadow-light-900 my-1 rounded bg-[#131313] px-4 py-2.5">
            {/* 头部 */}
            <ModTitle data={tokenData} />
            {/* 买卖盘模块 */}
            <ModPandL data={tokenData} type="mobile" />
          </div>
          {/* 图表模块 */}
          <Kline />
          {/* 交易模块 */}
          <div className="flex w-full justify-between px-4">
            {/* 下单模块 */}
            <ModPlaceOrder roomId={roomId} data={tokenData} />
          </div>
        </div>
      </TradeProvider>
    )
  }

  return (
    <LocalFullscreen>
      <TradeProvider>
        {/* 头部 */}
        <div className="flex w-full items-center justify-between border-b border-[#303030] p-2.5">
          <ModTitle data={tokenData} />
          <FullscreenToggleButton />
        </div>
        {/* 交易模块 */}
        <div className="flex h-[calc(100%-60px)] w-full justify-between">
          {/* 图表模块 */}
          <Kline />

          <div className="flex h-full w-[294px] flex-col overflow-y-auto overflow-x-hidden px-2.5 pt-2 no-scroll">
            {/* 下单模块 */}
            <ModPlaceOrder roomId={roomId} data={tokenData} />
            {/* 滑点模块 */}
            <ModSlippage className="mb-2" />
            {/* 买卖盘模块数据 */}
            <ModPandL data={tokenData} type="desktop" />
            {/* 挂单模块数据 */}
            <ModDiaplay data={tokenData} />
          </div>
        </div>
      </TradeProvider>
    </LocalFullscreen>
  )
})

function Kline() {
  function getKlineUrl(ops: string) {
    const chainId = tradeStore.currCrypto?.chainId
    const address = tradeStore.currCrypto?.contract_address
    if (chainId === 'solana') {
      return `https://www.gmgn.cc/kline/sol/${address}?${ops}`
    } else if (chainId === 'ethereum') {
      return `https://www.gmgn.cc/kline/eth/${address}?${ops}`
    } else if (chainId === 'base') {
      return `https://www.gmgn.cc/kline/base/${address}?${ops}`
    } else if (chainId === 'bsc') {
      return `https://www.gmgn.cc/kline/bsc/${address}?${ops}`
    }
    return ''
  }

  return (
    <div className="relative h-full flex-1">
      <iframe
        className="z-0 h-full w-full"
        id="geckoterminal-embed"
        title="GeckoTerminal Embed"
        src={getKlineUrl('interval=15')}
        allow="clipboard-write"
        allowFullScreen
        loading="lazy"
        referrerPolicy="no-referrer"
        sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
      ></iframe>
      <div className="absolute bottom-0 left-0 z-10 flex h-8 w-full items-center justify-center bg-custom-dark text-center text-white/40">
        Powered by Pumpkin
      </div>
    </div>
  )
}

export default TradePlate
