import { Flex } from 'antd'
import { useState, useEffect, useMemo } from 'react'
import { observer } from 'mobx-react-lite'
import { perpsStore, walletStore } from '@/store'
import { GET_ARBITRUM_ADDRESS } from '@/api/interface/GET_ARBITRUM_ADDRESS'
import { Hex } from 'viem'
import { ColoringText } from '@/components/ColoringText'
import {
  SvgIconWalletCopy,
  SvgIconWalletEye,
  SvgIconWalletEyeSlash,
  SvgIconWalletDeposit,
  SvgIconWalletWithdrawal,
  SvgIconWalletHyperLiquid,
  SvgIconExplorerLink,
} from '@/imgs/icons'
import { message } from 'antd'
import i18n from '@/i18n'
import { DepositModal } from '@/pages/Wallet/DepositModal'
import { WithdrawalPerpsModal } from '@/pages/Wallet/WithdrawalPerpsModal'
import {
  PositionTab,
  CurrentOrderTab,
  HistoryTradeTab,
  FundingHistoryTab,
  HistoryOrdersTab,
} from '@/pages/Wallet/tabs'

interface PerpsWalletContentProps {
  currentAddress: string
}

export const PerpsWalletContent: React.FC<PerpsWalletContentProps> = observer(
  ({ currentAddress: initialAddress }) => {
    const [currentAddress, setCurrentAddress] = useState(initialAddress)
    const [showBalance, setShowBalance] = useState(true)
    const [loading, setLoading] = useState(false)
    const [showDepositModal, setShowDepositModal] = useState(false)
    const [showWithdrawalModal, setShowWithdrawalModal] = useState(false)

    // 获取Arbitrum地址，优先从 walletStore 中获取
    useEffect(() => {
      const fetchArbitrumAddress = async () => {
        try {
          setLoading(true)

          // 先从 walletStore 中获取地址
          const cachedAddress = walletStore.arbitrumAddress
          if (cachedAddress) {
            setCurrentAddress(cachedAddress)
            setLoading(false)
            return
          }

          // 如果 walletStore 中没有地址，则从 API 中获取
          const response = await GET_ARBITRUM_ADDRESS()
          if (response.code === 200 && response.data) {
            setCurrentAddress(response.data)
            // 将地址存储在 walletStore 中
            walletStore.setArbitrumAddress(response.data)
          }
        } catch (error) {
          console.error('获取Arbitrum地址失败:', error)
        } finally {
          setLoading(false)
        }
      }

      fetchArbitrumAddress()
    }, [])

    // 初始化 perpsStore
    useEffect(() => {
      // 确保 perpsStore.meta 已初始化
      if (Object.keys(perpsStore.meta.allData).length === 0) {
        // 初始化 meta 数据
        try {
          perpsStore.meta.getAllCoinsInfo()
        } catch (error: any) {
          console.error('Failed to initialize perpsStore.meta', error)
        }
      }
      perpsStore.setting.getMinDepositAmount()
    }, [])

    // 从店铺中获取数据
    const [realizedPnl, setRealizedPnl] = useState('0')
    const [availableBalance, setAvailableBalance] = useState('0')
    const [totalBalance, setTotalBalance] = useState('0')
    const [unrealizedPnl, setUnrealizedPnl] = useState('0')

    // 使用 perpsStore.user 获取余额信息
    useEffect(() => {
      const fetchAccountOverview = async () => {
        if (currentAddress && perpsStore.user) {
          try {
            // 将地址转换为 Hex 类型
            const userAddress = currentAddress as Hex

            // 获取账户概览数据
            perpsStore.user.getAccountOverview(userAddress)

            // 等待一段时间，确保数据已更新
            await new Promise((resolve) => setTimeout(resolve, 1000))

            // 获取余额信息
            const balance = perpsStore.user.rawBalance

            // 更新状态 - 使用 parseFloat().toFixed(6) 格式化数字，保留两位小数
            setAvailableBalance(balance.withdrawable)
            setTotalBalance(balance.accountValue)
            setUnrealizedPnl(perpsStore.user.unrealizedPnlUsd)
          } catch (error: any) {
            console.error('Failed to load account overview', error)
          }
        }
      }

      // 首次加载数据
      fetchAccountOverview()

      // 设置定时器，每5秒刷新一次数据
      const timer = setInterval(fetchAccountOverview, 10000)

      // 组件卸载时清理定时器
      return () => clearInterval(timer)
    }, [currentAddress])

    // 加载订单数据
    useEffect(() => {
      // 定义加载订单数据的函数
      const loadOrderData = async () => {
        // 使用当前地址作为用户地址
        if (currentAddress && Object.keys(perpsStore.meta.allData).length > 0) {
          // 将地址转换为 Hex 类型
          const userAddress = currentAddress as Hex

          try {
            // 加载历史成交订单
            await perpsStore.order.getHistoryOrder(userAddress)
            console.log('History orders loaded')

            // 计算已实现盈利
            let totalRealizedPnl = 0
            perpsStore.order.historyOrders.forEach((order) => {
              // 从订单的 closedPnl 字段获取已实现盈亏
              if (order.closedPnl) {
                totalRealizedPnl += parseFloat(order.closedPnl)
              }
            })

            setRealizedPnl(totalRealizedPnl.toFixed(6))
          } catch (error) {
            console.error('Failed to load history orders', error)
          }

          try {
            // 加载资金费历史
            await perpsStore.order.getFundingHistory(userAddress)
            console.log('Funding history loaded')
          } catch (error) {
            console.error('Failed to load funding history', error)
          }

          try {
            // 加载历史委托订单
            await perpsStore.order.getHistoricalOrders(userAddress)
            console.log('Historical orders loaded')
          } catch (error) {
            console.error('Failed to load historical orders', error)
          }
        }
      }

      // 高频刷新持仓订单和当前委托订单
      const loadFrequentOrders = async () => {
        if (currentAddress && Object.keys(perpsStore.meta.allData).length > 0) {
          const userAddress = currentAddress as Hex

          try {
            // 加载持仓订单
            await perpsStore.order.getPositionOrders(userAddress)
            console.log('Position orders loaded')
          } catch (error) {
            console.error('Failed to load position orders', error)
          }

          try {
            // 加载当前委托订单
            await perpsStore.order.getPendingOrder(userAddress)
            console.log('Pending orders loaded')
          } catch (error) {
            console.error('Failed to load pending orders', error)
          }
        }
      }

      // 首次加载所有数据
      loadOrderData()
      loadFrequentOrders()

      // 设置定时器，每30秒刷新一次历史数据
      const historyTimer = setInterval(loadOrderData, 30000)
      // 设置定时器，每10秒刷新一次持仓和当前委托订单
      const frequentTimer = setInterval(loadFrequentOrders, 10000)

      // 组件卸载时清理定时器
      return () => {
        clearInterval(historyTimer)
        clearInterval(frequentTimer)
      }
    }, [currentAddress, perpsStore.meta.allData])

    // 标签数据
    const tabs = useMemo(
      () => [
        {
          key: 'position',
          label: `${i18n.t('position')} ${perpsStore.order.positionOrders?.length > 0 ? `(${perpsStore.order.positionOrders.length})` : ''}`,
        },
        {
          key: 'current',
          label: `${i18n.t('current_order')} ${perpsStore.order.pendingOrders?.length > 0 ? `(${perpsStore.order.pendingOrders.length})` : ''}`,
        },
        {
          key: 'history',
          label: `${i18n.t('history_trade')}`,
        },
        {
          key: 'funding',
          label: `${i18n.t('funding_history')}`,
        },
        {
          key: 'orders',
          label: `${i18n.t('history_order')}`,
        },
      ],
      [
        perpsStore.order.positionOrders?.length,
        perpsStore.order.pendingOrders?.length,
      ],
    )

    const [activeTab, setActiveTab] = useState('position')

    return (
      <>
        {/* 账户信息区域 */}
        <Flex vertical className="w-full bg-white p-4 md:p-5">
          <Flex align="center" className="mb-4">
            <span className="text-sm font-normal text-[#1A1B1E]">
              {i18n.t('wallet_perp_realized_profit')}
            </span>
            <span className="ml-2 text-sm font-medium">
              {showBalance ? (
                <ColoringText amount={realizedPnl}>
                  {parseFloat(realizedPnl) >= 0 ? '$' : '-$'}
                  {Math.abs(parseFloat(realizedPnl)).toFixed(6)}
                </ColoringText>
              ) : (
                '****'
              )}
            </span>
            {showBalance ? (
              <SvgIconWalletEye
                className="ml-2 size-5 cursor-pointer"
                onClick={() => setShowBalance(false)}
              />
            ) : (
              <SvgIconWalletEyeSlash
                className="ml-2 size-5 cursor-pointer"
                onClick={() => setShowBalance(true)}
              />
            )}
          </Flex>

          <Flex vertical className="mb-4 w-full gap-4">
            <Flex justify="space-between" align="center" className="w-full">
              <span className="text-xs text-[#9293A0]">
                {i18n.t('wallet_perp_withdrawable_balance')} (USDC)
              </span>
              <span className="text-base font-bold md:text-xl">
                {showBalance
                  ? `$${parseFloat(
                      parseFloat(availableBalance).toFixed(6),
                    ).toString()}`
                  : '****'}
              </span>
            </Flex>

            <Flex justify="space-between" align="center" className="w-full">
              <span className="text-xs text-[#9293A0]">
                {i18n.t('wallet_perp_total_balance')} (USDC)
              </span>
              <span className="text-base font-bold md:text-xl">
                {showBalance
                  ? `$${parseFloat(parseFloat(totalBalance).toFixed(6)).toString()}`
                  : '****'}
              </span>
            </Flex>

            <Flex justify="space-between" align="center" className="w-full">
              <span className="text-xs text-[#9293A0]">
                {i18n.t('wallet_perp_unrealized_pnl')} (USDC)
              </span>
              <span className="text-base font-bold md:text-xl">
                {showBalance ? (
                  <ColoringText amount={unrealizedPnl}>
                    {parseFloat(unrealizedPnl) >= 0 ? '$' : '-$'}
                    {Math.abs(parseFloat(unrealizedPnl)).toFixed(6)}
                  </ColoringText>
                ) : (
                  '****'
                )}
              </span>
            </Flex>
          </Flex>
        </Flex>

        {/* 钱包地址和按钮区域 */}
        <Flex className="mt-4 w-full bg-white p-4 md:p-5">
          <Flex vertical className="relative w-full gap-2 md:gap-2">
            <Flex align="center" className="mb-3">
              <div className="mr-2 flex h-8 items-center bg-[#F2F3F7] px-2.5">
                <SvgIconWalletHyperLiquid className="mr-2 size-5" />
                <span className="text-base font-medium">HyperLiquid</span>
              </div>
            </Flex>

            <Flex align="flex-start" className="mb-3">
              {loading ? (
                <span className="text-sm text-[#9293A0]">加载中...</span>
              ) : (
                <Flex
                  align="center"
                  justify="space-between"
                  className="relative w-full"
                >
                  {/* 钱包地址和外部链接图标 */}
                  <a
                    href={`${import.meta.env.VITE_APP_HL_SVG_URL}/explorer/address/${currentAddress}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex flex-grow flex-row flex-wrap items-center font-lexend text-base font-normal leading-6 text-[#1A1B1E] underline hover:text-[#1A1B1E] md:text-base"
                  >
                    <span className="break-all">
                      {currentAddress}
                      <SvgIconExplorerLink className="ml-2 inline-block size-4 md:-mt-0.5" />
                    </span>
                  </a>
                  {/* 复制图标 */}
                  <SvgIconWalletCopy
                    className="ml-4 size-4 flex-shrink-0 cursor-pointer align-middle"
                    onClick={async () => {
                      try {
                        await navigator.clipboard.writeText(currentAddress)
                        message.success(i18n.t('copy_success'))
                      } catch (error) {
                        message.error(i18n.t('copy_failed'))
                        console.error('Copy failed:', error)
                      }
                    }}
                  />
                </Flex>
              )}
            </Flex>

            <Flex gap={30} className="mt-2">
              <button
                className="flex h-8 flex-1 items-center justify-center rounded-lg bg-[#1A1B1E] px-4 text-sm font-medium text-white"
                onClick={() => setShowDepositModal(true)}
              >
                <SvgIconWalletDeposit className="mr-1 size-4" />
                {i18n.t('deposit')}
              </button>
              <button
                className="flex h-8 flex-1 items-center justify-center rounded-lg bg-[#1A1B1E] px-4 text-sm font-medium text-white"
                onClick={() => setShowWithdrawalModal(true)}
              >
                <SvgIconWalletWithdrawal className="mr-1 size-4" />
                {i18n.t('withdrawal')}
              </button>
            </Flex>
          </Flex>
        </Flex>

        {/* 交易历史标签和表格 */}
        <Flex vertical className="mt-4 w-full rounded-lg bg-white">
          {/* Tab区域 */}
          <div className="w-full border-b border-[#F2F3F7]">
            <div className="scrollbar-hide overflow-x-auto">
              <div className="flex h-12 items-center gap-4 px-4 md:px-6">
                {tabs.map((tab) => (
                  <div
                    key={tab.key}
                    className={`relative flex h-full cursor-pointer items-center whitespace-nowrap text-sm transition-colors duration-200 ${
                      activeTab === tab.key
                        ? 'font-medium text-black after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] after:bg-black after:content-[""]'
                        : 'text-[#9293A0]'
                    }`}
                    onClick={() => setActiveTab(tab.key)}
                  >
                    <span className="mr-2 md:mr-4">{tab.label}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 表格内容区域 */}
          <div
            className="w-full min-w-0 px-4 py-4 md:px-6"
            style={{ boxSizing: 'border-box' }}
          >
            <div style={{ overflowX: 'auto' }}>
              <div style={{ minWidth: 1000 }}>
                {activeTab === 'position' && <PositionTab />}
                {activeTab === 'current' && <CurrentOrderTab />}
                {activeTab === 'history' && <HistoryTradeTab />}
                {activeTab === 'funding' && <FundingHistoryTab />}
                {activeTab === 'orders' && <HistoryOrdersTab />}
              </div>
            </div>
          </div>
        </Flex>

        {/* 充值弹窗 - 不显示链选择 */}
        <DepositModal
          visible={showDepositModal}
          onClose={() => setShowDepositModal(false)}
          currentNetwork="Arbitrum"
          address={currentAddress}
          onNetworkChange={() => {}}
          showNetworkSelector={false}
          type="perps"
        />

        {/* 提现弹窗 - 不显示链选择 */}
        {showWithdrawalModal && (
          <WithdrawalPerpsModal
            visible={showWithdrawalModal}
            onClose={() => setShowWithdrawalModal(false)}
            currentNetwork="Arbitrum"
            onNetworkChange={() => {}}
            onSuccess={async () => {
              // 刷新余额
              if (currentAddress) {
                try {
                  // 获取账户概览数据
                  await perpsStore.user.getAccountOverview(
                    currentAddress as Hex,
                  )

                  // 等待一段时间，确保数据已更新
                  await new Promise((resolve) => setTimeout(resolve, 1000))

                  // 获取最新的余额信息
                  const balance = perpsStore.user.rawBalance

                  // 更新状态
                  setAvailableBalance(balance.withdrawable)
                  setTotalBalance(balance.accountValue)
                  setUnrealizedPnl(perpsStore.user.unrealizedPnlUsd)
                } catch (error) {
                  console.error('更新余额失败', error)
                }
              }
            }}
            tokens={[
              {
                name: 'USD Coin',
                symbol: 'USDC',
                amount: availableBalance,
                value: availableBalance,
                decimals: 6,
              },
            ]}
          />
        )}
      </>
    )
  },
)

export default PerpsWalletContent
