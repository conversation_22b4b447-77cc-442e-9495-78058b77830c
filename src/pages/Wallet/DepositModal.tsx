import { useState, useEffect } from 'react'
import { Flex } from 'antd'
import { message } from 'antd'
import { QRCodeSVG } from 'qrcode.react'
import styled from 'styled-components'
import {
  SvgIconProfileCopy,
  SvgIconWalletSelectDown,
  SvgIconWalletSolana,
  SvgIconWalletBase,
  SvgIconWalletBsc,
  SvgIconWalletEth,
  SvgIconWalletArb,
  SvgIconModalClose,
  SvgIconWalletHyperLiquid,
} from '@/imgs/icons'
import { WarningOutlined } from '@ant-design/icons'
import i18n from '@/i18n'
import { isMobile } from 'react-device-detect'
import { perpsStore } from '@/store'
import { observer } from 'mobx-react-lite'

interface DepositModalProps {
  visible: boolean
  onClose: () => void
  currentNetwork: string
  address: string
  onNetworkChange: (network: string) => void
  showNetworkSelector?: boolean
  type?: 'spot' | 'perps'
}

// 移动端特定样式组件，使用全屏抽屉方式
const MobileDrawer = styled.div<{ open: boolean }>`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: white;
  border-radius: 16px 16px 0 0;
  padding: 20px;
  z-index: 1000;
  max-height: 80vh;
  overflow-y: auto;
  transition: transform 0.3s ease-out;
  transform: ${(props) => (props.open ? 'translateY(0)' : 'translateY(100%)')};
`

const Overlay = styled.div<{ open: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 999;
  opacity: ${(props) => (props.open ? 1 : 0)};
  visibility: ${(props) => (props.open ? 'visible' : 'hidden')};
  transition: opacity 0.3s ease-out;
`

export const DepositModal = observer(
  ({
    visible,
    onClose,
    currentNetwork,
    address,
    onNetworkChange,
    showNetworkSelector = true,
    type = 'spot',
  }: DepositModalProps) => {
    const [showNetworkSelect, setShowNetworkSelect] = useState(false)

    // 当弹窗关闭时重置下拉框状态
    useEffect(() => {
      if (!visible) {
        setShowNetworkSelect(false)
      }
    }, [visible])

    const networks = [
      { name: 'Solana', icon: <SvgIconWalletSolana className="mr-1 size-5" /> },
      { name: 'Base', icon: <SvgIconWalletBase className="mr-1 size-5" /> },
      { name: 'ETH', icon: <SvgIconWalletEth className="mr-1 size-5" /> },
      { name: 'BSC', icon: <SvgIconWalletBsc className="mr-1 size-5" /> },
    ]

    if (!visible) return null

    // 弹窗内容，在移动端和桌面端共享
    const ModalContent = () => (
      <Flex vertical gap={2} align="center">
        {showNetworkSelector ? (
          <div className="relative flex w-full justify-center">
            <Flex
              align="center"
              gap={2}
              className="mb-3 cursor-pointer md:mb-4"
              onClick={() => setShowNetworkSelect(!showNetworkSelect)}
            >
              {networks.find((n) => n.name === currentNetwork)?.icon}
              <span className="text-sm text-theme-primary md:text-base">
                {currentNetwork}
              </span>
              <SvgIconWalletSelectDown
                className={`ml-1 size-3 transition-transform md:size-4 ${
                  showNetworkSelect ? 'rotate-180' : ''
                }`}
              />
            </Flex>
            {showNetworkSelect && (
              <div className="absolute left-1/2 top-full z-10 -mt-2 w-[140px] -translate-x-1/2 rounded-lg border border-[#E5E5E5] bg-white py-1 shadow-lg">
                {networks.map((network) => (
                  <div
                    key={network.name}
                    className={`flex cursor-pointer items-center gap-2 px-3 py-1.5 hover:bg-[#F8F9FB] md:px-4 md:py-2 ${
                      currentNetwork === network.name ? 'bg-[#F8F9FB]' : ''
                    }`}
                    onClick={() => {
                      onNetworkChange(network.name)
                      setShowNetworkSelect(false)
                    }}
                  >
                    {network.icon}
                    <span className="text-sm font-normal leading-5 text-theme-primary md:text-base md:leading-6">
                      {network.name}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="mb-3 flex justify-center md:mb-4">
            <div className="flex items-center">
              {networks.find((n) => n.name === currentNetwork)?.icon || (
                <SvgIconWalletArb className="mr-1 size-5" />
              )}
              <span className="text-base font-medium">{currentNetwork}</span>
            </div>
          </div>
        )}
        <div className="mb-3 flex justify-center md:mb-4">
          <QRCodeSVG
            value={address}
            size={80}
            bgColor={'#ffffff'}
            fgColor={'#000000'}
            level={'L'}
            includeMargin={false}
            className="md:size-[100px]"
          />
        </div>
        <div className="mb-2 text-center text-xs text-[#5F606D] md:text-sm">
          {type === 'perps'
            ? i18n.t('scan_qr_code_to_deposit')
            : i18n.t('scan_spot_to_deposit')}
        </div>
        <div className="center mx-auto w-full md:w-96">
          <div className="mb-2 w-full break-words rounded-lg bg-[#F8F9FB] px-3 py-2 text-left text-xs text-[#1A1B1E] md:mb-2.5 md:w-96 md:px-4 md:py-3 md:text-sm">
            {address}
          </div>
          {type === 'perps' && (
            <div className="mb-2 inline-flex items-start justify-start gap-1 rounded-sm bg-yellow-500/10 px-2.5 py-1.5">
              <WarningOutlined className="size-3.5 text-red-600" />
              <div className="justify-start text-center text-xs font-normal text-red-600">
                {i18n.t('min_transfer_usdc', {
                  amount: perpsStore.setting.contractMinDepositAmount,
                })}
              </div>
            </div>
          )}
          <button
            className="mt-2 flex h-9 w-full items-center justify-center rounded-lg bg-black text-xs font-medium text-white md:h-10 md:text-sm"
            onClick={async () => {
              try {
                await navigator.clipboard.writeText(address)
                message.success(i18n.t('copy_success'))
              } catch (error) {
                message.error(i18n.t('copy_failed'))
              }
            }}
          >
            <SvgIconProfileCopy className="mr-1 size-3 md:size-4" />
            {i18n.t('copy')}
          </button>
        </div>
      </Flex>
    )

    // 根据设备类型渲染不同的UI
    if (isMobile) {
      return (
        <>
          <Overlay open={visible} onClick={onClose} />
          <MobileDrawer open={visible}>
            <div className="relative w-full">
              <SvgIconModalClose
                className="absolute right-0 top-0 size-5 cursor-pointer"
                onClick={onClose}
              />
              <div className="mb-4 text-center text-base font-bold">
                {i18n.t('deposit')}
              </div>
              <ModalContent />
            </div>
          </MobileDrawer>
        </>
      )
    }

    // 桌面端使用原来的弹窗样式
    return (
      <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black/50">
        <div className="relative w-[90%] max-w-[504px] rounded-lg bg-white p-6 md:w-[504px] md:p-14">
          <SvgIconModalClose
            className="absolute right-2.5 top-2.5 size-5 cursor-pointer md:size-6"
            onClick={onClose}
          />
          <ModalContent />
        </div>
      </div>
    )
  },
)
