import { useState, useEffect } from 'react'
import { Flex, Input, message } from 'antd'
import {
  usePrivy,
  useWallets,
  useSolanaWallets,
  ConnectedWallet,
  ConnectedSolanaWallet,
} from '@privy-io/react-auth'
import { SvgIconModalClose, SvgIconTUSDC, SvgIconWalletArb } from '@/imgs/icons'
// 不再需要 getTokenInfo
import { HY_WITHDRAW } from '@/api/interface/HY_WITHDRAW'
import { PublicKey } from '@solana/web3.js'
import { isAddress as isEthAddress } from 'ethers'
import i18n from '@/i18n'
import { userStore } from '@/store'

interface WithdrawalModalProps {
  visible: boolean
  onClose: () => void
  currentNetwork: string
  onNetworkChange?: (network: string) => void
  onSuccess?: () => void
  tokens: {
    name: string
    symbol: string
    amount: string
    value: string
    imageUrl?: string
    decimals?: number
  }[]
  showNetworkSelector?: boolean
}

export function WithdrawalPerpsModal({
  visible,
  onClose,
  onSuccess,
  currentNetwork,
  tokens,
}: WithdrawalModalProps) {
  // 固定使用USDC
  const [address, setAddress] = useState('')
  const [amount, setAmount] = useState('')
  // 固定资产为USDC
  const selectedAsset = 'USDC'

  // 使用 Privy 钱包 hooks
  const { authenticated, user, ready } = usePrivy()
  const { wallets: evmWallets } = useWallets()
  const { wallets: solanaWallets } = useSolanaWallets()
  const [_evmWallet, setEvmWallet] = useState<ConnectedWallet | null>(null)
  const [_solanaWallet, setSolanaWallet] =
    useState<ConnectedSolanaWallet | null>(null)

  const [isLoading, setIsLoading] = useState(false)
  const [isValidAddress, setIsValidAddress] = useState(false)

  // 检查金额是否超出余额或低于最小提币数量
  const selectedToken = tokens.find((token) => token.symbol === selectedAsset)
  const balance = selectedToken ? parseFloat(selectedToken.amount) : 0
  const inputAmount = amount ? parseFloat(amount) : 0
  const isAmountExceedBalance = inputAmount > balance

  // 检查是否低于最小提币数量(5 USDC)
  const MIN_WITHDRAWAL_AMOUNT = 5
  const isAmountBelowMinimum =
    inputAmount > 0 && inputAmount < MIN_WITHDRAWAL_AMOUNT

  // 检查小数位数是否超过8位
  const hasValidDecimals =
    !amount.includes('.') || amount.split('.')[1].length <= 8

  // 更新按钮禁用状态
  const isButtonDisabled =
    isLoading ||
    !address ||
    !amount ||
    Number(amount) <= 0 ||
    !isValidAddress ||
    isAmountExceedBalance ||
    isAmountBelowMinimum ||
    !hasValidDecimals

  const validateAddress = (addr: string, network: string) => {
    if (!addr) return false

    try {
      if (network === 'Solana') {
        // 验证 Solana 地址
        new PublicKey(addr)
        return true
      } else {
        // 验证 EVM 地址
        return isEthAddress(addr)
      }
    } catch {
      return false
    }
  }

  useEffect(() => {
    setIsValidAddress(validateAddress(address, currentNetwork))
  }, [address, currentNetwork])

  useEffect(() => {
    // 获取用户链接的钱包信息
    const privyWalletList = user?.linkedAccounts?.filter(
      (item) => item.type === 'wallet',
    ) as any[]

    if (evmWallets && evmWallets.length > 0) {
      // 获取用户链接的 EVM 钱包类型
      const evmWalletClientType =
        privyWalletList?.find((item) => item.chainType === 'ethereum')
          ?.walletClientType || 'privy'

      console.log('EVM 钱包类型:', evmWalletClientType)

      // 根据用户链接的钱包类型筛选
      const filteredWallets = evmWallets.filter(
        (w) => w.walletClientType === evmWalletClientType,
      )

      setEvmWallet(
        filteredWallets.length > 0 ? filteredWallets[0] : evmWallets[0],
      )

      // console.log('已选择 EVM 钱包:', filteredWallets.length > 0 ? filteredWallets[0] : evmWallets[0]);
    }

    if (solanaWallets && solanaWallets.length > 0) {
      // 获取用户链接的 Solana 钱包类型
      const solWalletClientType =
        privyWalletList?.find((item) => item.chainType === 'solana')
          ?.walletClientType || 'privy'

      // console.log('Solana 钱包类型:', solWalletClientType);

      // 根据用户链接的钱包类型筛选
      const filteredWallets = solanaWallets.filter(
        (w) => w.walletClientType === solWalletClientType,
      )

      setSolanaWallet(
        filteredWallets.length > 0 ? filteredWallets[0] : solanaWallets[0],
      )

      // console.log('已选择 Solana 钱包:', filteredWallets.length > 0 ? filteredWallets[0] : solanaWallets[0]);
    }
  }, [evmWallets, solanaWallets, user, ready])

  // 不再需要获取原生代币符号，因为固定使用USDC

  // 不再需要根据网络和代币设置可提现资产，因为固定使用USDC

  const resetInputs = () => {
    setAddress('')
    setAmount('')
  }

  // 不再需要资产选择功能

  useEffect(() => {
    resetInputs()
  }, [currentNetwork])

  useEffect(() => {
    setAmount('')
  }, [selectedAsset])

  if (!visible) return null

  // 不再需要获取当前网络对应的钱包

  const handleSubmit = async () => {
    if (isLoading) return // 防止多次触发
    setIsLoading(true)
    try {
      // 验证地址格式
      if (!isValidAddress) {
        message.error(i18n.t('invalid_address'))
        return
      }

      // 验证金额
      const amountNum = parseFloat(amount)
      if (isNaN(amountNum) || amountNum <= 0) {
        message.error(i18n.t('invalid_amount'))
        return
      }

      // 验证最小提币数量
      if (amountNum < MIN_WITHDRAWAL_AMOUNT) {
        message.error(`最小提币数量为 ${MIN_WITHDRAWAL_AMOUNT} USDC`)
        return
      }

      // 验证小数位数
      if (!hasValidDecimals) {
        message.error('提币金额最多支持8位小数')
        return
      }

      // 验证余额
      const tokenAmount = selectedTokenAmount
      const tokenAmountNum = parseFloat(tokenAmount)
      if (amountNum > tokenAmountNum) {
        message.error(i18n.t('insufficient_balance'))
        return
      }

      // 调用 HY_WITHDRAW 接口
      const response = await HY_WITHDRAW({
        amount: amountNum,
        address: address,
      })

      // 根据响应码判断成功或失败
      if (response && response.code === 200) {
        // 成功情况
        message.success(response.message || i18n.t('withdrawal_submitted'))
        onClose()
        // 刷新余额
        if (onSuccess) {
          onSuccess()
        }
      } else {
        // 失败情况 - 显示服务器返回的错误信息
        const errorMsg = response?.message || 'Unknown error'
        console.error('提现失败:', errorMsg, response)
        message.error('提现失败')
      }
    } catch (error: any) {
      // 网络错误或其他异常
      console.error('提现请求异常:', error)
      message.error('提现失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 不再需要获取资产图标，因为固定使用USDC

  const formatTokenAmount = (_symbol: string, amount: string) => {
    // 对于USDC，使用固定的6位小数
    const decimals = 6

    // 简单格式化，保留适当小数位
    const displayDecimals = Math.min(6, decimals)
    const num = parseFloat(amount)

    // 如果是NaN或0，返回"0"
    if (isNaN(num) || num === 0) {
      return '0'
    }

    // 格式化数字，去掉末尾的0
    return parseFloat(num.toFixed(displayDecimals)).toString()
  }

  // 查找选中资产的余额
  const selectedTokenAmount =
    tokens.find((token) => token.symbol === selectedAsset)?.amount || '0'

  // 格式化后的余额显示
  const formattedTokenAmount = formatTokenAmount(
    selectedAsset,
    selectedTokenAmount,
  )
  // 不再需要获取当前网络图标

  const handleClose = () => {
    const modal = document.querySelector('.modal-content')
    const backdrop = document.querySelector('.modal-backdrop')
    if (modal && backdrop) {
      modal.classList.remove('animate-slide-up')
      modal.classList.add('animate-slide-down')
      backdrop.classList.remove('animate-fade-in')
      backdrop.classList.add('animate-fade-out')
      setTimeout(() => {
        onClose()
      }, 300)
    } else {
      onClose()
    }
  }

  return (
    <div
      className="modal-backdrop animate-fade-in fixed inset-0 z-[60] flex items-end justify-center bg-black/50 md:items-center"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          handleClose()
        }
      }}
    >
      <div
        className="modal-content animate-slide-up max-h-[90vh] w-full overflow-y-auto rounded-t-lg bg-white p-4 md:max-h-[80vh] md:w-[400px] md:rounded-lg md:p-5"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="sticky top-0 z-10 mb-4 bg-white">
          <Flex justify="space-between" align="center">
            <span className="text-base font-bold md:text-lg">
              {i18n.t('withdrawal')}
            </span>
            <SvgIconModalClose
              className="size-4 cursor-pointer"
              onClick={handleClose}
            />
          </Flex>
        </div>
        <Flex vertical gap={3} className="md:gap-4">
          <div>
            <div className="mb-1 text-xs md:mb-2 md:text-sm">
              {i18n.t('receiver')}
            </div>
            <Input
              placeholder={i18n.t('input_wallet_address')}
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              className={`h-[38px] rounded-lg py-1 md:h-[42px] md:py-2 ${
                address && !isValidAddress ? 'border-red-500' : ''
              }`}
              status={address && !isValidAddress ? 'error' : ''}
            />
            {address && !isValidAddress && (
              <div className="mt-1 text-xs text-red-500">
                {i18n.t('valid_wallet_address')}
              </div>
            )}
          </div>

          <div>
            <div className="mb-1 text-xs md:mb-2 md:text-sm">
              {i18n.t('network')}
            </div>
            <div className="relative">
              <Flex
                align="center"
                gap={2}
                className="rounded-lg border border-[#E5E5E5] px-2 py-1.5 md:px-3 md:py-2"
              >
                <SvgIconWalletArb className="mr-1 size-5" />
                <span className="text-sm md:text-base">Arbitrum</span>
              </Flex>
            </div>
          </div>

          <div>
            <div className="mb-1 text-xs md:mb-2 md:text-sm">
              {i18n.t('asset')}
            </div>
            <div className="relative">
              <Flex
                align="center"
                gap={2}
                className="rounded-lg border border-[#E5E5E5] px-2 py-1.5 md:px-3 md:py-2"
              >
                <SvgIconTUSDC className="size-6" />
                <span className="ml-1 text-sm md:text-base">USDC</span>
                <span className="ml-auto text-xs text-gray-500 md:text-sm">
                  {formattedTokenAmount}
                </span>
              </Flex>
            </div>
          </div>

          <div>
            <div className="mb-1 text-xs md:mb-2 md:text-sm">
              {i18n.t('amount')}
            </div>
            <div className="relative">
              <Input
                placeholder={i18n.t('asset_amount')}
                value={amount}
                onChange={(e) => {
                  const value = e.target.value
                  // 只允许输入数字和小数点
                  if (!/^\d*\.?\d*$/.test(value)) return

                  // 处理前导零的情况
                  if (value === '0') {
                    setAmount('0')
                    return
                  }
                  if (value.startsWith('0') && !value.startsWith('0.')) {
                    setAmount(value.replace(/^0+/, ''))
                    return
                  }

                  // 限制小数位数为8位
                  if (value.includes('.')) {
                    const [integer, decimal] = value.split('.')
                    if (decimal?.length > 8) return
                    // 处理空小数点的情况
                    if (decimal === '') {
                      setAmount(value)
                      return
                    }
                  }

                  setAmount(value)
                }}
                className={`h-[38px] rounded-lg py-1.5 pr-14 md:h-[42px] md:py-2 md:pr-16 ${isAmountExceedBalance ? 'border-red-500' : ''}`}
                status={isAmountExceedBalance ? 'error' : ''}
              />
              <button
                className={`absolute right-2 top-2 h-[28px] rounded md:h-[28px] ${
                  isAmountExceedBalance ? 'bg-gray-200' : 'bg-gray-100'
                } px-1.5 text-xs md:px-2`}
                onClick={() => setAmount(selectedTokenAmount)}
              >
                {i18n.t('max')}
              </button>
              {isAmountExceedBalance && (
                <div className="mt-1 text-xs text-red-500">
                  {i18n.t('error_insufficient_funds')}
                </div>
              )}
              {isAmountBelowMinimum && (
                <div className="mt-1 text-xs text-red-500">Min: 5 USDC</div>
              )}
            </div>
          </div>

          <div className="mt-2 text-xs text-red-500">
            <span className="flex items-center">
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="mr-1"
              >
                <path
                  d="M8 0C3.6 0 0 3.6 0 8C0 12.4 3.6 16 8 16C12.4 16 16 12.4 16 8C16 3.6 12.4 0 8 0ZM8 12C7.4 12 7 11.6 7 11C7 10.4 7.4 10 8 10C8.6 10 9 10.4 9 11C9 11.6 8.6 12 8 12ZM9 9H7V4H9V9Z"
                  fill="#DA2525"
                />
              </svg>
              {i18n.t('perp_withdrawal_notice')}
            </span>
          </div>

          <button
            className={`mt-2 h-9 w-full rounded-lg md:h-10 ${
              isButtonDisabled || isAmountExceedBalance
                ? 'bg-gray-400'
                : 'bg-[#1A1B1E]'
            } text-white`}
            onClick={async (e) => {
              if (isLoading) return
              await handleSubmit()
            }}
            disabled={isButtonDisabled || isAmountExceedBalance}
          >
            {isLoading ? `${i18n.t('processing')}...` : i18n.t('confirm')}
          </button>
        </Flex>
      </div>
    </div>
  )
}
