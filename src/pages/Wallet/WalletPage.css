/* 钱包页面标签样式 */
.wallet-tabs .ant-tabs-nav {
  margin-bottom: 0;
  background-color: transparent;
}

.wallet-tabs .ant-tabs-nav-wrap {
  justify-content: center;
}

.wallet-tabs .ant-tabs-nav::before {
  border-bottom: none;
}

.wallet-tabs .ant-tabs-tab {
  padding: 12px 0;
  font-size: 16px;
  font-weight: 400;
  background-color: transparent;
  text-align: center;
  font-family:
    Lexend,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    sans-serif;
  line-height: 20px;
}

.wallet-tabs .ant-tabs-tab-btn {
  padding: 0 8px;
}

.wallet-tabs .ant-tabs-tab + .ant-tabs-tab {
  margin-left: 24px;
}

.wallet-tabs .ant-tabs-ink-bar {
  display: none;
}

.wallet-tabs .ant-tabs-tab.ant-tabs-tab-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 2px;
  background-color: #000;
}

.wallet-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #000;
  font-weight: 700;
}

.wallet-tabs .ant-tabs-content-holder {
  display: none;
}

@media (max-width: 768px) {
  .wallet-tabs .ant-tabs-tab {
    padding: 8px 0;
    font-size: 14px;
  }

  .wallet-tabs .ant-tabs-tab + .ant-tabs-tab {
    margin-left: 16px;
  }

  /* 移动端表格样式 */
  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  /* 确保表格内容不会被截断 */
  td,
  th {
    min-width: 80px;
  }

  /* 移动端页面内容样式 */
  .h-screen {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
  }
}
