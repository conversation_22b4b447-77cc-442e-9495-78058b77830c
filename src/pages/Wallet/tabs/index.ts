// 从 Perp 模块导入组件并重新导出
import {
  PositionTab as PerpPositionTab,
  CurrentOrderTab as PerpCurrentOrderTab,
  HistoryTradeTab as PerpHistoryTradeTab,
  FundingHistoryTab as PerpFundingHistoryTab,
  HistoryOrdersTab as PerpHistoryOrdersTab,
} from '@/pages/Perp/module/tabs'

// 创建包装组件，传入 theme="light" 参数
import { createWalletTabWrapper } from './tabWrapper'

// 使用包装器创建适配 Wallet 页面的组件
export const PositionTab = createWalletTabWrapper(PerpPositionTab)
export const CurrentOrderTab = createWalletTabWrapper(PerpCurrentOrderTab)
export const HistoryTradeTab = createWalletTabWrapper(PerpHistoryTradeTab)
export const FundingHistoryTab = createWalletTabWrapper(PerpFundingHistoryTab)
export const HistoryOrdersTab = createWalletTabWrapper(PerpHistoryOrdersTab)
