import { Flex, Dropdown } from 'antd'
import { useState, useEffect, useRef } from 'react'
import { message } from 'antd'
import i18n from '@/i18n'
import { walletStore } from '@/store'
import { usePrivy } from '@privy-io/react-auth'
import { useSolanaWallets } from '@privy-io/react-auth/solana'
import {
  SvgIconWalletCopy,
  SvgIconWalletSelectDown,
  SvgIconWalletEye,
  SvgIconWalletEyeSlash,
  SvgIconWalletSolana,
  SvgIconWalletBase,
  SvgIconWalletBsc,
  SvgIconWalletEth,
  SvgIconWalletArb,
  SvgIconTUSDT,
  SvgIconTEth,
  SvgIconTSol,
  SvgIconTBsc,
  SvgIconWalletDeposit,
  SvgIconWalletWithdrawal,
} from '@/imgs/icons'
import {
  GET_ALL_TOKEN_BALANCE,
  ChainId,
} from '@/api/interface/GET_ALL_TOKEN_BALANCE'
import { GET_TOKEN_INFO } from '@/api/interface/GET_TOKEN_INFO'
import {
  GET_USER_BIND_ACCOUNT,
  BindAccountType,
} from '@/api/interface/GET_USER_BIND_ACCOUNT'
import { DepositModal } from '@/pages/Wallet/DepositModal'
import { WithdrawalModal } from './WithdrawalModal'

interface SpotWalletContentProps {
  currentAddress: string
  addressCache: {
    solana?: { address: string; connectorType: string | null }
    evm?: { address: string; connectorType: string | null }
  }
}

const tokenInfoCache: Record<string, any> = {}

export const SpotWalletContent: React.FC<SpotWalletContentProps> = ({
  currentAddress: initialAddress,
  addressCache: initialAddressCache,
}) => {
  const [currentAddress, setCurrentAddress] = useState(initialAddress)
  const [addressCache, setAddressCache] = useState(initialAddressCache)
  const [balance, setBalance] = useState('0')
  const [tokens, setTokens] = useState<
    {
      name: string
      symbol: string
      amount: string
      value: string
      imageUrl?: string
      tokenAddress?: string
    }[]
  >([])
  const [showDepositModal, setShowDepositModal] = useState(false)
  const [showWithdrawalModal, setShowWithdrawalModal] = useState(false)
  const [showBalance, setShowBalance] = useState(() => {
    const saved = localStorage.getItem('walletShowBalance')
    return saved !== null ? saved === 'true' : true
  })
  const [showNetworkSelect, setShowNetworkSelect] = useState(false)
  const [selectedNetwork, setSelectedNetwork] = useState(() => {
    const saved = localStorage.getItem('walletSelectedNetwork')
    return saved || 'Solana'
  })
  const dropdownRef = useRef<HTMLDivElement>(null)
  const [loading, setLoading] = useState(true)

  // 使用 Privy 钩子
  const { exportWallet } = usePrivy()
  const { exportWallet: exportSolanaWallet } = useSolanaWallets()

  const networks = [
    { name: 'Solana', icon: <SvgIconWalletSolana className="mr-1 size-5" /> },
    { name: 'Base', icon: <SvgIconWalletBase className="mr-1 size-5" /> },
    { name: 'ETH', icon: <SvgIconWalletEth className="mr-1 size-5" /> },
    { name: 'BSC', icon: <SvgIconWalletBsc className="mr-1 size-5" /> },
  ]

  // 导出私钥菜单项
  const moreMenuItems = [
    {
      key: 'exportPrivateKey',
      label: i18n.t('export_wallet'),
      onClick: async () => {
        try {
          const addressInfo =
            selectedNetwork.toLowerCase() === 'solana'
              ? addressCache.solana
              : addressCache.evm

          if (selectedNetwork.toLowerCase() === 'solana') {
            await exportSolanaWallet({ address: addressInfo!.address })
          } else {
            await exportWallet({ address: addressInfo!.address })
          }
        } catch (error) {
          console.error('export wallet failed:', error)
        }
      },
    },
  ]

  // 处理点击外部关闭下拉菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowNetworkSelect(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // 保存显示余额设置到本地存储
  useEffect(() => {
    localStorage.setItem('walletShowBalance', showBalance.toString())
  }, [showBalance])

  // 保存选择的网络到本地存储
  useEffect(() => {
    localStorage.setItem('walletSelectedNetwork', selectedNetwork)
  }, [selectedNetwork])

  // 获取代币图标
  const getAssetIcon = (symbol: string) => {
    switch (symbol.toUpperCase()) {
      case 'USDT':
        return <SvgIconTUSDT className="size-8" />
      case 'ETH':
        return <SvgIconTEth className="size-8" />
      case 'SOL':
        return <SvgIconTSol className="size-8" />
      case 'BNB':
        return <SvgIconTBsc className="size-8" />
      default:
        return null
    }
  }

  const getChainId = (network: string): ChainId => {
    const networkMap: Record<string, ChainId> = {
      ETH: 'ethereum',
      BSC: 'bsc',
      Base: 'base',
      Solana: 'solana',
    }
    return networkMap[network] || 'solana'
  }

  // 获取地址，优先使用缓存和 walletStore
  useEffect(() => {
    const fetchAddress = async () => {
      // 如果缓存中已有对应网络的地址，直接使用
      if (
        (selectedNetwork.toLowerCase() === 'solana' &&
          addressCache?.solana?.address) ||
        (selectedNetwork.toLowerCase() !== 'solana' &&
          addressCache?.evm?.address)
      ) {
        const addressInfo =
          selectedNetwork.toLowerCase() === 'solana'
            ? addressCache.solana
            : addressCache.evm
        setCurrentAddress(addressInfo?.address || '')
        return
      }

      // 如果缓存中没有，尝试从 walletStore 中获取
      const solanaAddress = walletStore.solanaAddress
      const solanaConnectorType = walletStore.solanaConnectorType
      const evmAddress = walletStore.evmAddress
      const evmConnectorType = walletStore.evmConnectorType

      if (solanaAddress || evmAddress) {
        // 更新地址缓存
        const newCache = {
          ...addressCache,
          solana: solanaAddress
            ? {
                address: solanaAddress,
                connectorType: solanaConnectorType,
              }
            : addressCache.solana,
          evm: evmAddress
            ? {
                address: evmAddress,
                connectorType: evmConnectorType,
              }
            : addressCache.evm,
        }

        setAddressCache(newCache)

        // 设置当前选中网络的地址
        if (selectedNetwork.toLowerCase() === 'solana' && solanaAddress) {
          setCurrentAddress(solanaAddress)
          return
        } else if (selectedNetwork.toLowerCase() !== 'solana' && evmAddress) {
          setCurrentAddress(evmAddress)
          return
        }
      }

      // 如果缓存和 walletStore 中都没有，则从 API 中获取
      try {
        const res = await GET_USER_BIND_ACCOUNT()
        if (res.code === 200) {
          // 获取Solana和EVM地址及connector_type
          const solanaAccount = res.data.bindAccountList.find(
            (account) => account.type === BindAccountType.WALLET_SOL,
          )
          const evmAccount = res.data.bindAccountList.find(
            (account) => account.type === BindAccountType.WALLET,
          )

          // 将地址存储在 walletStore 中
          if (solanaAccount?.address) {
            walletStore.setSolanaAddress(
              solanaAccount.address,
              solanaAccount.connector_type || '',
            )
          }
          if (evmAccount?.address) {
            walletStore.setEvmAddress(
              evmAccount.address,
              evmAccount.connector_type || '',
            )
          }

          // 更新地址缓存
          setAddressCache(walletStore.getAddressCache())

          // 设置当前选中网络的地址
          const currentAccount =
            selectedNetwork.toLowerCase() === 'solana'
              ? solanaAccount
              : evmAccount
          setCurrentAddress(currentAccount?.address || '')
        }
      } catch (error) {
        console.error('获取地址失败:', error)
      }
    }

    fetchAddress()
  }, [selectedNetwork, addressCache])

  // 获取钱包余额
  useEffect(() => {
    const fetchTokenBalance = async () => {
      if (!currentAddress) {
        setLoading(false)
        return
      }

      const isSolanaAddress = !currentAddress.startsWith('0x') // Solana地址不以0x开头
      const isCorrectChain =
        (selectedNetwork.toLowerCase() === 'solana' && isSolanaAddress) ||
        (selectedNetwork.toLowerCase() !== 'solana' && !isSolanaAddress)

      if (!isCorrectChain) {
        setLoading(false)
        return
      }

      setLoading(true)
      try {
        const res = await GET_ALL_TOKEN_BALANCE(
          currentAddress,
          getChainId(selectedNetwork),
        )
        if (res.code === 200) {
          const tokenAssets = Array.isArray(res.data)
            ? res.data[0]?.tokenAssets
            : res.data.tokens || []

          const validTokens =
            tokenAssets && Array.isArray(tokenAssets)
              ? tokenAssets.filter((token: any) => Number(token.tokenPrice) > 0)
              : []

          const formattedTokens = validTokens.map((token: any) => ({
            name: token.symbol,
            symbol: token.symbol,
            amount: token.balance,
            value: `$ ${(Number(token.balance) * Number(token.tokenPrice)).toFixed(4)}`,
            tokenAddress: token.tokenAddress || '',
            // 添加数值型价值用于计算总额
            numericValue: Number(token.balance) * Number(token.tokenPrice),
          }))

          // 先设置基本数据，让用户可以看到余额信息
          setTokens(formattedTokens)

          // 计算总余额
          const totalBalance = formattedTokens.reduce(
            (sum: number, token: { numericValue?: number }) =>
              sum + (token.numericValue || 0),
            0,
          )
          setBalance(totalBalance.toFixed(6))

          // 完成基本数据加载
          setLoading(false)

          // 异步加载代币图标
          loadTokenIcons(formattedTokens, getChainId(selectedNetwork))
        } else if (res.code === 500) {
          // 如果返回 500 错误，当作余额为 0 处理
          setTokens([])
          setBalance('0')
          setLoading(false)
        } else {
          setTokens([])
          setBalance('0')
          setLoading(false)
        }
      } catch (error) {
        console.error('Get Token Balance Fail:', error)
        setTokens([])
        setBalance('0')
        setLoading(false)
      }
    }

    fetchTokenBalance()
  }, [currentAddress, selectedNetwork])

  // 当选择的网络变化时，更新当前地址
  useEffect(() => {
    if (addressCache.solana || addressCache.evm) {
      const addressInfo =
        selectedNetwork.toLowerCase() === 'solana'
          ? addressCache.solana
          : addressCache.evm
      setCurrentAddress(addressInfo?.address || '')
    }
  }, [selectedNetwork, addressCache])

  // 新增异步加载代币图标的函数
  const loadTokenIcons = async (
    tokensList: Array<{
      symbol: string
      tokenAddress?: string
      name: string
      amount: string
      value: string
      numericValue?: number
    }>,
    chainId: ChainId,
  ) => {
    // 筛选出需要获取图标的代币地址
    const uncommonTokens = tokensList.filter(
      (token) =>
        !['USDT', 'ETH', 'SOL', 'BNB'].includes(token.symbol.toUpperCase()) &&
        token.tokenAddress,
    )
    const tokenAddresses = uncommonTokens
      .map((token) => token.tokenAddress!)
      .filter(Boolean)

    if (tokenAddresses.length === 0) return

    try {
      // 单个处理每个代币，获取到一个就更新一个
      for (let i = 0; i < tokenAddresses.length; i++) {
        const address = tokenAddresses[i]

        // 检查缓存
        const cacheKey = `${chainId}_${address}`
        let iconData = null

        if (tokenInfoCache[cacheKey]) {
          iconData = tokenInfoCache[cacheKey]
        } else {
          try {
            const tokenInfo = await GET_TOKEN_INFO(address, chainId)
            if (tokenInfo.code === 200) {
              // 存入缓存
              tokenInfoCache[cacheKey] = tokenInfo.data
              iconData = tokenInfo.data
            }
          } catch (err) {
            console.error(`get ${address} info fail:`, err)
          }
        }

        // 如果获取到了图标数据，立即更新对应的代币
        if (iconData) {
          setTokens((prevTokens) => {
            return prevTokens.map((token) => {
              if (token.tokenAddress === address) {
                return {
                  ...token,
                  imageUrl: iconData.imageUrl,
                  // 添加代币精度信息
                  decimals:
                    iconData.decimals || (chainId === 'solana' ? 9 : 18),
                }
              }
              return token
            })
          })
        }

        // 添加小延迟，避免请求过于密集
        if (i < tokenAddresses.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 100))
        }
      }
    } catch (error) {
      console.error('get token icon fail:', error)
    }
  }

  return (
    <>
      <Flex vertical className="w-full bg-white p-4 md:p-5">
        <Flex align="center" className="mb-4">
          <span className="text-sm font-normal text-[#1A1B1E]">
            {i18n.t('my_assets')}
          </span>
          {showBalance ? (
            <SvgIconWalletEye
              className="ml-2 size-5 cursor-pointer"
              onClick={() => setShowBalance(false)}
            />
          ) : (
            <SvgIconWalletEyeSlash
              className="ml-2 size-5 cursor-pointer"
              onClick={() => setShowBalance(true)}
            />
          )}
        </Flex>
        <Flex align="center" justify="center" className="mb-6">
          <span className="text-center text-2xl font-bold text-[#1A1B1E] md:text-[32px]">
            {showBalance ? `${balance} USD` : '**** USD'}
          </span>
        </Flex>
      </Flex>

      <Flex className="mt-4 w-full bg-white p-4 md:p-5">
        <Flex vertical className="relative w-full gap-2 md:gap-2">
          {/* 添加右上角下拉菜单 - 仅对嵌入式钱包显示 */}
          {((selectedNetwork.toLowerCase() === 'solana' &&
            addressCache.solana?.connectorType === 'embedded') ||
            (selectedNetwork.toLowerCase() !== 'solana' &&
              addressCache.evm?.connectorType === 'embedded')) && (
            <Flex className="absolute right-0 top-0">
              <Dropdown
                menu={{ items: moreMenuItems }}
                placement="bottomRight"
                trigger={['click']}
              >
                <div className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full hover:bg-[#F8F9FB]">
                  <span className="text-lg font-bold">⋯</span>
                </div>
              </Dropdown>
            </Flex>
          )}

          <Flex align="center" className="mb-3">
            <div className="relative" ref={dropdownRef}>
              <div
                className="flex cursor-pointer items-center rounded-lg px-2 py-1"
                onClick={() => setShowNetworkSelect(!showNetworkSelect)}
              >
                <span className="text-base">
                  {networks.find((n) => n.name === selectedNetwork)?.icon || (
                    <SvgIconWalletSolana className="mr-1 size-5" />
                  )}
                </span>
                <span className="text-base font-medium">{selectedNetwork}</span>
                <SvgIconWalletSelectDown
                  className={`ml-1 size-3 transition-transform ${
                    showNetworkSelect ? 'rotate-180' : ''
                  }`}
                />
              </div>
              {showNetworkSelect && (
                <div className="absolute left-0 top-full z-10 mt-1 min-w-[120px] rounded-lg border border-[#E5E5E5] bg-white py-1 shadow-lg">
                  {networks.map((network) => (
                    <div
                      key={network.name}
                      className={`flex cursor-pointer items-center gap-2 px-3 py-2 hover:bg-[#F8F9FB] ${
                        selectedNetwork === network.name ? 'bg-[#F8F9FB]' : ''
                      }`}
                      onClick={() => {
                        setSelectedNetwork(network.name)
                        setShowNetworkSelect(false)
                      }}
                    >
                      <span className="text-base">{network.icon}</span>
                      <span className="text-base font-normal">
                        {network.name}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Flex>

          <Flex align="center" className="mb-3">
            <span className="max-w-[calc(100%-30px)] break-all font-lexend text-sm font-normal leading-6 text-[#1A1B1E] md:text-base">
              {currentAddress}
            </span>
            <SvgIconWalletCopy
              className="ml-2 size-4 flex-shrink-0 cursor-pointer"
              onClick={async () => {
                try {
                  await navigator.clipboard.writeText(currentAddress)
                  message.success(i18n.t('copy_success'))
                } catch (error) {
                  message.error(i18n.t('copy_failed'))
                  console.error('Copy failed:', error)
                }
              }}
            />
          </Flex>

          <Flex gap={30} className="mt-2">
            <button
              className="flex h-8 flex-1 items-center justify-center rounded-lg bg-[#1A1B1E] px-4 text-sm font-medium text-white"
              onClick={() => setShowDepositModal(true)}
            >
              <SvgIconWalletDeposit className="mr-1 size-4" />
              {i18n.t('deposit')}
            </button>
            <button
              className="flex h-8 flex-1 items-center justify-center rounded-lg bg-[#1A1B1E] px-4 text-sm font-medium text-white"
              onClick={() => setShowWithdrawalModal(true)}
            >
              <SvgIconWalletWithdrawal className="mr-1 size-4" />
              {i18n.t('withdrawal')}
            </button>
          </Flex>
        </Flex>
      </Flex>

      <Flex vertical className="rounded-lg bg-white p-0 md:p-5">
        <span className="mb-3 ml-2 mt-2 text-base font-bold md:mb-4 md:ml-0 md:mt-0 md:text-lg">
          {i18n.t('tokens')}
        </span>
        <Flex vertical className="max-h-[280px] overflow-y-auto p-4 md:p-0">
          {/* 在移动端隐藏表头，在桌面端显示 */}
          <Flex className="mb-3 hidden text-xs font-normal leading-4 text-[#5F606D] md:mb-4 md:flex md:text-[14px] md:leading-5">
            <span className="w-2/5">{i18n.t('asset')}</span>
            <span className="w-1/5 text-right">{i18n.t('amount')}</span>
            <span className="w-2/5 pr-2 text-right md:pr-4">
              {i18n.t('value')}
            </span>
          </Flex>

          {loading ? (
            <Flex className="h-12 items-center justify-center md:h-14">
              <span className="text-sm text-[#5F606D] md:text-base">
                {i18n.t('loading')}...
              </span>
            </Flex>
          ) : tokens.length > 0 ? (
            tokens.map((token) => (
              <Flex
                key={token.symbol}
                className="mb-3 h-10 items-center justify-between text-sm font-normal leading-5 text-[#1A1B1E] md:mb-4 md:h-14 md:text-base md:leading-6"
              >
                {/* 移动端左侧：代币图标和名称 */}
                <Flex className="items-center gap-2 overflow-hidden md:w-2/5 md:gap-2">
                  {getAssetIcon(token.symbol) ||
                    (token.imageUrl ? (
                      <img
                        src={token.imageUrl}
                        alt={token.symbol}
                        className="size-8 flex-shrink-0 rounded-full md:size-8"
                      />
                    ) : (
                      <div className="flex size-8 flex-shrink-0 items-center justify-center rounded-full bg-gray-200 md:size-8">
                        {token.symbol.substring(0, 1)}
                      </div>
                    ))}
                  <span className="truncate font-medium">{token.name}</span>
                </Flex>

                {/* 移动端右侧：代币数量和价值垂直排列 */}
                <Flex vertical className="items-end md:hidden">
                  <span className="text-right text-sm font-medium text-[#1A1B1E]">
                    {showBalance
                      ? Number(token.amount)
                          .toFixed(8)
                          .replace(/\.?0+$/, '')
                      : '****'}
                  </span>
                  <span className="text-right text-xs text-[#9293A0]">
                    {showBalance ? token.value : '****'}
                  </span>
                </Flex>

                {/* 桌面端保持原来的水平布局 */}
                <span className="hidden w-1/5 text-right md:block">
                  {showBalance
                    ? Number(token.amount)
                        .toFixed(8)
                        .replace(/\.?0+$/, '')
                    : '****'}
                </span>
                <span className="hidden w-2/5 pr-2 text-right md:block md:pr-4">
                  {showBalance ? token.value : '****'}
                </span>
              </Flex>
            ))
          ) : (
            <Flex className="h-12 items-center justify-center md:h-14">
              <span className="text-sm text-[#5F606D] md:text-base">
                {i18n.t('no_assets')}
              </span>
            </Flex>
          )}
        </Flex>
      </Flex>

      <DepositModal
        visible={showDepositModal}
        onClose={() => setShowDepositModal(false)}
        currentNetwork={selectedNetwork}
        address={currentAddress}
        onNetworkChange={setSelectedNetwork}
        type="spot"
      />
      {showWithdrawalModal && (
        <WithdrawalModal
          visible={showWithdrawalModal}
          onClose={() => setShowWithdrawalModal(false)}
          currentNetwork={selectedNetwork}
          onNetworkChange={setSelectedNetwork}
          tokens={tokens}
        />
      )}
    </>
  )
}

export default SpotWalletContent
