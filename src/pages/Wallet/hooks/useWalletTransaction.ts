import { useState } from 'react'
import { message } from 'antd'
import { ConnectedWallet, ConnectedSolanaWallet } from '@privy-io/react-auth'
import { parseUnits, parseEther } from 'ethers'
import { Interface } from 'ethers'
import { getChainId } from '@/pages/Wallet/constants/networks'
import { getTokenInfo } from '@/pages/Wallet/constants/tokens'
import { switchToTargetChain } from '@/pages/Trade/hooks/method/setChain'
import {
  Connection,
  LAMPORTS_PER_SOL,
  PublicKey,
  SystemProgram,
  Transaction,
  SendTransactionError,
  VersionedTransaction,
} from '@solana/web3.js'
import {
  createTransferInstruction,
  getAssociatedTokenAddress,
} from '@solana/spl-token'
import i18n from '@/i18n'

interface TransactionParams {
  address: string
  amount: string
  network: string
  asset: string
  tokenAddress?: string
  decimals?: number
  evmWallet: ConnectedWallet | null
  solanaWallet: ConnectedSolanaWallet | null
}

export const useWalletTransaction = () => {
  const [isLoading, setIsLoading] = useState(false)

  // 处理 Solana 交易
  const handleSolanaTransaction = async (
    to: string,
    amount: string,
    wallet: ConnectedSolanaWallet,
    tokenAddress?: string,
    decimals: number = 6,
  ) => {
    try {
      // 创建连接
      const connection = new Connection(
        import.meta.env.VITE_CUSTOM_SOLANA_RPC_URL,
        {
          commitment: 'confirmed',
        },
      )

      // 创建发送方和接收方的公钥
      const fromPubkey = new PublicKey(wallet.address)
      const toPubkey = new PublicKey(to)

      // 创建交易对象
      const transaction = new Transaction()

      if (tokenAddress) {
        // SPL 代币转账
        // 获取发送方的代币账户地址
        const fromTokenAccount = await getAssociatedTokenAddress(
          new PublicKey(tokenAddress),
          fromPubkey,
        )

        // 获取接收方的代币账户地址
        const toTokenAccount = await getAssociatedTokenAddress(
          new PublicKey(tokenAddress),
          toPubkey,
        )

        // 将金额转换为最小单位，使用传入的精度
        const amountInSmallestUnit = Math.floor(
          parseFloat(amount) * 10 ** decimals,
        )

        // 创建转账指令
        const transferInstruction = createTransferInstruction(
          fromTokenAccount,
          toTokenAccount,
          fromPubkey,
          BigInt(amountInSmallestUnit),
        )

        // 添加指令到交易
        transaction.add(transferInstruction)
      } else {
        // SOL 原生代币转账
        // 将金额转换为 lamports
        const amountInLamports = Math.floor(
          parseFloat(amount) * LAMPORTS_PER_SOL,
        )

        // 创建转账指令
        const transferInstruction = SystemProgram.transfer({
          fromPubkey,
          toPubkey,
          lamports: amountInLamports,
        })

        // 添加指令到交易
        transaction.add(transferInstruction)
      }

      // 获取最新区块哈希
      const { blockhash, lastValidBlockHeight } =
        await connection.getLatestBlockhash()
      transaction.recentBlockhash = blockhash
      transaction.feePayer = fromPubkey

      const serializedTransaction = transaction.serialize({
        requireAllSignatures: false,
        verifySignatures: false,
      })

      const tx = VersionedTransaction.deserialize(serializedTransaction)

      // let signature
      // 检查钱包类型是否为 Phantom
      // if (
      //   wallet.walletClientType === 'phantom' &&
      //   (window as any).phantom?.solana?.isPhantom
      // ) {
      //   // 使用 Phantom 的 signAndSendTransaction 方法直接签名并发送交易
      //   const phantomProvider = (window as any).phantom?.solana
      //   // 创建未签名的交易（确保有足够空间给 Lighthouse guard instructions）
      //   const { signature: phantomSignature } =
      //     await phantomProvider.signAndSendTransaction(tx)
      //   signature = phantomSignature
      // } else {
      //   // 使用钱包签名交易
      //   const signedTx = await wallet.signTransaction(tx)

      //   // 立即发送交易
      //   signature = await connection.sendRawTransaction(signedTx.serialize(), {
      //     skipPreflight: false,
      //   })
      // }

      // 使用 Phantom 的 signAndSendTransaction 方法直接签名并发送交易
      const phantomProvider = (window as any).phantom?.solana
      // 创建未签名的交易（确保有足够空间给 Lighthouse guard instructions）
      const { signature } =
        await phantomProvider.signAndSendTransaction(transaction)

      return signature
    } catch (error) {
      console.error('Solana transaction failed:', error)

      // 增强错误处理
      if (error instanceof SendTransactionError) {
        throw new Error(`Transaction fail: ${error.message}`)
      }

      // 处理其他类型的错误
      const errorMessage =
        error instanceof Error ? error.message : String(error)

      if (errorMessage.includes('Blockhash not found')) {
        throw new Error('The transaction has expired, please try again')
      }

      // 处理区块高度超过错误
      if (errorMessage.includes('block height exceeded')) {
        throw new Error(
          'The transaction has been submitted but not confirmed. Please check the transaction history',
        )
      }

      throw error instanceof Error ? error : new Error(String(error))
    }
  }
  // 处理 EVM 交易
  const handleEVMTransaction = async (
    to: string,
    amount: string,
    chainId: number,
    network: string,
    wallet: ConnectedWallet,
    tokenAddress?: string,
    decimals: number = 18,
  ) => {
    try {
      // 获取 provider
      const privyProvider = await wallet.getEthereumProvider()

      const chainType = (() => {
        const networkLower = network.toLowerCase()
        if (networkLower === 'eth') return 'ethereum'
        if (networkLower === 'bsc') return 'bsc'
        if (networkLower === 'base') return 'base'
        if (networkLower === 'solana') return 'solana'
        return 'ethereum'
      })() as 'ethereum' | 'bsc' | 'base' | 'solana'

      await switchToTargetChain({ wallet, chainId: chainType })

      // 检查当前网络
      const currentChainId = await privyProvider.request({
        method: 'eth_chainId',
      })

      // 如果网络不对，先切换网络
      if (currentChainId !== `0x${chainId.toString(16)}`) {
        await privyProvider.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: `0x${chainId.toString(16)}` }],
        })
      }

      if (tokenAddress) {
        // 代币转账
        const erc20Interface = new Interface([
          'function transfer(address to, uint256 amount) returns (bool)',
        ])

        // 使用正确的精度解析金额
        const amountInSmallestUnit = parseUnits(amount, decimals)

        const data = erc20Interface.encodeFunctionData('transfer', [
          to,
          amountInSmallestUnit,
        ])

        // 构建交易参数
        const txParams = {
          from: wallet.address,
          to: tokenAddress,
          data,
          value: '0x0',
        }

        // 发送交易
        const txHash = await privyProvider.request({
          method: 'eth_sendTransaction',
          params: [txParams],
        })

        return txHash
      } else {
        // 原生代币转账
        const amountInWei = parseEther(amount)

        // 构建交易参数
        const txParams = {
          from: wallet.address,
          to,
          value: `0x${amountInWei.toString(16)}`,
        }

        // 发送交易
        const txHash = await privyProvider.request({
          method: 'eth_sendTransaction',
          params: [txParams],
        })

        return txHash
      }
    } catch (error: any) {
      console.error('EVM transaction failed:', error)
      if (error.code === 4001) {
        throw new Error(i18n.t('user_cancels_transaction'))
      }
      throw error
    }
  }

  // 执行交易
  const executeTransaction = async ({
    address,
    amount,
    network,
    asset,
    tokenAddress,
    decimals,
    evmWallet,
    solanaWallet,
  }: TransactionParams) => {
    if (!address || !amount || Number(amount) <= 0) {
      message.error(i18n.t('valid_address_and_amount'))
      return false
    }

    setIsLoading(true)
    try {
      // 根据不同网络获取对应的链ID
      const chainId = getChainId(network)

      // 如果是USDT等代币，需要调用合约
      if (asset !== 'ETH' && asset !== 'SOL' && asset !== 'BNB') {
        // 优先使用传入的tokenAddress，如果没有则从getTokenInfo获取
        const actualTokenAddress =
          tokenAddress || getTokenInfo(asset, network).address
        if (!actualTokenAddress) {
          throw new Error(
            `Contract address for ${asset} on ${network} not found`,
          )
        }

        // 获取代币精度
        const tokenDecimals =
          decimals || getTokenInfo(asset, network).decimals || 18

        // 根据不同链处理交易
        if (network === 'Solana') {
          if (!solanaWallet)
            throw new Error(i18n.t('not_connected_wallet', { network }))
          await handleSolanaTransaction(
            address,
            amount,
            solanaWallet,
            actualTokenAddress,
            tokenDecimals,
          )
        } else {
          if (!evmWallet)
            throw new Error(i18n.t('not_connected_wallet', { network }))
          await handleEVMTransaction(
            address,
            amount,
            chainId,
            network,
            evmWallet,
            actualTokenAddress,
            tokenDecimals,
          )
        }
      } else {
        // 原生代币转账
        if (network === 'Solana') {
          if (!solanaWallet)
            throw new Error(i18n.t('not_connected_wallet', { network }))

          await handleSolanaTransaction(
            address,
            amount,
            solanaWallet,
            undefined,
            9,
          )
        } else {
          if (!evmWallet)
            throw new Error(i18n.t('not_connected_wallet', { network }))
          await handleEVMTransaction(
            address,
            amount,
            chainId,
            network,
            evmWallet,
          )
        }
      }

      message.success(i18n.t('withdrawal_request_submitted'))
      return true
    } catch (error: any) {
      console.error('withdraw fail:', error)
      message.error(`withdraw fail: please try again`)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  return {
    isLoading,
    executeTransaction,
  }
}
