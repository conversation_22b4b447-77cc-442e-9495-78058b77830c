import { Flex, Table, Tooltip, Spin } from 'antd'
import { SvgIconQuestionCircle, SvgIconReferralUSDC } from '@/imgs/icons'
import i18n from '@/i18n'
import { useDevice } from '@/hooks/useDevice'
import { useState, useEffect } from 'react'
import { SHARE_PERPS_RECORDS } from '@/api/interface/SHARE_PERPS_RECORDS'
import type { ColumnsType } from 'antd/es/table'
import styles from '@/pages/Profile/css/profile.module.css'
import { useRoute } from '@/hooks/useRoute'
import { $ten } from '@/utils/_calculate'

interface PerpsReferralModalProps {
  inverterInfo: any
  settings: any
  handleReceive: (
    chainId: 'ethereum' | 'bsc' | 'base' | 'solana' | 'hyperliquid',
  ) => void
  getMinAmount: (network: string, settings: any) => number
  getTooltipText: (network: string, minAmount: number) => string
}

function PerpsReferralModal({
  inverterInfo,
  settings,
  handleReceive,
}: PerpsReferralModalProps) {
  const { isMobile } = useDevice()
  const { path } = useRoute()
  const [records, setRecords] = useState<any[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const minReceiveAmount = settings?.contractMinReceiveAmount || 10
  const isDisabled =
    !inverterInfo?.swapAmount || inverterInfo?.swapAmount < minReceiveAmount

  // 桌面端表格列配置
  const desktopColumns: ColumnsType<any> = [
    {
      title: i18n.t('time'),
      dataIndex: 'createTime',
      key: 'createTime',
      render: (time: number) => {
        const date = new Date(time)
        const formatNumber = (num: number) => String(num).padStart(2, '0')

        const year = date.getFullYear()
        const month = formatNumber(date.getMonth() + 1)
        const day = formatNumber(date.getDate())
        const hours = formatNumber(date.getHours())
        const minutes = formatNumber(date.getMinutes())
        const seconds = formatNumber(date.getSeconds())

        return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`
      },
    },
    {
      title: i18n.t('user'),
      dataIndex: 'nickName',
      key: 'nickName',
      render: (nickName: string, record: any) => (
        <span
          className="cursor-pointer text-[#1A1B1E] hover:text-blue-500 hover:underline"
          onClick={() => path(`/user/${record.userId}`)}
        >
          {nickName}
        </span>
      ),
    },
    {
      title: i18n.t('commission'),
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number, record: any) => {
        const formattedAmount = $ten.toFixed(amount, 6, false)
        return `${formattedAmount} USDC`
      },
    },
    {
      title: i18n.t('network'),
      dataIndex: 'netWork',
      key: 'netWork',
    },
    {
      title: i18n.t('source'),
      dataIndex: 'shareTypeEnum',
      key: 'shareTypeEnum',
      render: (type: string) => {
        return type === 'ROOM'
          ? i18n.t('room_trade')
          : i18n.t('invite_register')
      },
    },
    {
      title: i18n.t('status'),
      dataIndex: 's',
      key: 's',
      render: (status: string) => {
        switch (status) {
          case 'init':
            return i18n.t('not_received')
          case 'receiving':
            return i18n.t('receiving')
          case 'received':
            return i18n.t('received')
          default:
            return status
        }
      },
    },
  ]

  useEffect(() => {
    fetchData(currentPage)
  }, [currentPage])

  // 解析嵌套的API响应数据
  const parseNestedResponse = (response: any) => {
    // 处理可能的嵌套数据结构
    if (response?.data?.data?.records) {
      return {
        records: response.data.data.records,
        last: response.data.data.last,
      }
    }

    // 处理标准数据结构
    return {
      records: response.data?.records || [],
      last: response.data?.last ?? true,
    }
  }

  const fetchData = async (page = currentPage) => {
    try {
      const recordsRes = await SHARE_PERPS_RECORDS({
        page: page - 1,
        size: 10,
      })

      // 使用辅助函数解析嵌套响应
      const { records: recordsData, last } = parseNestedResponse(recordsRes)

      // 设置记录数据
      setRecords(recordsData)

      // 判断是否有更多数据
      setHasMore(!last)

      console.log('合约佣金记录数据:', recordsData)
    } catch (error) {
      console.error('获取数据失败:', error)
      // 确保发生错误时状态有默认值
      setRecords([])
      setHasMore(false)
    }
  }

  const goToNextPage = () => {
    if (hasMore) {
      setCurrentPage((prev) => prev + 1)
    }
  }

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1)
    }
  }

  // 包装 handleReceive 函数
  const handleReceiveWithLoading = async (
    chainId: 'ethereum' | 'bsc' | 'base' | 'solana' | 'hyperliquid',
  ) => {
    try {
      setIsLoading(true)
      await handleReceive(chainId)
    } finally {
      setIsLoading(false)
    }
  }

  // 移动端布局
  if (isMobile) {
    return (
      <Flex vertical className="w-full">
        <Flex className="mb-2 text-base leading-5">
          {i18n.t('unclaimed_commission​​')}{' '}
          {/* <Tooltip
            title={i18n.t('perp_referral_notice')}
            overlayInnerStyle={{
              backgroundColor: '#ffffff',
              color: '#000000',
              borderRadius: '8px',
              padding: '10px',
            }}
            placement="bottom"
            color="#ffffff"
            className="ml-1 size-6 p-0.5"
          >
            <SvgIconQuestionCircle className="size-4 md:size-5" />
          </Tooltip> */}
        </Flex>
        <Flex className={'items-center justify-between'}>
          <div className="flex items-center">
            <div className="mr-8 flex h-6 w-6 items-center justify-center">
              <SvgIconReferralUSDC className="size-6" />
            </div>
            <div className="flex flex-col">
              <span className="text-base font-medium">
                {$ten.toFixed(inverterInfo?.swapAmount || 0, 6, false)} USDC
              </span>
            </div>
          </div>
          {inverterInfo?.swapAmount >= minReceiveAmount ? (
            <button
              className="h-8 w-14 rounded bg-black text-xs font-medium text-white"
              onClick={() => handleReceiveWithLoading('hyperliquid')}
              disabled={isLoading}
            >
              {isLoading ? (
                <Flex align="center" justify="center" gap={2}>
                  <Spin size="small" />
                  {i18n.t('processing')}
                </Flex>
              ) : (
                i18n.t('receive')
              )}
            </button>
          ) : (
            <div className="group relative">
              <button className="h-8 w-14 rounded bg-gray-300 text-xs font-medium text-white">
                {i18n.t('receive')}
              </button>
              <div className="absolute bottom-full right-0 mb-1 hidden max-w-[200px] whitespace-normal rounded bg-black px-3 py-2 text-[12px] text-white before:absolute before:left-1/2 before:top-full before:-translate-x-1/2 before:border-4 before:border-transparent before:border-t-black group-hover:block">
                {i18n.t('min_amount', {
                  symbol: 'USDC',
                  minAmount: minReceiveAmount,
                })}
              </div>
            </div>
          )}
        </Flex>

        {/* 明细 */}
        <Flex className="mt-4 w-full rounded-lg bg-white" vertical>
          <div className="mb-3 ml-5 h-8 text-sm font-medium leading-8 text-black">
            {i18n.t('details')}
          </div>
          <div className="w-full">
            {(records || []).map((record, index) => (
              <Flex
                key={index}
                vertical
                className="mb-4 border-b border-gray-100 pb-3"
              >
                <Flex justify="space-between" className="mb-2">
                  <span className="text-xs text-gray-500">
                    {i18n.t('time')}
                  </span>
                  <span className="text-xs font-medium">
                    {new Date(record.createTime).toLocaleString()}
                  </span>
                </Flex>
                <Flex justify="space-between" className="mb-2">
                  <span className="text-xs text-gray-500">
                    {i18n.t('user')}
                  </span>
                  <span
                    className="cursor-pointer text-xs font-medium hover:text-blue-500 hover:underline"
                    onClick={() => path(`/user/${record.userId}`)}
                  >
                    {record.nickName}
                  </span>
                </Flex>
                <Flex justify="space-between" className="mb-2">
                  <span className="text-xs text-gray-500">
                    {i18n.t('commission')}
                  </span>
                  <span className="text-xs font-medium">
                    {$ten.toFixed(record.amount, 6, false)} USDC
                  </span>
                </Flex>
                <Flex justify="space-between" className="mb-2">
                  <span className="text-xs text-gray-500">
                    {i18n.t('network')}
                  </span>
                  <span className="text-xs font-medium">{record.netWork}</span>
                </Flex>
                <Flex justify="space-between" className="mb-2">
                  <span className="text-xs text-gray-500">
                    {i18n.t('source')}
                  </span>
                  <span className="text-xs font-medium">
                    {record.shareTypeEnum === 'ROOM'
                      ? record.netWork === 'HyperLiquid'
                        ? i18n.t('room_trade')
                        : '合约交易'
                      : i18n.t('invite_register')}
                  </span>
                </Flex>
                <Flex justify="space-between">
                  <span className="text-xs text-gray-500">
                    {i18n.t('status')}
                  </span>
                  <span
                    className={`text-xs font-medium ${record.s === 'receiving' ? 'text-gray-400' : record.s === 'received' ? 'text-green-500' : ''}`}
                  >
                    {record.s === 'init'
                      ? i18n.t('not_received')
                      : record.s === 'receiving'
                        ? 'Pending'
                        : record.s === 'received'
                          ? i18n.t('received')
                          : record.s}
                  </span>
                </Flex>
              </Flex>
            ))}
            {(!records || records.length === 0) && (
              <div className="py-5 text-center text-sm text-gray-500">
                {i18n.t('error_get_data_empty')}
              </div>
            )}
            {/* 调试信息 */}
            <div className="hidden">{JSON.stringify(records)}</div>
          </div>
          <Flex className="mt-4 justify-between">
            <button
              className={`rounded px-3 py-1.5 text-xs font-medium ${
                currentPage > 1
                  ? 'bg-black text-white'
                  : 'bg-gray-300 text-white'
              }`}
              onClick={goToPrevPage}
              disabled={currentPage <= 1}
            >
              {i18n.t('prev_page')}
            </button>
            <button
              className={`rounded px-3 py-1.5 text-xs font-medium ${
                hasMore ? 'bg-black text-white' : 'bg-gray-300 text-white'
              }`}
              onClick={goToNextPage}
              disabled={!hasMore}
            >
              {i18n.t('next_page')}
            </button>
          </Flex>
        </Flex>
      </Flex>
    )
  }

  // PC端布局
  return (
    <>
      <Flex className="w-full rounded-[8px] bg-white p-6" vertical gap={4}>
        <Flex className="text-base font-bold leading-6">
          {i18n.t('unclaimed_commission​​')}
        </Flex>
        <Flex className="h-10 flex-nowrap items-center justify-between rounded-[4px] border-b border-[#F2F3F7]">
          <Flex className="w-auto flex-nowrap items-center gap-2">
            <span className="w-40 text-sm font-bold text-[#5F606D]">
              {i18n.t('network')}
            </span>
            <span className="ml-28 w-40 text-sm font-bold text-[#5F606D]">
              {i18n.t('commission')}
            </span>
          </Flex>
          {/* <Flex className="items-center gap-4">
            <div className="group relative">
              <Tooltip
                title={i18n.t('perp_referral_notice')}
                overlayInnerStyle={{
                  backgroundColor: '#ffffff',
                  color: '#000000',
                  fontWeight: 'bold',
                  borderRadius: '8px',
                  padding: '10px',
                }}
                placement="bottomRight"
                color="#ffffff"
                className="float-right"
              >
                <SvgIconQuestionCircle className="size-4 md:size-5" />
              </Tooltip>
            </div>
          </Flex> */}
        </Flex>
        <Flex className="h-10 flex-nowrap items-center justify-between rounded-[4px]">
          <Flex className="w-auto flex-nowrap items-center gap-2">
            <span className="w-40 text-sm font-bold text-[#1A1B1E]">
              HyperLiquid
            </span>
            <span className="ml-28 text-sm font-bold text-[#1A1B1E]">
              {$ten.toFixed(inverterInfo?.swapAmount || 0, 6, false)} USDC
            </span>
          </Flex>
          <Flex className="items-center gap-4">
            <div className="group relative">
              <button
                className={`h-7 rounded-[4px] px-4 text-sm font-bold ${
                  !inverterInfo?.swapAmount ||
                  inverterInfo?.swapAmount < minReceiveAmount
                    ? 'bg-[#9293A0] text-[#F9FAFB]'
                    : 'bg-[#1A1B1E] text-white'
                }`}
                onClick={() => handleReceiveWithLoading('hyperliquid')}
                disabled={
                  isLoading ||
                  !inverterInfo?.swapAmount ||
                  inverterInfo?.swapAmount < minReceiveAmount
                }
              >
                {isLoading ? (
                  <Flex align="center" justify="center" gap={2}>
                    <Spin size="small" />
                    {i18n.t('processing')}
                  </Flex>
                ) : (
                  i18n.t('receive')
                )}
              </button>
              {(!inverterInfo?.swapAmount ||
                inverterInfo?.swapAmount < minReceiveAmount) && (
                <div className="absolute bottom-full left-1/2 mb-1 hidden -translate-x-1/2 whitespace-nowrap rounded bg-black px-3 py-2 text-[12px] text-white before:absolute before:left-1/2 before:top-full before:-translate-x-1/2 before:border-4 before:border-transparent before:border-t-black group-hover:block">
                  {i18n.t('min_amount', {
                    symbol: 'USDC',
                    minAmount: minReceiveAmount,
                  })}
                </div>
              )}
            </div>
          </Flex>
        </Flex>
      </Flex>

      <Flex className="mt-6 w-full rounded-[8px] bg-white p-6" vertical gap={4}>
        <div className="mb-4 text-[16px] font-normal leading-[24px] text-[#1A1B1E]">
          {i18n.t('commission_details')}
        </div>
        <div className={`${styles.referralTable} w-full overflow-x-auto`}>
          <Table
            dataSource={records}
            columns={desktopColumns}
            pagination={false}
            rowKey="id"
            className="w-full"
            locale={{ emptyText: i18n.t('error_get_data_empty') }}
          />
        </div>
        <Flex className="mt-4 justify-between">
          <button
            className={`rounded-[4px] px-4 py-2 text-sm font-bold ${
              currentPage > 1
                ? 'bg-[#1A1B1E] text-white'
                : 'bg-[#9293A0] text-[#F9FAFB]'
            }`}
            onClick={goToPrevPage}
            disabled={currentPage <= 1}
          >
            {i18n.t('prev_page')}
          </button>
          <button
            className={`rounded-[4px] px-4 py-2 text-sm font-bold ${
              hasMore
                ? 'bg-[#1A1B1E] text-white'
                : 'bg-[#9293A0] text-[#F9FAFB]'
            }`}
            onClick={goToNextPage}
            disabled={!hasMore}
          >
            {i18n.t('next_page')}
          </button>
        </Flex>
      </Flex>
    </>
  )
}

export { PerpsReferralModal }
export default PerpsReferralModal
