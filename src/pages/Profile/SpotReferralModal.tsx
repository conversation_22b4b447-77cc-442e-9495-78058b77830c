import { Flex, Table } from 'antd'
import {
  SvgIconReferralEthereum,
  SvgIconReferralBase,
  SvgIconReferralBsc,
  SvgIconReferralSolana,
  SvgIconModalClose,
} from '@/imgs/icons'
import i18n from '@/i18n'
import { useDevice } from '@/hooks/useDevice'
import { useState, useEffect } from 'react'
import { SHARE_RECORDS } from '@/api/interface/SHARE_RECORDS'
import type { ColumnsType } from 'antd/es/table'
import styles from '@/pages/Profile/css/profile.module.css'
import { useRoute } from '@/hooks/useRoute'
import { $ten } from '@/utils/_calculate'

interface SpotReferralModalProps {
  inverterInfo: any
  settings: any
  handleReceive: (chainId: 'ethereum' | 'bsc' | 'base' | 'solana') => void
  getMinAmount: (network: string, settings: any) => number
  getTooltipText: (network: string, minAmount: number) => string
}

export function SpotReferralModal({
  inverterInfo,
  settings,
  handleReceive,
  getMinAmount,
  getTooltipText,
}: SpotReferralModalProps) {
  const { isMobile } = useDevice()
  const { path } = useRoute()
  const [records, setRecords] = useState<any[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  // 桌面端表格列配置
  const desktopColumns: ColumnsType<any> = [
    {
      title: i18n.t('time'),
      dataIndex: 'createTime',
      key: 'createTime',
      render: (time: number) => {
        const date = new Date(time)
        const formatNumber = (num: number) => String(num).padStart(2, '0')

        const year = date.getFullYear()
        const month = formatNumber(date.getMonth() + 1)
        const day = formatNumber(date.getDate())
        const hours = formatNumber(date.getHours())
        const minutes = formatNumber(date.getMinutes())
        const seconds = formatNumber(date.getSeconds())

        return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`
      },
    },
    {
      title: i18n.t('user'),
      dataIndex: 'nickName',
      key: 'nickName',
      render: (nickName: string, record: any) => (
        <span
          className="cursor-pointer text-[#1A1B1E] hover:text-blue-500 hover:underline"
          onClick={() => path(`/user/${record.userId}`)}
        >
          {nickName}
        </span>
      ),
    },
    {
      title: i18n.t('commission'),
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number, record: any) => {
        const formattedAmount = $ten.toFixed(amount, 6, false)
        return `${formattedAmount} ${record.netWork === 'Solana' ? 'SOL' : record.netWork === 'Binance Smart Chain' ? 'BNB' : 'ETH'}`
      },
    },
    {
      title: i18n.t('network'),
      dataIndex: 'netWork',
      key: 'netWork',
    },
    {
      title: i18n.t('source'),
      dataIndex: 'shareTypeEnum',
      key: 'shareTypeEnum',
      render: (type: string) => {
        return type === 'ROOM'
          ? i18n.t('room_trade')
          : i18n.t('invite_register')
      },
    },
    {
      title: i18n.t('status'),
      dataIndex: 's',
      key: 's',
      render: (status: string) => {
        switch (status) {
          case 'init':
            return i18n.t('not_received')
          case 'receiving':
            return i18n.t('receiving')
          case 'received':
            return i18n.t('received')
          default:
            return status
        }
      },
    },
  ]

  useEffect(() => {
    fetchData(currentPage)
  }, [currentPage])

  const fetchData = async (page = currentPage) => {
    try {
      const recordsRes = await SHARE_RECORDS({
        page: page - 1,
        size: 10,
      })

      setRecords(recordsRes.data?.records || [])
      setHasMore(!recordsRes.data?.last)
    } catch (error) {
      console.error('获取数据失败:', error)
      // 确保发生错误时状态有默认值
      setRecords([])
      setHasMore(false)
    }
  }

  const goToNextPage = () => {
    if (hasMore) {
      setCurrentPage((prev) => prev + 1)
    }
  }

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1)
    }
  }

  // 移动端布局
  if (isMobile) {
    return (
      <Flex vertical className="w-full">
        <Flex vertical>
          {(inverterInfo?.receiveList || []).map((item: any, index: number) => {
            const minAmount = getMinAmount(item.netWork, settings)
            const isDisabled = item.receive == 0 || item.amount < minAmount

            return (
              <Flex
                key={index}
                className={`items-center justify-between ${
                  index < (inverterInfo?.receiveList?.length || 0) - 1
                    ? 'mb-3 border-b border-gray-100 pb-3'
                    : ''
                }`}
              >
                <div className="flex items-center">
                  <div className="mr-8 flex h-6 w-6 items-center justify-center">
                    {item.symbol === 'SOL' ? (
                      <SvgIconReferralSolana className="size-6" />
                    ) : item.symbol === 'BNB' ? (
                      <SvgIconReferralBsc className="size-6" />
                    ) : item.netWork === 'Base' ? (
                      <SvgIconReferralBase className="size-6" />
                    ) : (
                      <SvgIconReferralEthereum className="size-6" />
                    )}
                  </div>
                  <div className="flex flex-col">
                    <span className="text-base font-medium">
                      {$ten.toFixed(item.amount, 6, false)} {item.symbol}
                    </span>
                    {/* <div className="text-xs text-gray-500">
                        {item.netWork}
                      </div> */}
                  </div>
                </div>
                {!isDisabled ? (
                  <button
                    className="h-8 w-14 rounded bg-black text-xs font-medium text-white"
                    onClick={() => handleReceive(item.netWork.toLowerCase())}
                  >
                    {i18n.t('receive')}
                  </button>
                ) : item.receive === 'receiving' ? (
                  <button className="h-8 w-14 rounded bg-gray-300 text-xs font-medium text-white">
                    {i18n.t('receiving')}
                  </button>
                ) : (
                  <div className="group relative">
                    <button className="h-8 w-14 rounded bg-gray-300 text-xs font-medium text-white">
                      {i18n.t('receive')}
                    </button>
                    {item.amount < minAmount && (
                      <div className="absolute bottom-full right-0 mb-1 hidden max-w-[200px] whitespace-normal rounded bg-black px-3 py-2 text-[12px] text-white before:absolute before:left-1/2 before:top-full before:-translate-x-1/2 before:border-4 before:border-transparent before:border-t-black group-hover:block">
                        {getTooltipText(item.netWork, minAmount)}
                      </div>
                    )}
                  </div>
                )}
              </Flex>
            )
          })}
        </Flex>

        {/* 明细 */}
        <Flex className="mt-4 w-full rounded-lg bg-white" vertical>
          <div className="mb-3 text-sm font-medium text-black">
            {i18n.t('details')}
          </div>
          <div className="w-full">
            {(records || []).map((record, index) => (
              <Flex
                key={index}
                vertical
                className="mb-4 border-b border-gray-100 pb-3"
              >
                <Flex justify="space-between" className="mb-2">
                  <span className="text-xs text-gray-500">
                    {i18n.t('time')}
                  </span>
                  <span className="text-xs font-medium">
                    {new Date(record.createTime).toLocaleString()}
                  </span>
                </Flex>
                <Flex justify="space-between" className="mb-2">
                  <span className="text-xs text-gray-500">
                    {i18n.t('user')}
                  </span>
                  <span
                    className="cursor-pointer text-xs font-medium hover:text-blue-500 hover:underline"
                    onClick={() => path(`/user/${record.userId}`)}
                  >
                    {record.nickName}
                  </span>
                </Flex>
                <Flex justify="space-between" className="mb-2">
                  <span className="text-xs text-gray-500">
                    {i18n.t('commission')}
                  </span>
                  <span className="text-xs font-medium">{`${Number(record.amount).toFixed(6)} ${record.netWork === 'Solana' ? 'SOL' : record.netWork === 'Binance Smart Chain' ? 'BNB' : 'ETH'}`}</span>
                </Flex>
                <Flex justify="space-between" className="mb-2">
                  <span className="text-xs text-gray-500">
                    {i18n.t('network')}
                  </span>
                  <span className="text-xs font-medium">{record.netWork}</span>
                </Flex>
                <Flex justify="space-between" className="mb-2">
                  <span className="text-xs text-gray-500">
                    {i18n.t('source')}
                  </span>
                  <span className="text-xs font-medium">
                    {record.shareTypeEnum === 'ROOM'
                      ? i18n.t('room_trade')
                      : i18n.t('invite_register')}
                  </span>
                </Flex>
                <Flex justify="space-between">
                  <span className="text-xs text-gray-500">
                    {i18n.t('status')}
                  </span>
                  <span
                    className={`text-xs font-medium ${record.s === 'receiving' ? 'text-gray-400' : record.s === 'received' ? 'text-green-500' : ''}`}
                  >
                    {record.s === 'init'
                      ? i18n.t('not_received')
                      : record.s === 'receiving'
                        ? 'Pending'
                        : record.s === 'received'
                          ? i18n.t('received')
                          : record.s}
                  </span>
                </Flex>
              </Flex>
            ))}
            {(!records || records.length === 0) && (
              <div className="py-5 text-center text-sm text-gray-500">
                {i18n.t('error_get_data_empty')}
              </div>
            )}
          </div>
          <Flex className="mt-4 justify-between">
            <button
              className={`rounded px-3 py-1.5 text-xs font-medium ${
                currentPage > 1
                  ? 'bg-black text-white'
                  : 'bg-gray-300 text-white'
              }`}
              onClick={goToPrevPage}
              disabled={currentPage <= 1}
            >
              {i18n.t('prev_page')}
            </button>
            <button
              className={`rounded px-3 py-1.5 text-xs font-medium ${
                hasMore ? 'bg-black text-white' : 'bg-gray-300 text-white'
              }`}
              onClick={goToNextPage}
              disabled={!hasMore}
            >
              {i18n.t('next_page')}
            </button>
          </Flex>
        </Flex>
      </Flex>
    )
  }

  // PC端布局
  return (
    <>
      <Flex className="w-full rounded-[8px] bg-white p-6" vertical gap={4}>
        <Flex className="text-base font-bold leading-6">
          {i18n.t('unclaimed_commission​​')}
        </Flex>
        <Flex className="h-10 flex-nowrap items-center justify-between rounded-[4px] border-b border-[#F2F3F7]">
          <Flex className="w-auto flex-nowrap items-center gap-2">
            <span className="w-40 text-sm font-bold text-[#5F606D]">
              {i18n.t('network')}
            </span>
            <span className="ml-28 w-40 text-sm font-bold text-[#5F606D]">
              {i18n.t('commission')}
            </span>
          </Flex>
        </Flex>
        {inverterInfo?.receiveList?.map((item: any) => {
          const minAmount = getMinAmount(item.netWork, settings)
          const isDisabled = item.receive == 0 || item.amount < minAmount
          const tooltipText = getTooltipText(item.netWork, minAmount)

          return (
            <Flex
              key={item.netWork}
              className="h-10 flex-nowrap items-center justify-between rounded-[4px] border-b border-[#F2F3F7]"
            >
              <Flex className="w-auto flex-nowrap items-center gap-2">
                <span className="w-40 text-sm font-bold text-[#1A1B1E]">
                  {item.netWork}
                </span>
                <span className="ml-28 text-sm font-bold text-[#1A1B1E]">
                  {Number(item.amount).toFixed(6)} {item.symbol}
                </span>
              </Flex>
              <Flex className="items-center gap-4">
                <div className="group relative">
                  <button
                    className={`h-7 rounded-[4px] px-4 text-sm font-bold ${
                      isDisabled
                        ? 'bg-[#9293A0] text-[#F9FAFB]'
                        : 'bg-[#1A1B1E] text-white'
                    }`}
                    onClick={() => handleReceive(item.netWork.toLowerCase())}
                    disabled={isDisabled}
                  >
                    {i18n.t('receive')}
                  </button>
                  {item.amount < minAmount && (
                    <div className="absolute bottom-full left-1/2 mb-1 hidden -translate-x-1/2 whitespace-nowrap rounded bg-black px-3 py-2 text-[12px] text-white before:absolute before:left-1/2 before:top-full before:-translate-x-1/2 before:border-4 before:border-transparent before:border-t-black group-hover:block">
                      {tooltipText}
                    </div>
                  )}
                </div>
              </Flex>
            </Flex>
          )
        })}
      </Flex>

      <Flex className="mt-6 w-full rounded-[8px] bg-white p-6" vertical gap={4}>
        <div className="mb-4 text-base font-bold text-[#1A1B1E]">
          {i18n.t('commission_details')}
        </div>
        <div className={`${styles.referralTable} w-full overflow-x-auto`}>
          <Table
            dataSource={records}
            columns={desktopColumns}
            pagination={false}
            rowKey="id"
            className="w-full"
            locale={{ emptyText: i18n.t('error_get_data_empty') }}
          />
        </div>
        <Flex className="mt-4 justify-between">
          <button
            className={`rounded-[4px] px-4 py-2 text-sm font-bold ${
              currentPage > 1
                ? 'bg-[#1A1B1E] text-white'
                : 'bg-[#9293A0] text-[#F9FAFB]'
            }`}
            onClick={goToPrevPage}
            disabled={currentPage <= 1}
          >
            {i18n.t('prev_page')}
          </button>
          <button
            className={`rounded-[4px] px-4 py-2 text-sm font-bold ${
              hasMore
                ? 'bg-[#1A1B1E] text-white'
                : 'bg-[#9293A0] text-[#F9FAFB]'
            }`}
            onClick={goToNextPage}
            disabled={!hasMore}
          >
            {i18n.t('next_page')}
          </button>
        </Flex>
      </Flex>
    </>
  )
}

export default SpotReferralModal
