import {
  IconHomeRoomLocked,
  IconHomeOpening,
  IconRoomFire,
  IconHomeOnlineUsers,
  SvgIconPerpsHome,
  SvgIconSpotHome,
} from '@/imgs/icons'
import RoomTag from '@/pages/ChatRoom/module/RoomTag'
import { Flex } from 'antd'
import { observer } from 'mobx-react-lite'
import { ModHomeTabSwitchType } from '@/pages/Home/module/type.home'
import SetPassword from '@/pages/ChatRoom/module/SetPassword'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { useRoute } from '@/hooks/useRoute'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { useEffect, useState } from 'react'
import { CHAT_ROOM_LIST_DATA } from '@/api/interface/CHAT_ROOM_LIST_ALL'
import { ChatroomErrorCode, chatroomManager } from '@/managers/chatroomManager'
import clsx from 'clsx'
import { userStore } from '@/store'
import { useAuthLogin } from '@/hooks/useAuthLogin'
import HomeMutiModal from './HomeMutiModal'
import i18n from '@/i18n'
import { HlAvatar } from '@/pages/Perp/module/utils'

export const HomeCard = observer(
  ({
    item,
    curTab,
    className,
    onUpdateRoomPwdStatus,
    onRoomClosed,
  }: {
    item: any
    type: 'SEARCH' | 'HOME'
    className?: string
    onUpdateRoomPwdStatus?: (roomId: string, isLocked?: boolean) => void
    onRoomClosed?: (roomId: string) => void
    curTab?: ModHomeTabSwitchType
  }) => {
    const store = useStore()
    const { path } = useRoute()
    const { toastErr } = useToastMessage()
    const [passwordIsOpen, setPasswordIsOpen] = useState(false)
    const [mutiModalClientOpen, setMutiModalClientOpen] = useState(false)
    const [selectedItem, setSelectedItem] =
      useState<CHAT_ROOM_LIST_DATA | null>()
    // 添加一个状态来跟踪是否正在加入房间
    const [isJoining, setIsJoining] = useState(false)

    const { login } = useAuthLogin()

    const handlePwdConfrim = async (password: string[]) => {
      if (password && password.length === 4) {
        if (selectedItem && selectedItem.roomId) {
          store
            .joinRoom(selectedItem.roomId, password.join(''), true)
            .then((res) => {
              if (res.code === ChatroomErrorCode.PK_MUTI_CLIENT_IN_ROOM) {
                setMutiModalClientOpen(true)
              } else {
                path(`/chatroom?roomId=${selectedItem.roomId}`)
                handlePwdCancel()
              }
            })
            .catch((error) => {
              if (
                error &&
                error.code === ChatroomErrorCode.RC_CHATROOM_USER_KICKED
              ) {
                toastErr(
                  i18n.t('you_have_been_blocked_and_cannot_join_the_room'),
                )
                return
              }
              if (
                error &&
                error.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST
              ) {
                toastErr(i18n.t('this_room_is_closed'))
                return
              }
              if (
                error &&
                error.code === ChatroomErrorCode.PK_PASSWORD_IS_INCORRECT
              ) {
                toastErr(i18n.t('password_incorrect'))
                return
              }
              toastErr(`${error.message}(${error.code})`)
            })
        }
      }
    }

    const handlePwdCancel = () => {
      onUpdateRoomPwdStatus?.(item.roomId!, passwordIsOpen)
      setSelectedItem(null)
      setPasswordIsOpen(false)
    }

    const handleJoinRoom = () => {
      // 如果已经在加入房间过程中，则不再重复执行
      if (isJoining) return

      setIsJoining(true)
      chatroomManager
        .fetchRoomDetail(item.roomId!, '', false)
        .then((res) => {
          if (res?.code === ChatroomErrorCode.PK_SUCCESS) {
            store
              .joinRoom(item.roomId!, '', true)
              .then((res) => {
                if (res.code === ChatroomErrorCode.PK_MUTI_CLIENT_IN_ROOM) {
                  setMutiModalClientOpen(true)
                } else {
                  path(`/chatroom?roomId=${item.roomId}`)
                }
              })
              .catch((error) => {
                if (
                  error &&
                  error.code === ChatroomErrorCode.RC_CHATROOM_USER_KICKED
                ) {
                  toastErr(
                    i18n.t('you_have_been_blocked_and_cannot_join_the_room'),
                  )
                  return
                }
                if (
                  error &&
                  error.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST
                ) {
                  toastErr(i18n.t('this_room_is_closed'))
                  return
                }
                if (
                  error &&
                  error.code === ChatroomErrorCode.PK_PASSWORD_IS_INCORRECT
                ) {
                  toastErr(i18n.t('password_incorrect'))
                  return
                }
                toastErr(`${error.message}(${error.code})`)
              })
          } else {
            if (res?.code === ChatroomErrorCode.PK_PASSWORD_IS_INCORRECT) {
              setPasswordIsOpen(true)
              return
            } else if (
              res?.code === ChatroomErrorCode.RC_CHATROOM_USER_KICKED
            ) {
              toastErr(i18n.t('you_have_been_blocked_and_cannot_join_the_room'))
              return
            } else if (res?.code === ChatroomErrorCode.RC_CHATROOM_NOT_EXIST) {
              toastErr(i18n.t('this_room_is_closed'))
              return
            } else if (res?.code === ChatroomErrorCode.PK_ROOM_CLOSED) {
              toastErr(i18n.t('this_room_is_closed'))
              onRoomClosed?.(item.roomId!)
              return
            } else {
              toastErr(`${res?.message}(${res?.code})`)
            }
          }
        })
        .catch((err) => {
          toastErr(`${err}`)
          setIsJoining(false) // 重置加入状态
        })
    }

    const handleItemClicked = (item: CHAT_ROOM_LIST_DATA) => {
      // 如果已经在加入房间过程中，则不再重复执行
      if (isJoining) return

      // 检查用户是否登录
      if (!userStore.IS_LOGIN()) {
        login()
        return
      }

      setSelectedItem(item)
      if (item.isPw === true && item.roomId) {
        // 有小窗
        if (store.isFloating) {
          if (item.roomId === store.roomId) {
            path(`/chatroom?roomId=${store.roomDetial.roomId}`)
          } else {
            handleJoinRoom()
          }
        } else {
          // 无小窗
          handleJoinRoom()
        }
      } else {
        // 缓存无密码
        if (item.roomId) {
          // 先拉详情，确实无密码
          handleJoinRoom()
        }
      }
    }

    const handleMutiModalClientClose = () => {
      setMutiModalClientOpen(false)
    }

    const handleMutiModalClientConfirm = async () => {
      const resultKickOutOtherClient =
        await chatroomManager.pkKickOutOtherClient(store.roomId)
      if (resultKickOutOtherClient.code === ChatroomErrorCode.PK_SUCCESS) {
        chatroomManager
          .fetchRoomDetailByUser()
          .then((res) => {
            if (res && res.code === 200) {
              const roomDetail = res.data
              const { s, roomId } = roomDetail
              if (roomId && s === 1) {
                store
                  .joinRoom(roomId, '', false)
                  .then((res) => {
                    if (res.code === ChatroomErrorCode.PK_MUTI_CLIENT_IN_ROOM) {
                      setMutiModalClientOpen(true)
                    } else {
                      path(`/chatroom?roomId=${roomId}`)
                    }
                  })
                  .catch((error) => {
                    toastErr(`${error.message}(${error.code})`)
                  })
                return
              }
            } else {
              toastErr('Try again later')
            }
          })
          .catch((error) => {
            console.error(`fetchRoomDetailByUser: ${error}`)
          })
      }
    }

    const handleMutiModalClientCancel = () => {
      setMutiModalClientOpen(false)
    }

    return (
      <div
        className={clsx(
          `cursor-pointer overflow-hidden rounded border-transparent bg-white font-lexend transition-transform duration-300 hover:-translate-y-1 hover:border-4 hover:border-black hover:shadow-lg`,
          'md:h-[310px]',
          'w-full',
          'mx-0',
          className,
        )}
      >
        <div className="h-full w-full" onClick={() => handleItemClicked(item)}>
          <Flex className="relative h-0 pb-[56.25%]">
            <RoomTags item={item} curTab={curTab} />
            <img
              className="absolute h-full w-full object-cover"
              src={item.roomPic}
              alt="room background"
            />
            {item.isPw && (
              <div className="absolute inset-0 flex items-center justify-center">
                <IconHomeRoomLocked className="h-[80px] w-[80px] md:h-[110px] md:w-[110px]" />
              </div>
            )}
          </Flex>
          <Flex className="p-1 px-3 md:p-2 md:px-5">
            <div className="line-clamp-3 min-h-7 font-lexend text-[16px] font-bold md:line-clamp-1 md:min-h-9 md:text-[18px]">
              {item.roomTitle}
            </div>
          </Flex>
          <Flex className="mt-1 h-[16px] w-full items-center justify-between px-3 pb-4 pt-1 md:h-[20px] md:px-5 md:pb-1">
            <Flex className="items-center space-x-1">
              <IconRoomFire className="h-[10px] w-[10px] md:h-[12px] md:w-[12px]" />
              <div className="font-lexend text-[12px] font-bold text-[#9293A0] md:text-[14px]">
                {item.hot ?? 0}
              </div>
            </Flex>
            <Flex className="items-center space-x-1">
              <IconHomeOnlineUsers className="h-[10px] w-[10px] md:h-[12px] md:w-[12px]" />
              <div className="font-lexend text-[12px] font-bold text-[#9293A0] md:text-[14px]">
                {item.num ?? 0}
              </div>
            </Flex>
          </Flex>
        </div>

        <SetPassword
          open={passwordIsOpen}
          onCancel={handlePwdCancel}
          onConfirm={handlePwdConfrim}
        />

        <HomeMutiModal
          open={mutiModalClientOpen}
          onClose={handleMutiModalClientClose}
          onCancel={handleMutiModalClientCancel}
          onConfirm={handleMutiModalClientConfirm}
        />
      </div>
    )
  },
)

function RoomTags({
  item,
  curTab,
}: {
  item: CHAT_ROOM_LIST_DATA
  curTab?: ModHomeTabSwitchType
}) {
  const { tagType, tagContent, tagIcon } = item

  // 合约
  if (tagType === 4 && tagContent) {
    return (
      <Flex className="absolute left-2 top-2 z-10 font-lexend text-sm font-black text-black md:text-base">
        <div className="flex items-center gap-2">
          <div className="inline-flex items-center justify-end gap-0.5 rounded bg-gradient-to-r from-yellow-200 to-orange-200 p-1">
            <SvgIconPerpsHome className="size-2.5" />
            <div className="justify-start text-xs font-bold text-zinc-900">
              {i18n.t('contract_trade')}
            </div>
          </div>
          <div className="inline-flex items-center justify-end gap-0.5 rounded bg-white/50 p-1">
            <HlAvatar
              coin={tagContent}
              className="size-3 rounded-full border border-black"
            />
            <div className="justify-start text-xs font-bold text-zinc-900">
              {tagContent}
            </div>
          </div>
        </div>
      </Flex>
    )
  }

  // 现货
  if (tagType === 1 && tagContent) {
    return (
      <Flex className="absolute left-2 top-2 z-10 font-lexend text-sm font-black text-black md:text-base">
        <div className="flex items-center gap-2">
          <div className="inline-flex items-center justify-end gap-0.5 rounded bg-gradient-to-r from-yellow-200 to-orange-200 p-1">
            <SvgIconSpotHome className="size-2.5" />
            <div className="justify-start text-xs font-bold text-zinc-900">
              {i18n.t('spot_trade')}
            </div>
          </div>
          <div className="inline-flex items-center justify-end gap-0.5 rounded bg-white/50 p-1">
            {tagIcon ? (
              <img
                className="size-3 rounded-full border border-black"
                src={tagIcon}
                alt="token icon"
              />
            ) : (
              <div className="flex size-3 items-center justify-center overflow-hidden rounded-full border border-black text-[8px]">
                {tagContent?.charAt(0).toUpperCase()}
              </div>
            )}
            <div className="justify-start text-xs font-bold text-zinc-900">
              {tagContent}
            </div>
          </div>
        </div>
      </Flex>
    )
  }

  return (
    <Flex className="absolute left-2 top-2 z-10 font-lexend text-sm font-black text-black md:text-base">
      <div className="flex items-center gap-2">
        {curTab === ModHomeTabSwitchType.COLLECT && item.s === 1 ? (
          <IconHomeOpening className="mr-1 h-[10px] w-[14px] md:mr-2 md:h-[12px] md:w-[16px]" />
        ) : null}
        <RoomTag type={item.tagType?.toString() ?? '3'} />
      </div>
    </Flex>
  )
}
