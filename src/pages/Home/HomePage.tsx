import {
  CHAT_ROOM_LIST_ALL,
  CHAT_ROOM_LIST_DATA,
} from '@/api/interface/CHAT_ROOM_LIST_ALL'
import { useRoute } from '@/hooks/useRoute'
import { Flex } from 'antd'
import { useLayoutEffect, useState, useEffect } from 'react'
import { userStore } from '@/store'
import InfiniteS<PERSON>roll from 'react-infinite-scroll-component'
import { lodash } from '@/utils'
import { CHAT_ROOM_LIST_FAVORITE } from '@/api/interface/CHAT_ROOM_LIST_FAVORITE'
import { useSearchParams } from 'react-router-dom'
import { HomeHeader } from '@/pages/Home/module/HomeHeader'
import { HomeCard } from '@/pages/Home/module/HomeCard'
import { ModHomeTabSwitchType } from '@/pages/Home/module/type.home'
import { StorageUtils } from '@/utils/STORAGE_REG'
import i18n from '@/i18n'
import { useAuthLogin } from '@/hooks/useAuthLogin'
import { CannyModal } from '@/components/CannyModal'

export function Component() {
  const { query } = useRoute()
  const [loading, setLoading] = useState(false)
  const [allHasMore, setAllHasMore] = useState(true)
  const [collectHasMore, setCollectHasMore] = useState(true)
  const [curTab, setCurTab] = useState<ModHomeTabSwitchType>(
    query.tab || ModHomeTabSwitchType.ALL,
  )
  const [cacheAllData, setCacheAllData] = useState<CHAT_ROOM_LIST_DATA[]>([])
  const [cacheCollectData, setCacheCollectData] = useState<
    CHAT_ROOM_LIST_DATA[]
  >([])

  const { path } = useRoute()
  const [searchParams] = useSearchParams()

  const { login } = useAuthLogin()

  useEffect(() => {
    if (!userStore.IS_LOGIN()) {
      login()
    }
  }, [])

  useLayoutEffect(() => {
    const cid = searchParams.get('cid')
    if (cid) StorageUtils.setReferralCid(cid)
  }, [searchParams])

  const loadMoreAllData = async () => {
    if (loading) {
      return
    }
    setLoading(true)
    try {
      const res = await CHAT_ROOM_LIST_ALL({
        page: 1,
        size: 1000,
      })
      setLoading(false)
      if (res?.code !== 200) return
      // 获取新拉取的数据
      const newRecords = res.data.records
      const mergedRecords = lodash.uniqBy(
        [...cacheAllData, ...newRecords],
        'id',
      )
      // 更新组件状态，显示最新数据
      setCacheAllData(mergedRecords)
      setAllHasMore(res.data.last !== true)
    } catch {
      setLoading(false)
    }
  }

  const loadMoreCollectData = async () => {
    if (loading) {
      return
    }
    setLoading(true)
    try {
      const res = await CHAT_ROOM_LIST_FAVORITE({
        page: 1,
        size: 1000,
      })
      setLoading(false)
      if (res?.code !== 200) return
      // 获取新拉取的数据
      const newRecords = res.data
      const mergedRecords = lodash.uniqBy(
        [...cacheCollectData, ...newRecords],
        'id',
      )
      // 更新组件状态，显示最新数据
      setCacheCollectData(mergedRecords)
      setCollectHasMore(res.data.length === 1000)
    } catch {
      setLoading(false)
    }
  }

  const handleTabChanged = (tab: ModHomeTabSwitchType) => {
    setCurTab(tab)
    // 根据当前的选项卡加载对应数据
    if (tab === ModHomeTabSwitchType.ALL) {
      loadMoreAllData()
    } else if (tab === ModHomeTabSwitchType.COLLECT) {
      loadMoreCollectData()
    }
  }

  const handleUpdateRoomPwdStatus = (roomId: string, isLocked?: boolean) => {
    if (curTab === ModHomeTabSwitchType.COLLECT) {
      setCacheCollectData((prevData) =>
        prevData.map((item) =>
          item.roomId === roomId ? { ...item, isPw: isLocked } : item,
        ),
      )
    } else {
      setCacheAllData((prevData) =>
        prevData.map((item) =>
          item.roomId === roomId ? { ...item, isPw: isLocked } : item,
        ),
      )
    }
  }

  const handleRoomClosed = (roomId: string) => {
    if (curTab === ModHomeTabSwitchType.COLLECT) {
      // 收藏列表无需移除
      // setCacheCollectData((prevData) =>
      //   prevData.filter((item) => item.roomId !== roomId),
      // )
    } else {
      setCacheAllData((prevData) =>
        prevData.filter((item) => item.roomId !== roomId),
      )
    }
  }

  const BuildList = ({ curTab }: { curTab: ModHomeTabSwitchType }) => {
    // 根据 curTab 设置对应的数据、加载方法和是否还有更多数据
    const data =
      curTab === ModHomeTabSwitchType.COLLECT ? cacheCollectData : cacheAllData
    const loadMoreData =
      curTab === 'Collect' ? loadMoreCollectData : loadMoreAllData
    const hasMore = curTab === 'Collect' ? collectHasMore : allHasMore

    // 如果数据为空且没有更多数据，则显示空状态
    if (!data?.length && !hasMore) {
      return (
        <Flex className="items-center justify-center py-10 font-lexend text-gray-400">
          {i18n.t('no_data')}
        </Flex>
      )
    }

    return (
      <Flex key={curTab} className="w-full" vertical>
        <InfiniteScroll
          dataLength={data.length}
          next={loadMoreData}
          hasMore={hasMore}
          loader={
            <Flex className="items-center justify-center">
              <div className="pt-2 font-lexend text-gray-400">
                {i18n.t('loading')}
              </div>
            </Flex>
          }
          scrollableTarget="scrollableDiv"
          className="h-full w-full overflow-y-auto overflow-x-hidden no-scroll"
        >
          <div className="mb-20 grid h-full w-full grid-cols-1 gap-4 px-3 py-3 pb-5 sm:grid-cols-2 md:mb-0 md:grid-cols-3 md:gap-5 md:px-0 md:py-5">
            {data.map((item) => (
              <HomeCard
                key={item.id}
                item={item}
                curTab={curTab}
                type="HOME"
                onUpdateRoomPwdStatus={handleUpdateRoomPwdStatus}
                onRoomClosed={handleRoomClosed}
              />
            ))}
          </div>
        </InfiniteScroll>
      </Flex>
    )
  }

  const handleSearch = (value: string) => {
    if (value) {
      path(`/search?keyword=${value}`)
    }
  }

  return (
    <div className="h-screen bg-[#F2F3F7]">
      <HomeHeader
        bgColor
        defaultValue={curTab}
        onSwitchChange={handleTabChanged}
        onSearch={handleSearch}
      />
      {/* 包裹列表区域的可滚动容器，使用动态视口高度，并添加安全区域边距 */}
      <Flex
        id="scrollableDiv"
        className="w-full items-start overflow-y-auto overflow-x-hidden no-scroll"
        style={{
          height: 'calc(100dvh - 78px)',
          paddingBottom: 'env(safe-area-inset-bottom)',
          paddingTop: 'env(safe-area-inset-top)',
        }}
      >
        <div className="mx-auto w-full md:w-default">
          <BuildList curTab={curTab} />
        </div>
      </Flex>

      {/* canny 弹窗管理器 */}
      <CannyModal />
    </div>
  )
}
Component.displayName = 'HomePage'
export default Component
