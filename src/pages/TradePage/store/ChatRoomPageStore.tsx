import { FRIENDS } from '@/api/interface/FRIENDS'
import { GET_CHATROOM_USER_INFO } from '@/api/interface/GET_CHATROOM_USER_INFO'
import { FOLLOWING } from '@/api/interface/PROFILE_FOLLOWING'
import { ROOM_DETIAL } from '@/api/interface/QUERY_ROOM_DETIAL_BY_ID'
import { USER_INFO_RETURN } from '@/api/interface/QUERY_USERS_INFO'
import { IS_ENV_MAIN, IS_OPEN_ROOM_LOG } from '@/config'
import { kChatRoomMessage, PKMsgExtraMsgTypeKey } from '@/defines/defines'
import i18n from '@/i18n'
import {
  ChatroomCommonPushKey,
  ChatroomErrorCode,
  chatroomManager,
  ChatroomManagerEvents,
  MicPosIndex,
  Mode,
  RongIMLib,
  RoomRole,
} from '@/managers/chatroomManager'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import WebSocketService from '@/socket/WebSocketService'
import { perpsStore, userStore } from '@/store'
import { getStore, lodash, removeStore, setStore, throttle } from '@/utils'
import emitter from '@/utils/emitter'
import { StorageUtils } from '@/utils/STORAGE_REG'
import { ErrorCode } from '@rongcloud/engine'
import { RCRemoteAudioTrack, RCRTCCode } from '@rongcloud/plugin-rtc'
import { Modal } from 'antd'
import { makeAutoObservable, observable } from 'mobx'
import '@/pages/ChatRoom/css/model.css'
import { Manager } from 'danmu'
import { IDanmuMessage } from '@/pages/ChatRoom/module/Danmu'

export enum MicPosActions {
  MutePosMic = 1, // 禁言
  LeaveMic = 2, // 下麦
  Kick = 3, // 踢飞
  ClosePos = 4, // 关闭麦位
  Admin = 5, // 设为管理
  TakeMic = 6, // 上麦
}

export interface IChatMessage {
  senderUserId: string
  avatar: string
  nickname: string
  message: string
  msgType?: string
  oriModel: any
  twitterInfo?: {
    authTime: string
    id: number
    screenName: string
  }
}

interface IOnlineUserItem {
  createTime?: number
  giftPumpkinAmount?: number
  identify?: number
  nickname?: string
  nobleLevel?: number
  roomRole?: string // 房主(role_master) > 房管(role_admin) > 观众(role_civilian)
  userId?: string
  userThumbnail?: string
  username?: string
  wealthCurrency?: string
  wealthLevel?: number
  isOnMicPos?: boolean
  posIndex?: string
  conciseUserId?: string
  blacklistTime?: number
  twitterInfo?: any
}

export interface IMicPostion {
  role?: string
  micPosIndex: string
  isMuted?: boolean // 自己关闭麦克风
  isMutedByAdmin?: boolean // 被管理员禁用麦克风
  isSpeaking?: boolean // 是否正在说话
  isLock?: boolean // 此麦位是否被关闭
  nickname?: string
  pumpkinAmount?: string
  userId?: string
  userThumbnail?: string
  username?: string
  wealthCurrency?: string
  wealthLevel?: number
  twitterInfo?: any

  // 业务逻辑相关
  isRcGotoMic?: boolean
}

/**
 * @type IStatuses
 * @description 弹幕状态的类型定义，用于存储弹幕相关的状态信息
 */
export type IStatuses = Record<string, string>

class PrivateRoomWebsocket {
  messages: any[] = []
  private service: WebSocketService

  constructor(roomId: string) {
    makeAutoObservable(this, {
      messages: observable,
    })

    const baseUrl = `${import.meta.env.VITE_APP_WS_URL_01}/private_room?roomId=${roomId}&token=${userStore.token}`
    this.service = new WebSocketService(baseUrl)
    this.service.messages$.subscribe((message) => {
      switch (message.type) {
        case 101: // 有人加入
          !IS_ENV_MAIN &&
            console.log(
              '🚀🚀++🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀++有人加入原始数据------',
              message,
            )
          emitter.emit(
            ChatroomManagerEvents.PKOnlineFakeUserEnter,
            message.data,
          )
          break
        case 102: // 有人退出
          !IS_ENV_MAIN &&
            console.log(
              '🚀🚀++🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀++有人退出原始数据------',
              message,
            )
          emitter.emit(
            ChatroomManagerEvents.PKOnlineFakeUserLeave,
            message.data,
          )
          break
        // 直播热聊
        case 105:
          !IS_ENV_MAIN &&
            console.log(
              '🚀🚀++🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀++直播热聊原始数据------',
              message,
            )
          emitter.emit(ChatroomManagerEvents.PKDanmuMsgLiveChat, message)
          break
        // 交易消息
        case 107:
          !IS_ENV_MAIN &&
            console.log(
              '🚀🚀++🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀++交易消息原始数据------',
              message,
            )
          emitter.emit(ChatroomManagerEvents.PKDanmuMsgTrade, message)
          break
      }
    })
  }

  sendMessage(msg: any) {
    this.service.sendMessage(msg)
  }

  close() {
    this.service.close()
  }
}

export class ChatRoomPageStore {
  public k_CUR_ROOM_ID = 'cur_room_id.pumpkin'
  public k_CUR_PASSWORD = 'cur_room_pwd.pumpkin'

  public roomWebSocket: PrivateRoomWebsocket | null = null

  // 在列用户
  public onlineUsers: IOnlineUserItem[] = []
  setOnlineUsers(
    newUsersOrFn:
      | IOnlineUserItem[]
      | ((prev: IOnlineUserItem[]) => IOnlineUserItem[]),
  ) {
    if (typeof newUsersOrFn === 'function') {
      this.onlineUsers = newUsersOrFn(this.onlineUsers)
    } else {
      this.onlineUsers = newUsersOrFn
    }
  }
  // 麦位上，关闭了自己麦克风的人
  public mutedUserIds: string[] = []
  // 麦位上，被管理员禁麦的人
  public mutedByAdminUserIds: string[] = []

  // 是否展示浮窗
  public isFloating: boolean = false
  // 弹窗显隐
  public isOpenInviteJoinMicPosModel: boolean = false
  // 拉黑弹窗
  public isOpenAddBlackListModel: boolean = false
  // 等待拉黑的用户
  public clickedNeedBlackUserId: string | null = null
  // 收到邀请上麦通知对象
  public recivedInviteJoinMicPosObj: any | null = null
  // 邀请上麦弹窗的展示
  public isOpenInviteJoinMicPos: boolean = false

  // 存储所有的聊天消息
  public messages: IChatMessage[] = []
  // 麦克位信息List
  public micPosInfos: IMicPostion[] = [
    { micPosIndex: MicPosIndex.compere },
    { micPosIndex: MicPosIndex.microphone_owner1 },
    { micPosIndex: MicPosIndex.microphone_owner2 },
    { micPosIndex: MicPosIndex.microphone_owner3 },
    { micPosIndex: MicPosIndex.microphone_owner4 },
    { micPosIndex: MicPosIndex.microphone_owner5 },
    { micPosIndex: MicPosIndex.microphone_owner6 },
    { micPosIndex: MicPosIndex.microphone_owner7 },
    { micPosIndex: MicPosIndex.microphone_owner8 },
  ]
  // 静音(未实现)
  public isMutedAllAudio: boolean = false

  /**
   * @description 弹幕管理器实例，负责控制弹幕的显示、隐藏及发送
   */
  public danmuManager: Manager<IDanmuMessage, IStatuses> | null = null

  /**
   * @description 弹幕是否显示的状态
   * @private 私有变量，通过 getter/setter 控制访问
   */
  private _isDanmuVisible: boolean = true

  constructor() {
    makeAutoObservable(this, {
      danmuManager: false, // 不需要观察 manager 实例本身，避免性能问题
    })

    // 从存储中获取弹幕显示状态，如果没有则默认为显示
    this._isDanmuVisible = StorageUtils.getDanmuVisible() ?? true

    // 初始化数据
    this.fetchFollowingList()
    this.fetchFriendList()
  }

  /**
   * @description 获取弹幕显示状态
   * @returns {boolean} 当前弹幕是否显示
   */
  get isDanmuVisible(): boolean {
    return this._isDanmuVisible
  }

  /**
   * @description 设置弹幕显示状态
   * @param {boolean} value - 是否显示弹幕
   */
  set isDanmuVisible(value: boolean) {
    this._isDanmuVisible = value
    // 保存到本地存储，确保页面刷新后状态保持一致
    StorageUtils.setDanmuVisible(value)
    // 根据状态控制弹幕显示或隐藏
    if (this.danmuManager) {
      value ? this.danmuManager.show() : this.danmuManager.hide()
    }
  }

  isShowMonitor = () => {
    return this.micPosInfos.some((micInfo) => !!micInfo.userId)
  }

  removeMutedBySelf = async () => {
    const selfMicPosInfo = this.micPosInfos.find(
      (item) => item.userId === userStore.info.id,
    )
    if (selfMicPosInfo) {
      selfMicPosInfo.isMuted = false
      if (this.selfPosInfoRef) {
        this.selfPosInfoRef.isMuted = false
      }
      this.mutedUserIds = this.mutedUserIds.filter(
        (id) => id !== userStore.info.id,
      )
      const mutedUserIdsString = JSON.stringify(this.mutedUserIds)
      await chatroomManager.rcSetCloseMicList(
        this.roomId.toString(),
        mutedUserIdsString,
      )
      this.refreshMicPosInfos()
    }
  }

  turnOnSelfMic = async () => {
    const localAudioTrackSelf = await chatroomManager.rcGetLocalAudioTrack(
      userStore.info.id,
    )
    if (localAudioTrackSelf) {
      chatroomManager
        .rcUnmuteUserBySelf(userStore.info.id)
        .then(() => {
          IS_OPEN_ROOM_LOG && console.log(`开麦成功 success`)
          this.removeMutedBySelf()
        })
        .catch((err) => {
          console.error('anchorUnmuteMic error', err)
        })
    } else {
      chatroomManager.rcRoomGotoMic()
      this.removeMutedBySelf()
    }
  }
  turnOffSelfMic = () => {
    chatroomManager
      .rcMuteUserBySelf(userStore.info.id)
      .then(() => {
        IS_OPEN_ROOM_LOG && console.log(`闭麦成功 success`)
        const selfMicPosInfo = this.micPosInfos.find(
          (item) => item.userId === userStore.info.id,
        )
        if (selfMicPosInfo) {
          selfMicPosInfo.isMuted = true
          if (this.selfPosInfoRef) {
            this.selfPosInfoRef.isMuted = true
          }
          if (!this.mutedUserIds.includes(userStore.info.id)) {
            this.mutedUserIds.push(userStore.info.id)
          }
          const mutedUserIdsString = JSON.stringify(this.mutedUserIds)
          chatroomManager.rcSetCloseMicList(
            this.roomId.toString(),
            mutedUserIdsString,
          )
          this.refreshMicPosInfos()
        }
      })
      .catch((err) => {
        console.error('anchorMuteMic error', err)
      })
  }
  // 自己的麦位信息
  public selfPosInfoRef: IMicPostion | null = null
  // 房间，房主交易的币种
  public roomTagContent: string = ''
  //1.现货交易，4.永续合约，3.聊天
  public roomTagType: string = '3'
  // 当前用户是房主的情况下，是否正在进行屏幕分享
  public isScreenShareing: boolean = false
  // 房间是否加锁
  public isLocked: boolean = false
  // 房间热度
  public roomHot: number = 0
  // 在线人数
  public roomOnlineUserCount: number = 0
  // 房间模式
  public roomMode: Mode = Mode.Chat
  // 房间ID
  public roomId: string = ''
  // 房间密码
  public roomPassword: string = ''
  // 房间详情Object
  public roomDetial: ROOM_DETIAL = {}
  // 管理员列表
  public adminList: IOnlineUserItem[] = []
  setAdminList(newAdminList: IOnlineUserItem[]) {
    this.adminList = newAdminList
  }
  public adminListFiltered: IOnlineUserItem[] = []
  setAdminListFiltered(newAdminListFiltered: IOnlineUserItem[]) {
    this.adminListFiltered = newAdminListFiltered
  }
  // 黑名单列表
  public blockList: IOnlineUserItem[] = []
  setBlockList(newBlockList: IOnlineUserItem[]) {
    this.blockList = newBlockList
  }
  public blockListFiltered: IOnlineUserItem[] = []
  setBlockListFiltered(newBlockListFiltered: IOnlineUserItem[]) {
    this.blockListFiltered = newBlockListFiltered
  }

  public followingList: USER_INFO_RETURN[] = []
  public friendList: USER_INFO_RETURN[] = []
  isFriend = (userId: string) => {
    return this.friendList.some((friend) => friend.id === userId)
  }

  isFollowing = (userId: string) => {
    return this.followingList.some((user) => user.id === userId)
  }

  fetchFollowingList = (): Promise<any> => {
    return FOLLOWING()
      .then((res) => {
        if (res.code === 200) {
          this.followingList = res.data
          return res.data // 返回获取的数据
        } else {
          return Promise.reject(new Error(`FOLLOWING error ${res.code}`))
        }
      })
      .catch((err) => {
        console.error('fetchFollowing error', err)
        return Promise.reject(err)
      })
  }

  fetchFriendList = (): Promise<any> => {
    return FRIENDS()
      .then((res) => {
        if (res.code === 200) {
          this.friendList = res.data
          return res.data // 返回获取到的数据
        } else {
          return Promise.reject(new Error(`FRIENDS error ${res.code}`))
        }
      })
      .catch((err) => {
        console.error('fetchFriend error', err)
        return Promise.reject(err)
      })
  }

  getCurPassword = () => {
    return sessionStorage.getItem(this.k_CUR_PASSWORD)

    // try {
    //   const roomInfoValue = getStore(this.k_CUR_PASSWORD).toString()
    //   return roomInfoValue
    // } catch {
    //   return null
    // }
  }

  getCurRoomId = () => {
    return sessionStorage.getItem(this.k_CUR_ROOM_ID)

    // try {
    //   const roomInfoValue = getStore(this.k_CUR_ROOM_ID).toString()
    //   return roomInfoValue
    // } catch {
    //   return null
    // }
  }

  setCurRoomIdLocal = (roomId: string) => {
    this.roomId = roomId
    sessionStorage.setItem(this.k_CUR_ROOM_ID, roomId)

    // if (this.roomId && this.roomDetial) {
    //   setStore(this.k_CUR_ROOM_ID, roomId)
    // } else {
    //   removeStore(this.k_CUR_ROOM_ID)
    // }
  }

  setCurPasswordLocal = (password: string) => {
    this.roomPassword = password
    sessionStorage.setItem(this.k_CUR_PASSWORD, password)

    // if (this.roomPassword && this.roomDetial) {
    //   setStore(this.k_CUR_PASSWORD, password)
    // } else {
    //   removeStore(this.k_CUR_PASSWORD)
    // }
  }

  isHost = (userId: string) => {
    const hostItem = this.micPosInfos.find(
      (item) => item.micPosIndex === MicPosIndex.compere,
    )
    return hostItem ? hostItem.userId === userId : false
  }

  /**
   * 判断当前用户是否是房主
   * @returns {boolean} 如果当前用户是房主则返回 true，否则返回 false
   */
  isRoomMaster = () => {
    // 检查当前用户的 userId 是否与房主的 userId 匹配，而不仅仅依赖 roomRole
    const roomMaster = this.onlineUsers.find(
      (user) => user.roomRole === RoomRole.Master,
    )
    const currentUserId = userStore.info.id

    // 只有当 roomRole 是 Master 且当前用户 ID 与房主 ID 匹配时才返回 true
    return (
      this.roomDetial.roomRole === RoomRole.Master &&
      roomMaster?.userId === currentUserId
    )
  }

  /**
   * 判断当前用户是否是房管
   * @returns {boolean} 如果当前用户是房管则返回 true，否则返回 false
   */
  isRoomAdmin = (userId: string) => {
    const roomMaster = this.onlineUsers.find(
      (user) => user.roomRole === RoomRole.Admin,
    )
    return (
      this.roomDetial.roomRole === RoomRole.Admin &&
      roomMaster?.userId === userId
    )
  }

  /**
   * 判断当前房主是否在主持麦位上
   * @returns {boolean} 如果房主在主持麦位上则返回 true，否则返回 false
   */
  isHostOnMic = () => {
    // 找到主持麦位
    const hostItem = this.micPosInfos.find(
      (item) => item.micPosIndex === MicPosIndex.compere,
    )

    // 如果主持麦位有人
    if (hostItem && hostItem.userId) {
      // 找到在线用户列表中的这个用户
      const hostUser = this.onlineUsers.find(
        (user) => user.userId === hostItem.userId,
      )
      // 检查这个用户是否是房主
      return hostUser?.roomRole === RoomRole.Master
    }

    return false
  }

  // 获取用户的角色('role_master'表示房主/'role_admin'表示房管/'role_civilian'表示观众)
  getUserRole = (userId: string): string | undefined => {
    const targetUser = this.onlineUsers.find((user) => user.userId === userId)
    return targetUser ? targetUser.roomRole : undefined
  }

  // 获取用户的麦位信息('compere'表示主持人, 'microphone_owner1-10'表示嘉宾, undefined表示没有上麦)
  getUserMicPosInfo = (userId: string): string | undefined => {
    const targetMicInfo = this.micPosInfos.find(
      (item) => item.userId === userId,
    )
    if (targetMicInfo) {
      return targetMicInfo.micPosIndex
    }
    return undefined
  }

  /**
   * 获取房间的房主信息
   * @returns {IOnlineUserItem | undefined} 房主信息，如果找不到则返回 undefined
   */
  getRoomMaster = () => {
    // 从在线用户列表中找到房主
    return this.onlineUsers.find((user) => user.roomRole === RoomRole.Master)
  }

  /**
   * 判断房主是否在任何麦位上（不仅仅是主持麦位）
   * @returns {boolean} 如果房主在任何麦位上则返回 true，否则返回 false
   */
  isRoomMasterOnAnyMic = () => {
    // 获取房主信息
    const roomMaster = this.getRoomMaster()
    if (!roomMaster) return false

    // 检查房主是否在任何麦位上
    return this.micPosInfos.some(
      (micPos) => micPos.userId === roomMaster.userId,
    )
  }

  handleRoomHotChanged = (event: any) => {
    try {
      const { key, value } = event
      if (key === ChatroomCommonPushKey.RoomHot) {
        const valueModel = JSON.parse(value)
        const { hot, num } = valueModel
        this.roomHot = hot
        this.roomOnlineUserCount = num
      }
    } catch {
      console.error(`handleRoomHotChanged`)
    }
  }

  handleRoomDetailChanged = (event: any) => {
    try {
      let newDetial
      const { value } = event
      if (value) {
        newDetial = event.value
      } else {
        const { chat_room_info } = event
        newDetial = chat_room_info
      }
      if (newDetial) {
        this.roomDetial = newDetial
        this.isLocked = this.roomDetial.isPw ?? false
        if (event.value?.roomRole === RoomRole.Master) {
          this.fetchAdminList()
          this.fetchBlockList()
        }
        if (event.value?.roomRole === RoomRole.Admin) {
          this.fetchBlockList()
        }
      }
    } catch (err) {
      console.error(`handleRoomDetailChanged error`, err)
    }
  }

  handleScreenShareStart = (event: any) => {
    try {
      const { key, value } = event
      if (key === ChatroomCommonPushKey.IsScreenShareing) {
        this.isScreenShareing = value === 'true'
      }
    } catch (error) {
      console.error(`handleScreenShareStart: ${error}`)
    }
  }

  handleMessageEvent = (msgs: any) => {
    msgs.forEach((msg: any) => {
      const msgExtraStr = msg.content.extra
      if (msgExtraStr) {
        const msgExtraObj = JSON.parse(msgExtraStr)
        if (
          msgExtraObj &&
          msgExtraObj.msgType === PKMsgExtraMsgTypeKey.MTGitf
        ) {
          return
        }
      }
      // const targetUser = this.onlineUsers.find((item)=>item.userId === msg.senderUserId)
      const twitterInfo =
        msg.content.user?.extra &&
        JSON.parse(msg.content.user?.extra ?? {}).twitterInfo
      const newMsg: IChatMessage = {
        senderUserId: msg.senderUserId,
        avatar: msg.content.user?.portrait,
        nickname: msg.content.user?.name,
        message: msg.content.content,
        msgType: msg.messageType,
        oriModel: msg,
        twitterInfo: twitterInfo,
      }
      this.addMessage(newMsg)
    })
  }

  handleModeChanged = (newMode: Mode) => {
    this.roomMode = newMode
  }

  removeMutedListAfterLeaveMic = (micPosIndex: string) => {
    if (
      this.selfPosInfoRef &&
      this.selfPosInfoRef.micPosIndex === micPosIndex
    ) {
      this.mutedUserIds = this.mutedUserIds.filter(
        (id) => id !== userStore.info.id,
      )
      const mutedUserIdsString = JSON.stringify(this.mutedUserIds)
      chatroomManager.rcSetCloseMicList(
        this.roomId.toString(),
        mutedUserIdsString,
      )
    }
  }

  handleHostMicPosChanged = (event: any) => {
    const { isEmpty } = lodash
    if (isEmpty(event.value) === false) {
      // 主持麦位有人
      const valueObj = JSON.parse(event.value)
      if (valueObj.userId) {
        IS_OPEN_ROOM_LOG &&
          console.log(
            `****** ${event.key}位置有人. nickname: ${valueObj.nickname},userId: ${valueObj.userId}`,
          )
        const curMicPosInfo = this.micPosInfos[0]
        curMicPosInfo.nickname = valueObj.nickname
        curMicPosInfo.userThumbnail = valueObj.userThumbnail
        curMicPosInfo.isLock = valueObj.isLock
        curMicPosInfo.userId = valueObj.userId
        curMicPosInfo.micPosIndex = event.key
        curMicPosInfo.isMuted = this.mutedUserIds.includes(valueObj.userId)
        curMicPosInfo.twitterInfo = valueObj.twitterInfo

        if (valueObj.userId === userStore.info.id) {
          // 当前用户上到此麦
          curMicPosInfo.isMuted = false
          ;(async () => {
            let waitTime = 0
            const interval = 500
            const maxWait = 4000
            while (
              chatroomManager.audienceRoom === null &&
              waitTime < maxWait
            ) {
              await new Promise((resolve) => setTimeout(resolve, interval))
              waitTime += interval
            }
            if (
              chatroomManager.audienceRoom !== null &&
              chatroomManager.anchorRoom === null &&
              curMicPosInfo.isMuted === false
            ) {
              await chatroomManager.rcRoomGotoMic()
              this.mutedUserIds = this.mutedUserIds.filter(
                (id) => id !== userStore.info.id,
              )
              const mutedUserIdsString = JSON.stringify(this.mutedUserIds)
              chatroomManager.rcSetCloseMicList(
                this.roomId.toString(),
                mutedUserIdsString,
              )
            }
          })()
          this.selfPosInfoRef = this.micPosInfos[0]
        }
      }
    } else {
      // 主持麦位没人
      IS_OPEN_ROOM_LOG && console.log(`****** ${event.key}位置没人`)
      const curMicPosInfo = this.micPosInfos[0]
      const curMicPosIndex = curMicPosInfo.micPosIndex
      this.micPosInfos[0] = { micPosIndex: curMicPosIndex }

      if (
        this.selfPosInfoRef &&
        this.selfPosInfoRef.micPosIndex === event.key
      ) {
        this.removeMutedListAfterLeaveMic(event.key)

        // 当前用户上到此麦
        IS_OPEN_ROOM_LOG &&
          console.log(
            `****** ${event.key}位置没人. 之前登录用户: ${userStore.info.nickname},userId: ${userStore.info.id}, 在此位，现在把内存置空`,
          )
        // 如果此时当前用户正在屏幕分享。则需要还原为Chat模式
        if (this.isScreenShareing) {
          this.isScreenShareing = false
          chatroomManager
            .updateRoomType(this.roomId, 3, '', '')
            .then((res) => {
              if (res.code !== ChatroomErrorCode.PK_SUCCESS) {
                console.error(`${res.message}(${res.code})`)
                return
              }
              this.handleModeChanged(Mode.Chat)
            })
            .catch((err) => {
              console.error('updateRoomType', err)
            })

          chatroomManager
            .updateRoomSetting(this.roomId, { isScreenShareing: false })
            .then(() => {
              IS_OPEN_ROOM_LOG && console.log(`updateRoomSetting`)
            })
            .catch((err) => {
              console.error(`updateRoomSetting error`, err)
            })
        }
        chatroomManager.rcExitScreenShare()
        // 执行RC的下麦操作
        if (
          chatroomManager.audienceRoom === null &&
          chatroomManager.anchorRoom !== null
        ) {
          chatroomManager.rcRoomLeaveMic()
        }
        this.selfPosInfoRef = null
      }
    }
    this.refreshMicPosInfos()
  }

  handleMicChanged = async (event: any) => {
    const leaveMic = () => {
      this.removeMutedListAfterLeaveMic(event.key)
      // 当前用户上到此麦
      IS_OPEN_ROOM_LOG &&
        console.log(
          `****** ${micIndex}位置没人. 之前登录用户: ${userStore.info.nickname},userId: ${userStore.info.id}, 在此位，现在把内存置空`,
        )
      if (
        chatroomManager.audienceRoom === null &&
        chatroomManager.anchorRoom !== null
      ) {
        chatroomManager.rcRoomLeaveMic()
      }
      this.selfPosInfoRef = null
    }

    const { isEmpty } = lodash
    const micIndex = parseInt(event.key.replace('microphone_owner', ''), 10)
    if (micIndex >= 1 && micIndex <= 8) {
      if (isEmpty(event.value) === false) {
        // 麦位有人 or 封麦
        const valueObj = JSON.parse(event.value)
        if (event.value) {
          const curMicPosInfo = this.micPosInfos[micIndex]
          if (valueObj.userId) {
            // 麦位有人
            IS_OPEN_ROOM_LOG &&
              console.log(
                `****** ${event.key}位置有人. nickname: ${valueObj.nickname},userId: ${valueObj.userId}`,
              )
            curMicPosInfo.nickname = valueObj.nickname
            curMicPosInfo.userThumbnail = valueObj.userThumbnail
            curMicPosInfo.isLock = valueObj.isLock
            curMicPosInfo.userId = valueObj.userId
            curMicPosInfo.micPosIndex = event.key
            curMicPosInfo.isMuted = this.mutedUserIds.includes(valueObj.userId)
            curMicPosInfo.isMutedByAdmin = this.mutedByAdminUserIds.includes(
              valueObj.userId,
            )
            curMicPosInfo.twitterInfo = valueObj.twitterInfo
          } else {
            // 封麦/解封 逻辑
            if (valueObj.isLock) {
              // 封麦
              const curMicPosInfo = this.micPosInfos[micIndex]
              const curMicPosIndex = curMicPosInfo.micPosIndex
              this.micPosInfos[micIndex] = {
                micPosIndex: curMicPosIndex,
                isLock: valueObj.isLock,
              }
              if (
                this.selfPosInfoRef &&
                this.selfPosInfoRef.micPosIndex === event.key
              ) {
                leaveMic()
              }
            } else {
              // 解封麦
              const curMicPosInfo = this.micPosInfos[micIndex]
              const curMicPosIndex = curMicPosInfo.micPosIndex
              this.micPosInfos[micIndex] = {
                micPosIndex: curMicPosIndex,
                isLock: valueObj.isLock,
              }
            }
          }
          // 当前用户上到此麦
          if (valueObj.userId === userStore.info.id) {
            ;(async () => {
              let waitTime = 0
              const interval = 500
              const maxWait = 4000
              while (
                chatroomManager.audienceRoom === null &&
                waitTime < maxWait
              ) {
                await new Promise((resolve) => setTimeout(resolve, interval))
                waitTime += interval
              }
              if (chatroomManager.audienceRoom !== null) {
                // 当前用户已经加入直播间
                if (chatroomManager.anchorRoom === null) {
                  await chatroomManager.rcRoomGotoMic()
                  await this.removeMutedBySelf()
                  // 检查自己是否已经被禁麦
                  if (this.mutedByAdminUserIds.includes(valueObj.userId)) {
                    await chatroomManager.rcRoomLeaveMic()
                  }
                }
              }
            })()
            this.selfPosInfoRef = curMicPosInfo
          }
        }
      } else {
        // 麦位没人
        IS_OPEN_ROOM_LOG && console.log(`****** ${micIndex}位置没人`)
        const curMicPosInfo = this.micPosInfos[micIndex]
        const curMicPosIndex = curMicPosInfo.micPosIndex
        this.micPosInfos[micIndex] = { micPosIndex: curMicPosIndex }

        if (
          this.selfPosInfoRef &&
          this.selfPosInfoRef.micPosIndex === event.key
        ) {
          leaveMic()
        }
      }
      this.refreshMicPosInfos()
    }
  }

  // 房间内所有关闭了麦克风的用户(这里其实可以不用了)
  handleAllMicPosMutedListChanged = (event: any) => {
    // if (event.value) {
    //   try {
    //     const mutedUserIds: string[] = JSON.parse(event.value)
    //     this.mutedUserIds = mutedUserIds
    //     this.micPosInfos = this.micPosInfos.map((micInfo) => {
    //       if (micInfo.userId) {
    //         return {
    //           ...micInfo,
    //           isMuted: mutedUserIds.includes(micInfo.userId),
    //         }
    //       }
    //       return micInfo
    //     })
    //     this.refreshMicPosInfos()
    //   } catch (error) {
    //     console.error('handleMicCloseChanged error:', error)
    //   }
    // }
  }

  updateSpeakingState = (speakingUserIds: string[]) => {
    // 只更新状态发生变化的项，保持未变化项的引用不变
    this.micPosInfos = this.micPosInfos.map((micInfo) => {
      if (micInfo.userId) {
        const newIsSpeaking = speakingUserIds.includes(micInfo.userId)
        // 如果状态相同，则直接返回原对象，避免不必要的重新渲染
        if (micInfo.isSpeaking === newIsSpeaking) {
          return micInfo
        }
        return {
          ...micInfo,
          isSpeaking: newIsSpeaking,
        }
      }
      return micInfo
    })
    // 如需通知界面更新，请调用对应的刷新方法
    // this.refreshMicPosInfos();
  }
  throttledUpdateSpeakingState = throttle(this.updateSpeakingState, 1000)

  handleAudioStatesChanged = (event: any) => {
    this.throttledUpdateSpeakingState(event)
  }

  handleHostAudioStatesChanged = (event: any) => {
    try {
      const { track, audioLevel } = event
      const userId = track.getUserId()
      const index = this.micPosInfos.findIndex((user) => user.userId === userId)
      const hostMicPos = this.micPosInfos[index]
      if (hostMicPos) {
        const currentMuted = this.micPosInfos[index].isMuted
        this.updateMicPos(index, {
          isSpeaking: currentMuted ? false : audioLevel > 5,
        })
      }
    } catch (err) {
      console.error(`handleHostAudioStatesChanged`, err)
    }
  }

  handleMicPosMicChanged = (audioTrack: RCRemoteAudioTrack) => {
    const userId = audioTrack.getUserId()
    const isMuted = audioTrack.isOwnerMuted()
    const index = this.micPosInfos.findIndex((item) => item.userId === userId)
    if (index !== -1) {
      const oldItem = this.micPosInfos[index]
      const newItem = {
        ...oldItem,
        isMuted: isMuted,
        isSpeaking: isMuted ? false : oldItem.isSpeaking,
      }
      this.updateMicPos(index, newItem)
    }
  }

  // 用户被管理员或者房主禁止发言的列表
  handleAllMicPosMutedByAdmin = (event: any) => {
    if (event.value) {
      try {
        // 解析出一个包含被禁言用户ID的数组
        const mutedUserIds: string[] = JSON.parse(event.value)
        this.mutedByAdminUserIds = mutedUserIds

        // 自己在麦位上
        if (this.selfPosInfoRef && this.selfPosInfoRef.userId) {
          if (this.mutedByAdminUserIds.includes(this.selfPosInfoRef.userId)) {
            chatroomManager.rcRoomLeaveMic()
          } else {
            if (this.selfPosInfoRef.isMuted === false) {
              chatroomManager.rcRoomGotoMic()
            } else {
              chatroomManager.rcRoomLeaveMic()
            }
          }
        }

        this.micPosInfos = this.micPosInfos.map((micInfo) => {
          if (micInfo.userId) {
            return {
              ...micInfo,
              isMutedByAdmin: mutedUserIds.includes(micInfo.userId),
            }
          }
          return micInfo
        })
        this.refreshMicPosInfos()
      } catch (error) {
        console.error('handleMutedByAdmin error:', error)
      }
    }
  }

  handleRecivedInviteJoinMicPos = (event: any) => {
    this.recivedInviteJoinMicPosObj = event
    if (this.recivedInviteJoinMicPosObj) {
      this.isOpenInviteJoinMicPosModel = true
    }
  }

  handleRoomTagChangedNew = (event: any) => {
    this.roomTagType = event.value
  }

  handleRoomTagChanged = (event: any) => {
    const { isEmpty } = lodash

    setTimeout(() => {
      this.roomTagType = event.value
      switch (event.value) {
        case '1':
          this.roomMode = Mode.Trade
          if (
            chatroomManager.currCryptoString &&
            isEmpty(chatroomManager.currCryptoString) === false
          ) {
            const currCrypto = JSON.parse(chatroomManager.currCryptoString)
            tradeStore.setCurrCrypto(currCrypto)
          }
          break
        case '3':
          this.roomMode = Mode.Chat
          break
        case '4':
          if (
            chatroomManager.currCryptoString &&
            isEmpty(chatroomManager.currCryptoString) === false
          ) {
            const currCrypto = JSON.parse(chatroomManager.currCryptoString)
            perpsStore.init.setCoin(currCrypto?.coin || 'BTC')
          }

          this.roomMode = Mode.Perp
          break
      }
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKRoomTagChanged,
        this.handleRoomTagChanged,
      )
      chatroomManager.addEventListener(
        ChatroomManagerEvents.PKRoomTagChanged,
        this.handleRoomTagChangedNew,
      )
    }, 1000)
  }

  handleRoomTagContentChanged = (event: any) => {
    this.roomTagContent = event.value
  }

  handleScreenShareEnd = () => {
    chatroomManager
      .updateRoomSetting(this.roomId, { isScreenShareing: false })
      .then(() => {
        this.isScreenShareing = false
        chatroomManager.rcExitScreenShare()
      })
      .catch((err) => {
        console.error(`updateRoomSetting error`, err)
      })
  }

  handleClearRoomMsgs = () => {
    this.clearMessages()
  }

  handleDisconnected = () => {
    if (userStore.imToken === '') {
      this.leaveRoom()
    }
  }

  handleScreenShareLocalPublished = (event: any) => {
    chatroomManager
      .updateRoomSetting(this.roomId, { isScreenShareing: true })
      .then(() => {
        this.isScreenShareing = true
      })
      .catch((err) => {
        console.error(`updateRoomSetting error`, err)
      })
  }

  addListeners = () => {
    IS_OPEN_ROOM_LOG && console.log('****** ChatroomPageStore addListeners')
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKScreenShareLocalPublished,
      this.handleScreenShareLocalPublished,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKDisconnected,
      this.handleDisconnected,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKClearRoomMsgs,
      this.handleClearRoomMsgs,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKScreenShareEnd,
      this.handleScreenShareEnd,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKRoomTagContentChanged,
      this.handleRoomTagContentChanged,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKRoomTagChanged,
      this.handleRoomTagChanged,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKRoomHot,
      this.handleRoomHotChanged,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKRoomDetialChanged,
      this.handleRoomDetailChanged,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKScreenShareStart,
      this.handleScreenShareStart,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKModeChanged,
      this.handleModeChanged,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKHostMicPosChanged,
      this.handleHostMicPosChanged,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKMicPosChanged,
      this.handleMicChanged,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKMicPosAudioStatesChanged,
      this.handleAudioStatesChanged,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKMicPosMicChanged,
      this.handleMicPosMicChanged,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKAllMicPosMutedByAdmin,
      this.handleAllMicPosMutedByAdmin,
    )
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKAllMicPosMutedListChanged,
      this.handleAllMicPosMutedListChanged,
    )

    // MSG:
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKRecivedInviteJoinMicPosMSG,
      this.handleRecivedInviteJoinMicPos,
    )

    // IM
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKChatRoomRecivedNewMsg,
      this.handleMessageEvent,
    )
  }

  removeListeners = () => {
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKScreenShareLocalPublished,
      this.handleScreenShareLocalPublished,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKDisconnected,
      this.handleDisconnected,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKClearRoomMsgs,
      this.handleClearRoomMsgs,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKScreenShareEnd,
      this.handleScreenShareEnd,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKRoomTagContentChanged,
      this.handleRoomTagContentChanged,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKRoomTagChanged,
      this.handleRoomTagChanged,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKRoomHot,
      this.handleRoomHotChanged,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKRoomDetialChanged,
      this.handleRoomDetailChanged,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKScreenShareStart,
      this.handleScreenShareStart,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKModeChanged,
      this.handleModeChanged,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKHostMicPosChanged,
      this.handleHostMicPosChanged,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKMicPosChanged,
      this.handleMicChanged,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKMicPosAudioStatesChanged,
      this.handleAudioStatesChanged,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKMicPosMicChanged,
      this.handleMicPosMicChanged,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKAllMicPosMutedByAdmin,
      this.handleAllMicPosMutedByAdmin,
    )
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKAllMicPosMutedListChanged,
      this.handleAllMicPosMutedListChanged,
    )

    // MSG:
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKRecivedInviteJoinMicPosMSG,
      this.handleRecivedInviteJoinMicPos,
    )

    // IM
    chatroomManager.removeEventListener(
      ChatroomManagerEvents.PKChatRoomRecivedNewMsg,
      this.handleMessageEvent,
    )
  }

  // 添加一条消息
  addMessage = (message: IChatMessage) => {
    this.messages.push(message)
  }

  // 设置整个消息列表（例如重置时调用）
  setMessages = (messages: IChatMessage[]) => {
    this.messages = messages
  }

  clearMessages = () => {
    if (this.messages.length > 0) {
      const firstMsg = this.messages[0]
      const tipsMessages = this.messages.filter((msg, index) => {
        if (index === 0) return false // 跳过第一条消息
        const tips = msg?.oriModel?.content?.msgFields?.tips
        return tips === 'chat_room_join'
      })
      this.messages = [firstMsg, ...tipsMessages]
    }
  }

  resetRoom = () => {
    // 重置所有状态变量为初始值
    this.isFloating = false
    this.isOpenInviteJoinMicPosModel = false
    this.isOpenAddBlackListModel = false
    this.clickedNeedBlackUserId = null
    this.recivedInviteJoinMicPosObj = null
    this.isOpenInviteJoinMicPos = false

    // 重置消息列表
    this.messages = []

    // 重置麦位信息为初始状态
    this.micPosInfos = [
      { micPosIndex: MicPosIndex.compere },
      { micPosIndex: MicPosIndex.microphone_owner1 },
      { micPosIndex: MicPosIndex.microphone_owner2 },
      { micPosIndex: MicPosIndex.microphone_owner3 },
      { micPosIndex: MicPosIndex.microphone_owner4 },
      { micPosIndex: MicPosIndex.microphone_owner5 },
      { micPosIndex: MicPosIndex.microphone_owner6 },
      { micPosIndex: MicPosIndex.microphone_owner7 },
      { micPosIndex: MicPosIndex.microphone_owner8 },
    ]

    // 重置音频状态
    this.isMutedAllAudio = false

    // 重置用户列表
    this.onlineUsers = []
    this.adminList = []
    this.adminListFiltered = []
    this.blockList = []
    this.blockListFiltered = []

    // 重置自己的麦位信息
    this.selfPosInfoRef = null

    // 重置房间状态
    this.roomTagContent = ''
    this.roomTagType = '3'
    this.isScreenShareing = false
    this.isLocked = false
    this.roomHot = 0
    this.roomOnlineUserCount = 0
    this.roomMode = Mode.Chat
    this.roomId = ''
    this.roomPassword = ''
    this.roomDetial = {} as ROOM_DETIAL

    // 清理弹幕相关资源
    this.clearDanmuManager()

    // removeStore(this.k_CUR_ROOM_ID)
    // removeStore(this.k_CUR_PASSWORD)
    sessionStorage.removeItem(this.k_CUR_ROOM_ID)
    sessionStorage.removeItem(this.k_CUR_PASSWORD)
  }

  leaveRoom = async () => {
    if (this.roomDetial.roomId) {
      Modal.confirm({
        title: null,
        icon: null,
        content: (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              height: '100%',
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <div>{i18n.t('exiting_room_plz_wait')}</div>
          </div>
        ),
        width: 280,
        className: 'leave-room-modal',
        okButtonProps: { style: { display: 'none' } },
        cancelButtonProps: { style: { display: 'none' } },
        footer: null,
        maskClosable: false,
        closable: false,
      })
      const startTime = Date.now()
      chatroomManager.rcExitScreenShare()
      await chatroomManager.leaveChatRoom(this.roomDetial.roomId?.toString())
      await chatroomManager.rcLeaveRooms()
      this.resetRoom()
      this.removeListeners()
      this.roomWebSocket?.close()
      this.roomWebSocket = null
      localStorage.removeItem(kChatRoomMessage)
      const elapsedTime = Date.now() - startTime
      if (elapsedTime < 1000) {
        await new Promise((resolve) => setTimeout(resolve, 1000 - elapsedTime))
      }
      this.isDanmuVisible = true
      Modal.destroyAll()
    }
  }

  // 设置整个数组（触发重渲染）
  setMicPosInfos = (data: IMicPostion[]) => {
    this.micPosInfos = data
  }

  // 为了触发 Mobx 浅拷贝刷新，可以调用该方法（适用于更新后引用未变化的场景）
  refreshMicPosInfos = () => {
    this.micPosInfos = [...this.micPosInfos]
  }

  updateMicPos = (index: number, updateData: Partial<IMicPostion>) => {
    if (index >= 0 && index < this.micPosInfos.length) {
      // 利用展开运算符将原来的对象和 updateData 合并
      this.micPosInfos[index] = {
        ...this.micPosInfos[index],
        ...updateData,
      }
    }
    this.refreshMicPosInfos()
  }

  // 提供一个根据 micPosIndex 进行更新的辅助方法
  updateMicPosByKey = (
    micPosIndex: string,
    updateData: Partial<IMicPostion>,
  ) => {
    const index = this.micPosInfos.findIndex(
      (item) => item.micPosIndex === micPosIndex,
    )
    if (index !== -1) {
      this.updateMicPos(index, updateData)
    }
  }

  joinAudioRoom = async (
    roomRole: string,
    roomId: string,
    roomStatus?: number,
  ): Promise<{ code: number; message: string; data?: any }> => {
    try {
      const resultJoinRoom = await chatroomManager.joinRoomAudience(roomId)
      if (resultJoinRoom.code === RCRTCCode.SUCCESS) {
        this.roomWebSocket = new PrivateRoomWebsocket(roomId)

        // 房主逻辑： 房主自动上麦
        if (roomRole === RoomRole.Master) {
          const hostItem = this.micPosInfos[0]
          // 主持位没人，则直接上麦
          if (hostItem.userId === undefined) {
            if (roomStatus === 1) {
              // 房间可用，抢主持位
              const resultgrabMic = await chatroomManager.pkGrabMic(
                this.roomId,
                MicPosIndex.compere,
              )
              if (resultgrabMic.code !== ChatroomErrorCode.PK_SUCCESS) {
                console.error(
                  `joinAudioRoom grabMic失败, code: ${resultgrabMic.code} msg: ${resultgrabMic.message}`,
                )
              }
            }
            return {
              code: RCRTCCode.SUCCESS,
              message: '加入房间成功并处理主持位',
              data: resultJoinRoom.data,
            }
          } else {
            // 主持位已有用户
            return {
              code: RCRTCCode.SUCCESS,
              message: '加入房间成功，但主持位已有用户',
              data: resultJoinRoom.data,
            }
          }
        } else {
          // 如果是观众角色
          return {
            code: RCRTCCode.SUCCESS,
            message: '加入房间成功',
            data: resultJoinRoom.data,
          }
        }
      } else {
        // 加入房间失败
        return {
          code: resultJoinRoom.code,
          message: '加入房间失败',
          data: null,
        }
      }
    } catch (error) {
      console.error('joinAudioRoom 错误:', error)
      return {
        code: ChatroomErrorCode.PK_UNKNOW_ERROR,
        message: error instanceof Error ? error.message : '未知错误',
        data: null,
      }
    }
  }

  joinChatroom = async (
    roomId: string,
  ): Promise<{ code: number; message: string; data: any }> => {
    const tempRoomId = roomId.toString()
    try {
      // 连接 IM
      const conRes = await chatroomManager.connect(userStore.imToken)
      if (
        conRes.code !== ErrorCode.SUCCESS &&
        conRes.code !== ChatroomErrorCode.PK_SUCCESS
      ) {
        return {
          code: conRes ? conRes.code : ChatroomErrorCode.PK_UNKNOW_ERROR,
          message: 'IM 连接失败',
          data: null,
        }
      }
      // 加入已存在的聊天室
      const joinChatRes = await chatroomManager.joinExistChatRoom(tempRoomId)
      if (!joinChatRes || joinChatRes.code !== RongIMLib.ErrorCode.SUCCESS) {
        return {
          code: joinChatRes
            ? joinChatRes.code
            : ChatroomErrorCode.PK_UNKNOW_ERROR,
          message: '加入聊天室失败',
          data: null,
        }
      }

      // 发送欢迎提示消息
      const welcomeRes = await chatroomManager.sendWelcomeTipsMsg(tempRoomId)
      const joinedRes = await chatroomManager.sendJoinedTipsMsg(tempRoomId)
      return {
        code: RongIMLib.ErrorCode.SUCCESS,
        message: '聊天室加入成功',
        data: {
          welcomeRes,
          joinedRes,
        },
      }
    } catch (error) {
      console.error(`joinChatroom error: `, error)
      return {
        code: -1,
        message: error instanceof Error ? error.message : '未知错误',
        data: null,
      }
    }
  }

  joinsChatRoomAndAudioRoom = async (
    roomId: string,
  ): Promise<{ message: string; code: number }> => {
    if (!roomId) {
      return {
        message: 'roomId 无效',
        code: ChatroomErrorCode.PK_ROOM_NOT_EXIST,
      }
    }
    const tempRoomId = roomId.toString()
    const resultJoinChatroom = await this.joinChatroom(tempRoomId)
    if (
      resultJoinChatroom &&
      resultJoinChatroom.code === RongIMLib.ErrorCode.SUCCESS
    ) {
      if (this.roomDetial.roomRole) {
        if (!chatroomManager.anchorRoom && !chatroomManager.audienceRoom) {
          this.joinAudioRoom(
            this.roomDetial.roomRole!,
            tempRoomId,
            this.roomDetial.s,
          )
        }
      }
      return {
        message: '加入成功',
        code: ErrorCode.SUCCESS,
      }
    } else {
      return {
        message: '加入失败',
        code: resultJoinChatroom.code,
      }
    }
  }

  joinRoom = async (
    roomId: string,
    password: string,
    needMutiClientCheck: boolean,
  ): Promise<{ message: string; code: number; data: any }> => {
    await this.leaveRoom()
    try {
      // 获取房间详情
      const res: any = await chatroomManager.fetchRoomDetail(roomId, password)
      if (res.code !== 200) {
        // 出现错误则抛出异常，而不是返回对象
        throw {
          message: res.message,
          code: res.code,
          data: null,
        }
      }
      // const tempRoomId = `${this.roomId}`
      if (res.data.s === 1) {
        // 互踢检查
        if (
          false &&
          res.data.locationChatRoomId &&
          res.data.locationChatRoomId === roomId
        ) {
          // 有其他端在房间
          return {
            message: '有app端在房间内',
            code: ChatroomErrorCode.PK_MUTI_CLIENT_IN_ROOM,
            data: this.roomDetial,
          }
        } else {
          this.roomDetial = res.data
          this.isLocked = this.roomDetial.isPw ?? false

          this.setCurRoomIdLocal(roomId)
          this.setCurPasswordLocal(password)
          // this.roomId = roomId
          // this.roomPassword = password

          this.removeListeners()
          this.addListeners()
          // 加入聊天室和音频房间
          const resultJoin = await this.joinsChatRoomAndAudioRoom(this.roomId)
          if (resultJoin && resultJoin.code !== ErrorCode.SUCCESS) {
            this.roomDetial = {} as ROOM_DETIAL
            throw {
              message: resultJoin.message || '加入聊天室或音频房间失败',
              code: resultJoin.code || ChatroomErrorCode.PK_UNKNOW_ERROR,
              data: null,
            }
          }

          if (
            this.roomDetial.roomRole === RoomRole.Admin ||
            this.roomDetial.roomRole === RoomRole.Master
          ) {
            this.fetchAdminList()
            this.fetchBlockList()
          }
          // 正常业务，返回成功的对象
          return {
            message: '加入成功',
            code: 200,
            data: this.roomDetial,
          }
        }
      } else {
        // 房间状态不正常，抛出异常
        this.roomDetial = {} as ROOM_DETIAL
        throw {
          message: '房间已经关闭，请刷新列表',
          code: ChatroomErrorCode.RC_CHATROOM_NOT_EXIST,
          data: null,
        }
      }
    } catch (error: any) {
      // 外层捕获异常，继续抛出
      this.roomDetial = {} as ROOM_DETIAL
      throw {
        message: error.message || error,
        code: error.code || ChatroomErrorCode.PK_UNKNOW_ERROR,
        data: null,
      }
    }
  }

  updateRoomDetial = () => {
    return chatroomManager
      .fetchRoomDetail(this.roomId, this.roomPassword)
      .then((res: any) => {
        if (res.code !== 200) {
          IS_OPEN_ROOM_LOG &&
            console.log(`updateRoomDetial fail: ${res.message} ${res.code}`)
        }
        this.roomDetial = res.data
        return res.data
      })
      .catch((error) => {
        console.error('updateRoomDetial error:', error)
        // 可以根据需要返回 reject，也可以直接抛出错误
        return Promise.reject(error)
      })
  }

  updateOnlineUserList = (roomId: string, userIds: string) => {
    GET_CHATROOM_USER_INFO({
      roomId: roomId,
      userIds: userIds,
    })
      .then((result) => {
        if (result.code === 200) {
          if ((result.data as IOnlineUserItem[]).length > 0) {
            const updatedUser = (result.data as IOnlineUserItem[])[0]
            const existingIndex = this.onlineUsers.findIndex(
              (user) => user.userId === updatedUser.userId,
            )
            if (existingIndex !== -1) {
              // 更新该用户数据
              this.onlineUsers[existingIndex] = updatedUser
            } else {
              // 不存在则添加到 onlineUsers 数组中
              this.onlineUsers.push(updatedUser)
            }
            // 替换数组引用以触发 MobX 的响应式更新
            this.onlineUsers = [...this.onlineUsers]
          }
        }
      })
      .catch((err) => {
        console.error(`更新用户 ${userIds} 信息失败:`, err)
      })
  }

  fetchAdminList = (): Promise<any> => {
    return chatroomManager
      .fetchAdminList(this.roomId)
      .then((res) => {
        if (res.code !== 200) {
          console.error('error', `${res.message}${res.code}`)
          return Promise.reject(new Error(`${res.message}${res.code}`))
        }
        const { records } = res.data
        // 更新数据
        this.adminList = records ?? []
        this.adminListFiltered = records ?? []
        return records // resolve 返回管理列表数据
      })
      .catch((error) => {
        console.error(`fetchAdminList error ${error}`)
        return Promise.reject(error)
      })
  }

  fetchBlockList = (): Promise<any> => {
    return chatroomManager
      .fetchBlackList(this.roomId)
      .then((res) => {
        if (res.code !== 200) {
          console.error('error', `${res.message}${res.code}`)
          return Promise.reject(new Error(`${res.message}${res.code}`))
        }
        const records = res.data
        // 更新数据
        this.blockList = records ?? []
        this.blockListFiltered = records ?? []
        return records // resolve 返回黑名单数据
      })
      .catch((error) => {
        console.error(`fetchBlackList error ${error}`)
        return Promise.reject(error)
      })
  }

  /**
   * @description 显示边框闪烁效果
   * @param {number} duration - 显示时长（毫秒）
   */
  showFlash = (duration: number = 700) => {
    // 获取所有需要闪烁的容器
    const containers = document.querySelectorAll('.trade-flash-container')

    // 为每个容器添加动画类
    containers.forEach((container) => {
      container.classList.add('animate-border-flash')
    })

    // 定时移除动画类
    setTimeout(() => {
      containers.forEach((container) => {
        container.classList.remove('animate-border-flash')
      })
    }, duration)
  }
  /**
   * @description 清理弹幕管理器，在组件卸载或离开聊天室时调用
   */
  clearDanmuManager = () => {
    if (this.danmuManager) {
      this.danmuManager.unmount()
      this.danmuManager = null
    }
  }

  /**
   * @description 设置弹幕管理器实例
   * @param {Manager<IDanmuMessage, IStatuses>} manager - 弹幕管理器实例
   */
  setDanmuManager = (manager: Manager<any, IStatuses>) => {
    this.danmuManager = manager
  }
}
