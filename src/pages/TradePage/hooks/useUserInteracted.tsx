import React, { createContext, useState, useEffect, useContext } from 'react';
export const UserInteractionContext = createContext({
  hasUserInteracted: false,
  setUserInteracted: (value: boolean) => {},
});
export const UserInteractionProvider = ({ children }: { children: React.ReactNode }) => {
  const [hasUserInteracted, setUserInteracted] = useState(false);
  useEffect(() => {
    const handleInteraction = () => {
      // 一旦有交互，则更新状态，并移除监听，防止重复更新
      setUserInteracted(true);
      window.removeEventListener('click', handleInteraction);
      window.removeEventListener('touchstart', handleInteraction);
    };
    window.addEventListener('click', handleInteraction);
    window.addEventListener('touchstart', handleInteraction);
    return () => {
      window.removeEventListener('click', handleInteraction);
      window.removeEventListener('touchstart', handleInteraction);
    };
  }, []);
  return (
    <UserInteractionContext.Provider value={{ hasUserInteracted, setUserInteracted }}>
      {children}
    </UserInteractionContext.Provider>
  );
};
// 封装一个 Hook 以方便使用
export const useUserInteracted = () => {
  return useContext(UserInteractionContext);
};