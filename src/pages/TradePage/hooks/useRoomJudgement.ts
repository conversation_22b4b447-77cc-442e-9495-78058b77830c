import { MicPosIndex, RoomRole } from '@/managers/chatroomManager'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { userStore } from '@/store'
import { useState, useEffect } from 'react'

export const useRoomJudgement = (): {
  // 自己是否在麦上
  isSelfOnMic: boolean
  // 自己是否在主持麦上
  isOnCompereMic: boolean
  // 是否是房主身份
  isMaster: boolean
  // 是否是房管身份
  isAdmin: boolean
  // 是否是观众
  isCivilian: boolean
} => {
  const store = useStore()
  // 自己是否已经上麦
  const [isSelfOnMic, setIsSelfOnMic] = useState<boolean>(false)
  // 自己是否在主持麦上
  const [isOnCompereMic, setIsOnCompereMic] = useState<boolean>(false)
  // 自己是否是房主
  const [isMaster, setIsMaster] = useState<boolean>(false)
  // 自己是否是房管
  const [isAdmin, setIsAdmin] = useState<boolean>(false)
  // 自己是否是观众
  const [isCivilian, setIsCivilian] = useState<boolean>(false)

  useEffect(() => {
    setIsSelfOnMic(
      Boolean(
        store.micPosInfos.find((item) => item.userId === userStore.info.id),
      ),
    )
    setIsOnCompereMic(
      Boolean(
        store.micPosInfos.find(
          (item) =>
            item.userId === userStore.info.id &&
            item.micPosIndex === MicPosIndex.compere,
        ),
      ),
    )
  }, [store.micPosInfos])

  useEffect(() => {
    setIsMaster(store.roomDetial.roomRole === RoomRole.Master)
    setIsAdmin(store.roomDetial.roomRole === RoomRole.Admin)
    setIsCivilian(store.roomDetial.roomRole === RoomRole.Civilian)
  }, [store.roomDetial.roomRole])

  return {
    isSelfOnMic,
    isOnCompereMic,
    isMaster,
    isAdmin,
    isCivilian,
  }
}
