/**
 * @file useRoleBasedPermission.ts
 * @description 基于角色和麦位的权限判断，返回用户角色和麦位状态的布尔值
 */

import { RoomRole } from '@/managers/chatroomManager'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { userStore } from '@/store'
import { useMemo } from 'react'

/**
 * 角色检查结果
 * @interface RoleCheck
 */
interface RoleCheck {
  /**
   * 是否是房主
   * @description 用户角色为 role_master 时返回 true
   */
  isMaster: boolean
  /**
   * 是否是管理员
   * @description 用户角色为 role_admin 时返回 true
   */
  isAdmin: boolean
  /**
   * 是否是普通用户
   * @description 用户角色为 role_civilian 时返回 true
   */
  isCivilian: boolean
}

/**
 * 麦位检查结果
 * @interface MicPosCheck
 */
interface MicPosCheck {
  /**
   * 是否是主持人
   * @description 用户在主持人麦位（compere）时返回 true
   */
  isCompere: boolean
  /**
   * 是否是嘉宾
   * @description 用户在嘉宾麦位（microphone_owner1-10）时返回 true
   */
  isGuest: boolean
  /**
   * 是否未上麦
   * @description 用户没有上麦时返回 true
   */
  isOffMic: boolean
}

/**
 * 权限判断结果接口定义
 * @interface RolePermissionResult
 */
interface RolePermissionResult {
  /**
   * 角色判断结果  包含用户是否是房主、管理员、普通用户的布尔值判断
   * @description 包含用户是否是房主、管理员、普通用户的布尔值判断
   */
  roleCheck: RoleCheck
  /**
   * 麦位判断结果  包含用户是否是主持人、嘉宾、未上麦的布尔值判断
   * @description 包含用户是否是主持人、嘉宾、未上麦的布尔值判断
   */
  micPosCheck: MicPosCheck
}

/**
 * 基于角色和麦位的权限判断Hook
 * @returns {RolePermissionResult} 包含角色和麦位状态的布尔值判断结果
 */
export const useRoleBasedPermission = (): RolePermissionResult => {
  const store = useStore()
  const userId = userStore.info.id

  return useMemo(() => {
    // 获取当前用户角色
    const currentUserRole = store.getUserRole(userId)
    // console.log('currentUserRole', currentUserRole)
    // 获取当前用户麦位信息
    const currentUserMicPos = store.getUserMicPosInfo(userId)

    // 角色判断
    const roleCheck: RoleCheck = {
      isMaster: currentUserRole === RoomRole.Master,
      isAdmin: currentUserRole === RoomRole.Admin,
      isCivilian: currentUserRole === RoomRole.Civilian,
    }

    // 麦位判断
    const micPosCheck: MicPosCheck = {
      isCompere: currentUserMicPos === 'compere',
      isGuest: currentUserMicPos?.startsWith('microphone_owner') ?? false,
      isOffMic: currentUserMicPos === undefined,
    }

    return {
      roleCheck,
      micPosCheck,
    }
  }, [
    userId,
    store.onlineUsers, // 监听在线用户列表变化（影响角色）
    store.micPosInfos, // 监听麦位信息变化
  ])
}
