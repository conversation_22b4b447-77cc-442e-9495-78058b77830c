import { IS_OPEN_ROOM_LOG } from '@/config'
import {
  chatroomManager,
  ChatroomManagerEvents,
} from '@/managers/chatroomManager'
import { userStore } from '@/store'
import { useEffect, useRef } from 'react'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { ModeSwitchType } from '@/pages/ChatRoom/module/ModeSwitch'

export const useScreenShare = (mode: ModeSwitchType) => {
  const store = useStore()
  const { roomMode, isScreenShareing } = store

  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKScreenShareStateChanged,
      handlerScreenShareStateChanged,
    )

    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKScreenShareLocalPublished,
      handleScreenShareLocalPublished,
    )

    return () => {
      // 确保在组件卸载时移除事件监听器
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKScreenShareStateChanged,
        handlerScreenShareStateChanged,
      )

      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKScreenShareLocalPublished,
        handleScreenShareLocalPublished,
      )
    }
  }, [])

  useEffect(() => {
    handleScreenShareLocalPublished()
    handlerScreenShareStateChanged(true)
  }, [roomMode, isScreenShareing, mode])

  // 房主投屏
  const handleScreenShareLocalPublished = () => {
    if (!videoRef?.current) return
    // 不是房主直接退出
    if (!store.isHost(userStore.info.id)) return

    chatroomManager
      .playScreenShareSelf(videoRef.current)
      .then(() => IS_OPEN_ROOM_LOG && console.log(`投屏播放成功`))
      .catch((err) => IS_OPEN_ROOM_LOG && console.error(`投屏播放失败`, err))
  }

  // 观众接受到屏幕共享
  const handlerScreenShareStateChanged = (status: boolean) => {
    if (!videoRef?.current) return
    // 不是观众直接退出
    if (!status) return

    chatroomManager
      .playScreenShare(videoRef.current)
      .then(() => IS_OPEN_ROOM_LOG && console.log(`观众接受到屏幕共享`))
      .catch(
        (err) => IS_OPEN_ROOM_LOG && console.error(`观众未接受到屏幕共享`, err),
      )
  }

  return { videoRef }
}
