import { Popover } from 'antd'
import { IMicPostion } from '../store/ChatRoomPageStore'
import { useState } from 'react'
import { useStore } from '../TradeComponent'
import {
  ChatroomErrorCode,
  chatroomManager,
  MicPosIndex,
  Mode,
  RoomRole,
} from '@/managers/chatroomManager'
import { t } from 'i18next'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import i18n from '@/i18n'
import { observer } from 'mobx-react-lite'
import { useRoomJudgement } from '../hooks/useRoomJudgement'
import styled from 'styled-components'
import tw from 'twin.macro'

const JoinButton = styled.div`
  ${tw`inline-flex h-fit w-fit items-center justify-center`}
`

interface EmptyMicPosPopoverProps {
  micInfo: IMicPostion
  children: React.ReactNode
}

export const EmptyMicPosPopover = observer(
  ({ micInfo, children }: EmptyMicPosPopoverProps) => {
    const store = useStore()
    const { isSelfOnMic, isMaster, isAdmin } = useRoomJudgement()
    const { toastSuc, toastErr, toastInfo } = useToastMessage()
    const [outerVisible, setOuterVisible] = useState<boolean>(false)

    // 麦位是否是主持人
    const isHostMicPos = micInfo.micPosIndex === MicPosIndex.compere

    // 加入麦位
    async function onJoinClick() {
      const currentRole = store.roomDetial.roomRole
      if (currentRole === RoomRole.Civilian && isHostMicPos) {
        toastInfo(i18n.t('only_host_admin_can_use_this_location'))
      } else if (micInfo?.micPosIndex) {
        const resultGrabMic = await chatroomManager.pkGrabMic(
          store.roomId,
          micInfo.micPosIndex,
        )
        if (resultGrabMic.code !== ChatroomErrorCode.PK_SUCCESS) {
          toastErr(`${resultGrabMic.message}(${resultGrabMic.code})`)
        } else if (micInfo.micPosIndex === MicPosIndex.compere) {
          const roomTagType =
            store.roomMode === Mode.Chat
              ? 3
              : store.roomMode === Mode.Perp
                ? 4
                : 1
          const resultUpdateRoomType = await chatroomManager.updateRoomType(
            store.roomId,
            roomTagType,
            '',
            '',
          )
          if (resultUpdateRoomType.code !== ChatroomErrorCode.PK_SUCCESS) {
            toastErr(`${resultGrabMic.message}(${resultGrabMic.code})`)
          }
        }
      }
    }

    const buildEmptyMicPosContent = (micInfo: IMicPostion) => {
      const css = 'font-lexend cursor-pointer text-white hover:text-gray-300'

      // 房管或房主身份
      const isMasterOrAdmin = isMaster || isAdmin

      // 辅助函数：邀请按钮
      const renderInviteButton = (key: string) => (
        <p
          key={key}
          className={css}
          onClick={() => {
            setOuterVisible(false)
            store.isOpenInviteJoinMicPos = true
          }}
        >
          {t('invite')}
        </p>
      )
      // 辅助函数：关闭麦位按钮
      const renderCloseButton = (key: string) => (
        <p
          key={key}
          className={css}
          onClick={() => {
            setOuterVisible(false)
            if (micInfo?.micPosIndex) {
              const lock = !micInfo.isLock
              chatroomManager
                .lockOrUnlockMic(store.roomId, micInfo.micPosIndex, lock)
                .then(() => {
                  toastSuc(t('success'))
                })
                .catch((error) => {
                  console.error('lockOrUnlockMic error', error)
                })
            }
          }}
        >
          {t('close_mic')}
        </p>
      )
      // 辅助函数：加入麦位按钮
      const renderJoinButton = () => (
        <p
          key="join"
          className={css}
          onClick={async () => {
            setOuterVisible(false)
            onJoinClick()
          }}
        >
          {t('join')}
        </p>
      )

      // 自己已经在麦位上
      if (isSelfOnMic) {
        // 是房管且不是主持人
        if (isMasterOrAdmin && !isHostMicPos) {
          return (
            <>
              {renderInviteButton('invite')}
              {renderCloseButton('close')}
            </>
          )
        }
        return null
      }
      // 自己不在麦位上
      else {
        const elements = [renderJoinButton()]
        // 是房管且不是主持人
        if (isMasterOrAdmin && !isHostMicPos) {
          elements.push(renderInviteButton('invite'))
          elements.push(renderCloseButton('close'))
        }
        return <div>{elements}</div>
      }
    }

    const content = buildEmptyMicPosContent(micInfo)

    // if (!content) {
    //   return <>{children}</>
    // }

    if (isSelfOnMic && (isMaster || isAdmin) && isHostMicPos) {
      return (
        <JoinButton onClick={() => toastInfo('你已在麦位')}>
          {children}
        </JoinButton>
      )
    }

    if (!isSelfOnMic) {
      return <JoinButton onClick={onJoinClick}>{children}</JoinButton>
    }

    return (
      <Popover
        color="#28292F"
        placement="right"
        content={content}
        trigger="click"
        open={outerVisible}
        onOpenChange={setOuterVisible}
      >
        {children}
      </Popover>
    )
  },
)
