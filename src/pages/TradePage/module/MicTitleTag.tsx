import i18n from '@/i18n'
import { IconRoomMicGuest, IconRoomMicHost } from '@/imgs/icons'
import { Flex } from 'antd'
import clsx from 'clsx'

export function Component({
  type,
  isTypeMobile,
}: {
  type: string
  isTypeMobile: boolean
}) {
  return (
    <Flex
      className={clsx(
        'items-center rounded-full bg-[#292A30]',
        isTypeMobile ? 'px-1' : 'px-1 py-0.5',
      )}
    >
      {type === '1' ? (
        <IconRoomMicHost className={clsx(isTypeMobile ? 'size-2' : 'size-4')} />
      ) : (
        <IconRoomMicGuest
          className={clsx(isTypeMobile ? 'size-2' : 'size-4')}
        />
      )}
      <div
        className={clsx(
          'font-lexend text-[#FFB800]',
          isTypeMobile ? 'ml-0.5 text-[7px]' : 'ml-1 mr-1 text-[10px]',
        )}
      >
        {type === '1' ? i18n.t('host') : i18n.t('guest')}
      </div>
    </Flex>
  )
}
Component.displayName = 'MicTitleTag'
export default Component
