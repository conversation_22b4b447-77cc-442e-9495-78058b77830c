import i18n from '@/i18n'
import { IconHomeTagChat, IconRoomTagTrade } from '@/imgs/icons'
import { Flex } from 'antd'
import { observer } from 'mobx-react-lite'

export const Component = observer(({ type }: { type: string }) => {
  return type === '3' ? (
    <Flex className="flex items-center justify-center gap-1 rounded bg-gradient-to-r from-[#FFE3D8] to-[#FF9290] px-1 py-0">
      <IconHomeTagChat className="size-3" />
      <div className="font-lexend text-xs font-semibold text-black">
        {i18n.t('chat')}
      </div>
    </Flex>
  ) : (
    <Flex className="flex items-center justify-center gap-1 rounded bg-gradient-to-r from-[#FFFB00] to-[#FBAB00] px-1 py-0">
      <IconRoomTagTrade className="size-3" />
      <div className="font-lexend text-xs font-semibold text-black">
        {i18n.t('trade')}
      </div>
    </Flex>
  )
})

Component.displayName = 'RoomTag'

export default Component
