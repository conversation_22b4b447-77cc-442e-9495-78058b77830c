import { Flex, List, Skeleton } from 'antd'
import { observer } from 'mobx-react-lite'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import InfiniteScroll from 'react-infinite-scroll-component'
import { chatroomManager, ChatRoomMsgType } from '@/managers/chatroomManager'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import PKAvatar from '@/components/Customize/PKAvatar'
import i18n from '@/i18n'
import { Modal } from '@/components/Customize/Modal'
import { ButtonBlack } from '@/components/Customize/Button'

export const Component = observer(({ open }: { open: boolean }) => {
  const { toastSuc, toastErr } = useToastMessage()
  const store = useStore()
  const filteredDataSource = store.onlineUsers.filter((user) => {
    return !store.micPosInfos.some((micInfo) => micInfo.userId === user.userId)
  })
  return (
    <Modal
      open={open}
      onCancel={() => (store.isOpenInviteJoinMicPos = false)}
      title={i18n.t('invite_join')}
    >
      <InfiniteScroll
        className="max-h-[330px] no-scroll"
        dataLength={filteredDataSource.length}
        next={() => {}}
        hasMore={false}
        loader={<Skeleton paragraph={{ rows: 2 }} active />}
        scrollableTarget="scrollableDiv"
      >
        <List
          dataSource={filteredDataSource}
          renderItem={(item) => (
            <List.Item key={item.userId}>
              <Flex className="w-full items-center justify-between">
                <Flex className="items-center">
                  <PKAvatar src={item.userThumbnail} className="size-12" />
                  <Flex vertical={true} className="ml-4">
                    <div className="text-lg font-bold text-black">
                      {item.nickname}
                    </div>
                    {/* <div className="mt-1 text-sm font-normal text-[#9293A0]">
                      ID:{item.conciseUserId}
                    </div> */}
                  </Flex>
                </Flex>
                <ButtonBlack
                  onClick={() => {
                    if (item.userId) {
                      chatroomManager
                        .inviteJoin(
                          store.roomId,
                          item.userId,
                          ChatRoomMsgType.InviteJoinMicPosMSG,
                        )
                        .then((res) => {
                          if (res.code !== 200) {
                            toastErr(`${res.message}(${res.code})`)
                            return
                          }
                          toastSuc(i18n.t('invite_join_success'))
                          store.isOpenInviteJoinMicPos = false
                        })
                        .catch(() => {
                          toastErr(i18n.t('invite_fail'))
                        })
                    }
                  }}
                >
                  {i18n.t('invite')}
                </ButtonBlack>
              </Flex>
            </List.Item>
          )}
        />
      </InfiniteScroll>
    </Modal>
  )
})

Component.displayName = 'InviteJoin'

export default Component
