import { useEffect, useState, useRef, useMemo } from 'react'
import { Flex } from 'antd'
import styled from 'styled-components'
import tw from 'twin.macro'
import i18n from '@/i18n'
import InputOtp from '@/components/InputOtp'
import { ButtonBlack } from '@/components/Customize/Button'
import { Modal } from '@/components/Customize/Modal'

// 添加Modal样式
const StyledModal = styled.div`
  .password-modal-wrap .ant-modal-content {
    ${tw`max-h-[90vh] overflow-y-auto`}
  }
`
export function Component({
  open,
  onCancel,
  onConfirm,
}: {
  open: boolean
  onCancel: (e: any) => void
  onConfirm: (lockPasswordValue: string[]) => Promise<any>
}) {
  // 用于存储原始body样式的引用
  const originalBodyStyleRef = useRef<{
    overflow?: string
    position?: string
    top?: string
    width?: string
  }>({})
  const [lockConfirmBtnLoading, setLockConfirmBtnLoading] =
    useState<boolean>(false)
  const [lockPasswordValue, setLockPasswordValue] = useState<string>('')

  const lockConfirmBtnDisabled = useMemo(() => {
    return lockPasswordValue.length !== 4
  }, [lockPasswordValue])

  // 控制InputOtp的渲染时机，避免在Modal关闭时，InputOtp组件仍然存在
  const [showOtp, setShowOtp] = useState(false)

  useEffect(() => {
    if (open) {
      // 延迟显示OTP，等待Modal完全打开
      // const timer = setTimeout(() => {
      //   setShowOtp(true)
      // }, 100)

      // 保存原始body样式
      const bodyStyle = document.body.style
      originalBodyStyleRef.current = {
        overflow: bodyStyle.overflow,
        position: bodyStyle.position,
        top: bodyStyle.top,
        width: bodyStyle.width,
      }

      // 禁止背景滚动，只设置overflow为hidden，不改变定位方式
      document.body.style.overflow = 'hidden'

      // return () => clearTimeout(timer)
    } else {
      // 恢复原始body样式，包括所有属性
      document.body.style.overflow = originalBodyStyleRef.current.overflow || ''
      document.body.style.position = originalBodyStyleRef.current.position || ''
      document.body.style.width = originalBodyStyleRef.current.width || ''
    }

    // 添加键盘事件监听
    const handleKeyboardShow = () => {
      // 只在弹窗打开时才应用fixed定位
      if (open) {
        // 确保在键盘弹出时背景不滚动
        document.body.style.overflow = 'hidden'
        document.body.style.position = 'fixed'
        document.body.style.width = '100%'
        // 保存当前滚动位置，以便在键盘隐藏时恢复
        document.body.style.top = `-${window.scrollY}px`
      }
    }

    // 添加键盘隐藏事件处理
    const handleKeyboardHide = () => {
      // 只在弹窗打开时才恢复样式
      if (open) {
        // 恢复原始position和width，但保持overflow为hidden以防止背景滚动
        document.body.style.position =
          originalBodyStyleRef.current.position || ''
        document.body.style.width = originalBodyStyleRef.current.width || ''
        document.body.style.overflow = 'hidden'

        // 添加一个小延迟来确保页面布局正确恢复
        setTimeout(() => {
          // 滚动到页面顶部以修复头部不被顶上去的问题
          window.scrollTo(0, 0)
        }, 100)
      }
    }

    // 使用touchmove事件来防止iOS上的背景滚动
    const preventScroll = (e: TouchEvent) => {
      // 只在弹窗打开时阻止滚动
      if (
        open &&
        (!e.target || !(e.target as Element).closest('.ant-modal-content'))
      ) {
        e.preventDefault()
      }
    }

    window.addEventListener('resize', handleKeyboardShow)
    // 监听键盘隐藏事件，可以通过window的blur事件或orientationchange事件来模拟
    window.addEventListener('blur', handleKeyboardHide)
    window.addEventListener('orientationchange', handleKeyboardHide)
    document.addEventListener('touchmove', preventScroll, { passive: false })

    return () => {
      window.removeEventListener('resize', handleKeyboardShow)
      window.removeEventListener('blur', handleKeyboardHide)
      window.removeEventListener('orientationchange', handleKeyboardHide)
      document.removeEventListener('touchmove', preventScroll)
      // 确保在组件卸载时恢复原始样式，无论弹窗是否打开
      document.body.style.overflow = originalBodyStyleRef.current.overflow || ''
      document.body.style.position = originalBodyStyleRef.current.position || ''
      document.body.style.width = originalBodyStyleRef.current.width || ''

      // 确保在组件卸载时页面布局正确恢复
      setTimeout(() => {
        window.scrollTo(0, 0)
      }, 100)
    }
  }, [open])

  // 处理OTP值变化
  const handleOtpChange = (value: string) => {
    setLockPasswordValue(value)
    // 不再需要在这里设置 setLockConfirmBtnDisabled，由 useMemo 自动计算
  }

  const handleConfirmClick = async () => {
    setLockConfirmBtnLoading(true)
    try {
      await onConfirm(lockPasswordValue.split(''))
    } catch (error) {
      console.error('onConfirm error:', error)
    } finally {
      setLockConfirmBtnLoading(false)
    }
  }

  return (
    <StyledModal>
      <Modal
        open={open}
        onCancel={onCancel}
        title={i18n.t('vibe_password')}
        maskClosable={false}
        destroyOnClose={true}
        afterOpenChange={(visible) => {
          visible ? setShowOtp(true) : setShowOtp(false)
        }}
        wrapClassName="password-modal-wrap"
        style={{ maxHeight: '90vh' }}
        afterClose={() => {
          // 确保在Modal关闭后恢复原始body样式
          document.body.style.overflow =
            originalBodyStyleRef.current.overflow || ''
          document.body.style.position =
            originalBodyStyleRef.current.position || ''
          document.body.style.width = originalBodyStyleRef.current.width || ''

          // 添加延迟滚动到页面顶部，确保页面布局正确恢复
          setTimeout(() => {
            window.scrollTo(0, 0)
          }, 100)
        }}
      >
        <Flex
          className="items-center justify-center font-lexend"
          vertical={true}
        >
          <div className="mb-[10px] text-sm font-normal text-[#5F606D]">
            {i18n.t('please_enter_the_4th_number_password')}
          </div>

          <div className="mb-8 mt-2 h-10">
            {showOtp && (
              <InputOtp
                autoFocus
                value={lockPasswordValue}
                onChange={handleOtpChange}
              />
            )}
          </div>

          <Flex className="h-10 w-full">
            <ButtonBlack
              disabled={lockConfirmBtnDisabled}
              loading={lockConfirmBtnLoading}
              className="h-full w-full"
              onClick={handleConfirmClick}
            >
              <div>{i18n.t('confirm')}</div>
            </ButtonBlack>
          </Flex>
        </Flex>
      </Modal>
    </StyledModal>
  )
}
Component.displayName = 'SetPassword'
export default Component
