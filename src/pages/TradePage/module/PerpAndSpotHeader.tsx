import { Flex } from 'antd'
import { observer } from 'mobx-react-lite'
import { Mode } from '@/managers/chatroomManager'
import clsx from 'clsx'
import { useState, useEffect } from 'react'
import { useDevice } from '@/hooks/useDevice'
import { RelatedLiveRoomsMobile } from './RelatedLiveRoomsMobile'

const PerpAndSpotHeader = observer(
  ({
    onModeChange,
    initialMode,
  }: {
    onModeChange?: (mode: Mode) => void
    initialMode?: Mode
  }) => {
    const { isMobile } = useDevice()
    const [selectedMode, setSelectedMode] = useState<Mode>(
      initialMode || Mode.Trade,
    )
    const [showRelatedLive, setShowRelatedLive] = useState(false)

    useEffect(() => {
      if (initialMode) {
        setSelectedMode(initialMode)
      }
    }, [initialMode])

    const handleModeChange = (mode: Mode) => {
      setSelectedMode(mode)
      onModeChange?.(mode)
    }

    const handleRelatedLiveClick = () => {
      setShowRelatedLive(true)
    }

    const handleCloseRelatedLive = () => {
      setShowRelatedLive(false)
    }

    return (
      <>
        <Flex className="h-[40px] w-full items-center justify-between rounded-[8px] bg-[#292929] px-1 py-1">
          <Flex className="items-center">
            <Flex
              className={clsx(
                'h-full w-[100px] cursor-pointer items-center justify-center rounded-[6px] text-[14px] font-bold',
                selectedMode === Mode.Perp
                  ? 'bg-[#1B1B1B] text-white'
                  : 'text-[#979797] hover:text-[#AAAACBB]',
              )}
              onClick={() => handleModeChange(Mode.Perp)}
            >
              Perpetuals
            </Flex>
            <Flex
              className={clsx(
                'h-full w-[100px] cursor-pointer items-center justify-center rounded-[6px] text-[14px] font-bold',
                selectedMode === Mode.Trade
                  ? 'bg-[#1B1B1B] text-white'
                  : 'text-[#979797] hover:text-[#AAAACBB]',
              )}
              onClick={() => handleModeChange(Mode.Trade)}
            >
              Spot
            </Flex>
          </Flex>

          {isMobile && (
            <Flex
              className="h-[32px] cursor-pointer items-center justify-center rounded-[6px] bg-[#1B1B1B] px-3"
              onClick={handleRelatedLiveClick}
            >
              <span className="text-[12px] font-bold text-white">
                Related live
              </span>
            </Flex>
          )}
        </Flex>

        {isMobile && (
          <RelatedLiveRoomsMobile
            visible={showRelatedLive}
            onClose={handleCloseRelatedLive}
            modeType={selectedMode === Mode.Trade ? 'trade' : 'perp'}
          />
        )}
      </>
    )
  },
)

export default PerpAndSpotHeader
