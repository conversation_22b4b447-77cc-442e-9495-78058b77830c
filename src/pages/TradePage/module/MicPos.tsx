import React from 'react'
import { observer } from 'mobx-react-lite'
import {
  IconRoomMicBan,
  IconRoomMicEmpty,
  IconRoomMicMuted,
  IconRoomSelfMicOff,
  IconRoomSelfMicOn,
  SvgIconTwitterXRoom,
} from '@/imgs/icons'
import { Flex, Popover } from 'antd'
import { IMicPostion } from '../store/ChatRoomPageStore'
import MicTitleTag from './MicTitleTag'
import {
  chatroomManager,
  MicPosIndex,
  Mode,
  RoomRole,
} from '@/managers/chatroomManager'
import { userStore } from '@/store'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import CombinedUserPopover from '@/pages/ChatRoom/module/CombinedUserPopover'
import PKAvatar from '@/components/Customize/PKAvatar'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { t } from 'i18next'
import clsx from 'clsx'
import { useDevice } from '@/hooks/useDevice'
import { EmptyMicPosPopover } from '@/pages/ChatRoom/module/EmptyMicPosPopover'

export interface MicPosProps {
  micPosInfo: IMicPostion
  className?: string
}

export const Component: React.FC<MicPosProps> = observer(
  ({ micPosInfo, className }) => {
    const store = useStore()
    const { isMobile } = useDevice()

    const isTradeOrGame =
      [Mode.Trade, Mode.Perp].includes(store.roomMode) || store.isScreenShareing

    const isTypeMobile = isMobile || isTradeOrGame

    return (
      <Flex
        className={clsx(
          'max-h-1/3 max-w-1/3 relative flex h-full min-h-16 w-full cursor-pointer flex-col items-center justify-center rounded-lg bg-[#1A1B1E] px-1 py-6',
          className,
        )}
      >
        <Flex
          className={clsx(
            'absolute left-0 top-0 h-5 w-full justify-between',
            isTypeMobile ? 'p-1' : 'p-2',
          )}
        >
          <BuildTags micInfo={micPosInfo} isTypeMobile={isTypeMobile} />
          <BuildSelfMuteSwitch
            micInfo={micPosInfo}
            isTypeMobile={isTypeMobile}
          />
        </Flex>
        <Flex
          vertical
          className="mt-1 w-full flex-1 items-center justify-center"
        >
          <BuildHeaderArea micInfo={micPosInfo} />
          <BuildNickname micInfo={micPosInfo} isTypeMobile={isTypeMobile} />
        </Flex>
      </Flex>
    )
  },
)

Component.displayName = 'MicPos'

/**
 * 麦位昵称
 * @param micInfo
 * @returns
 */
const BuildNickname = observer(
  ({
    micInfo,
    isTypeMobile,
  }: {
    micInfo: IMicPostion
    isTypeMobile: boolean
  }) => {
    return (
      <Flex className={clsx('items-center', isTypeMobile ? 'mt-1' : 'mt-2')}>
        {micInfo.userId &&
          (micInfo.isMuted ? (
            <IconRoomMicMuted className="h-[16px] w-[16px]" />
          ) : null)}
        <div
          className={clsx(
            'ml-1 font-lexend font-bold',
            isTypeMobile ? 'text-xs' : 'text-base',
          )}
        >
          {micInfo.isLock ? (
            <div className="text-[#9293A0]">{t('mic_closed')}</div>
          ) : micInfo.userId ? (
            <Flex align="center">
              <span>{micInfo.nickname || 'unknow'}</span>
              {micInfo?.twitterInfo?.id && (
                <a
                  href={`https://x.com/${micInfo.twitterInfo.screenName}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={(e) => e.stopPropagation()}
                >
                  <SvgIconTwitterXRoom className="ml-2 h-4 w-4 cursor-pointer" />
                </a>
              )}
            </Flex>
          ) : micInfo.micPosIndex === MicPosIndex.compere ? (
            <div className="text-[#9293A0]">{t('host')}</div>
          ) : (
            <div className="text-[#9293A0]">{t('join')}</div>
          )}
        </div>
      </Flex>
    )
  },
)

/**
 * 麦位标签
 * @param micInfo
 * @returns
 */
const BuildTags = observer(
  ({
    micInfo,
    isTypeMobile,
  }: {
    micInfo: IMicPostion
    isTypeMobile: boolean
  }) => {
    return micInfo.userId ? (
      <Flex className="w-full items-start">
        <MicTitleTag
          isTypeMobile={isTypeMobile}
          type={micInfo.micPosIndex === MicPosIndex.compere ? '1' : '2'}
        />
      </Flex>
    ) : null
  },
)

/**
 * 麦位禁言开关
 * @param micInfo
 * @returns
 */
const BuildSelfMuteSwitch = observer(
  ({
    micInfo,
    isTypeMobile,
  }: {
    micInfo: IMicPostion
    isTypeMobile: boolean
  }) => {
    const store = useStore()

    const iconSize = clsx(isTypeMobile ? 'size-6' : 'size-8')

    // 房管禁言
    if (micInfo.userId && micInfo.isMutedByAdmin) {
      return (
        micInfo.userId &&
        micInfo.userId === userStore.info.id && (
          <IconRoomSelfMicOn
            className={iconSize}
            onClick={() => {
              console.log('已经被房管禁言')
            }}
          />
        )
      )
    }

    // 自己麦位
    if (micInfo.userId && !micInfo.isMutedByAdmin) {
      const selfMicPosInfo = store.micPosInfos.find(
        (item) => item.userId === userStore.info.id,
      )
      return (
        micInfo.userId &&
        micInfo.userId === userStore.info.id && (
          <Flex>
            {selfMicPosInfo?.isMuted === false ? (
              <IconRoomSelfMicOff
                className={iconSize}
                onClick={() => store.turnOffSelfMic()}
              />
            ) : (
              <IconRoomSelfMicOn
                className={iconSize}
                onClick={() => store.turnOnSelfMic()}
              />
            )}
          </Flex>
        )
      )
    }

    return null
  },
)

/**
 * 麦位头像
 * @param micInfo
 * @returns
 */
const BuildHeaderArea = observer(({ micInfo }: { micInfo: IMicPostion }) => {
  const store = useStore()
  const { toastSuc } = useToastMessage()

  // 锁麦
  const buildLockMicPosContent = (micPosInfo: IMicPostion) => {
    return (
      micPosInfo &&
      micPosInfo.isLock === true &&
      (store.roomDetial.roomRole === RoomRole.Master ||
        store.roomDetial.roomRole === RoomRole.Admin) && (
        <div>
          <p
            className="cursor-pointer font-lexend text-white hover:text-gray-300"
            onClick={() => {
              let lock = micPosInfo.isLock === true
              if (micPosInfo.isLock) {
                lock = false
              }
              chatroomManager
                .lockOrUnlockMic(store.roomId, micPosInfo.micPosIndex, lock)
                .then(() => {
                  toastSuc('Success')
                })
                .catch((error) => {
                  console.error('handleClose error', error)
                })
            }}
          >
            {t('open_mic')}
          </p>
        </div>
      )
    )
  }
  const buildLock = () => {
    return (
      <Popover
        color="#28292F"
        placement="right"
        trigger="click"
        content={buildLockMicPosContent(micInfo)}
      >
        <IconRoomMicBan className="max-w-30 w-1/3 min-w-10 rounded-full" />
      </Popover>
    )
  }
  const buildNotEmpty = () => {
    return (
      <CombinedUserPopover key={micInfo.userId} userId={micInfo.userId}>
        <div className="relative flex w-full items-center justify-center rounded-full">
          <PKAvatar
            src={micInfo.userThumbnail}
            className="max-w-30 w-1/3 min-w-10"
            isMutedByAdmin={micInfo.isMutedByAdmin}
            isSpeaking={micInfo.isSpeaking && micInfo.isMuted === false}
          />
        </div>
      </CombinedUserPopover>
    )
  }
  const buildEmpty = () => {
    return (
      <EmptyMicPosPopover micInfo={micInfo}>
        <IconRoomMicEmpty className="max-w-30 w-1/3 min-w-10 rounded-full" />
      </EmptyMicPosPopover>
    )
  }

  return micInfo.isLock
    ? buildLock()
    : micInfo.userId
      ? buildNotEmpty()
      : buildEmpty()
})

export default Component
