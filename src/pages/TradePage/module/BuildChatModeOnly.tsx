import { Flex } from 'antd'
import { observer } from 'mobx-react-lite'
import AudioRoom from '@/pages/ChatRoom/module/AduioRoom'
import ChatRoom from '@/pages/ChatRoom/module/ChatRoom'
import { ModeSwitch } from '@/pages/ChatRoom/module/ModeSwitch'
import OnlineUserList from '@/pages/ChatRoom/module/OnlineUserList'
import { useDevice } from '@/hooks/useDevice'

export const BuildChatModeOnly = observer(() => {
  const { isMobile } = useDevice()

  if (isMobile) {
    return (
      <div className="flex h-full flex-col">
        <div className="h-[40px] w-full">
          <ModeSwitch type="mobile" />
          <OnlineUserList type="hidden" />
        </div>
        <div className="flex h-[calc(100%-230px-40px)] w-full">
          <AudioRoom />
        </div>
        <div className="flex h-[230px] w-full">
          <ChatRoom />
        </div>
      </div>
    )
  }

  return (
    <Flex className="h-full flex-grow">
      <Flex className="h-full w-44">
        <OnlineUserList type="convenient" />
      </Flex>

      <Flex className="flex-1 flex-grow">
        <AudioRoom />
      </Flex>

      <Flex className="h-[calc(100vh-64px)] w-[360px]">
        <ChatRoom />
      </Flex>

      <Flex vertical={true} className="w-16 border-l border-[#313131]">
        <ModeSwitch type="desktop" />
      </Flex>
    </Flex>
  )
})
