import i18n from '@/i18n'
import { observer } from 'mobx-react-lite'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import {
  ChatroomCommonPush<PERSON>ey,
  chatroomManager,
  ChatroomManagerEvents,
  Mode,
} from '@/managers/chatroomManager'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import { SEARCH_TOKEN_RETURN } from '@/api/interface/SEARCH_TOKEN'
import { perpsStore, userStore } from '@/store'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { lodash } from '@/utils'
import { useDevice } from '@/hooks/useDevice'
import clsx from 'clsx'
import SymbolIcon from '@/pages/Trade/module/SymbolIcon'
import { HlAvatar } from '@/pages/Perp/module/utils'

export const ViewButton = observer(() => {
  const store = useStore()
  const initedRef = useRef(false)
  const { isMobile } = useDevice()
  const { toastSuc } = useToastMessage()
  const { isEmpty } = lodash

  const [isShow, setIsShow] = useState(false)
  const [tagType, setTagType] = useState<'1' | '3' | '4'>('3') // 默认是聊天
  const [coin, setCoin] = useState<string>('')
  const [crypto, setCrypto] = useState<SEARCH_TOKEN_RETURN['data']>(
    {} as SEARCH_TOKEN_RETURN['data'],
  )

  // 是否是永续合约
  const isPerp = useMemo(() => tagType === '4', [tagType])

  useEffect(() => {
    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKRoomTagContentChanged,
      (data: { key: string; value: string }) => {
        if (data.key === ChatroomCommonPushKey.CurrCrypto) {
          if (isEmpty(data.value)) {
            setIsShow(false)
          } else {
            const result = JSON.parse(data.value)
            if (result?.type === Mode.Perp) {
              setCoin(result?.coin || 'BTC')
            } else {
              setCrypto(JSON.parse(data.value))
            }
            const hostMicPosInfo = store.micPosInfos[0]
            if (
              isEmpty(hostMicPosInfo.userId) === false &&
              hostMicPosInfo.userId !== userStore.info.id
            ) {
              setIsShow(true)
            }
          }
        }
      },
    )

    chatroomManager.addEventListener(
      ChatroomManagerEvents.PKRoomTagChanged,
      (data: { key: string; value: '1' | '3' | '4' }) => {
        if (data.key === ChatroomCommonPushKey.RoomTagType) {
          setTagType(data.value)
        }
      },
    )

    if (!initedRef.current && chatroomManager.currCryptoString) {
      const result = JSON.parse(chatroomManager.currCryptoString)
      const hostMicPosInfo = store.micPosInfos[0]
      if (
        isEmpty(hostMicPosInfo.userId) === false &&
        hostMicPosInfo.userId !== userStore.info.id
      ) {
        if (result?.type === Mode.Perp) {
          setCoin(result?.coin || 'BTC')
          setTagType('4')
        } else {
          setCrypto(result)
        }
        setIsShow(true)
      }
      initedRef.current = true
    }

    return () => {
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKRoomTagContentChanged,
      )
      chatroomManager.removeEventListener(
        ChatroomManagerEvents.PKRoomTagChanged,
      )
    }
  }, [chatroomManager])

  function onTradeView() {
    if (isPerp) {
      toastSuc(i18n.t('returned_to_host_view'))
      perpsStore.init.setCoin(coin)
      store.handleModeChanged(Mode.Perp)
    } else {
      const currCrypto = crypto?.contract_address
        ? crypto
        : tradeStore.currCrypto
      toastSuc(i18n.t('returned_to_host_view'))
      tradeStore.setCurrCrypto(currCrypto)
      store.handleModeChanged(Mode.Trade)
    }
  }

  const buildTradeViewBtn = useMemo(() => {
    if (isPerp) {
      const tokenName = `${coin}-USD`
      return i18n.t('host_trade_token', { tokenName })
    } else {
      const currCrypto = crypto?.contract_address
        ? crypto
        : tradeStore.currCrypto
      const { symbol } = currCrypto
      const tokenName = `$${symbol}`
      return i18n.t('host_trade_token', { tokenName })
    }
  }, [isPerp, coin, crypto])

  return (
    isShow && (
      <div
        onClick={onTradeView}
        className={clsx(
          'inline-flex cursor-pointer items-center justify-center gap-1 rounded bg-[#ffb700]/20',
          isMobile
            ? 'w-full p-1 leading-3'
            : 'mr-4 h-[42px] p-2.5 text-sm leading-normal',
        )}
      >
        <div>
          {isPerp ? (
            <HlAvatar coin={coin} className="size-5" />
          ) : (
            <SymbolIcon
              chainId={crypto.chainId}
              image_url={crypto.image_url}
              name={crypto.name}
              size={isMobile ? 18 : 24}
            />
          )}
        </div>
        <div
          className={clsx(
            'break-all text-[#ffb700]',
            isMobile ? 'text-[10px]' : 'text-sm font-medium',
          )}
        >
          {buildTradeViewBtn}
        </div>
      </div>
    )
  )
})
