import TradePlate from '@/pages/Trade/TradePlate'
import { Flex } from 'antd'
import { observer } from 'mobx-react-lite'
import { ModeSwitch } from '@/pages/ChatRoom/module/ModeSwitch'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { useDevice } from '@/hooks/useDevice'
import { RelatedLiveRooms } from './RelatedLiveRooms'

export const BuildTradeModeOnly = observer(() => {
  const store = useStore()
  const { isMobile } = useDevice()

  if (isMobile) {
    return (
      <div className="flex h-full flex-col">
        {/* <div className="h-[40px] w-full">
          <ModeSwitch type="mobile" />
        </div> */}
        <TradePlate roomId={store.roomDetial.roomId ?? ''} />
      </div>
    )
  }

  return (
    <Flex className="h-full flex-grow">
      <Flex className="h-full w-[220px]">
        <RelatedLiveRooms modeType="trade" />
      </Flex>

      {/* 麦克位与聊天部分 */}
      {/* <Flex vertical={true} className="w-[360px]">
        <Flex className="h-[360px] w-full border-r border-[#313131]">
          <AudioRoom />
        </Flex>

        <Flex className="h-[calc(100vh-64px-360px)] flex-grow overflow-hidden">
          <ChatRoom />
        </Flex>
      </Flex> */}

      {/* PC交易面板 */}
      <Flex className="trade-flash-container h-[calc(100vh-64px)] flex-grow">
        <TradePlate roomId={store.roomDetial.roomId ?? ''} />
      </Flex>

      {/* <Flex vertical={true} className="w-16 border-l border-[#313131]">
        <ModeSwitch type="desktop" />
      </Flex> */}
    </Flex>
  )
})
