import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { IconRoomLock, IconRoomUnlock } from '@/imgs/icons'
import { chatroomManager, RoomRole } from '@/managers/chatroomManager'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { t } from 'i18next'
import { observer } from 'mobx-react-lite'
import { useRoomButtonModal } from '@/pages/ChatRoom/module/button/hooks'
import SetPassword from '@/pages/ChatRoom/module/SetPassword'
import { RoomButton } from './RoomButton'
import { useDevice } from '@/hooks/useDevice'

/**
 * 上锁按钮
 */
export const ButtonLock = observer(({ className }: { className?: string }) => {
  const store = useStore()
  const { toastErr, toastSuc } = useToastMessage()
  const { setVisibleLock, setVisibleMore } = useRoomButtonModal()
  const { isMobile } = useDevice()
  const handleLockClicked = () => {
    if (store.isLocked) {
      chatroomManager
        .updateRoomPassword(store.roomId.toString())
        .then((res) => {
          if (res.code !== 200) {
            toastErr(`${res.message}${res.code}`)
          } else {
            toastSuc(t('success'))
            store.isLocked = !store.isLocked
          }
        })
        .catch((error) => {
          console.error(`[AudioRoomHeader] error: ${error}`)
        })
    } else {
      setVisibleLock(true)
      setVisibleMore(false)
    }
  }

  // 普通用户不能设置
  if (store.roomDetial.roomRole === RoomRole.Civilian) {
    return null
  }

  return (
    <RoomButton
      icon={
        store.isLocked ? (
          <IconRoomLock className="size-5" onClick={handleLockClicked} />
        ) : (
          <IconRoomUnlock className="size-5" onClick={handleLockClicked} />
        )
      }
      label={store.isLocked ? t('chatroom_unlock') : t('chatroom_lock')}
      onClick={handleLockClicked}
      deviceType={isMobile ? 'mobile' : 'pc'}
    />
  )
})

export const ButtonLockModal = observer(() => {
  const { visibleLock, setVisibleLock } = useRoomButtonModal()
  const { toastErr, toastSuc } = useToastMessage()
  const store = useStore()

  const handlePwdConfrim = async (password: string[]) => {
    if (password && password.length === 4) {
      chatroomManager
        .updateRoomPassword(store.roomId, password.join(''))
        .then((res) => {
          if (res.code !== 200) {
            toastErr(`${res.message}${res.code}`)
          } else {
            toastSuc(t('success'))
          }
          setVisibleLock(false)
        })
        .catch((error) => {
          console.error(`[AudioRoomHeader] error: ${error}`)
          setVisibleLock(false)
        })
    }
  }

  return (
    <SetPassword
      open={visibleLock}
      onCancel={() => setVisibleLock(false)}
      onConfirm={handlePwdConfrim}
    />
  )
})
