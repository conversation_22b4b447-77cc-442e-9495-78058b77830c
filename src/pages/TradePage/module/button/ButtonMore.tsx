import i18n from '@/i18n'
import { IconRoomMore } from '@/imgs/icons'
import { Popover, Flex, Divider } from 'antd'
import { Popup } from 'antd-mobile'
import { observer } from 'mobx-react-lite'
import { ButtonColose } from '@/pages/ChatRoom/module/button/ButtonColose'
import { ButtonExit } from '@/pages/ChatRoom/module/button/ButtonExit'
import { ButtonRetract } from '@/pages/ChatRoom/module/button/ButtonRetract'
import clsx from 'clsx'
import { ButtonAdmin } from './ButtonAdmin'
import { ButtonLock } from './ButtonLock'
import { ButtonSetting } from './ButtonSetting'
import { useRoomButtonModal } from './hooks'
import ButtonDanmu from './ButtonDanmu'
import { RoomButton } from './RoomButton'
import ButtonClearMsg from './ButtonClearMsg'

export const ButtonMoreDesktop = observer(
  ({ className }: { className?: string }) => {
    const { visibleMore, setVisibleMore } = useRoomButtonModal()

    return (
      <Popover
        trigger={['click', 'hover']}
        arrow={false}
        color="#1a1b1e"
        placement="bottom"
        open={visibleMore}
        onOpenChange={() => setVisibleMore(!visibleMore)}
        content={
          <div className="grid grid-cols-3 gap-2.5 p-2.5 [&>*]:min-h-[60px] [&>*]:min-w-20">
            {/* 设置按钮 */}
            <ButtonSetting />
            {/* 关闭按钮 */}
            <ButtonColose />
            {/* 退出按钮 */}
            <ButtonExit />
            {/* 收回按钮 */}
            <ButtonRetract />
          </div>
        }
      >
        <Flex
          className={clsx(
            'mr-2 cursor-pointer flex-col items-center justify-center',
            className,
          )}
        >
          <RoomButton
            icon={<IconRoomMore className="size-5" />}
            label={i18n.t('more')}
            className={className}
          />
        </Flex>
      </Popover>
    )
  },
)

export const ButtonMoreMobile = observer(
  ({ className }: { className?: string }) => {
    const { visibleMore, setVisibleMore } = useRoomButtonModal()

    return (
      <>
        <Flex
          className={clsx(
            'cursor-pointer flex-col items-center justify-center',
            className,
          )}
          onClick={() => setVisibleMore(true)}
        >
          <RoomButton
            icon={<IconRoomMore className="size-[30px]" />}
            label={i18n.t('more')}
            className={className}
            iconClassName="!bg-gray-600 !p-0"
            showLabel={false}
          />
        </Flex>

        <Popup
          visible={visibleMore}
          onClose={() => setVisibleMore(false)}
          closeOnMaskClick
          position="top"
          bodyStyle={{
            backgroundColor: '#1a1b1e',
            borderRadius: '0 0 16px 16px',
          }}
        >
          <div className="flex flex-col p-4 px-3 pb-6 text-white">
            <h1 className="my-3 mb-6 text-center text-16 font-400 text-white">
              {i18n.t('more')}
            </h1>
            <section className="grid grid-cols-4 gap-y-7">
              {/* 弹幕按钮 */}
              <ButtonDanmu />
              {/* 清空消息按钮 */}
              <ButtonClearMsg />
              {/* 上锁按钮 */}
              <ButtonLock />
              {/* 黑名单按钮 */}
              <ButtonAdmin type="blacklist" />
              {/* 管理员按钮 */}
              <ButtonAdmin type="adminlist" />
              {/* 设置按钮 */}
              <ButtonSetting />
            </section>
            <div className="px-[8%]">
              <Divider className="my-5 bg-gray-600" />
            </div>
            <section className="grid grid-cols-4 gap-y-7">
              {/* 关闭按钮 */}
              <ButtonColose />
              {/* 退出按钮 */}
              <ButtonExit />
              {/* 收回按钮 */}
              <ButtonRetract />
            </section>
          </div>
        </Popup>
      </>
    )
  },
)
