import { SvgIconRoomDanmuDisabled, SvgIconRoomDanmuEnabled } from '@/imgs/icons'
import { t } from 'i18next'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { observer } from 'mobx-react-lite'
import { RoomButton } from './RoomButton'
import { useDevice } from '@/hooks/useDevice'
import { useRoomButtonModal } from './hooks'
/**
 * @component ButtonDanmu
 * @description 弹幕开关按钮组件
 */
const ButtonDanmu = observer(() => {
  const store = useStore()
  const { isMobile } = useDevice()
  const { setVisibleMore } = useRoomButtonModal()
  // 处理弹幕显示状态变更
  const handleVisibilityChange = (visible: boolean) => {
    store.isDanmuVisible = visible
    setVisibleMore(false)
  }

  return (
    <RoomButton
      icon={
        !store.isDanmuVisible ? (
          <SvgIconRoomDanmuDisabled className="size-5" />
        ) : (
          <SvgIconRoomDanmuEnabled className="size-5" />
        )
      }
      deviceType={isMobile ? 'mobile' : 'pc'}
      label={
        store.isDanmuVisible
          ? t('danmu_toggle_disable')
          : t('danmu_toggle_enable')
      }
      onClick={() => handleVisibilityChange(!store.isDanmuVisible)}
    />
  )
})

export default ButtonDanmu
