import { observer } from 'mobx-react-lite'
import { useRoomButtonModal } from '@/pages/ChatRoom/module/button/hooks'
import i18n from '@/i18n'
import { IconRoomAdmin, IconRoomBlacklist } from '@/imgs/icons'
import { chatroomManager, IOnlineUserItem } from '@/managers/chatroomManager'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import PKAvatar from '@/components/Customize/PKAvatar'
import PKSearchInput from '@/components/Customize/PKSearchInput'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { Flex, Skeleton, List } from 'antd'
import { InfiniteScroll } from 'antd-mobile'
import { useState, useEffect } from 'react'
import { ButtonBlack } from '@/components/Customize/Button'
import { Modal } from '@/components/Customize/Modal'
import { useRoleBasedPermission } from '../../hooks/useRoleBasedPermission'
import { RoomButton } from './RoomButton'
import { useDevice } from '@/hooks/useDevice'
/**
 * 管理员/黑名单按钮
 * @param type - adminlist：管理员按钮，blacklist：黑名单按钮
 */
export const ButtonAdmin = observer(
  ({
    type,
    className,
  }: {
    type: 'adminlist' | 'blacklist'
    className?: string
  }) => {
    const { isMobile } = useDevice()
    const isAdminList = type === 'adminlist'
    const { setVisibleAdmin, setAdminModel, setVisibleMore } =
      useRoomButtonModal()

    const handleAdminClicked = () => {
      setAdminModel(isAdminList ? 1 : 2)
      setVisibleAdmin(true)
      setVisibleMore(false)
    }
    const { roleCheck } = useRoleBasedPermission()

    // 普通用户不显示（只有房主和管理员可以看到）
    if (!roleCheck.isMaster && !roleCheck.isAdmin) {
      return null
    }

    // 管理员列表只有房主可以看到
    if (isAdminList && !roleCheck.isMaster) {
      return null
    }

    if (isAdminList) {
      return (
        <RoomButton
          icon={<IconRoomAdmin className="size-5" />}
          label={i18n.t('chatroom_adminlist')}
          onClick={handleAdminClicked}
          className={className}
          deviceType={isMobile ? 'mobile' : 'pc'}
        />
      )
    }

    return (
      <RoomButton
        icon={<IconRoomBlacklist className="size-5" />}
        label={i18n.t('chatroom_blacklist')}
        onClick={handleAdminClicked}
        className={className}
        deviceType={isMobile ? 'mobile' : 'pc'}
      />
    )
  },
)

export const ButtonAdminModal = observer(() => {
  const { visibleAdmin, setVisibleAdmin, adminModel } = useRoomButtonModal()
  const { toastErr, toastSuc } = useToastMessage()
  const store = useStore()

  const [abList, setAbList] = useState<IOnlineUserItem[]>([])
  const [abListFiltered, setAbListFiltered] = useState<IOnlineUserItem[]>([])
  const [abSearchValue, setAbSearchValue] = useState('')

  useEffect(() => {
    if (adminModel === 1) {
      setAbList(store.adminList)
      setAbListFiltered(store.adminListFiltered)
    } else if (adminModel === 2) {
      setAbList(store.blockList)
      setAbListFiltered(store.blockListFiltered)
    }
  }, [
    adminModel,
    store.adminList,
    store.adminListFiltered,
    store.blockList,
    store.blockListFiltered,
  ])

  const onSearch = (value: string) => {
    const searchStr = value.trim()
    if (!searchStr) {
      setAbListFiltered(abList)
      return
    }

    const upperSearch = searchStr.toUpperCase()
    // 1. 从 abList 中依据 nickname 搜索
    const filteredByNickname = abList.filter(
      (item: IOnlineUserItem) =>
        item.nickname && item.nickname.toUpperCase().includes(upperSearch),
    )

    // 2. 从 abList 中依据 userId 搜索
    const filteredByUserId = abList.filter(
      (item: IOnlineUserItem) =>
        item.conciseUserId &&
        item.conciseUserId.toUpperCase().includes(upperSearch),
    )
    // 合并并去重（以 userId 为标识），保证 abList 匹配结果优先展示
    const abListMap = new Map<string, IOnlineUserItem>()
    ;[...filteredByNickname, ...filteredByUserId].forEach((item) => {
      if (item.userId) {
        abListMap.set(item.userId, item)
      }
    })
    const abListResults = Array.from(abListMap.values())
    // 3. 从 store.onlineUsers 中依据 nickname 搜索
    const onlineByNickname = store.onlineUsers.filter(
      (item: IOnlineUserItem) =>
        item.nickname && item.nickname.toUpperCase().includes(upperSearch),
    )

    // 4. 从 store.onlineUsers 中依据 userId 搜索
    const onlineByUserId = store.onlineUsers.filter(
      (item: IOnlineUserItem) =>
        item.conciseUserId &&
        item.conciseUserId.toUpperCase().includes(upperSearch),
    )
    // 合并在线用户结果并去重
    const onlineMap = new Map<string, IOnlineUserItem>()
    ;[...onlineByNickname, ...onlineByUserId].forEach((item) => {
      if (item.userId) {
        onlineMap.set(item.userId, item)
      }
    })
    const onlineResults = Array.from(onlineMap.values())
    // 5. 将 abList 中匹配到的结果优先展示，其次是在线用户中匹配到且不在 abList 匹配结果中的数据
    const abUserIds = new Set(abListResults.map((item) => item.userId))
    const finalResults = [
      ...abListResults,
      ...onlineResults.filter((item) => !abUserIds.has(item.userId)),
    ]
    setAbListFiltered(finalResults)
  }

  const onClear = () => setAbListFiltered(abList)

  const isAdmin = (item: IOnlineUserItem) => {
    return store.adminList.some((admin) => admin.userId === item.userId)
  }
  const isBlock = (item: IOnlineUserItem) => {
    return store.blockList.some((blocked) => blocked.userId === item.userId)
  }

  return (
    <Modal
      open={visibleAdmin}
      onCancel={() => {
        setVisibleAdmin(false)
        setAbListFiltered(abList)
        setAbSearchValue('')
      }}
      afterClose={() => {
        setAbSearchValue('')
      }}
      title={
        adminModel === 1
          ? i18n.t('chatroom_adminlist')
          : i18n.t('chatroom_blacklist')
      }
    >
      <Flex className="w-full items-center justify-center" vertical={true}>
        <PKSearchInput
          defaultValue={abSearchValue}
          className="h-[40px] w-full border border-[#DADBE1]"
          placeholder="Search"
          onSearch={onSearch}
          onClear={onClear}
          onChange={(val) => setAbSearchValue(val)}
        />
        <div
          className="mt-4 w-full items-start"
          style={{ maxHeight: '20.625rem', overflowY: 'auto' }}
        >
          <InfiniteScroll
            dataLength={abListFiltered.length}
            next={() => {}}
            hasMore={false}
            loader={<Skeleton paragraph={{ rows: 2 }} active />}
            scrollableTarget="scrollableDiv"
          >
            <List
              className="w-full items-center justify-between"
              dataSource={abListFiltered}
              renderItem={(item) => (
                <List.Item key={item.userId}>
                  <Flex className="w-full items-center justify-between">
                    <Flex>
                      <PKAvatar src={item.userThumbnail} className="size-12" />
                      <Flex vertical={true} className="ml-4">
                        <div className="text-lg font-bold text-black">
                          {item.nickname}
                        </div>
                        <div className="mt-1 text-sm font-normal text-[#9293A0]">
                          ID:{item.conciseUserId}
                        </div>
                      </Flex>
                    </Flex>
                    <ButtonBlack
                      onClick={() => {
                        if (adminModel === 1) {
                          if (isAdmin(item)) {
                            // 如果当前用户已经是管理员，则调用移除管理员接口
                            chatroomManager
                              .removeAdmin(store.roomId, item.userId!)
                              .then((res) => {
                                if (res.code !== 200) {
                                  toastErr(`${res.message}(${res.code})`)
                                  return
                                }
                                store.updateOnlineUserList(
                                  store.roomId,
                                  item.userId!,
                                )
                                store.setAdminList?.(
                                  store.adminList.filter(
                                    (admin) => admin.userId !== item.userId,
                                  ),
                                )
                                store.setAdminListFiltered?.(
                                  store.adminListFiltered.filter(
                                    (admin) => admin.userId !== item.userId,
                                  ),
                                )
                                toastSuc(i18n.t('success'))
                              })
                              .catch((error) => {
                                console.error('removeAdmin error:', error)
                              })
                          } else {
                            // 如果当前用户不在管理员列表中，则调用添加管理员接口
                            chatroomManager
                              .addAdmin(store.roomId, item.userId!)
                              .then((res) => {
                                if (res.code !== 200) {
                                  toastErr(`${res.message}(${res.code})`)
                                  return
                                }
                                store.updateOnlineUserList(
                                  store.roomId,
                                  item.userId!,
                                )
                                store.setAdminList?.([...store.adminList, item])
                                store.setAdminListFiltered?.([
                                  ...store.adminListFiltered,
                                  item,
                                ])
                                toastSuc(i18n.t('success'))
                              })
                              .catch((error) => {
                                console.error('addAdmin error:', error)
                              })
                          }
                        } else if (adminModel === 2) {
                          if (isBlock(item)) {
                            // 如果当前用户已在黑名单中，则调用解除禁言（移除黑名单）接口
                            chatroomManager
                              .removeBlack(store.roomId, [item.userId!], true)
                              .then((res) => {
                                if (res.code !== 200) {
                                  toastErr(`${res.message}(${res.code})`)
                                  return
                                }
                                store.setBlockList?.(
                                  store.blockList.filter(
                                    (block) => block.userId !== item.userId,
                                  ),
                                )
                                store.setBlockListFiltered?.(
                                  store.blockListFiltered.filter(
                                    (block) => block.userId !== item.userId,
                                  ),
                                )
                                toastSuc(i18n.t('success'))
                              })
                              .catch((error) => {
                                console.error('removeBlack error:', error)
                              })
                          } else {
                            setAbListFiltered(abList)
                            store.clickedNeedBlackUserId = item.userId!
                            store.isOpenAddBlackListModel = true
                          }
                        }
                      }}
                    >
                      <div>
                        {adminModel === 1
                          ? isAdmin(item)
                            ? i18n.t('remove')
                            : i18n.t('make_admin')
                          : isBlock(item)
                            ? i18n.t('unblock')
                            : i18n.t('kick_out')}
                      </div>
                    </ButtonBlack>
                  </Flex>
                </List.Item>
              )}
            />
          </InfiniteScroll>
        </div>
      </Flex>
    </Modal>
  )
})
