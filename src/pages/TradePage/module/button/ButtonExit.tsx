import { useRoute } from '@/hooks/useRoute'
import { IconRoomExit } from '@/imgs/icons'
import { observer } from 'mobx-react-lite'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import i18n from '@/i18n'
import {
  chatroomManager,
  ChatroomErrorCode,
  Mode,
} from '@/managers/chatroomManager'
import { userStore } from '@/store'
import { useRoomButtonModal } from '@/pages/ChatRoom/module/button/hooks'
import { RoomButton } from './RoomButton'
import { useDevice } from '@/hooks/useDevice'
/**
 * 退出按钮
 */
export const ButtonExit = observer(() => {
  const { path } = useRoute()
  const store = useStore()
  const { setVisibleMore } = useRoomButtonModal()
  const { isMobile } = useDevice()

  const handleExit = async () => {
    if (store.micPosInfos[0].userId === userStore.info.id) {
      const res = await chatroomManager.updateRoomType(store.roomId, 3, '', '')
      if (res.code === ChatroomErrorCode.PK_SUCCESS) {
        store.handleModeChanged(Mode.Chat)
      }
    }
    if (store.selfPosInfoRef) {
      await chatroomManager.pkLeaveMic(
        store.roomId,
        store.selfPosInfoRef.micPosIndex,
      )
    }
    await store.leaveRoom()
    setVisibleMore(false)
    path('/')
  }

  return (
    <RoomButton
      mode={isMobile ? 'standard' : 'simple'}
      icon={<IconRoomExit className="size-5 rotate-90" />}
      label={i18n.t('exit')}
      onClick={handleExit}
      deviceType={isMobile ? 'mobile' : 'pc'}
    />
  )
})
