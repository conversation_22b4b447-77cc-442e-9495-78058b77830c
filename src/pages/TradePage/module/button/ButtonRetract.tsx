import { useRoute } from '@/hooks/useRoute'
import { IconRoomRetract } from '@/imgs/icons'
import { observer } from 'mobx-react-lite'
import i18n from '@/i18n'
import { useRoomButtonModal } from '@/pages/ChatRoom/module/button/hooks'
import { RoomButton } from './RoomButton'
import { useDevice } from '@/hooks/useDevice'
/**
 * 小窗口按钮
 */
export const ButtonRetract = observer(() => {
  const { path } = useRoute()
  const { setVisibleMore } = useRoomButtonModal()
  const { isMobile } = useDevice()
  return (
    <RoomButton
      mode={isMobile ? 'standard' : 'simple'}
      icon={<IconRoomRetract className="size-5" />}
      label={i18n.t('retract')}
      onClick={() => {
        setVisibleMore(false)
        path('/')
      }}
      deviceType={isMobile ? 'mobile' : 'pc'}
    />
  )
})
