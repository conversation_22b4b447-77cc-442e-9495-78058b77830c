import { IconChatClearMsg } from '@/imgs/icons'
import { t } from 'i18next'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { observer } from 'mobx-react-lite'
import { RoomButton } from './RoomButton'
import { chatroomManager } from '@/managers/chatroomManager'
import { ErrorCode } from '@rongcloud/engine'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import i18n from '@/i18n'
import { useRoleBasedPermission } from '../../hooks/useRoleBasedPermission'
import { useDevice } from '@/hooks/useDevice'
import { useRoomButtonModal } from './hooks'
/**
 * @component ButtonClearMsg
 * @description 清空消息按钮组件
 */
const ButtonClearMsg = observer(() => {
  const store = useStore()
  const { isMobile } = useDevice()
  const { toastSuc } = useToastMessage()
  const { roleCheck } = useRoleBasedPermission()
  const { setVisibleMore } = useRoomButtonModal()
  const handleClearMessages = () => {
    chatroomManager
      .clearRoomMsgs(store.roomId.toString())
      .then((res: any) => {
        if (res && res?.code === ErrorCode.SUCCESS) {
          toastSuc(i18n.t('chat_cleared'))
          setVisibleMore(false)
        }
      })
      .catch((err: any) => {
        console.error('clearRoomMsgs', err)
      })
  }

  // 普通用户不能设置
  if (roleCheck.isCivilian) {
    return null
  }

  return (
    <RoomButton
      deviceType={isMobile ? 'mobile' : 'pc'}
      icon={<IconChatClearMsg className="size-5" />}
      label={t('clear_msg')}
      onClick={handleClearMessages}
    />
  )
})

export default ButtonClearMsg
