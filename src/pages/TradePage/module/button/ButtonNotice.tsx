import { IS_OPEN_ROOM_LOG } from '@/config'
import { IconRoomNotice } from '@/imgs/icons'
import { chatroomManager, RoomRole } from '@/managers/chatroomManager'
import { Flex, Input } from 'antd'
import { t } from 'i18next'
import { observer } from 'mobx-react-lite'
import { useEffect, useState } from 'react'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { useRoomButtonModal } from './hooks'
import { ButtonBlack, ButtonSecondary } from '@/components/Customize/Button'
import { Modal } from '@/components/Customize/Modal'
import { RoomButton } from './RoomButton'
import { useDevice } from '@/hooks/useDevice'
/**
 * 公告按钮
 */
export const ButtonNotice = observer(() => {
  const { setVisibleNotice, setVisibleMore } = useRoomButtonModal()
  const { isMobile } = useDevice()

  const handleNoticeClicked = () => {
    IS_OPEN_ROOM_LOG && console.log('handleEditNotice')
    setVisibleNotice(true)
    setVisibleMore(false)
  }

  return (
    <RoomButton
      icon={<IconRoomNotice className={isMobile ? 'size-3.5' : 'size-5'} />}
      label={t('chatroom_notice')}
      showLabel={!isMobile}
      onClick={handleNoticeClicked}
      iconClassName={isMobile ? '!bg-gray-600 !p-2' : ''}
    />
  )
})

export const ButtonNoticeModal = observer(() => {
  const { visibleNotice, setVisibleNotice } = useRoomButtonModal()

  const store = useStore()
  const TAG = '[AudioRoomHeader]'
  const { toastSuc, toastErr } = useToastMessage()
  const [loading, setLoading] = useState(false)
  const [noticeTempContent, setNoticeTempContent] = useState<string>(
    store.roomDetial.roomContent ?? '',
  )
  const [noticeContentEditDisable, setNoticeContentEditDisable] =
    useState<boolean>(false)

  useEffect(() => {
    setNoticeTempContent(store.roomDetial.roomContent ?? '')
  }, [store.roomDetial])

  const onClose = () => setVisibleNotice(false)

  return (
    <Modal
      open={visibleNotice}
      // open={true}
      onCancel={onClose}
      afterClose={onClose}
      title={t('vibe_announcement')}
      width={453}
    >
      <Flex>
        {noticeContentEditDisable ? (
          <div className="mt-[20px] w-full">
            <Input.TextArea
              className="w-full"
              showCount
              maxLength={600}
              rows={6}
              value={noticeTempContent}
              onChange={(e) => {
                setNoticeTempContent(e.target.value)
              }}
              style={{
                height: '180px',
                maxHeight: '180px',
                resize: 'none',
                overflow: 'auto',
              }}
            />
          </div>
        ) : (
          <Input.TextArea
            maxLength={600}
            readOnly
            rows={6}
            value={store.roomDetial.roomContent ?? 'unknow'}
            className="min-h-[180px]"
          />
        )}
      </Flex>
      <div>
        {noticeContentEditDisable ? (
          <ButtonBlack
            className="mt-10 h-10 w-full font-lexend text-14 font-700"
            loading={loading}
            onClick={() => {
              if (noticeTempContent && store.roomDetial) {
                setLoading(true)
                chatroomManager
                  .updateRoomDetail(
                    store.roomId,
                    store.roomDetial.roomPic ?? '',
                    store.roomDetial.roomTitle ?? '',
                    noticeTempContent,
                  )
                  .then((res) => {
                    setLoading(false)
                    if (res.code !== 200) {
                      toastErr(`${res.message}${res.code}`)
                    } else {
                      onClose()
                      toastSuc(t('success'))
                    }
                  })
                  .catch((error) => {
                    console.error(`${TAG} error: ${error}`)
                    setLoading(false)
                  })
              }
            }}
          >
            <div>{t('save')}</div>
          </ButtonBlack>
        ) : store.roomDetial.roomRole === RoomRole.Master ||
          store.roomDetial.roomRole === RoomRole.Admin ? (
          <div className="mb-7 mt-10 grid grid-cols-2 gap-2 md:gap-4">
            <ButtonSecondary onClick={() => setNoticeContentEditDisable(true)}>
              {t('edit')}
            </ButtonSecondary>
            <ButtonBlack className="h-10" onClick={onClose}>
              <div>{t('got_it')}</div>
            </ButtonBlack>
          </div>
        ) : (
          <Flex className="mt-4 items-center justify-center">
            <ButtonBlack className="h-10 w-full" onClick={onClose}>
              <div>{t('got_it')}</div>
            </ButtonBlack>
          </Flex>
        )}
      </div>
    </Modal>
  )
})
