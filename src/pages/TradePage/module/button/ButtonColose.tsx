import { useRoute } from '@/hooks/useRoute'
import { IconRoomClose } from '@/imgs/icons'
import { Flex } from 'antd'
import { observer } from 'mobx-react-lite'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import i18n from '@/i18n'
import { useRoomButtonModal } from '@/pages/ChatRoom/module/button/hooks'
import { chatroomManager } from '@/managers/chatroomManager'
import { t } from 'i18next'
import { Trans } from 'react-i18next'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { ButtonBlack, ButtonSecondary } from '@/components/Customize/Button'
import { Modal } from '@/components/Customize/Modal'
import { useRoleBasedPermission } from '../../hooks/useRoleBasedPermission'
import { RoomButton } from './RoomButton'
import { useDevice } from '@/hooks/useDevice'
/**
 * 关闭按钮
 */
export const ButtonColose = observer(() => {
  const { setVisibleColose, setVisibleMore } = useRoomButtonModal()
  const { roleCheck } = useRoleBasedPermission()
  const { isMobile } = useDevice()
  // 普通用户不能设置
  if (roleCheck.isCivilian) {
    return null
  }

  return (
    <RoomButton
      mode={isMobile ? 'standard' : 'simple'}
      icon={<IconRoomClose className="size-5" />}
      label={i18n.t('close')}
      onClick={() => {
        setVisibleColose(true)
        setVisibleMore(false)
      }}
      deviceType={isMobile ? 'mobile' : 'pc'}
    />
  )
})

export const ButtonColoseModal = observer(() => {
  const store = useStore()
  const { path } = useRoute()
  const { toastErr } = useToastMessage()
  const { visibleColose, setVisibleColose } = useRoomButtonModal()

  function onClose() {
    setVisibleColose(false)
  }

  return (
    <Modal open={visibleColose} onCancel={onClose}>
      <Flex vertical className="items-center justify-center">
        <Trans
          i18nKey="are_you_sure_you_want_to_close_this_vibe"
          className="mt-[20px] text-[18px] font-normal text-[#1A1B1E]"
          values={{ name: store.recivedInviteJoinMicPosObj?.fromNickname }}
        />
        <div className="mb-8 mt-10 grid w-full grid-cols-2 gap-2 md:gap-4">
          <ButtonSecondary onClick={onClose}>{t('cancel')}</ButtonSecondary>
          <ButtonBlack
            className="h-10"
            onClick={() => {
              chatroomManager
                .pkDestroyRoom(store.roomId)
                .then(async (res) => {
                  if (res.code === 200) {
                    await store.leaveRoom()
                    path('/')
                  } else {
                    toastErr(`${res.message}(${res.code})`)
                  }
                })
                .catch((err) => {
                  console.error(`destroyRoom error`, err)
                })
            }}
          >
            <div>{t('confirm')}</div>
          </ButtonBlack>
        </div>
      </Flex>
    </Modal>
  )
})
