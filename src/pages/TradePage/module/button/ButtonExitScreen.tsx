import { IS_OPEN_ROOM_LOG } from '@/config'
import { i18n, useI18n } from '@/hooks/useI18n'
import { observer } from 'mobx-react-lite'
import { chatroomManager } from '@/managers/chatroomManager'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { IconRoomScreen } from '@/imgs/icons'
import { Divider, Flex } from 'antd'
import clsx from 'clsx'
import { useRoleBasedPermission } from '../../hooks/useRoleBasedPermission'
import { RoomButton } from './RoomButton'

/**
 * 屏幕分享按钮
 */
export const ButtonExitScreen = observer(
  ({ className }: { className?: string }) => {
    const store = useStore()
    const { t } = useI18n()
    const { roleCheck, micPosCheck } = useRoleBasedPermission()

    const handleExitScreen = () => {
      chatroomManager
        .updateRoomSetting(store.roomId, { isScreenShareing: false })
        .then(() => {
          IS_OPEN_ROOM_LOG && console.log('updateRoomSetting bingo')
          store.isScreenShareing = false
        })
        .catch((err) => {
          console.error('updateRoomSetting error', err)
        })
      chatroomManager.rcExitScreenShare()
    }

    const handleScreenShare = () => {
      chatroomManager.screenShare()
    }

    // 普通用户不展示
    if (roleCheck.isCivilian) {
      return null
    }

    // 如果不是房主，不显示按钮
    if (!micPosCheck.isCompere) return null

    // 正在共享时显示按钮
    if (store.isScreenShareing) {
      return (
        <Flex
          className={clsx(
            'cursor-pointer flex-col items-center justify-center',
            className,
          )}
        >
          <div
            onClick={handleExitScreen}
            className="cursor-pointer rounded-3xl border-none bg-white px-4 py-2 text-black"
          >
            {t('chatroom_exit_screen')}
          </div>
        </Flex>
      )
    }

    // 未共享时显示按钮
    return (
      <div className="flex h-full w-full items-center justify-center">
        <RoomButton
          icon={<IconRoomScreen className="size-5" />}
          label={t('chatroom_screen')}
          onClick={handleScreenShare}
          className="mr-2"
        />
        <Divider type="vertical" className="h-10 bg-[#303030]" />
      </div>
    )
  },
)
