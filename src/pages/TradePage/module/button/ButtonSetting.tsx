import i18n from '@/i18n'
import { IconRoomSettings } from '@/imgs/icons'
import { observer } from 'mobx-react-lite'
import { useRoomButtonModal } from '@/pages/ChatRoom/module/button/hooks'
import CreateRoom, { CreateRoomType } from '@/pages/ChatRoom/module/CreateRoom'
import { useRoleBasedPermission } from '../../hooks/useRoleBasedPermission'
import { RoomButton } from './RoomButton'
import { useDevice } from '@/hooks/useDevice'

/**
 * 设置按钮
 */
export const ButtonSetting = observer(
  ({ className }: { className?: string }) => {
    const { setVisibleSettings, setVisibleMore } = useRoomButtonModal()
    const { roleCheck } = useRoleBasedPermission()
    const { isMobile } = useDevice()
    const handleSettingsClicked = () => {
      setVisibleSettings(true)
      setVisibleMore(false)
    }

    // 普通用户不能设置
    if (roleCheck.isCivilian) {
      return null
    }

    return (
      <RoomButton
        mode={isMobile ? 'standard' : 'simple'}
        deviceType={isMobile ? 'mobile' : 'pc'}
        icon={<IconRoomSettings className="size-5" />}
        label={i18n.t('chatroom_settings')}
        onClick={handleSettingsClicked}
        className={className}
      />
    )
  },
)

export const ButtonSettingModal = observer(() => {
  const { visibleSettings, setVisibleSettings } = useRoomButtonModal()

  function onClose() {
    setVisibleSettings(false)
  }

  return (
    <CreateRoom
      type={CreateRoomType.Edit}
      open={visibleSettings}
      onDone={onClose}
      onCancel={onClose}
    />
  )
})
