/**
 * @file RoomButton.tsx
 * @description 房间按钮组件，提供统一的图标和文本样式
 */

import { ReactNode } from 'react'
import clsx from 'clsx'
import { Popover } from 'antd'

/**
 * 按钮展示模式
 */
type RoomButtonMode = 'standard' | 'simple'

/**
 * 设备类型
 */
type DeviceType = 'pc' | 'mobile'

interface RoomButtonProps {
  /** 图标内容 */
  icon: ReactNode
  /** 按钮文本 */
  label: string | ReactNode
  /** 点击事件处理函数 */
  onClick?: () => void
  /** 自定义类名，作用于最外层容器 */
  className?: string
  /** 图标容器的自定义类名 */
  iconClassName?: string
  /** 文本容器的自定义类名 */
  labelClassName?: string
  /** 按钮展示模式 */
  mode?: RoomButtonMode
  /** 是否显示文案 */
  showLabel?: boolean
  /** 设备类型，影响样式表现 */
  deviceType?: DeviceType
}

/**
 * @component RoomButton
 * @description 房间功能按钮通用组件，提供统一的样式
 */
export const RoomButton = ({
  icon,
  label,
  onClick,
  className,
  iconClassName,
  labelClassName,
  mode = 'standard',
  showLabel = true,
  deviceType = 'pc',
}: RoomButtonProps) => {
  // 处理文本内容，如果是字符串则可以显示省略号，否则直接显示
  const labelContent =
    typeof label === 'string' ? <div className="truncate">{label}</div> : label

  // 文本容器
  const labelContainer = (
    <div
      className={clsx(
        // 基础文本样式
        'break-words text-center',
        // PC端的文本样式
        deviceType === 'pc' &&
          (mode === 'standard'
            ? 'max-w-16 text-10 tracking-tight'
            : 'mt-2.5 max-w-16 text-[12px] leading-[1em] text-gray-400'),
        // 移动端的文本样式
        deviceType === 'mobile' &&
          (mode === 'standard'
            ? 'mt-2 max-w-full text-12 tracking-tight text-[#AAABBB]'
            : 'mt-2 text-12 leading-[1em] text-[#AAABBB]'),
        labelClassName,
      )}
    >
      {labelContent}
    </div>
  )

  return (
    <div
      className={clsx(
        'flex cursor-pointer flex-col items-center justify-center',
        // 仅在PC端且显示标签时添加最小宽度（除非用户自定义了宽度）
        deviceType === 'pc' &&
          showLabel &&
          !className?.includes('min-w-') &&
          'min-w-16',
        className,
      )}
    >
      {/* 图标容器 */}
      <div
        onClick={onClick}
        className={clsx(
          'flex items-center justify-center rounded-full',
          // 标准模式下的图标样式
          mode === 'standard' &&
            (deviceType === 'pc'
              ? 'bg-theme-primary/50 p-2.5'
              : 'bg-gradient-to-b from-white/[0.07] to-white/[0.01] p-2.5 shadow-lg'),
          // 简洁模式下移动端的额外样式
          mode === 'simple' && deviceType === 'mobile' && 'p-1',
          iconClassName,
        )}
      >
        {icon}
      </div>

      {/* 文本容器 */}
      {showLabel &&
        (typeof label === 'string' ? (
          <Popover
            content={<span className="text-white">{label}</span>}
            mouseEnterDelay={0.5}
            arrow={false}
            trigger={['click', 'hover']}
            overlayInnerStyle={{
              backgroundColor: '#1A1B1E',
            }}
          >
            {labelContainer}
          </Popover>
        ) : (
          labelContainer
        ))}
    </div>
  )
}
