import { createContext, useState } from 'react'

interface RoomModalContextType {
  // 公告按钮 Modal 态
  visibleNotice: boolean
  setVisibleNotice: (visible: boolean) => void
  // 加锁按钮 Modal 态
  visibleLock: boolean
  setVisibleLock: (visible: boolean) => void
  // 管理员按钮 Modal 态
  visibleAdmin: boolean
  setVisibleAdmin: (visible: boolean) => void
  // 管理员列表类型 [1: 管理员列表, 2: 黑名单列表]
  adminModel: 0 | 1 | 2
  setAdminModel: (model: 0 | 1 | 2) => void
  // 设置按钮 Modal 态
  visibleSettings: boolean
  setVisibleSettings: (visible: boolean) => void
  // 关闭按钮 Modal 态
  visibleColose: boolean
  setVisibleColose: (visible: boolean) => void
  // 更多按钮 Modal 态
  visibleMore: boolean
  setVisibleMore: (visible: boolean) => void
}

export const RoomModalContext = createContext<RoomModalContextType | null>(null)

export const RoomModalProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const [visibleNotice, setVisibleNotice] = useState(false)
  const [visibleLock, setVisibleLock] = useState(false)
  const [visibleAdmin, setVisibleAdmin] = useState(false)
  const [adminModel, setAdminModel] = useState<0 | 1 | 2>(0)
  const [visibleSettings, setVisibleSettings] = useState(false)
  const [visibleColose, setVisibleColose] = useState(false)
  const [visibleMore, setVisibleMore] = useState(false)

  return (
    <RoomModalContext.Provider
      value={{
        visibleNotice,
        setVisibleNotice,
        visibleLock,
        setVisibleLock,
        visibleAdmin,
        setVisibleAdmin,
        adminModel,
        setAdminModel,
        visibleSettings,
        setVisibleSettings,
        visibleColose,
        setVisibleColose,
        visibleMore,
        setVisibleMore,
      }}
    >
      {children}
    </RoomModalContext.Provider>
  )
}
