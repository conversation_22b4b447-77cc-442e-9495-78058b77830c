import { ButtonBlack, ButtonModalClose } from '@/components/Customize/Button'
import { Flex, Modal, Skeleton, Typography } from 'antd'
import SetPassword from '@/pages/ChatRoom/module/SetPassword'
import { useEffect, useState } from 'react'
import { ChatroomErrorCode } from '@/managers/chatroomManager'
import { QUERY_ROOM_DETIAL_BY_ID_WITHOUT_PWD } from '@/api/interface/QUERY_ROOM_DETIAL_BY_ID_WITHOUT_PWD'
import { i18n } from '@/hooks/useI18n'

const { Paragraph } = Typography

interface IJoinRoomContentProps {
  roomId: string
  onJoinRoom: (password: string) => void
  onReturnHome: () => void
}

const JoinRoomContent: React.FC<IJoinRoomContentProps> = ({
  roomId,
  onJoinRoom,
  onReturnHome,
}) => {
  const [visibleLock, setVisibleLock] = useState(false)
  const [skeletonLoading, setSkeletonLoading] = useState(true)
  const [roomDetial, setRoomDetial] = useState<any>({
    roomTitle: '',
    roomPic: '',
    roomId: '',
    isPw: false,
  })

  const handlePwdConfrim = async (password: string[]) => {
    if (password && password.length === 4) {
      onJoinRoom(password.join(''))
      setVisibleLock(false)
      Modal.destroyAll()
    }
  }

  const handlePwdCancel = () => {
    setVisibleLock(false)
  }

  useEffect(() => {
    QUERY_ROOM_DETIAL_BY_ID_WITHOUT_PWD({ roomId }).then((res) => {
      if (res?.code === ChatroomErrorCode.PK_SUCCESS) {
        setSkeletonLoading(false)
        setRoomDetial(res.data)
      } else {
        onReturnHome()
        Modal.destroyAll()
      }
    })
  }, [roomId])

  return (
    <Flex vertical className="items-center justify-center">
      <Skeleton active loading={skeletonLoading}>
        <Flex vertical className="w-full max-w-[358px]">
          <Flex className="mb-1 items-center justify-center text-center text-[20px] font-bold text-[#1A1B1E]">
            {i18n.t('coming_soon_to_join')}
          </Flex>
          <div className="h-[200px] w-full max-w-[358px] overflow-hidden">
            <img
              className="h-full w-full object-cover"
              src={roomDetial.roomPic}
            />
          </div>
          <Flex vertical className="mt-2 w-full">
            <Paragraph>{roomDetial.roomTitle}</Paragraph>
            <Flex>ID: {roomDetial.roomId}</Flex>
          </Flex>
          <Flex className="mt-2 w-full items-start justify-center">
            <ButtonModalClose
              className="flex-1 border-b border-[#292A2E]"
              onClick={() => {
                onReturnHome()
                Modal.destroyAll()
              }}
            >
              {i18n.t('back_home')}
            </ButtonModalClose>
            <ButtonBlack
              className="ml-2 flex-1 text-base font-bold"
              onClick={() => {
                if (roomDetial.isPw) {
                  setVisibleLock(true)
                } else {
                  onJoinRoom('')
                  Modal.destroyAll()
                }
              }}
            >
              {i18n.t('user_interaction_join')}
            </ButtonBlack>
          </Flex>
        </Flex>
      </Skeleton>
      <SetPassword
        open={visibleLock}
        onCancel={handlePwdCancel}
        onConfirm={handlePwdConfrim}
      />
    </Flex>
  )
}

export default JoinRoomContent
