import TradePlate from '@/pages/Trade/TradePlate'
import { Flex } from 'antd'
import { observer } from 'mobx-react-lite'
import AudioRoom from '@/pages/ChatRoom/module/AduioRoom'
import ChatRoom from '@/pages/ChatRoom/module/ChatRoom'
import { ModeSwitch, ModeSwitchType } from '@/pages/ChatRoom/module/ModeSwitch'
import OnlineUserList from '@/pages/ChatRoom/module/OnlineUserList'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { useState } from 'react'
import { useDevice } from '@/hooks/useDevice'
import { useScreenShare } from '@/pages/ChatRoom/hooks/useScreenShare'

export const BuildTradeModeAndScreenShareing = observer(() => {
  const store = useStore()
  const { isMobile } = useDevice()

  const [mode, setMode] = useState<ModeSwitchType>(1)
  const { videoRef } = useScreenShare(mode)

  const onModeChanged = (mode: ModeSwitchType) => setMode(mode)

  if (isMobile) {
    return (
      <div className="flex h-full flex-col">
        <div className="h-[40px] w-full">
          <ModeSwitch type="mobile" onModeChanged={onModeChanged} />
        </div>

        {mode === 4 ? (
          <>
            <Flex className="w-full flex-1 border-r border-[#313131]">
              <video
                ref={videoRef}
                muted
                autoPlay
                controls
                className="h-full w-full"
              />
            </Flex>
            <Flex className="h-[260px] flex-grow overflow-hidden">
              <ChatRoom />
            </Flex>
          </>
        ) : (
          <TradePlate roomId={store.roomDetial.roomId ?? ''} />
        )}
      </div>
    )
  }

  return (
    <Flex className="h-full flex-grow">
      <Flex className="h-full w-16">
        <OnlineUserList type="simple" />
      </Flex>

      {/* 麦克位与聊天部分 */}
      <Flex vertical={true} className="w-[360px]">
        <Flex className="h-[150px] border-r border-[#313131]">
          <AudioRoom />
        </Flex>
        <Flex className="h-[160px]" id="radeAndScreenShareingVideoContainer">
          <video
            muted
            ref={videoRef}
            autoPlay
            controls
            style={{ width: '100%', height: '100%' }}
          />
        </Flex>
        <Flex className="flex-1 overflow-hidden">
          <ChatRoom />
        </Flex>
      </Flex>

      {/* 交易面板 */}
      <Flex className="h-[calc(100vh-64px)] flex-grow">
        <TradePlate roomId={store.roomDetial.roomId ?? ''} />
      </Flex>

      <Flex vertical={true} className="w-16 border-l border-[#313131]">
        <ModeSwitch type="desktop" />
      </Flex>
    </Flex>
  )
})
