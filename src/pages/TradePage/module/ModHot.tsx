import { IconRoomFire } from '@/imgs/icons'
import { Flex } from 'antd'
import { observer } from 'mobx-react-lite'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import clsx from 'clsx'

export const ModHot = observer(({ type }: { type: 'icon' | 'text' }) => {
  const store = useStore()
  const isIcon = type === 'icon'
  const isText = type === 'text'

  return (
    <Flex
      className={clsx(
        'h-5 items-center justify-center px-2 md:h-auto md:py-1',
        isIcon && 'ml-6 mr-2 rounded-16 bg-[#28292F] md:rounded',
      )}
    >
      <IconRoomFire
        className={clsx('mr-1', isIcon && 'size-3', isText && 'size-[10px]')}
      />
      <div
        className={clsx(
          'font-lexend font-bold text-[#AAACBB]',
          isIcon && 'text-xs',
          isText && 'text-[10px]',
        )}
      >
        {store.roomHot}
      </div>
    </Flex>
  )
})
