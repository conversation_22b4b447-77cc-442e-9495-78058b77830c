import { Flex } from 'antd'
import { observer } from 'mobx-react-lite'
import AudioRoom from '@/pages/ChatRoom/module/AduioRoom'
import ChatRoom from '@/pages/ChatRoom/module/ChatRoom'
import { ModeSwitch, ModeSwitchType } from '@/pages/ChatRoom/module/ModeSwitch'
import OnlineUserList from '@/pages/ChatRoom/module/OnlineUserList'
import { useState } from 'react'
import { useDevice } from '@/hooks/useDevice'
import { useScreenShare } from '@/pages/ChatRoom/hooks/useScreenShare'

export const BuildChatModeAndScreenShareing = observer(() => {
  const [mode, setMode] = useState<ModeSwitchType>(1)

  const { isMobile } = useDevice()
  const { videoRef } = useScreenShare(mode)

  const onModeChanged = (mode: ModeSwitchType) => setMode(mode)

  if (isMobile) {
    return (
      <Flex className="flex h-full flex-col">
        <div className="h-[40px] w-full">
          <ModeSwitch type="mobile" onModeChanged={onModeChanged} />
          <OnlineUserList type="hidden" />
        </div>
        <Flex className="w-full flex-1 border-r border-[#313131]">
          {mode === 4 ? (
            <video
              ref={videoRef}
              muted
              autoPlay
              controls
              className="h-full w-full"
            />
          ) : (
            <AudioRoom />
          )}
        </Flex>
        <Flex className="h-[260px] flex-grow overflow-hidden">
          <ChatRoom />
        </Flex>
      </Flex>
    )
  }

  return (
    <Flex className="h-full w-full flex-grow justify-between">
      <Flex className="h-full w-16">
        <OnlineUserList type="simple" />
      </Flex>
      <Flex vertical={true} className="w-[360px]">
        <Flex className="h-[360px] w-full border-r border-[#313131]">
          <AudioRoom />
        </Flex>
        <Flex className="h-[calc(100vh-64px-360px)] flex-grow overflow-hidden">
          <ChatRoom />
        </Flex>
      </Flex>
      <Flex className="h-full flex-1">
        <video
          ref={videoRef}
          muted
          autoPlay
          controls
          className="h-full w-full"
        />
      </Flex>
      <Flex vertical={true} className="w-16 border-l border-[#313131]">
        <ModeSwitch type="desktop" />
      </Flex>
    </Flex>
  )
})
