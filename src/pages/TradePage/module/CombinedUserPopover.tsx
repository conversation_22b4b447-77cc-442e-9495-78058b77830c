import React, { useState, useEffect } from 'react'
import { ConfigProvider, Skeleton, Popover, Flex } from 'antd'
import { usersManager } from '@/managers/usersManager'
import { USER_INFO_RETURN } from '@/api/interface/QUERY_USERS_INFO'
import {
  IconChat,
  IconFemale,
  IconMale,
  SvgIconTwitterXRoom,
} from '@/imgs/icons'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { useRoute } from '@/hooks/useRoute'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import {
  ChatroomErrorCode,
  chatroomManager,
  IOnlineUserItem,
  MicPosIndex,
  Mode,
  RoomRole,
} from '@/managers/chatroomManager'
import { GET_CHATROOM_USER_INFO } from '@/api/interface/GET_CHATROOM_USER_INFO'
import { IMicPostion } from '@/pages/ChatRoom/store/ChatRoomPageStore'
import { userStore } from '@/store'
import PKAvatar from '@/components/Customize/PKAvatar'
import i18n from '@/i18n'
import { IS_OPEN_ROOM_LOG } from '@/config'
import { useRoomJudgement } from '../hooks/useRoomJudgement'
import CopyToClipboard from '@/components/CopyToClipboard'
import { t } from 'i18next'
import { ButtonSecondary } from '@/components/Customize/Button'

interface CombinedUserPopoverProps {
  userId?: string
  onClicked?: (userId: string) => void
  trigger?: 'hover' | 'click' | ('hover' | 'click')[]
  onNavigate?: (path: string, params?: any) => void
}

const CombinedUserPopover: React.FC<
  React.PropsWithChildren<CombinedUserPopoverProps>
> = ({ userId, children, trigger = 'click', onNavigate }) => {
  const [skeletonLoading, setSkeletonLoading] = useState<boolean>(true)
  const [curUserInfo, setCurUserInfo] = useState<USER_INFO_RETURN | null>()
  const [curOnlineUser, setCurOnlineUser] = useState<IOnlineUserItem | null>()
  const [, setCurMicPosInfo] = useState<IMicPostion | null>()
  const { toastSuc, toastErr } = useToastMessage()
  const { isAdmin, isMaster, isCivilian, isOnCompereMic } = useRoomJudgement()
  const { path } = useRoute()
  const store = useStore()
  const [outerVisible, setOuterVisible] = useState<boolean>(false)

  // 初始化用户数据
  useEffect(() => {
    if (userId && outerVisible) {
      const targetOnlineUser = store.onlineUsers.find(
        (item) => item.userId === userId,
      )
      setCurOnlineUser(targetOnlineUser)

      const targetMicPosInfo = store.micPosInfos.find(
        (item) => item.userId === userId,
      )
      setCurMicPosInfo(targetMicPosInfo)

      const userCache = usersManager.getUsersFromCache([userId])
      if (userCache?.length) {
        setCurUserInfo(userCache[0])
        setSkeletonLoading(false)
      } else {
        usersManager
          .getUsers([userId], true)
          .then((userInfos) => {
            setCurUserInfo(userInfos[0])
            setSkeletonLoading(false)
          })
          .catch((err) => {
            console.error('获取用户数据失败:', err)
            setSkeletonLoading(false)
          })
      }
    }
  }, [userId, outerVisible, store])

  const buildAge = (birthday: string) => {
    const birthdayDate = new Date(birthday)
    const today = new Date()
    let age = today.getFullYear() - birthdayDate.getFullYear()
    const m = today.getMonth() - birthdayDate.getMonth()
    if (m < 0 || (m === 0 && today.getDate() < birthdayDate.getDate())) {
      age--
    }
    return age
  }

  const handleApiError = (err: any) => {
    console.error('API Error:', err)
    toastErr(i18n.t('error'))
  }

  const handleChat = () => {
    console.log('--------handleChat--------', curUserInfo)
    setOuterVisible(false)
    if (onNavigate) {
      // onNavigate('/msgs', { userId: curUserInfo?.id })
      onNavigate(`/msgs`)
    } else {
      path('/msgs', { userId: curUserInfo?.id })
    }
  }

  const isShowFollowBtn = (userId: string) => {
    return (
      userStore.info.id !== userId &&
      !store.isFriend(userId) &&
      !store.isFollowing(userId)
    )
  }

  const handleFollow = (userId: string) => {
    chatroomManager
      .followUser(userId)
      .then((res) => {
        if (res.code === 200) {
          toastSuc(i18n.t('success'))
          store.fetchFollowingList()
          store.fetchFriendList()
          setOuterVisible(false)
        } else {
          toastErr(`${res.message}(${res.code})`)
        }
      })
      .catch(handleApiError)
  }

  const handleAdminChange = (isAdd: boolean) => {
    if (!curOnlineUser?.userId) return

    const adminAction = isAdd
      ? chatroomManager.addAdmin
      : chatroomManager.removeAdmin
    if (isAdd === false) {
      if (store.roomDetial.roomRole === RoomRole.Master) {
        if (store.isScreenShareing) {
          store.isScreenShareing = false
          chatroomManager
            .updateRoomSetting(store.roomId, { isScreenShareing: false })
            .then(() => {
              IS_OPEN_ROOM_LOG && console.log(`updateRoomSetting bingo`)
            })
            .catch((err) => {
              console.error(`updateRoomSetting error`, err)
            })
        }
      }
    }

    adminAction(store.roomId, curOnlineUser.userId)
      .then((res) => {
        if (res.code !== 200) {
          toastErr(`error: ${res.message}${res.code}`)
          return
        }
        store.fetchAdminList().then(() => {
          toastSuc(i18n.t('success'))
          setOuterVisible(false)
          updateOnlineUserInfo()
        })
      })
      .catch(handleApiError)
  }

  const updateOnlineUserInfo = () => {
    if (!curOnlineUser?.userId) return

    GET_CHATROOM_USER_INFO({
      roomId: store.roomId,
      userIds: curOnlineUser.userId,
    })
      .then((result) => {
        if (result.code === 200 && result.data?.length) {
          const updatedUser = result.data[0] as IOnlineUserItem
          setCurOnlineUser(updatedUser)
          const existingIndex = store.onlineUsers.findIndex(
            (user) => user.userId === updatedUser.userId,
          )
          if (existingIndex !== -1) {
            store.onlineUsers[existingIndex] = updatedUser
          } else {
            store.onlineUsers.push(updatedUser)
          }
        }
      })
      .catch((err) => console.error('更新用户信息失败:', err))
  }

  const handleKick = () => {
    if (curOnlineUser?.userId) {
      setOuterVisible(false)
      store.clickedNeedBlackUserId = curOnlineUser.userId
      store.isOpenAddBlackListModel = true
    }
  }

  const handleClose = () => {
    const micInfo = store.micPosInfos.find(
      (item: any) => item.userId === curOnlineUser?.userId,
    )
    if (micInfo?.micPosIndex) {
      chatroomManager
        .lockOrUnlockMic(store.roomId, micInfo.micPosIndex, !micInfo.isLock)
        .then(() => toastSuc(i18n.t('success')))
        .catch(handleApiError)
    }
  }

  const handleMute = () => {
    const micInfo = store.micPosInfos.find(
      (item: any) => item.userId === curOnlineUser?.userId,
    )
    if (micInfo) {
      chatroomManager
        .muteOrUnmuteMicrophone(
          store.roomId,
          curOnlineUser!.userId!,
          !micInfo.isMutedByAdmin,
        )
        .then(() => {
          toastSuc(i18n.t('success'))
          setOuterVisible(false)
        })
        .catch(handleApiError)
    }
  }

  const handleLeaveMicPos = async (micInfo: IMicPostion) => {
    try {
      const leaveMic = () =>
        chatroomManager.pkLeaveMic(store.roomId, micInfo.micPosIndex!)

      if (
        micInfo.userId === userStore.info.id &&
        store.isHost(userStore.info.id)
      ) {
        const res = await chatroomManager.updateRoomType(
          store.roomId,
          3,
          '',
          '',
        )
        if (res.code === ChatroomErrorCode.PK_SUCCESS) {
          store.handleModeChanged(Mode.Chat)
          await leaveMic()
        }
      } else {
        await leaveMic()
      }
    } catch (err) {
      console.error('handleLeaveMicPos error:', err)
    }
  }

  const ActionButton = ({
    label,
    onClick,
  }: {
    label: string
    onClick: () => void
  }) => (
    <div
      onClick={onClick}
      className="cursor-pointer select-none rounded bg-[#40414A] px-3 py-1 text-white"
    >
      {label}
    </div>
  )

  const renderActions = (actions: { label: string; onClick: () => void }[]) => (
    <>
      <div className="mb-[20px] w-full border-t border-[#383A42]" />
      <div className="mb-[20px] flex max-w-[260px] flex-wrap gap-2">
        {actions.map((action) => (
          <ActionButton key={action.label} {...action} />
        ))}
      </div>
    </>
  )

  const buildActions = () => {
    if (!curOnlineUser) return null

    // 被点击的是自己
    const isSelf = userStore.info.id === curOnlineUser.userId
    // 被点击的是主持麦
    const isCompere = curOnlineUser.posIndex === MicPosIndex.compere
    // 被点击的是房管
    const isClickAdmin = curOnlineUser.roomRole === RoomRole.Admin
    // 被点击的是房主
    const isClickMaster = curOnlineUser.roomRole === RoomRole.Master
    // 被点击的是观众
    const isClickCivilian = curOnlineUser.roomRole === RoomRole.Civilian
    // 被点击的用户是否在麦位上，有则返回麦位信息
    const clickedUserIsOnMic = store.micPosInfos.find(
      (item: IMicPostion) => item.userId === curOnlineUser.userId,
    )

    // 定义所有可能的操作
    const actionMuteOrUnMute = {
      label: clickedUserIsOnMic?.isMutedByAdmin
        ? i18n.t('unmute')
        : i18n.t('mute'),
      onClick: handleMute,
    }
    // 邀请上麦
    const actionInviteJoinMicPos = {
      label: i18n.t('invite'),
      onClick: () => (store.isOpenInviteJoinMicPos = true),
    }
    // 禁麦/解除禁麦
    const actionLeaveMic = {
      label: i18n.t('leave_mic'),
      onClick: () => handleLeaveMicPos(clickedUserIsOnMic!),
    }
    // 下麦旁听
    const actionKickOut = { label: i18n.t('kick_out'), onClick: handleKick }
    // 踢出房间
    const actionCloseMicPos = {
      label: i18n.t('close_mic'),
      onClick: handleClose,
    }
    // 封闭此麦
    const actionAddOrRemoveAdmin = {
      label: isClickAdmin ? i18n.t('remove_admin') : i18n.t('make_admin'),
      onClick: () => handleAdminChange(!isClickAdmin),
    }
    // 设为管理/取消管理

    // 房主逻辑
    if (isMaster) {
      if (isSelf) {
        // 房主自己：如果在麦位上，显示"下麦旁听"
        return clickedUserIsOnMic ? renderActions([actionLeaveMic]) : null
      }

      // 房主对其他人的操作
      const actions = []

      // 禁麦/解除禁麦
      if (clickedUserIsOnMic && !isCompere) actions.push(actionMuteOrUnMute)
      // 下麦旁听
      if (clickedUserIsOnMic) actions.push(actionLeaveMic)
      // 踢出房间
      actions.push(actionKickOut)
      // 封闭此麦
      if (
        clickedUserIsOnMic &&
        clickedUserIsOnMic?.micPosIndex !== MicPosIndex.compere
      ) {
        actions.push(actionCloseMicPos)
      }
      // 设为管理/取消管理
      actions.push(actionAddOrRemoveAdmin)

      return renderActions(actions)
    }

    // 房管逻辑
    if (isAdmin) {
      // 如果被点击者是房管自己
      if (isSelf) {
        // 房管自己：如果在麦位上，显示"下麦旁听"
        return clickedUserIsOnMic ? renderActions([actionLeaveMic]) : null
      }

      // 如果被点击者是房主，不显示任何操作
      if (isClickMaster) return null

      // 如果被点击者是其他房管
      if (isClickAdmin) {
        // 如果当前登录用户在主持麦上，显示操作
        if (isOnCompereMic) {
          const actions = []
          // 禁麦/解除禁麦
          if (clickedUserIsOnMic) actions.push(actionMuteOrUnMute)
          // 下麦旁听
          if (clickedUserIsOnMic) actions.push(actionLeaveMic)
          // 踢出房间
          actions.push(actionKickOut)
          // 封闭此麦
          if (clickedUserIsOnMic) actions.push(actionCloseMicPos)

          return renderActions(actions)
        } else {
          // 如果当前登录用户不在主持麦上，不显示任何操作
          return null
        }
      }

      // 如果被点击者是观众
      if (isClickCivilian) {
        const actions = []
        // 禁麦/解除禁麦
        if (clickedUserIsOnMic) actions.push(actionMuteOrUnMute)
        // 下麦旁听
        if (clickedUserIsOnMic) actions.push(actionLeaveMic)
        // 踢出房间
        actions.push(actionKickOut)
        // 封闭此麦
        if (clickedUserIsOnMic) actions.push(actionCloseMicPos)

        return renderActions(actions)
      }
    }

    // 观众逻辑
    if (isCivilian && isSelf && clickedUserIsOnMic) {
      // 观众自己：如果在麦位上，显示"下麦旁听"
      return renderActions([actionLeaveMic])
    }

    return null
  }

  const renderUserInfoContent = () => (
    <ConfigProvider
      theme={{
        components: {
          Skeleton: {
            gradientFromColor: 'rgba(255, 255, 255, 0.1)',
            gradientToColor: 'rgba(255, 255, 255, 0.2)',
          },
        },
      }}
    >
      <Flex className="min-h-[330px] w-[320px] items-center justify-center">
        <Skeleton active loading={skeletonLoading}>
          <div className="flex h-full w-full flex-col items-center justify-center">
            <Flex
              className="cursor-pointer"
              onClick={(e) => {
                if (curUserInfo) {
                  e.preventDefault()
                  // 保存当前滚动位置
                  const scrollPosition =
                    window.scrollY || document.documentElement.scrollTop
                  sessionStorage.setItem(
                    'scrollPosition',
                    scrollPosition.toString(),
                  )
                  // 跳转到用户页面
                  path(`/user/${curUserInfo.id}`)
                }
              }}
            >
              <PKAvatar
                src={curUserInfo?.profile?.photos?.[0]?.path}
                className="size-24"
              />
            </Flex>
            <div className="text-[24px] font-normal text-white">
              {curUserInfo?.nickname}
              {curUserInfo?.twitterInfo?.id && (
                <a
                  href={`https://x.com/${curUserInfo?.twitterInfo.screenName}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block"
                  onClick={(e) => e.stopPropagation()}
                >
                  <SvgIconTwitterXRoom className="ml-2 h-4 w-4 cursor-pointer" />
                </a>
              )}
            </div>
            <div className="flex items-center justify-center text-[16px] font-normal text-[#AAACBB]">
              <div className="mr-2 flex items-center rounded border border-[#40414A] px-1 py-[2px]">
                {curUserInfo?.profile.gender === 1 ? (
                  <IconMale className="h-[13px] w-[13px]" />
                ) : (
                  <IconFemale className="h-[13px] w-[13px]" />
                )}
                {curUserInfo?.profile.birthday && (
                  <span className="ml-1">
                    {buildAge(curUserInfo.profile.birthday)}
                  </span>
                )}
              </div>
              <CopyToClipboard
                text={curUserInfo?.conciseUserId || ''}
                onCopy={() => {
                  toastSuc(t('success'))
                }}
              >
                ID: {curUserInfo?.conciseUserId}
              </CopyToClipboard>
            </div>
            <div className="my-2 line-clamp-5 w-full break-words text-center text-[16px] font-normal text-white">
              {curUserInfo?.profile.about}
            </div>
            <div className="grid grid-cols-2 gap-6">
              {curUserInfo?.id && isShowFollowBtn(curUserInfo.id) && (
                <ButtonSecondary
                  className="h-9"
                  onClick={() => handleFollow(curUserInfo.id)}
                >
                  {i18n.t('follow')}
                </ButtonSecondary>
              )}
              {curUserInfo?.id && curUserInfo.id !== userStore.info.id && (
                <ButtonSecondary className="h-9" onClick={handleChat}>
                  <IconChat className="h-[16px] w-[18px]" />
                  {i18n.t('chat')}
                </ButtonSecondary>
              )}
            </div>
            {buildActions()}
          </div>
        </Skeleton>
      </Flex>
    </ConfigProvider>
  )

  return (
    <Popover
      color="#28292F"
      placement="right"
      content={renderUserInfoContent}
      trigger={trigger}
      open={outerVisible}
      onOpenChange={setOuterVisible}
    >
      {children}
    </Popover>
  )
}

export default CombinedUserPopover
