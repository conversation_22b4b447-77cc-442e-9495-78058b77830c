import { useState, useEffect } from 'react'
import { Flex, Input } from 'antd'
import { IconRoomCreateHeader } from '@/imgs/icons'
import { ChatroomErrorCode, chatroomManager } from '@/managers/chatroomManager'
import { userStore } from '@/store'
import { UPLOAD_CDN } from '@/api/interface/UPLOAD_CDN'
import { lodash } from '@/utils'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { useRoute } from '@/hooks/useRoute'
import PKSwitch from '@/components/Customize/PKSwtich'
import SetPassword from '@/pages/ChatRoom/module/SetPassword'
import i18n from '@/i18n'
import { GET_ROOM_DEFAULT_IMG } from '@/api/interface/GET_ROOM_DEFAULT_IMG'
import { ButtonBlack } from '@/components/Customize/Button'
import { Modal } from '@/components/Customize/Modal'
const { TextArea } = Input

export enum CreateRoomType {
  Create = 1,
  Edit = 2,
}
export function Component({
  type,
  open,
  onCancel,
  onDone,
}: {
  type: CreateRoomType
  open: boolean
  onCancel: (e: any) => void
  onDone?: () => void
}) {
  const { isEmpty } = lodash
  const store = useStore()
  const { path } = useRoute()
  const { toastSuc, toastErr } = useToastMessage()

  // 根据 type 初始化 roomDetial
  const [roomDetial, setRoomDetial] = useState<any>({})

  const [defaultRoomPic, setDefaultRoomPic] = useState<string>('')

  useEffect(() => {
    GET_ROOM_DEFAULT_IMG()
      .then((res) => {
        if (res.code === 200) {
          setDefaultRoomPic(res.data)
        }
      })
      .catch((error) => {
        console.error('get default img error:', error)
      })
  }, [open])

  useEffect(() => {
    if (type === CreateRoomType.Create) {
      // 创建房间时，获取默认的房间详情
      chatroomManager
        .fetchRoomDetailByUser()
        .then((res) => {
          if (res && res.code === ChatroomErrorCode.PK_SUCCESS) {
            setRoomDetial(res.data)
            setTempRoomTitle(
              res.data.roomTitle ??
                getRoomTitleDefault(userStore.info.nickname),
            )
            setTempRoomContent(res.data.roomContent ?? '')
          } else {
            if (userStore.IS_LOGIN()) {
              toastErr(`${res?.message}${res?.code}`)
            }
          }
        })
        .catch((error) => {
          console.error('fetchRoomDetailByUser error:', error)
        })
    } else if (type === CreateRoomType.Edit) {
      // 编辑房间时，使用 store 中的 roomDetial
      setRoomDetial(store.roomDetial)
      setTempRoomTitle(
        store.roomDetial.roomTitle ??
          getRoomTitleDefault(userStore.info.nickname),
      )
      setTempRoomContent(store.roomDetial.roomContent ?? '')
    }
  }, [type, store.roomDetial])

  const getRoomTitleDefault = (
    nickname: string | null | undefined,
    maxLength = 16,
    suffix = " 's room",
  ): string => {
    const name = nickname?.trim() || 'Anonymous'
    const availableLength = maxLength - suffix.length
    const adjustedNickname =
      name.length > availableLength ? name.substring(0, availableLength) : name
    return `${adjustedNickname}${suffix}`
  }

  const roomPicDefault = roomDetial.roomPic
    ? roomDetial.roomPic
    : defaultRoomPic
  const [btnDisable] = useState<boolean>(false)
  const [btnLoading, setBtnLoading] = useState<boolean>(false)

  const [tempRoomTitle, setTempRoomTitle] = useState<string>('')
  const [tempRoomContent, setTempRoomContent] = useState<string>('')
  const [tempRoomPic, setTempRoomPic] = useState<string>('')

  const [passwordIsOpen, setPasswordIsOpen] = useState<boolean>(false)
  const [password, setPassword] = useState<string>('')
  const [curSwitchValue, setCurSwitchValue] = useState<boolean>(false)

  const getTitle = () => {
    return type === CreateRoomType.Create
      ? i18n.t('create_room')
      : i18n.t('setting_room')
  }
  const handleSendImage = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0]
    if (file) {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', '1')
      UPLOAD_CDN(formData)
        .then((res) => {
          if (res.code !== 200) {
            toastErr(`${res.message}${res.code}`)
          }
          setTempRoomPic(res.data)
        })
        .catch((error) => {
          console.error('UPLOAD_CDN error', `${error}`)
        })
    }
  }
  const handleSave = async () => {
    setBtnLoading(true)

    const roomTitle = isEmpty(tempRoomTitle)
      ? getRoomTitleDefault(userStore.info.nickname)
      : tempRoomTitle
    const roomContent = isEmpty(tempRoomContent) ? '' : tempRoomContent
    const roomPic = isEmpty(tempRoomPic) ? roomPicDefault : tempRoomPic

    try {
      if (type === CreateRoomType.Create) {
        const res = await chatroomManager.createRoom({
          roomTitle,
          roomContent,
          ...(password.length === 4 ? { pw: password } : {}),
          roomPic,
        })

        if (res.code !== 200) {
          toastErr(`${res.message}${res.code}`)
        } else {
          onDone?.()
          if (res.data.roomId) {
            await store.joinRoom(res.data.roomId, '', true)
            store.roomDetial = res.data
            path(`/chatroom?roomId=${res.data.roomId}`)
          }
        }
      } else if (roomDetial.roomId) {
        const res = await chatroomManager.updateRoomDetail(
          roomDetial.roomId,
          roomPic,
          roomTitle,
          roomContent,
        )

        if (res.code !== 200) {
          toastErr(`${res.message}${res.code}`)
        } else {
          toastSuc(i18n.t('success'))
          onDone?.()
        }
      }
    } catch (error) {
      console.error('handleSave error:', error)
    } finally {
      setBtnLoading(false)
    }
  }

  const handleLockChanged = (value: any) => {
    if (value) {
      setPasswordIsOpen(true)
    } else {
      setPasswordIsOpen(false)
      setPassword('')
    }
    setCurSwitchValue(value)
  }
  const handlePwdCancel = () => {
    setPasswordIsOpen(false)
    setPassword('')
    setCurSwitchValue(false)
  }
  const handlePwdConfrim = async (password: string[]) => {
    if (password && password.length === 4) {
      setPassword(password.join(''))
      setCurSwitchValue(true)
      setPasswordIsOpen(false)
    }
  }
  return (
    <Flex>
      <SetPassword
        open={passwordIsOpen}
        onCancel={handlePwdCancel}
        onConfirm={handlePwdConfrim}
      />
      <Modal open={open} onCancel={onCancel} title={getTitle()}>
        <Flex
          vertical={true}
          className="mt-6 w-full items-center justify-center"
        >
          <Flex className="w-full items-start">
            <Flex vertical={true} className="w-[130px]">
              <div className="font-lexend text-[14px] font-bold">
                {i18n.t('room_cover')}
              </div>
              <Flex className="relative h-[67px] w-[120px] rounded-[8px]">
                <Flex className="absolute inset-0 items-center justify-center">
                  {isEmpty(tempRoomPic) === false ? (
                    <img
                      className="h-full w-full rounded-[8px] object-cover"
                      src={tempRoomPic}
                      alt="room cover"
                    />
                  ) : roomDetial.roomPic ? (
                    <img
                      className="h-full w-full rounded-[8px] object-cover"
                      src={roomDetial.roomPic}
                      alt="room cover"
                    />
                  ) : (
                    <img
                      className="h-full w-full rounded-[8px] object-cover"
                      src={roomPicDefault}
                    />
                  )}
                </Flex>
                <Flex className="absolute inset-0 cursor-pointer items-center justify-center">
                  <label htmlFor="imageUpload">
                    <IconRoomCreateHeader className="h-[40px] w-[40px] cursor-pointer" />
                  </label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleSendImage}
                    style={{ display: 'none' }}
                    id="imageUpload"
                  />
                </Flex>
              </Flex>
            </Flex>
            <Flex vertical={true} className="ml-[10px] mt-[2px] flex-grow">
              <div className="font-lexend text-[14px] font-bold">
                {i18n.t('room_name')}
              </div>
              <TextArea
                count={{
                  show: true,
                  max: 255,
                }}
                rows={type === CreateRoomType.Create ? 3 : 4}
                value={tempRoomTitle}
                onChange={(e) => {
                  if (e.target.value.length <= 255) {
                    setTempRoomTitle(e.target.value)
                  }
                }}
                style={{ resize: 'none' }}
              />
            </Flex>
          </Flex>

          {type === CreateRoomType.Create ? (
            <Flex className="mt-5 w-full items-center justify-between font-lexend text-[14px] font-bold">
              <span>{i18n.t('room_lock')}</span>
              <PKSwitch onChange={handleLockChanged} value={curSwitchValue} />
            </Flex>
          ) : null}

          <Flex vertical={true} className="mt-4 w-full items-start">
            <div className="font-lexend text-[14px] font-bold">
              {i18n.t('room_bulletin')}
            </div>
            <TextArea
              count={{
                show: true,
                max: 600,
              }}
              rows={6}
              value={tempRoomContent}
              onChange={(e) => {
                if (e.target.value.length <= 600) {
                  setTempRoomContent(e.target.value)
                }
              }}
              style={{ resize: 'none' }}
            />
          </Flex>
          <Flex className="mt-8 w-full">
            <ButtonBlack
              disabled={btnDisable || btnLoading}
              className="h-10 w-full"
              onClick={handleSave}
              loading={btnLoading}
            >
              <div>
                {type === CreateRoomType.Create
                  ? i18n.t('create_room_btn')
                  : i18n.t('done')}
              </div>
            </ButtonBlack>
          </Flex>
        </Flex>
      </Modal>
    </Flex>
  )
}

Component.displayName = 'CreateRoom'

export default Component
