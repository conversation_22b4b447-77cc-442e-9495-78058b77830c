import { Flex } from 'antd'
import { IconModeChat, IconModePerp, IconModeScreenShare } from '@/imgs/icons'
import { chatroomManager, Mode } from '@/managers/chatroomManager'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { observer } from 'mobx-react-lite'
import { useToastMessage } from '@/components/ToastMessage/useToastMessage'
import { userStore } from '@/store'
import { useEffect, useState } from 'react'
import clsx from 'clsx'
import i18n from '@/i18n'
import { SvgIconOnlineUsers } from '@/imgs/other'
import { PopupDown } from '@/components/PopupDown'
import OnlineUserList from './OnlineUserList'
// import { SvgIconOnlineUsers } from '@/imgs/other'
// import { PopupDown } from '@/components/PopupDown'
// import OnlineUserList from './OnlineUserList'
// import { ViewButton } from './ViewButton'

export type ModeSwitchType = 1 | 2 | 3 | 4

export const ModeSwitch = observer(
  ({
    type,
    onModeChanged,
  }: {
    type: 'desktop' | 'mobile'
    onModeChanged?: (mode: ModeSwitchType) => void
  }) => {
    const isTypeMobile = type === 'mobile'
    const { toastErr } = useToastMessage()
    const store = useStore()
    const [selectedKey, setSelectedKey] = useState<ModeSwitchType>(1)
    // const [visible, setVisible] = useState(false)

    useEffect(() => {
      switch (store.roomMode) {
        case Mode.Chat:
          setSelectedKey(1)
          break
        case Mode.Trade:
          setSelectedKey(2)
          break
        case Mode.Perp:
          setSelectedKey(3)
          break
        default:
          setSelectedKey(1)
      }
    }, [store.roomMode])

    useEffect(() => {
      onModeChanged && onModeChanged(selectedKey)
    }, [selectedKey])

    function onChatClick() {
      if (store.roomDetial.roomId && store.isHost(userStore.info.id)) {
        chatroomManager
          .updateRoomType(store.roomDetial.roomId, 3, '', '')
          .then((res) => {
            if (res.code !== 200) {
              toastErr(`${res.message}(${res.code})`)
              return
            }
            store.roomTagType = '3'
            store.handleModeChanged(Mode.Chat)
            setSelectedKey(1) // 设置选中的按钮
          })
          .catch((err) => {
            console.error('updateRoomType', err)
          })
      } else {
        store.handleModeChanged(Mode.Chat)
        setSelectedKey(1) // 设置选中的按钮
      }
    }

    function onTradeClick() {
      if (store.roomDetial.roomId && store.isHost(userStore.info.id)) {
        chatroomManager
          .updateRoomType(store.roomDetial.roomId, 1, '', '')
          .then((res) => {
            if (res.code !== 200) {
              toastErr(`${res.message}(${res.code})`)
              return
            }
            store.roomTagType = '1'
            store.handleModeChanged(Mode.Trade)
            setSelectedKey(2) // 设置选中的按钮
          })
          .catch((err) => {
            console.error('updateRoomType', err)
          })
      } else {
        store.handleModeChanged(Mode.Trade)
        setSelectedKey(2) // 设置选中的按钮
      }
    }

    function onPerpClick() {
      if (store.roomDetial.roomId && store.isHost(userStore.info.id)) {
        chatroomManager
          .updateRoomType(store.roomDetial.roomId, 4, '', '')
          .then((res) => {
            if (res.code !== 200) {
              toastErr(`${res.message}(${res.code})`)
              return
            }
            store.roomTagType = '4'
            store.handleModeChanged(Mode.Perp)
            setSelectedKey(3) // 设置选中的按钮
          })
          .catch((err) => {
            console.error('updateRoomType', err)
          })
      } else {
        store.handleModeChanged(Mode.Perp)
        setSelectedKey(3) // 设置选中的按钮
      }
    }

    function onScreenClick() {
      setSelectedKey(4)
    }

    const iconBtns = [
      {
        key: 1,
        mode: Mode.Chat,
        i18nName: 'chat',
        onClick: onChatClick,
        icon: <IconModeChat className="size-9" />,
      },
      {
        key: 2,
        mode: Mode.Trade,
        i18nName: 'spot',
        onClick: onTradeClick,
        icon: <IconModeScreenShare className="size-9" />,
      },
      {
        key: 3,
        mode: Mode.Perp,
        i18nName: 'perpetuo',
        onClick: onPerpClick,
        icon: <IconModePerp className="size-9" />,
      },
      isTypeMobile &&
        store.isScreenShareing && {
          key: 4,
          mode: 'screen',
          i18nName: 'screen',
          onClick: onScreenClick,
        },
    ]

    if (isTypeMobile) {
      return (
        <div className="flex h-full items-center justify-between px-2">
          <div className="flex h-full flex-1 items-center justify-start gap-4">
            {iconBtns.map((item: any, index) => (
              <Flex
                key={index}
                className={clsx(
                  'cursor-pointer justify-start py-2 text-center text-base font-normal leading-tight',
                  selectedKey === item.key
                    ? 'font-bold text-white'
                    : 'text-zinc-400',
                )}
                onClick={() => {
                  item.onClick()
                  setSelectedKey(item.key)
                }}
              >
                <div
                  className={clsx(
                    'border-b-2 pb-1',
                    selectedKey === item.key
                      ? 'border-b-2 border-white'
                      : 'border-none',
                  )}
                >
                  {i18n.t(item.i18nName)}
                </div>
              </Flex>
            ))}
          </div>
          {/* 
          <div className="relative flex items-center gap-2">
            <div
              className="flex cursor-pointer items-center gap-1"
              onClick={() => setVisible(true)}
            >
              <SvgIconOnlineUsers className="size-5" />
              {store.roomOnlineUserCount}
            </div>
          </div> */}

          {/* 用户列表 */}
          {/* <PopupDown visible={visible} onClose={() => setVisible(false)}>
            <div className="h-96 px-3">
              <OnlineUserList type="detailed" />
            </div>
          </PopupDown> */}
        </div>
      )
    }

    return (
      <Flex vertical={true} className="mt-6 items-center justify-start">
        {iconBtns.map((item: any, index: number) => (
          <Flex
            key={index}
            className={clsx(
              'mb-2 size-16 cursor-pointer items-center justify-center',
              selectedKey === item.key && 'bg-[#24262B]',
            )}
            onClick={() => {
              item.onClick()
              setSelectedKey(item.key)
            }}
          >
            <div className="flex flex-col items-center justify-center gap-0.5">
              <span>{item.icon}</span>
              <span>{i18n.t(item.i18nName)}</span>
            </div>
          </Flex>
        ))}
      </Flex>
    )
  },
)
