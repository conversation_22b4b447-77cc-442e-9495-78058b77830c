import React, { useEffect, useState } from 'react'
import { Flex } from 'antd'
import {
  CHAT_ROOM_LIST_ALL,
  CHAT_ROOM_LIST_DATA,
} from '@/api/interface/CHAT_ROOM_LIST_ALL'
import { lodash } from '@/utils'
import InfiniteScroll from 'react-infinite-scroll-component'
import { RoomCard } from './RoomCard'
import i18n from '@/i18n'
import { tradeStore } from '@/pages/Trade/store/tradeStore'
import { perpsStore } from '@/store'

interface RelatedLiveRoomsProps {
  modeType: 'trade' | 'perp'
}

export const RelatedLiveRooms: React.FC<RelatedLiveRoomsProps> = ({
  modeType,
}) => {
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [roomList, setRoomList] = useState<CHAT_ROOM_LIST_DATA[]>([])

  const loadAllRooms = async () => {
    if (loading) {
      return
    }
    setLoading(true)
    try {
      const res = await CHAT_ROOM_LIST_ALL({
        page: 1,
        size: 1000,
        tagContent:
          modeType == 'trade'
            ? tradeStore.currCrypto?.symbol
            : modeType == 'perp'
              ? perpsStore.init.coin
              : '',
        // tagType: modeType == 'trade' ? 1 : modeType == 'perp' ? 4 : '',
      })
      setLoading(false)
      if (res?.code !== 200) return
      const newRecords = res.data.records
      const mergedRecords = lodash.uniqBy([...roomList, ...newRecords], 'id')
      setRoomList(mergedRecords)
      setHasMore(res.data.last !== true)
    } catch (error) {
      console.error('Failed to load rooms:', error)
      setLoading(false)
    }
  }

  useEffect(() => {
    console.log(modeType, tradeStore.currCrypto?.symbol, perpsStore.init.coin)
    loadAllRooms()
  }, [modeType, tradeStore.currCrypto?.symbol, perpsStore.init.coin]) // 添加 modeType 作为依赖

  return (
    <Flex vertical={true} className="h-full w-full p-1">
      <div className="mb-4 text-lg font-bold text-white">相关直播</div>
      <div
        id="related-live-scrollable-div"
        className="h-full w-full overflow-y-auto overflow-x-hidden no-scroll"
      >
        {roomList.length === 0 && !loading ? (
          <Flex className="h-full items-center justify-center">
            <div className="font-lexend text-gray-400">暂无相关直播</div>
          </Flex>
        ) : (
          <InfiniteScroll
            dataLength={roomList.length}
            next={loadAllRooms}
            hasMore={hasMore}
            loader={
              <Flex className="items-center justify-center">
                <div className="pt-2 font-lexend text-gray-400">
                  {i18n.t('loading')}
                </div>
              </Flex>
            }
            scrollableTarget="related-live-scrollable-div"
          >
            <div className="mx-auto grid w-[200px] grid-cols-1 gap-4">
              {roomList.map((item) => (
                <RoomCard
                  key={item.id}
                  item={item}
                  type="HOME"
                  className="h-[228px] bg-[#2A2A2A]"
                />
              ))}
            </div>
          </InfiniteScroll>
        )}
      </div>
    </Flex>
  )
}
