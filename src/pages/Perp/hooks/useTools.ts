import { perpsStore } from '@/store'
import { PRECISION_TABLE } from '@/utils'
import numeral from 'numeral'

export const useTools = () => {
  /**
   * @description 价格格式化
   * @param {string} price 价格
   * @param {number} szDecimals 数量精度
   */
  function hlFormatPrice({
    price,
    szDecimals,
    isSimple,
  }: {
    price: string
    szDecimals?: number
    isSimple?: boolean
  }): string {
    if (!price) return '0'

    if (String(price).toLowerCase().includes('e')) return '0'

    let precision
    if (szDecimals !== undefined) {
      precision = szDecimals || 0
    } else {
      precision = perpsStore.meta?.activeMeta?.szDecimals || 0
    }

    // 计算最大小数位数
    const maxBit = Math.max(0, 6 - precision)

    const [before, after] = String(price).split('.')

    // 如果价格小于 1
    if (before && Number(before) === 0) {
      if (maxBit !== 6) return numeral(price).format(`0.${'0'.repeat(maxBit)}`)
      // 对小数位数=6的进行处理
      const afterStr = /^0+/.test(String(after))
      const maxWidth = afterStr ? 6 : 5
      return numeral(price).format(`0.${'0'.repeat(maxWidth)}`)
    }

    // 如果价格大于 1
    const maxWidth = Math.max(0, 5 - before.length)

    if (isSimple) {
      return numeral(price).format(`0.${'0'.repeat(maxWidth)}`)
    }
    return numeral(price).format(`0,0.${'0'.repeat(maxWidth)}`)
  }

  /**
   * @description 常规金额（USDC / USD）格式化
   * @param number 数量
   * @param precision 精度
   */
  function hlFormatNumber(number: string, precision?: number): string {
    if (!number) return '0'
    const bit = precision || PRECISION_TABLE.USDC
    return numeral(number).format(`0,0.${'0'.repeat(bit)}`)
  }

  /**
   * @description 币种数量格式化
   * @param number 数量
   */
  function hlFormatCoin(number: string, szDecimals?: number): string {
    if (!number) return '0'
    const bit = szDecimals || perpsStore.meta?.activeMeta?.szDecimals || 0
    return numeral(number).format(`0,0.${'0'.repeat(bit)}`)
  }

  /**
   * @description 币种数量格式化
   * @param number 数量
   */
  function hlFormatAnyCoin(number: string, szDecimals: number): string {
    if (!number) return '0'
    const bit = szDecimals || 0
    return numeral(number).format(`0,0.${'0'.repeat(bit)}`)
  }

  /**
   * @description 计算币种价格精度
   * @param {string} price 价格
   * @param {number} szDecimals 数量精度
   */
  function calculateCoinPrice(
    price: string | null,
    szDecimals?: number,
  ): number {
    if (!price) return 2
    if (String(price).toLowerCase().includes('e')) return 2

    let precision
    if (szDecimals !== undefined) {
      precision = szDecimals || 0
    } else {
      precision = perpsStore.meta?.activeMeta?.szDecimals || 0
    }

    const maxPrice = hlFormatPrice({ price, szDecimals: precision })
    const [, after] = String(maxPrice).split('.')
    // 返回小数剩余位数
    return Math.max(0, after?.length || 0)
  }

  /**
   * @description 过滤小数数据中末尾的 0
   * @param {string} number 价格
   */
  function filterZero(number: string | number | null): string {
    if (!number) return '0'
    let triggerPx = String(number)
    if (triggerPx.includes('.')) {
      triggerPx = triggerPx.replace(/(\.\d*?)0+$/, '$1')
      triggerPx = triggerPx.replace(/\.$/, '')
    }
    return triggerPx
  }

  return {
    hlFormatCoin,
    hlFormatPrice,
    hlFormatNumber,
    calculateCoinPrice,
    filterZero,
    hlFormatAnyCoin,
  }
}
