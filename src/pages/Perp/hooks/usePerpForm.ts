import { useTools } from '@/pages/Perp/hooks/useTools'
import { perpsStore } from '@/store'
import { $ten } from '@/utils'

export const usePerpForm = ({
  mode,
  szDecimals,
}: {
  mode: 'market' | 'limit'
  szDecimals: number
}) => {
  const { slippage } = perpsStore.init
  const { hlFormatPrice, filterZero } = useTools()

  // 计算滑点价格
  function calculateSlippagePrice(
    // 价格
    price: string | number,
    // 下单方向：B = 买单；A = 卖单
    side: 'B' | 'A',
    // 是否为止盈止损单
    isTrigger: boolean,
  ) {
    if (!price) return '0'

    const isMarket = mode === 'market'
    const isLimit = mode === 'limit'
    const isBid = side === 'B'
    const isAsk = side === 'A'

    let slippagePrice = '0'
    // 限价:普通买单
    if (isLimit && isBid) slippagePrice = price as string
    // 限价:普通卖单
    if (isLimit && isAsk) slippagePrice = price as string
    // 限价:止盈止损买单
    if (isLimit && isBid && isTrigger) {
      slippagePrice = $ten.mul(price, 1 - slippage / 100)
    }
    // 限价:止盈止损卖单
    if (isLimit && isAsk && isTrigger) {
      slippagePrice = $ten.mul(price, 1 + slippage / 100)
    }
    // 市价:普通买单
    if (isMarket && isBid && !isTrigger) {
      slippagePrice = $ten.mul(price, 1 + slippage / 100)
    }
    // 市价:普通卖单
    if (isMarket && isAsk && !isTrigger) {
      slippagePrice = $ten.mul(price, 1 - slippage / 100)
    }
    // 市价:止盈止损买单
    if (isMarket && isBid && isTrigger) {
      slippagePrice = $ten.mul(price, 1 - slippage / 100)
    }
    // 市价:止盈止损卖单
    if (isMarket && isAsk && isTrigger) {
      slippagePrice = $ten.mul(price, 1 + slippage / 100)
    }

    const value = hlFormatPrice({
      price: slippagePrice,
      szDecimals,
      isSimple: true,
    })

    return filterZero(value)
  }

  return { calculateSlippagePrice }
}
