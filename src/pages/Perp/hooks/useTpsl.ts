import { useTools } from '@/pages/Perp/hooks/useTools'
import { TPerpNumber } from '@/pages/Perp/module/types'
import { TCoinType, TSide } from '@/store/perps/types'
import { $ten } from '@/utils'

export const useTpsl = () => {
  const { hlFormatPrice } = useTools()

  /**
   * 通过价格计算百分比
   * @desc 计算盈利/亏损率 % = (止盈价/止损价 - 开仓价) / 开仓价 / 杠杆 * 100%
   */
  function priceToPct({
    exitPx,
    entryPx,
    direction,
    leverage,
  }: {
    // 止盈价/止损价
    exitPx: TPerpNumber
    // 开仓价
    entryPx: TPerpNumber
    // 杠杆
    leverage: number
    // 计算方向：
    // 止盈 = 买单 1; 卖单 -1;
    // 止损 = 买单 -1; 卖单 1;
    direction: '1' | '-1'
  }): string | null {
    if (!exitPx || !entryPx || !leverage) return null

    const pxRatio = $ten.div(exitPx, entryPx)
    const roiRatio = $ten.sub(pxRatio, '1')
    const dirRoi = $ten.mul(roiRatio, direction)
    const roeRatio = $ten.mul(dirRoi, leverage)
    const roePct = $ten.mul(roeRatio, '100')
    const roePctTo = $ten.div(roePct, '2')
    return $ten.toFixed(roePctTo, 2)
  }

  /**
   * 通过百分比计算盈亏价格 = 开仓价 * (1 + 盈亏百分比 / 杠杆)
   */
  function pctToPrice({
    roePct,
    entryPx,
    side,
    leverage,
    szDecimals,
    direction,
  }: {
    // 盈亏百分比
    roePct: TPerpNumber
    // 开仓价
    entryPx: TPerpNumber
    // 持仓方向「B = Bid/Buy, A = Ask/Sell」
    side: TSide
    // 杠杆
    leverage: number
    // 币种数量精度
    szDecimals: number
    // 计算方向:
    // 止盈 = 买单 -1; 卖单 1;
    // 止损 = 买单 1; 卖单 -1;
    direction: '1' | '-1'
  }): string | null {
    if (!roePct || !entryPx || !leverage) return null
    // 是否是买单
    const isBid = side === 'B'
    // 是否是止盈买单
    const isProfitBid = direction === '-1' && isBid
    // 是否是止损买单
    const isLossBid = direction === '1' && isBid

    // 调整交易方向
    const dirSymbol = $ten.mul(roePct || '0', isBid ? '-1' : '1')

    const roeRatio = $ten.div(dirSymbol, '100')
    const dirRoiRatio = $ten.div(roeRatio, leverage)
    const roiRatio = $ten.mul(dirRoiRatio, direction)
    const priceRatio =
      isProfitBid || isLossBid
        ? $ten.add(roiRatio, '1')
        : $ten.sub('1', roiRatio)
    const exitPx = $ten.mul(entryPx, priceRatio)

    return hlFormatPrice({
      price: exitPx,
      szDecimals,
      isSimple: true,
    })
  }

  /**
   * 通过价格计算盈亏金额
   */
  function priceToAmount({
    exitPx,
    entryPx,
    amount,
    coinType,
    side,
    direction,
  }: {
    // 止盈价/止损价
    exitPx: TPerpNumber
    // 开仓均价
    entryPx: TPerpNumber
    // 下单数量
    amount: TPerpNumber
    // 下单数量类型
    coinType: TCoinType
    // 持仓方向「B = Bid/Buy, A = Ask/Sell」
    side: TSide
    // 计算方向:
    // 止盈 = 买单 1; 卖单 -1;
    // 止损 = 买单 -1; 卖单 1;
    direction: '1' | '-1'
  }): string | null {
    if (!exitPx || !entryPx || !amount) return null

    // 下单数量类型
    const isUsdc = coinType === 'usdc'
    // 是否是买单
    const isBid = side === 'B'
    // 是否是卖单
    const isAsk = side === 'A'

    const baseQty = isUsdc ? $ten.div(amount, entryPx) : amount
    if (!baseQty || baseQty === '0') return '0'
    const dirRoi = $ten.mul(baseQty, direction)

    let profitLoss = '0'
    if (isBid) profitLoss = $ten.mul($ten.sub(exitPx, entryPx), dirRoi)
    if (isAsk) profitLoss = $ten.mul($ten.sub(entryPx, exitPx), dirRoi)

    return profitLoss
  }

  /**
   * 通过盈亏金额计算价格
   */
  function amountToPrice({
    exitAmount,
    entryPx,
    amount,
    coinType,
    side,
    szDecimals,
    direction,
  }: {
    // 止盈金额/止损金额
    exitAmount: TPerpNumber
    // 开仓均价
    entryPx: TPerpNumber
    // 下单数量
    amount: TPerpNumber
    // 下单数量类型
    coinType: TCoinType
    // 持仓方向「B = Bid/Buy, A = Ask/Sell」
    side: TSide
    // 币种数量精度
    szDecimals: number
    // 计算方向:
    // 止盈 = 买单 -1; 卖单 1;
    // 止损 = 买单 1; 卖单 -1;
    direction: '1' | '-1'
  }): string | null {
    if (!exitAmount || !entryPx || !amount) return null

    // 下单数量类型
    const isUsdc = coinType === 'usdc'
    // 是否是买单
    const isBid = side === 'B'
    // 是否是卖单
    const isAsk = side === 'A'

    // 调整交易方向
    const dirSymbol = $ten.mul(exitAmount || '0', isBid ? '-1' : '1')

    // 计算基础币数量
    const baseQty = isUsdc ? $ten.div(amount, entryPx) : amount

    if (!baseQty || baseQty === '0') return '0'
    const dirRoi = $ten.mul(baseQty, direction)

    let profitLoss = '0'
    if (isBid) profitLoss = $ten.add(entryPx, $ten.div(dirSymbol, dirRoi))
    if (isAsk) profitLoss = $ten.sub(entryPx, $ten.div(dirSymbol, dirRoi))

    if (!profitLoss) return '0'

    return hlFormatPrice({
      price: profitLoss,
      szDecimals,
      isSimple: true,
    })
  }

  return {
    priceToPct,
    pctToPrice,
    priceToAmount,
    amountToPrice,
  }
}
