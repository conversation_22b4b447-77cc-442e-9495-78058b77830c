import { useState, useEffect } from 'react'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { perpsStore } from '@/store'
dayjs.extend(utc)

/**
 * 资金费率结算倒计时
 * @param fundingHours 资金费率结算时间{小时}
 * @returns 倒计时
 */
export function useFundingCountdown(fundingHours?: number) {
  const [fundingIntervalHours, setFundingIntervalHours] = useState<number>(1)
  const [timeLeft, setTimeLeft] = useState('00:00:00')

  useEffect(() => {
    setFundingIntervalHours(
      fundingHours ||
        perpsStore.fee.activeFundingRate?.fundingIntervalHours ||
        1,
    )
  }, [perpsStore.fee.activeFundingRate, fundingHours])

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = dayjs().utc()
      const currentHour = now.hour()
      const currentMinute = now.minute()
      const currentSecond = now.second()

      // 计算下一个结算点的小时数
      let nextFundingHour =
        Math.ceil(
          (currentHour + currentMinute / 60 + currentSecond / 3600) /
            fundingIntervalHours,
        ) * fundingIntervalHours
      if (nextFundingHour >= 24) {
        nextFundingHour = nextFundingHour % 24
      }

      let nextFunding = now
        .set('hour', nextFundingHour)
        .set('minute', 0)
        .set('second', 0)
        .set('millisecond', 0)

      // 如果已经过了本周期的结算点，推到下一个周期
      if (nextFunding.isBefore(now)) {
        nextFunding = nextFunding.add(fundingIntervalHours, 'hour')
      }

      const diff = nextFunding.diff(now, 'second')
      const h = Math.floor(diff / 3600)
        .toString()
        .padStart(2, '0')
      const m = Math.floor((diff % 3600) / 60)
        .toString()
        .padStart(2, '0')
      const s = (diff % 60).toString().padStart(2, '0')
      return `${h}:${m}:${s}`
    }

    setTimeLeft(calculateTimeLeft())
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)
    return () => clearInterval(timer)
  }, [fundingIntervalHours])

  return { timeLeft }
}
