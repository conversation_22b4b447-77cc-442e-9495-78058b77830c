import { useTpsl } from '@/pages/Perp/hooks/useTpsl'
import { perpsStore } from '@/store'
import { TSide } from '@/store/perps/types'
import { $ten, isBlank } from '@/utils'
import { useMemo } from 'react'

export const useOrder = ({ coin }: { coin: string }) => {
  const { priceToAmount } = useTpsl()

  // 获取将要修改的持仓订单
  const activeOrder = useMemo(() => {
    return perpsStore.order.positionOrders.find(
      (item) => item.position.coin === coin,
    )
  }, [coin, perpsStore.order.positionOrders])

  // 获取具体参数
  const order = useMemo(() => {
    if (!activeOrder) return null

    const ctx = activeOrder.ctx
    const pos = activeOrder.position

    // 获取币种名称
    const name = ctx?.name
    // 获取持仓数量
    const szi = pos?.szi
    // 获取杠杆倍数
    const leverage = pos?.leverage?.value
    // 获取标记价格
    const markPx = ctx?.markPx
    // 获取开仓价格
    const entryPx = pos?.entryPx
    // 获取币种数量精度
    const szDecimals = ctx?.szDecimals
    // 获取资产id
    const assetid = ctx?.assetid
    // 获取爆仓价
    const liquidationPx = pos?.liquidationPx

    return {
      name,
      szi,
      leverage,
      markPx,
      entryPx,
      szDecimals,
      assetid,
      liquidationPx,
    }
  }, [activeOrder])

  // 获取订单方向
  const side: TSide = useMemo(() => {
    if (isBlank(order?.szi)) return 'B'
    return Number(order?.szi) >= 0 ? 'B' : 'A'
  }, [order?.szi])

  // 获取全仓止盈订单
  const positionTpslProfitOrder = useMemo(() => {
    return (
      perpsStore.order.pendingOrders.filter(
        (item) =>
          item.coin === coin &&
          item.isPositionTpsl &&
          item.orderType === 'Take Profit Market',
      )?.[0] || null
    )
  }, [coin, perpsStore.order.pendingOrders])

  // 获取全仓止损订单
  const positionTpslLossOrder = useMemo(() => {
    return (
      perpsStore.order.pendingOrders.filter(
        (item) =>
          item.coin === coin &&
          item.isPositionTpsl &&
          item.orderType === 'Stop Market',
      )?.[0] || null
    )
  }, [coin, perpsStore.order.pendingOrders])

  // 获取全仓止盈预期收益
  const profitToAmount = useMemo(() => {
    const exitPx = positionTpslProfitOrder?.triggerPx
    const entryPx = order?.entryPx || '0'
    const amount = order?.szi || '0'
    const direction = side === 'B' ? '1' : '-1'

    return $ten.toFixed(
      priceToAmount({
        exitPx,
        entryPx,
        amount,
        coinType: 'coin',
        side,
        direction,
      }) || '0',
      2,
    )
  }, [order, side, positionTpslProfitOrder])

  // 获取全仓止损预期收益
  const lossToAmount = useMemo(() => {
    const exitPx = positionTpslLossOrder?.triggerPx
    const entryPx = order?.entryPx || '0'
    const amount = order?.szi || '0'
    const direction = side === 'A' ? '1' : '-1'

    return $ten.toFixed(
      priceToAmount({
        exitPx,
        entryPx,
        amount,
        coinType: 'coin',
        side,
        direction,
      }) || '0',
      2,
    )
  }, [order, side, positionTpslLossOrder])

  return {
    order,
    side,
    positionTpslProfitOrder,
    positionTpslLossOrder,
    profitToAmount,
    lossToAmount,
  }
}
