import { UPD_ROOM_SETTING } from '@/api/interface/UPD_ROOM_SETTING'
import { UPD_ROOM_TAG } from '@/api/interface/UPD_ROOM_TAG'
import { LocalFullscreen } from '@/components/LocalFullsreen'
import { useRoute } from '@/hooks/useRoute'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { ModOpenOrder } from '@/pages/Perp/module/openOrder'
import { ModOrderTabs } from '@/pages/Perp/module/ModOrderTabs'
import { ModTopTabs } from '@/pages/Perp/module/ModTopTabs'
import { PerpHead } from '@/pages/Perp/module/PerpHead'
import { perpsStore, userStore } from '@/store'
import { observer } from 'mobx-react-lite'
import { useEffect } from 'react'
import { ModPerpProvider } from '@/pages/Perp/module/contPerp/provider'
import { Mode } from '@/managers/chatroomManager'
import { useMedia } from '@/hooks/useMedia'
import { PerpMobile } from '@/pages/Perp/PerpMobile'

export const Component = observer(() => {
  const { query } = useRoute()
  const { isMd } = useMedia()
  const store = useStore()

  const isHost = store.isHost(userStore.info.id)

  useEffect(() => {
    if (!query?.roomId) return

    if (isHost) {
      // 房主如果修改了交易信息，则更新语音房标签
      UPD_ROOM_TAG({
        tagContent: perpsStore.init.coin,
        roomId: query?.roomId,
        tagType: 4,
      })
      // 将房主的交易信息传递给后端
      UPD_ROOM_SETTING({
        roomId: query?.roomId,
        settingMap: {
          // 永续合约币种信息
          currCrypto: JSON.stringify({
            type: Mode.Perp,
            coin: perpsStore.init.coin,
          }),
        },
      })
    }
  }, [isHost, query?.roomId, perpsStore.init.coin])

  if (isMd) {
    return (
      <ModPerpProvider>
        <PerpMobile />
      </ModPerpProvider>
    )
  }

  return (
    <LocalFullscreen className="flex flex-col bg-[#131313]">
      <ModPerpProvider>
        {/* 头部固定高度 */}
        <div className="flex h-[60px] shrink-0 items-center justify-between border-b border-[#303030] px-3">
          <PerpHead />
        </div>
        {/* 交易区域 */}

        <div className="flex h-[calc(100%-60px)] w-full overflow-hidden">
          <div className="h-full flex-1">
            {/* 图表/订单簿/最新成交区域 */}
            <div className="h-[calc(100%-260px)] border-r border-[#303030]">
              <ModTopTabs />
            </div>
            {/* 订单区域 */}
            <div className="h-[250px] border-r border-[#303030]">
              <ModOrderTabs />
            </div>
          </div>
          {/* 下单区域 */}
          <div className="h-full w-[294px] shrink-0 overflow-y-auto overflow-x-hidden px-2.5 pb-4 pt-2 no-scroll">
            <ModOpenOrder />
          </div>
        </div>
      </ModPerpProvider>
    </LocalFullscreen>
  )
})

Component.displayName = 'PerpPage'

export default Component
