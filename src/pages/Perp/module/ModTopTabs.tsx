import { observer } from 'mobx-react-lite'
import { useState } from 'react'
import { ModKlineChart } from './ModKlineChart'
import { ModOrderBook } from './ModOrderBook'
import { ModLatestTrades } from './ModLatestTrades'
import i18n from '@/i18n'

export const ModTopTabs = observer(() => {
  const [activeTab, setActiveTab] = useState('chart')

  const tabs = [
    {
      id: 'chart',
      label: i18n.t('chart'),
    },
    {
      id: 'orderbook',
      label: i18n.t('orderbook'),
    },
    {
      id: 'latest',
      label: i18n.t('latest'),
    },
  ]

  return (
    <div className="flex h-full w-full flex-col overflow-hidden bg-[#131313] text-white">
      {/* Tabs */}
      <div className="border-b border-[#303030]">
        <div className="flex">
          {tabs.map((tab) => (
            <div
              key={tab.id}
              className={`cursor-pointer px-4 py-2 text-sm ${
                tab.id === activeTab
                  ? 'border-b-2 border-white text-white'
                  : 'text-[#868686]'
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label}
            </div>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div
        className="w-full flex-1 overflow-y-auto"
        style={{ position: 'relative' }}
      >
        {/* 图表 */}
        <div
          className="absolute inset-0 h-full w-full"
          style={{
            visibility: activeTab === 'chart' ? 'visible' : 'hidden',
            opacity: activeTab === 'chart' ? 1 : 0,
            transition: 'opacity 0.1s ease-in-out',
          }}
        >
          <ModKlineChart />
        </div>

        {/* 订单簿 */}
        <div
          className="absolute inset-0 h-full w-full"
          style={{
            visibility: activeTab === 'orderbook' ? 'visible' : 'hidden',
            opacity: activeTab === 'orderbook' ? 1 : 0,
            transition: 'opacity 0.1s ease-in-out',
          }}
        >
          <ModOrderBook />
        </div>

        {/* 最新成交 */}
        <div
          className="absolute inset-0 h-full w-full"
          style={{
            visibility: activeTab === 'latest' ? 'visible' : 'hidden',
            opacity: activeTab === 'latest' ? 1 : 0,
            transition: 'opacity 0.1s ease-in-out',
          }}
        >
          <ModLatestTrades />
        </div>
      </div>
    </div>
  )
})
