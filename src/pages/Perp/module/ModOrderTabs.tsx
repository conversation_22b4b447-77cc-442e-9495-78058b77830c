import { observer } from 'mobx-react-lite'
import { useState, useEffect } from 'react'
import { ModOrderArea } from './ModOrderArea'
import { Hex } from 'viem'
import { usePerp } from '@/pages/Perp/module/contPerp/usePerp'

export const ModOrderTabs = observer(() => {
  const { walletAddress } = usePerp()
  const [address, setAddress] = useState<Hex | null>(null)
  const [addressLoading, setAddressLoading] = useState(true)

  // 获取Arbitrum地址，优先从 walletStore 中获取
  useEffect(() => {
    if (walletAddress) {
      setAddress(walletAddress)
      setAddressLoading(false)
    } else {
      setAddressLoading(true)
    }
  }, [walletAddress])

  // 直接返回 ModOrderArea，不再显示自己的标签
  return <ModOrderArea address={address} addressLoading={addressLoading} />
})
