import { observer } from 'mobx-react-lite'
import { perpsStore } from '@/store'
import { useMemo, useState, useEffect } from 'react'
import { Hex } from 'viem'
import {
  PositionTab,
  CurrentOrderTab,
  HistoryTradeTab,
  FundingHistoryTab,
  HistoryOrdersTab,
} from './tabs'
import { TabContext } from './context/TabContext'
import i18n from '@/i18n'

interface ModOrderAreaProps {
  address: Hex | null
  addressLoading: boolean
  activeTab?: string
  setActiveTab?: React.Dispatch<React.SetStateAction<string>>
}

export const ModOrderArea = observer(
  ({
    address,
    addressLoading,
    activeTab: externalActiveTab,
    setActiveTab: externalSetActiveTab,
  }: ModOrderAreaProps) => {
    // State for active tab - 使用外部传入的或内部状态
    const [internalActiveTab, setInternalActiveTab] = useState('position')

    // 使用外部传入的或内部状态
    const activeTab = externalActiveTab || internalActiveTab
    const setActiveTab = externalSetActiveTab || setInternalActiveTab

    // Tab data
    const tabs = useMemo(
      () => [
        {
          id: 'position',
          label: `${i18n.t('position')} ${perpsStore.order.positionOrders.length > 0 ? `(${perpsStore.order.positionOrders.length})` : ''}`,
        },
        {
          id: 'current',
          label: `${i18n.t('current_order')} ${perpsStore.order.pendingOrders.length > 0 ? `(${perpsStore.order.pendingOrders.length})` : ''}`,
        },
        {
          id: 'history',
          label: `${i18n.t('history_trade')}`,
        },
        {
          id: 'funding',
          label: `${i18n.t('funding_history')}`,
        },
        {
          id: 'orders',
          label: `${i18n.t('history_order')}`,
        },
      ],
      [
        perpsStore.order.positionOrders.length,
        perpsStore.order.pendingOrders.length,
      ],
    )

    // 定义加载订单数据的函数
    const loadOrderData = async () => {
      try {
        // 使用从 props 传入的地址
        if (addressLoading || !address) {
          return
        }

        // 根据当前标签页加载相应的数据
        if (activeTab === 'position' || activeTab === 'all') {
          try {
            await perpsStore.order.getPositionOrders(address)
          } catch (error) {
            console.error('Failed to load position orders', error)
          }
        }

        if (activeTab === 'current' || activeTab === 'all') {
          try {
            await perpsStore.order.getPendingOrder(address)
          } catch (error) {
            console.error('Failed to load pending orders', error)
          }
        }

        if (activeTab === 'history' || activeTab === 'all') {
          try {
            await perpsStore.order.getHistoryOrder(address)
          } catch (error) {
            console.error('Failed to load history orders', error)
          }
        }

        if (activeTab === 'funding' || activeTab === 'all') {
          try {
            await perpsStore.order.getFundingHistory(address)
          } catch (error) {
            console.error('Failed to load funding history', error)
          }
        }

        if (activeTab === 'orders' || activeTab === 'all') {
          try {
            await perpsStore.order.getHistoricalOrders(address)
          } catch (error) {
            console.error('Failed to load historical orders', error)
          }
        }
      } catch (error) {
        console.error('loadOrderData - error: ', error)
      }
    }

    // 初始化 perpsStore.meta
    useEffect(() => {
      // 确保 perpsStore.meta 已初始化
      if (
        !perpsStore.meta.activeMeta ||
        Object.keys(perpsStore.meta.activeMeta).length === 0
      ) {
        // 初始化 meta 数据
        try {
          perpsStore.meta.getAllCoinsInfo()
        } catch (error) {
          console.error('Failed to initialize perpsStore.meta', error)
        }
      }
    }, [])

    // 当地址加载完成或变化时加载数据
    useEffect(() => {
      if (!addressLoading && address) {
        // 等待一下，确保 perpsStore.meta.activeCtx 已经初始化
        setTimeout(() => {
          loadOrderData()
        }, 1000)
      }
    }, [address, addressLoading])

    // 当标签页切换时重新加载数据
    useEffect(() => {
      loadOrderData()
    }, [activeTab])

    return (
      <TabContext.Provider value={{ setActiveTab, activeTab }}>
        <div className="flex h-full w-full flex-col bg-[#131313] text-white">
          {/* Tabs */}
          <div className="border-b border-[#303030]">
            <div
              className="scrollbar-hide overflow-x-auto"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              <div className="flex min-w-max pb-[1px] pr-10">
                {tabs.map((tab) => (
                  <div
                    key={tab.id}
                    className={`mr-2 cursor-pointer touch-manipulation select-none whitespace-nowrap px-4 py-2 text-sm transition-colors duration-200 ${tab.id === activeTab ? 'border-b-2 border-white font-medium text-white' : 'text-[#868686]'}`}
                    onClick={() => setActiveTab(tab.id)}
                  >
                    {tab.label}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Tab Content */}
          <div className="w-full min-w-0" style={{ boxSizing: 'border-box' }}>
            {activeTab === 'position' && <PositionTab />}
            {activeTab === 'current' && <CurrentOrderTab />}
            {activeTab === 'history' && <HistoryTradeTab />}
            {activeTab === 'funding' && <FundingHistoryTab />}
            {activeTab === 'orders' && <HistoryOrdersTab />}
          </div>
        </div>
      </TabContext.Provider>
    )
  },
)
