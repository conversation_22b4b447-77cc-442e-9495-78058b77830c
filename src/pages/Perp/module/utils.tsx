import { ColoringText } from '@/components/ColoringText'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { perpsStore } from '@/store'
import { Avatar, AvatarProps } from 'antd'
import { observer } from 'mobx-react-lite'
import { useMemo } from 'react'
import numeral from 'numeral'

/**
 * @description 24h涨跌 金额/百分比 UI渲染
 * @param markPx 标记价格
 * @param prevDayPx 前一天收盘价
 * @param szDecimals 精度
 */
export const DayChange = observer(
  ({
    markPx,
    prevDayPx,
    szDecimals,
  }: {
    markPx: string
    prevDayPx: string
    szDecimals?: number
  }) => {
    const { hlFormatNumber, calculateCoinPrice } = useTools()

    // 计算24h涨跌变化
    const dayChange = useMemo(() => {
      if (!markPx || !prevDayPx) return { amount: '0', percentage: '0' }
      return perpsStore.meta.dayChange(markPx, prevDayPx)
    }, [markPx, prevDayPx])

    const { amount, percentage } = dayChange

    const amountPrice = useMemo(() => {
      const maxWidth = calculateCoinPrice(markPx, szDecimals)
      return numeral(amount).format(`0,0.${'0'.repeat(maxWidth)}`)
    }, [amount, markPx, szDecimals])

    if (parseFloat(amount) === 0) {
      return (
        <ColoringText amount={amount}>
          {amountPrice}/{hlFormatNumber(percentage, 2)}%
        </ColoringText>
      )
    }

    if (parseFloat(amount) >= 0) {
      return (
        <ColoringText amount={amount}>
          +{amountPrice}/+
          {hlFormatNumber(percentage, 2)}%
        </ColoringText>
      )
    }

    return (
      <ColoringText amount={amount}>
        {amountPrice}/{hlFormatNumber(percentage, 2)}%
      </ColoringText>
    )
  },
)

/**
 * @description 获取 hyperliquid 币种图片
 */
export const HlAvatar = ({
  coin,
  className,
  ...props
}: {
  coin: string
  className?: string
  props?: AvatarProps
}): React.ReactNode => {
  const src = useMemo(() => {
    if (!coin) return ''
    const iconName = coin.replace('k', '')
    return `${import.meta.env.VITE_APP_HL_SVG_URL}/coins/${iconName}.svg`
  }, [coin])

  return (
    <Avatar
      {...props}
      className={className}
      src={<img src={src} alt="avatar" />}
    />
  )
}
