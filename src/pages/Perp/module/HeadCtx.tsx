import { perpsStore } from '@/store'
import { observer } from 'mobx-react-lite'
import { useMemo } from 'react'
import { useFundingCountdown } from '@/pages/Perp/hooks/useCountdown'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { ColoringText } from '@/components/ColoringText'
import { DayChange } from '@/pages/Perp/module/utils'
import i18n from '@/i18n'
import { useMedia } from '@/hooks/useMedia'
import clsx from 'clsx'

export const HeadCtx = observer(() => {
  const { isMd } = useMedia()
  const { timeLeft } = useFundingCountdown()
  const { hlFormatPrice, hlFormatNumber } = useTools()
  const {
    markPx,
    oraclePx,
    prevDayPx,
    szDecimals,
    dayNtlVlm,
    openInterest,
    funding,
  } = perpsStore.meta.activeMeta || {}

  // 合约持仓量
  const contractOpenInterest = useMemo(() => {
    if (!openInterest) return '0'
    return perpsStore.meta.calculateOpenPositions(openInterest, oraclePx)
  }, [openInterest, oraclePx])

  // 资金费率
  const fundingRate = useMemo(() => {
    if (!funding) return '0'
    return perpsStore.meta.calculateFunding(funding)
  }, [funding])

  const infoList = useMemo(() => {
    return [
      ...(!isMd
        ? [
            {
              label: i18n.t('mark_price'),
              value: markPx ? hlFormatPrice({ price: markPx }) : 'N/A',
            },
          ]
        : []),
      {
        label: i18n.t('oracle'),
        value: oraclePx ? hlFormatPrice({ price: oraclePx }) : 'N/A',
      },
      ...(!isMd
        ? [
            {
              label: i18n.t('change_24h'),
              value: (
                <DayChange
                  markPx={markPx}
                  prevDayPx={prevDayPx}
                  szDecimals={szDecimals}
                />
              ),
            },
          ]
        : []),
      {
        label: i18n.t('volume_24h'),
        value: `$${dayNtlVlm ? hlFormatNumber(dayNtlVlm) : 'N/A'}`,
      },
      {
        label: i18n.t('open_interest'),
        value: `$${contractOpenInterest ? hlFormatNumber(contractOpenInterest) : 'N/A'}`,
      },

      {
        label: i18n.t('funding_countdown'),
        value: (
          <div className="flex items-center">
            <ColoringText amount={funding}>
              {fundingRate ? fundingRate : 'N/A'}%
            </ColoringText>
            <span className="ml-2 text-white">{timeLeft}</span>
          </div>
        ),
      },
    ]
  }, [
    isMd,
    hlFormatNumber,
    hlFormatPrice,
    contractOpenInterest,
    funding,
    fundingRate,
    markPx,
    oraclePx,
    timeLeft,
    dayNtlVlm,
    prevDayPx,
    szDecimals,
  ])

  return (
    <div className="w-full font-['Lexend'] text-white lg:px-4 lg:py-2">
      <div
        className={clsx(
          isMd ? 'grid grid-cols-2 gap-x-5' : 'flex items-center gap-x-5',
        )}
      >
        {infoList.map((item) => (
          <div className="flex flex-col" key={item.label}>
            <span className="justify-start text-xs font-normal text-theme-secondary">
              {item.label}
            </span>
            <span className="justify-start text-xs font-normal text-gray-50">
              {item.value}
            </span>
          </div>
        ))}
      </div>
    </div>
  )
})
