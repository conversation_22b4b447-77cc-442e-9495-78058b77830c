import { observer } from 'mobx-react-lite'
import { useState, useMemo, useEffect } from 'react'
import { perpsStore } from '@/store'
import { usePerp } from '@/pages/Perp/module/contPerp/usePerp'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { DownOutlined } from '@ant-design/icons'
import { Dropdown, ConfigProvider } from 'antd'

// 自定义事件名称常量，与桌面版保持一致
const PRECISION_CHANGE_EVENT = 'orderbook_precision_change'

// 移动端订单簿，样式参考图片
/* eslint-disable react-hooks/rules-of-hooks */
export const ModOrderBookMobile = observer(() => {
  const { setLimitPrice } = usePerp()
  const { hlFormatPrice } = useTools()

  // 订单簿最大显示条数
  const depthLevel = 6

  // 精度状态
  const [precision, setPrecision] = useState<string>('0.01')
  // 是否用户手动设置了精度
  const [userSetPrecision, setUserSetPrecision] = useState<boolean>(false)
  // 加载状态
  const [isLoading, setIsLoading] = useState<boolean>(true)
  // 订阅状态
  const [isSubscribed, setIsSubscribed] = useState<boolean>(false)

  // 从sessionStorage获取精度设置，如果没有则使用默认值
  const getStoredPrecision = (coin: string): string => {
    try {
      const storedSettings = sessionStorage.getItem(
        'orderbook_precision_settings',
      )
      if (storedSettings) {
        const settings = JSON.parse(storedSettings)
        return settings[coin] || '0.01'
      }
    } catch (error) {
      console.error('获取精度设置失败:', error)
    }
    return '0.01'
  }

  // 保存精度设置到sessionStorage
  const savePrecisionSetting = (coin: string, value: string) => {
    try {
      const storedSettings = sessionStorage.getItem(
        'orderbook_precision_settings',
      )
      let settings: Record<string, string> = {}
      if (storedSettings) {
        settings = JSON.parse(storedSettings)
      }
      settings[coin] = value
      sessionStorage.setItem(
        'orderbook_precision_settings',
        JSON.stringify(settings),
      )
    } catch (error) {
      console.error('保存精度设置失败:', error)
    }
  }

  // 精度选择逻辑复用
  const getPrecisionOptionsByPrice = (price: number) => {
    if (!price || price <= 0) {
      // 默认返回最小精度
      return [
        { key: '0.000001', label: '0.000001' },
        { key: '0.000002', label: '0.000002' },
        { key: '0.000005', label: '0.000005' },
        { key: '0.00001', label: '0.00001' },
        { key: '0.0001', label: '0.0001' },
        { key: '0.001', label: '0.001' },
      ]
    }

    // 将价格转为字符串
    const priceStr = price.toString()

    // 分割整数部分和小数部分
    const parts = priceStr.split('.')

    // 获取小数位数
    const decimalPlaces = parts.length > 1 ? parts[1].length : 0

    // 根据小数位数确定基础单位
    let baseUnit = 1
    for (let i = 0; i < decimalPlaces; i++) {
      baseUnit *= 0.1
    }

    // 生成精度选项
    const baseUnitStr = baseUnit.toFixed(decimalPlaces)
    const doubleBaseUnitStr = (baseUnit * 2).toFixed(decimalPlaces)
    const fiveBaseUnitStr = (baseUnit * 5).toFixed(decimalPlaces)
    const tenBaseUnitStr = (baseUnit * 10).toFixed(decimalPlaces)
    const hundredBaseUnitStr = (baseUnit * 100).toFixed(decimalPlaces)
    const thousandBaseUnitStr = (baseUnit * 1000).toFixed(decimalPlaces)

    // 移除末尾的0
    const formatNumber = (numStr: string) => {
      if (!numStr.includes('.')) return numStr
      return numStr.replace(/\.?0+$/, '')
    }

    if (price >= 100000) {
      return [
        { key: '1', label: '1' },
        {
          key: formatNumber((baseUnit * 10).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 10).toFixed(decimalPlaces)),
        },
        {
          key: formatNumber((baseUnit * 20).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 20).toFixed(decimalPlaces)),
        },
        {
          key: formatNumber((baseUnit * 50).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 50).toFixed(decimalPlaces)),
        },
        {
          key: formatNumber((baseUnit * 100).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 100).toFixed(decimalPlaces)),
        },
        {
          key: formatNumber((baseUnit * 1000).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 1000).toFixed(decimalPlaces)),
        },
        {
          key: formatNumber((baseUnit * 10000).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 10000).toFixed(decimalPlaces)),
        },
      ]
    }

    return [
      { key: formatNumber(baseUnitStr), label: formatNumber(baseUnitStr) },
      {
        key: formatNumber(doubleBaseUnitStr),
        label: formatNumber(doubleBaseUnitStr),
      },
      {
        key: formatNumber(fiveBaseUnitStr),
        label: formatNumber(fiveBaseUnitStr),
      },
      {
        key: formatNumber(tenBaseUnitStr),
        label: formatNumber(tenBaseUnitStr),
      },
      {
        key: formatNumber(hundredBaseUnitStr),
        label: formatNumber(hundredBaseUnitStr),
      },
      {
        key: formatNumber(thousandBaseUnitStr),
        label: formatNumber(thousandBaseUnitStr),
      },
    ]
  }

  // 根据价格自动确定最佳精度选项
  const determineOptimalPrecision = (price: number): string => {
    if (!price || price <= 0) {
      return '0.000001'
    }

    // 获取价格对应的精度选项
    const options = getPrecisionOptionsByPrice(price)

    // 返回第一个选项（最小精度）
    if (options.length > 0) {
      return options[0].key
    }

    // 如果没有选项，返回默认值
    return '0.000001'
  }

  // 根据精度值获取选项索引
  const getPrecisionOptionIndex = (precisionValue: string): number => {
    // 获取当前币种价格
    const coinInfo = perpsStore.meta.getCoinInfo(perpsStore.init.coin)
    const price = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')

    // 获取当前价格对应的精度选项列表
    const options = getPrecisionOptionsByPrice(price)

    // 查找精度值在选项列表中的索引
    const index = options.findIndex((option) => option.key === precisionValue)

    // 如果找不到，返回0（第一个选项）
    return index >= 0 ? index : 0
  }

  // 根据价格和精度选项索引获取订阅参数
  const getSubscribeParamsByPriceAndIndex = (
    price: number,
    index: number,
  ): { nSigFigs: 2 | 3 | 4 | 5 | null; mantissa?: 2 | 5 } => {
    // 默认参数 - 用于价格为0或无效的情况
    const defaultParams: { nSigFigs: 2 | 3 | 4 | 5; mantissa?: 2 | 5 } = {
      nSigFigs: 5,
    }

    if (!price || price <= 0) {
      return defaultParams
    }

    // 获取价格对应的精度选项
    const options = getPrecisionOptionsByPrice(price)

    // 如果没有选项或索引超出范围，返回默认参数
    if (options.length === 0 || index < 0 || index >= options.length) {
      return defaultParams
    }

    // 获取选中的精度值
    const selectedPrecision = options[index].key
    const precisionValue = parseFloat(selectedPrecision)

    // 根据精度值确定订阅参数
    if (precisionValue >= 1) {
      if (price >= 100000) {
        if (precisionValue === 1) {
          return { nSigFigs: null }
        } else if (precisionValue === 10) {
          return { nSigFigs: 5 }
        } else if (precisionValue === 20) {
          return { nSigFigs: 5, mantissa: 2 }
        } else if (precisionValue === 50) {
          return { nSigFigs: 5, mantissa: 5 }
        } else if (precisionValue === 100) {
          return { nSigFigs: 4 }
        } else if (precisionValue == 1000) {
          return { nSigFigs: 3 }
        } else if (precisionValue == 10000) {
          return { nSigFigs: 2 }
        }
      } else {
        if (precisionValue === 1) {
          return { nSigFigs: 5 }
        } else if (precisionValue === 2) {
          return { nSigFigs: 5, mantissa: 2 }
        } else if (precisionValue === 5) {
          return { nSigFigs: 5, mantissa: 5 }
        } else if (precisionValue === 10) {
          return { nSigFigs: 4 }
        } else if (precisionValue === 100) {
          return { nSigFigs: 3 }
        } else if (precisionValue === 1000) {
          return { nSigFigs: 2 }
        }
      }
    } else if (precisionValue >= 0.1) {
      if (precisionValue === 0.1) {
        return { nSigFigs: 5 }
      } else if (precisionValue === 0.2) {
        return { nSigFigs: 5, mantissa: 2 }
      } else if (precisionValue === 0.5) {
        return { nSigFigs: 5, mantissa: 5 }
      }
    } else if (precisionValue >= 0.01) {
      if (precisionValue === 0.01) {
        return { nSigFigs: 5 }
      } else if (precisionValue === 0.02) {
        return { nSigFigs: 5, mantissa: 2 }
      } else if (precisionValue === 0.05) {
        return { nSigFigs: 5, mantissa: 5 }
      }
    } else if (precisionValue >= 0.001) {
      if (precisionValue === 0.001) {
        return { nSigFigs: 5 }
      } else if (precisionValue === 0.002) {
        return { nSigFigs: 5, mantissa: 2 }
      } else if (precisionValue === 0.005) {
        return { nSigFigs: 5, mantissa: 5 }
      }
    } else if (precisionValue >= 0.0001) {
      if (precisionValue === 0.0001) {
        return { nSigFigs: 5 }
      } else if (precisionValue === 0.0002) {
        return { nSigFigs: 5, mantissa: 2 }
      } else if (precisionValue === 0.0005) {
        return { nSigFigs: 5, mantissa: 5 }
      }
    } else {
      if (precisionValue.toString().endsWith('1')) {
        return { nSigFigs: 5 }
      } else if (precisionValue.toString().endsWith('2')) {
        return { nSigFigs: 5, mantissa: 2 }
      } else if (precisionValue.toString().endsWith('5')) {
        return { nSigFigs: 5, mantissa: 5 }
      }
    }

    // 默认返回最高精度
    return { nSigFigs: 5 }
  }

  // 统一的订阅函数
  const subscribeToOrderBook = (coin: string, precisionValue: string) => {
    if (!coin) return

    // 获取币种信息和价格
    const coinInfo = perpsStore.meta.getCoinInfo(coin)
    const price = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')

    // 获取精度选项索引
    const optionIndex = getPrecisionOptionIndex(precisionValue)
    // 获取订阅参数
    const subscribeParams = getSubscribeParamsByPriceAndIndex(
      price,
      optionIndex,
    )

    // 构建订阅选项
    const subscribeOptions: any = {
      coin,
      nSigFigs: subscribeParams.nSigFigs,
    }

    if (subscribeParams.mantissa === 2 || subscribeParams.mantissa === 5) {
      subscribeOptions.mantissa = subscribeParams.mantissa
    }

    // 设置加载状态
    setIsLoading(true)
    // 订阅订单簿
    perpsStore.book.subscribeBook(subscribeOptions)
    setIsSubscribed(true)
  }

  // 初始化订阅
  useEffect(() => {
    if (perpsStore.init.coin && !isSubscribed) {
      subscribeToOrderBook(perpsStore.init.coin, precision)

      // 设置超时检查
      const timeoutId = setTimeout(() => {
        if (isLoading) {
          subscribeToOrderBook(perpsStore.init.coin, precision)
        }
      }, 5000)

      return () => {
        clearTimeout(timeoutId)
        if (isSubscribed) {
          perpsStore.book.unsubscribeBook()
          setIsSubscribed(false)
        }
      }
    }
  }, [perpsStore.init.coin, isSubscribed])

  // 监听精度变化
  useEffect(() => {
    if (perpsStore.init.coin && isSubscribed) {
      subscribeToOrderBook(perpsStore.init.coin, precision)
    }
  }, [precision])

  // 监听订单簿数据变化
  useEffect(() => {
    if (perpsStore.book.data?.levels) {
      const hasData =
        perpsStore.book.data.levels[0]?.length > 0 ||
        perpsStore.book.data.levels[1]?.length > 0

      if (hasData && isLoading) {
        setIsLoading(false)
      }
    }
  }, [perpsStore.book.data, isLoading])

  // 处理精度变化
  const handlePrecisionChange = (value: string) => {
    if (value === precision) return

    const coinInfo = perpsStore.meta.getCoinInfo(perpsStore.init.coin)
    const price = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')
    const options = getPrecisionOptionsByPrice(price)
    const isPrecisionInOptions = options.some((option) => option.key === value)
    const finalValue = isPrecisionInOptions
      ? value
      : options.length > 0
        ? options[0].key
        : value

    setPrecision(finalValue)
    setUserSetPrecision(true)
    savePrecisionSetting(perpsStore.init.coin, finalValue)

    // 触发自定义事件
    const event = new CustomEvent(PRECISION_CHANGE_EVENT, {
      detail: { coin: perpsStore.init.coin, precision: finalValue },
    })
    window.dispatchEvent(event)
  }

  // 监听其他组件触发的精度变化事件
  useEffect(() => {
    const handlePrecisionChangeEvent = (e: CustomEvent) => {
      const { coin, precision: newPrecision } = e.detail

      // 只处理当前币种的事件，避免跨币种更新
      if (coin === perpsStore.init.coin && newPrecision !== precision) {
        setPrecision(newPrecision)
        setUserSetPrecision(true)
      }
    }

    // 添加事件监听器
    window.addEventListener(
      PRECISION_CHANGE_EVENT,
      handlePrecisionChangeEvent as EventListener,
    )

    // 清理事件监听器
    return () => {
      window.removeEventListener(
        PRECISION_CHANGE_EVENT,
        handlePrecisionChangeEvent as EventListener,
      )
    }
  }, [precision, perpsStore.init.coin])

  // 处理价格精度
  const processPriceByPrecision = (price: number): number => {
    const precisionValue = parseFloat(precision)
    if (Number.isInteger(precisionValue)) {
      return Math.round(price / precisionValue) * precisionValue
    }
    const multiplier = 1 / precisionValue
    return Math.round(price * multiplier) / multiplier
  }

  // 合并订单
  const mergeOrdersByPrice = (orders: any[], isAsk: boolean) => {
    if (!orders || orders.length === 0) return []
    const priceMap = new Map()
    orders.forEach((order) => {
      const originalPrice = parseFloat(order.px)
      const processedPrice = processPriceByPrecision(originalPrice)
      const size = parseFloat(order.sz)
      if (priceMap.has(processedPrice)) {
        const existingOrder = priceMap.get(processedPrice)
        existingOrder.sz = (parseFloat(existingOrder.sz) + size).toString()
      } else {
        priceMap.set(processedPrice, {
          px: processedPrice.toString(),
          sz: order.sz,
        })
      }
    })
    const mergedOrders = Array.from(priceMap.values())
    if (isAsk) {
      // 卖单按价格从低到高排序，取最低的6个
      mergedOrders.sort((a, b) => parseFloat(a.px) - parseFloat(b.px))
      const lowestAsks = mergedOrders.slice(0, depthLevel)
      // 返回时反转顺序，使其从高到低显示
      return lowestAsks.reverse()
    } else {
      // 买单按价格从高到低排序，取最高的6个
      mergedOrders.sort((a, b) => parseFloat(b.px) - parseFloat(a.px))
      return mergedOrders.slice(0, depthLevel)
    }
  }

  // 计算累计值和最大值
  const calculateAskTotals = (orders: any[]): number[] => {
    const totals: number[] = []
    let runningTotal = 0
    for (let i = orders.length - 1; i >= 0; i--) {
      runningTotal += parseFloat(orders[i].sz)
      totals[i] = runningTotal
    }
    return totals
  }
  const calculateBidTotals = (orders: any[]): number[] => {
    const totals: number[] = []
    let runningTotal = 0
    for (let i = 0; i < orders.length; i++) {
      runningTotal += parseFloat(orders[i].sz)
      totals[i] = runningTotal
    }
    return totals
  }

  // 监听币种变化，更新精度设置
  useEffect(() => {
    if (perpsStore.init.coin) {
      const coin = perpsStore.init.coin

      // 获取币种信息和价格
      const coinInfo = perpsStore.meta.getCoinInfo(coin)
      const price = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')

      // 获取当前价格对应的精度选项列表
      const currentPrecisionOptions = getPrecisionOptionsByPrice(price)

      // 检查是否有保存的精度设置
      const savedPrecision = getStoredPrecision(coin)

      // 检查保存的精度是否在当前选项列表中
      const isPrecisionInOptions = currentPrecisionOptions.some(
        (option) => option.key === savedPrecision,
      )

      if (savedPrecision && isPrecisionInOptions) {
        // 如果有保存的设置且在当前选项列表中，使用保存的设置
        setPrecision(savedPrecision)
        setUserSetPrecision(true)
      } else {
        // 如果没有保存的设置或保存的设置不在当前选项列表中
        if (price > 0 && currentPrecisionOptions.length > 0) {
          // 使用第一个精度选项
          const firstPrecision = currentPrecisionOptions[0].key
          setPrecision(firstPrecision)
          // 保存设置
          savePrecisionSetting(coin, firstPrecision)
          setUserSetPrecision(true)
        } else {
          // 如果无法获取精度选项，重置用户设置标志，以便自动设置精度
          setUserSetPrecision(false)
        }
      }
    }
  }, [perpsStore.init.coin])

  // 订单数据
  const {
    askOrders,
    bidOrders,
    askTotals,
    bidTotals,
    maxAskTotal,
    maxBidTotal,
    currentPrice,
  } = useMemo(() => {
    const data = perpsStore.book.data
    const asks = mergeOrdersByPrice(data?.levels?.[1] || [], true)
    const bids = mergeOrdersByPrice(data?.levels?.[0] || [], false)

    // 按照价格从高到低排序卖单
    const sortedAsks = [...asks].sort(
      (a, b) => parseFloat(b.px) - parseFloat(a.px),
    )

    // 计算累计值
    const askTotals = calculateAskTotals(sortedAsks)
    const bidTotals = calculateBidTotals(bids)

    // 获取最大累计值
    const maxAskTotal = askTotals.length > 0 ? Math.max(...askTotals) : 1
    const maxBidTotal = bidTotals.length > 0 ? Math.max(...bidTotals) : 1

    // 当前价取最优买/卖中间
    const coinInfo = perpsStore.meta.getCoinInfo(perpsStore.init.coin)
    let mid = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')
    if (asks.length && bids.length) {
      mid = (parseFloat(sortedAsks[0].px) + parseFloat(bids[0].px)) / 2
    }
    return {
      askOrders: sortedAsks,
      bidOrders: bids,
      askTotals,
      bidTotals,
      maxAskTotal,
      maxBidTotal,
      currentPrice: mid,
    }
  }, [perpsStore.book.data, precision])

  // 格式化数量，保留5位小数，最后一位是0的话舍去
  const formatAmount = (amount: string) => {
    const num = parseFloat(amount)
    if (isNaN(num)) return '0'

    // 先格式化为5位小数
    let formatted = num.toFixed(5)
    // 移除末尾的零
    while (formatted.endsWith('0')) {
      formatted = formatted.slice(0, -1)
    }
    // 如果以小数点结尾，移除小数点
    if (formatted.endsWith('.')) {
      formatted = formatted.slice(0, -1)
    }
    return formatted
  }
  const formatPrice = (price: string) => {
    const num = parseFloat(price)
    if (isNaN(num)) return '0'
    const coinInfo = perpsStore.meta.getCoinInfo(perpsStore.init.coin)
    const szDecimals = coinInfo?.szDecimals || 0
    return hlFormatPrice({ price: num.toString(), szDecimals, isSimple: true })
  }

  // 颜色
  const askColor = '#FF5C5C'
  const bidColor = '#00D09C'
  const priceColor = '#FF2222'

  // 只取要显示的6条
  const askDisplay = askOrders.slice(0, depthLevel)
  const bidDisplay = bidOrders.slice(0, depthLevel)

  // 只对这6条做累计量
  const askDisplayTotals: number[] = []
  let askRunning = 0
  for (let i = askDisplay.length - 1; i >= 0; i--) {
    askRunning += parseFloat(askDisplay[i]?.sz || '0')
    askDisplayTotals[i] = askRunning
  }
  const maxAskDisplayTotal =
    askDisplayTotals.length > 0 ? Math.max(...askDisplayTotals) : 1

  const bidDisplayTotals: number[] = []
  let bidRunning = 0
  for (let i = 0; i < bidDisplay.length; i++) {
    bidRunning += parseFloat(bidDisplay[i]?.sz || '0')
    bidDisplayTotals[i] = bidRunning
  }
  const maxBidDisplayTotal =
    bidDisplayTotals.length > 0 ? Math.max(...bidDisplayTotals) : 1

  return (
    <div
      className="relative flex h-full w-full flex-col bg-[#131313] text-xs text-white"
      style={{ minWidth: 120 }}
    >
      {/* 表头 */}
      <div className="flex justify-between px-2 pb-1 pt-2 text-[#868686]">
        <div>Price</div>
        <div>Size({perpsStore.init.coin})</div>
      </div>
      {/* 卖单（红） */}
      <div className="flex flex-col">
        {askDisplay.map((order, idx) => {
          // 计算背景色宽度，随累计量递增
          const askTotal = askDisplayTotals[idx] || 0
          const askWidth =
            maxAskDisplayTotal > 0 ? (askTotal / maxAskDisplayTotal) * 100 : 0
          // 确保至少有5%的宽度
          const finalWidth = Math.max(5, askWidth)

          return (
            <div
              key={order.px + idx}
              className="flex cursor-pointer items-center justify-between px-2 py-1"
              style={{
                color: askColor,
                fontWeight: 500,
                fontVariantNumeric: 'tabular-nums',
                position: 'relative',
              }}
              onClick={() => setLimitPrice(formatPrice(order.px))}
            >
              {/* 背景色 - 宽度随累计量变化，opacity 固定 */}
              <div
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  bottom: 0,
                  width: `${finalWidth}%`,
                  backgroundColor: askColor,
                  opacity: 0.15,
                  zIndex: 0,
                }}
              />
              <span style={{ position: 'relative', zIndex: 1 }}>
                {formatPrice(order.px)}
              </span>
              <span
                style={{
                  color: '#fff',
                  opacity: 0.7,
                  fontWeight: 400,
                  position: 'relative',
                  zIndex: 1,
                }}
              >
                {formatAmount(order.sz)}
              </span>
            </div>
          )
        })}
      </div>
      {/* 当前价高亮 */}
      <div
        className="flex items-center justify-between px-2 py-1 text-base font-bold"
        style={{ color: priceColor }}
      >
        <span>
          ${' '}
          {formatPrice(
            askDisplay.length > 0 && bidDisplay.length > 0
              ? (
                  (parseFloat(askDisplay[askDisplay.length - 1].px) +
                    parseFloat(bidDisplay[0].px)) /
                  2
                ).toString()
              : perpsStore.meta.getCoinInfo(perpsStore.init.coin)?.markPx ||
                  '0',
          )}
        </span>
        <span style={{ color: '#fff', opacity: 0.7, fontWeight: 400 }}>
          {/* 可选显示总量 */}
        </span>
      </div>
      {/* 买单（绿） */}
      <div className="flex flex-col">
        {bidDisplay.map((order, idx) => {
          // 计算背景色宽度，随累计量递增
          const bidTotal = bidDisplayTotals[idx] || 0
          const bidWidth =
            maxBidDisplayTotal > 0 ? (bidTotal / maxBidDisplayTotal) * 100 : 0
          // 确保至少有5%的宽度
          const finalWidth = Math.max(5, bidWidth)

          return (
            <div
              key={order.px + idx}
              className="flex cursor-pointer items-center justify-between px-2 py-1"
              style={{
                color: bidColor,
                fontWeight: 500,
                fontVariantNumeric: 'tabular-nums',
                position: 'relative',
              }}
              onClick={() => setLimitPrice(formatPrice(order.px))}
            >
              {/* 背景色 - 宽度随累计量变化，opacity 固定 */}
              <div
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  bottom: 0,
                  width: `${finalWidth}%`,
                  backgroundColor: bidColor,
                  opacity: 0.15,
                  zIndex: 0,
                }}
              />
              <span style={{ position: 'relative', zIndex: 1 }}>
                {formatPrice(order.px)}
              </span>
              <span
                style={{
                  color: '#fff',
                  opacity: 0.7,
                  fontWeight: 400,
                  position: 'relative',
                  zIndex: 1,
                }}
              >
                {formatAmount(order.sz)}
              </span>
            </div>
          )
        })}
      </div>
      {/* 精度选择下拉 */}
      <div className="absolute bottom-0 mt-1 flex items-center justify-start px-2 py-2">
        <ConfigProvider
          theme={{
            token: {
              colorBgContainer: '#232323',
              colorBgElevated: '#232323',
              colorText: '#F9FAFB',
            },
            components: {
              Dropdown: {
                colorBgElevated: '#232323',
                controlItemBgHover: '#2A2A2A',
                controlItemBgActive: '#2A2A2A',
              },
            },
          }}
        >
          <Dropdown
            menu={{
              onClick: (e) => handlePrecisionChange(e.key),
              selectedKeys: [precision],
              items: (() => {
                // 获取当前币种价格
                const coinInfo = perpsStore.meta.getCoinInfo(
                  perpsStore.init.coin,
                )
                const price = Number(
                  coinInfo?.markPx || coinInfo?.oraclePx || '0',
                )

                // 使用getPrecisionOptionsByPrice函数获取精度选项
                const options = getPrecisionOptionsByPrice(price)

                // 检查当前精度是否在选项列表中
                const isPrecisionInOptions = options.some(
                  (option) => option.key === precision,
                )

                return options
              })(),
            }}
            trigger={['click']}
            placement="bottomLeft"
            arrow={false}
            destroyPopupOnHide
          >
            <button className="flex items-center rounded border border-[#303030] bg-[#232323] px-2 py-1 text-xs">
              <span>{precision}</span>
              <DownOutlined className="ml-1 text-xs" />
            </button>
          </Dropdown>
        </ConfigProvider>
      </div>
    </div>
  )
})
/* eslint-enable react-hooks/rules-of-hooks */
