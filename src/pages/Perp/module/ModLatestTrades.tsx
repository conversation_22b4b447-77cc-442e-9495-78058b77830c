import { observer } from 'mobx-react-lite'
import { perpsStore } from '@/store'
import { useEffect, useMemo } from 'react'
import i18n from '@/i18n'
import { useTools } from '@/pages/Perp/hooks/useTools'

export const ModLatestTrades = observer(() => {
  // 订阅最新成交数据
  useEffect(() => {
    if (perpsStore.init.coin) {
      const coin = perpsStore.init.coin

      // 订阅最新成交
      perpsStore.trades.subscribeTrades(coin)

      return () => {
        // 取消订阅
        perpsStore.trades.unsubscribeTrades()
      }
    }
  }, [perpsStore.init.coin])

  // 使用 useMemo 缓存排序后的数据
  const latestTrades = useMemo(() => {
    return [...(perpsStore.trades.data || [])]
      .sort((a, b) => b.time - a.time)
      .slice(0, 30)
  }, [perpsStore.trades.data])

  const { hlFormatPrice } = useTools()

  // 格式化数量，保留5位小数，最后一位是0的话舍去
  const formatAmount = (amount: string) => {
    const num = parseFloat(amount)
    // 先格式化为5位小数
    let formatted = num.toFixed(5)
    // 移除末尾的零
    while (formatted.endsWith('0')) {
      formatted = formatted.slice(0, -1)
    }
    // 如果以小数点结尾，移除小数点
    if (formatted.endsWith('.')) {
      formatted = formatted.slice(0, -1)
    }
    return formatted
  }

  // 格式化时间为 HH:MM:SS
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    return `${hours}:${minutes}:${seconds}`
  }

  // 跳转到区块浏览器查看交易详情
  const openExplorer = (txHash: string) => {
    if (!txHash) return
    // 使用环境变量中的域名构建区块浏览器URL
    const baseUrl = import.meta.env.VITE_APP_HL_SVG_URL || ''
    const explorerUrl = `${baseUrl}/explorer/tx/${txHash}`
    window.open(explorerUrl, '_blank')
  }

  // 自定义滚动条样式
  const scrollbarStyle = `
    .custom-scrollbar::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: transparent;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 6px;
      border: 2px solid transparent;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
    .custom-scrollbar::-webkit-scrollbar-corner {
      background: transparent;
    }
  `

  return (
    <div className="flex h-full flex-col bg-[#131313] text-white">
      {/* 添加滚动条样式 */}
      <style>{scrollbarStyle}</style>

      {/* 表头 */}
      <table className="w-full table-fixed border-collapse">
        <thead>
          <tr className="border-b border-[#303030] text-xs text-[#868686]">
            <th className="w-1/3 px-2 py-2 text-left">{i18n.t('tit_price')}</th>
            <th className="w-1/3 px-2 py-2 text-center">
              {i18n.t('amount')} ({perpsStore.init.coin || '--'})
            </th>
            <th className="w-1/3 px-2 py-2 text-right">
              <div className="flex items-center justify-end">
                {i18n.t('time')}
              </div>
            </th>
          </tr>
        </thead>
      </table>

      {/* 交易列表 - 可滚动 */}
      <div
        className="custom-scrollbar w-full overflow-y-auto"
        style={{ height: 'calc(100% - 40px)' }}
      >
        <table className="w-full table-fixed border-collapse">
          <tbody>
            {latestTrades.map((trade, index) => {
              // 判断是做多还是做空
              const isBuy = trade.side === 'B'
              // 根据做多做空设置颜色
              const priceColor = isBuy ? 'text-[#00D09C]' : 'text-[#FF5C5C]'
              // 获取币种精度
              const coinInfo = perpsStore.meta.getCoinInfo(perpsStore.init.coin)
              const szDecimals = coinInfo?.szDecimals || 0
              return (
                <tr key={`trade-${trade.tid}-${index}`} className="text-sm">
                  <td className={`w-1/3 px-2 py-1 text-left ${priceColor}`}>
                    {hlFormatPrice({
                      price: trade.px,
                      szDecimals,
                      isSimple: true,
                    })}
                  </td>
                  <td className="w-1/3 px-2 py-1 text-center">
                    {formatAmount(trade.sz)}
                  </td>
                  <td className="w-1/3 px-2 py-1 text-right">
                    <div className="flex items-center justify-end">
                      <span className="mr-1">{formatTime(trade.time)}</span>
                      <button
                        className="text-[#868686] hover:text-white"
                        onClick={() => openExplorer(trade.hash)}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                          <polyline points="15 3 21 3 21 9"></polyline>
                          <line x1="10" y1="14" x2="21" y2="3"></line>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>
    </div>
  )
})
