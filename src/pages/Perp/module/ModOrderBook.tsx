import { observer } from 'mobx-react-lite'
import { runInAction } from 'mobx'
import { perpsStore } from '@/store'
import { useState, useEffect, useMemo } from 'react'
import { DownOutlined } from '@ant-design/icons'
import { Dropdown, ConfigProvider } from 'antd'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { usePerp } from '@/pages/Perp/module/contPerp/usePerp'
import i18n from '@/i18n'

// 自定义事件名称常量
const PRECISION_CHANGE_EVENT = 'orderbook_precision_change'

export const ModOrderBook = observer(() => {
  const { setLimitPrice } = usePerp()
  // 获取工具函数
  const { hlFormatPrice } = useTools()

  // 从sessionStorage获取精度设置，如果没有则使用默认值
  const getStoredPrecision = (coin: string): string => {
    try {
      const storedSettings = sessionStorage.getItem(
        'orderbook_precision_settings',
      )
      if (storedSettings) {
        const settings = JSON.parse(storedSettings)
        return settings[coin] || '0.01'
      }
    } catch (error) {
      console.error('获取精度设置失败:', error)
    }
    return '0.01'
  }

  // 精度和单位状态
  const [precision, setPrecision] = useState<string>(
    getStoredPrecision(perpsStore.init.coin),
  )
  // 设置单位，默认为USD
  const [unit, setUnit] = useState<string>('USD')
  // 加载状态
  const [isLoading, setIsLoading] = useState<boolean>(true)
  // 是否用户手动设置了精度
  const [userSetPrecision, setUserSetPrecision] = useState<boolean>(false)
  // 订阅状态
  const [isSubscribed, setIsSubscribed] = useState<boolean>(false)

  // 统一的订阅函数
  const subscribeToOrderBook = (coin: string, precisionValue: string) => {
    if (!coin) return

    // 获取币种信息和价格
    const coinInfo = perpsStore.meta.getCoinInfo(coin)
    const price = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')

    // 获取精度选项索引
    const optionIndex = getPrecisionOptionIndex(precisionValue)
    // 获取订阅参数
    const subscribeParams = getSubscribeParamsByPriceAndIndex(
      price,
      optionIndex,
    )

    // 构建订阅选项
    const subscribeOptions: any = {
      coin,
      nSigFigs: subscribeParams.nSigFigs,
    }

    if (subscribeParams.mantissa === 2 || subscribeParams.mantissa === 5) {
      subscribeOptions.mantissa = subscribeParams.mantissa
    }

    // 设置加载状态
    setIsLoading(true)
    // 订阅订单簿
    perpsStore.book.subscribeBook(subscribeOptions)
    setIsSubscribed(true)
  }

  // 初始化订阅
  useEffect(() => {
    if (perpsStore.init.coin && !isSubscribed) {
      subscribeToOrderBook(perpsStore.init.coin, precision)

      // 设置超时检查
      const timeoutId = setTimeout(() => {
        if (isLoading) {
          subscribeToOrderBook(perpsStore.init.coin, precision)
        }
      }, 5000)

      return () => {
        clearTimeout(timeoutId)
        if (isSubscribed) {
          perpsStore.book.unsubscribeBook()
          setIsSubscribed(false)
        }
      }
    }
  }, [perpsStore.init.coin, isSubscribed])

  // 监听精度变化
  useEffect(() => {
    if (perpsStore.init.coin && isSubscribed) {
      subscribeToOrderBook(perpsStore.init.coin, precision)
    }
  }, [precision])

  // 监听订单簿数据变化
  useEffect(() => {
    if (perpsStore.book.data?.levels) {
      const hasData =
        perpsStore.book.data.levels[0]?.length > 0 ||
        perpsStore.book.data.levels[1]?.length > 0

      if (hasData && isLoading) {
        setIsLoading(false)
      }
    }
  }, [perpsStore.book.data, isLoading])

  // 保存精度设置到sessionStorage
  const savePrecisionSetting = (coin: string, value: string) => {
    try {
      const storedSettings = sessionStorage.getItem(
        'orderbook_precision_settings',
      )
      let settings: Record<string, string> = {}
      if (storedSettings) {
        settings = JSON.parse(storedSettings)
      }
      settings[coin] = value
      sessionStorage.setItem(
        'orderbook_precision_settings',
        JSON.stringify(settings),
      )
    } catch (error) {
      console.error('保存精度设置失败:', error)
    }
  }

  // 根据价格获取精度选项
  const getPrecisionOptionsByPrice = (price: number) => {
    if (!price || price <= 0) {
      // 默认返回最小精度
      return [
        { key: '0.000001', label: '0.000001' },
        { key: '0.000002', label: '0.000002' },
        { key: '0.000005', label: '0.000005' },
        { key: '0.00001', label: '0.00001' },
        { key: '0.0001', label: '0.0001' },
        { key: '0.001', label: '0.001' },
      ]
    }

    // 将价格转为字符串
    const priceStr = price.toString()

    // 分割整数部分和小数部分
    const parts = priceStr.split('.')

    // 获取小数位数
    const decimalPlaces = parts.length > 1 ? parts[1].length : 0

    // 根据小数位数确定基础单位
    let baseUnit = 1
    for (let i = 0; i < decimalPlaces; i++) {
      baseUnit *= 0.1
    }

    // 生成精度选项
    const baseUnitStr = baseUnit.toFixed(decimalPlaces)
    const doubleBaseUnitStr = (baseUnit * 2).toFixed(decimalPlaces)
    const fiveBaseUnitStr = (baseUnit * 5).toFixed(decimalPlaces)
    const tenBaseUnitStr = (baseUnit * 10).toFixed(decimalPlaces)
    const hundredBaseUnitStr = (baseUnit * 100).toFixed(decimalPlaces)
    const thousandBaseUnitStr = (baseUnit * 1000).toFixed(decimalPlaces)

    // 移除末尾的0
    const formatNumber = (numStr: string) => {
      if (!numStr.includes('.')) return numStr
      return numStr.replace(/\.?0+$/, '')
    }

    if (price >= 100000) {
      return [
        { key: '1', label: '1' },
        {
          key: formatNumber((baseUnit * 10).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 10).toFixed(decimalPlaces)),
        },
        {
          key: formatNumber((baseUnit * 20).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 20).toFixed(decimalPlaces)),
        },
        {
          key: formatNumber((baseUnit * 50).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 50).toFixed(decimalPlaces)),
        },
        {
          key: formatNumber((baseUnit * 100).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 100).toFixed(decimalPlaces)),
        },
        {
          key: formatNumber((baseUnit * 1000).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 1000).toFixed(decimalPlaces)),
        },
        {
          key: formatNumber((baseUnit * 10000).toFixed(decimalPlaces)),
          label: formatNumber((baseUnit * 10000).toFixed(decimalPlaces)),
        },
      ]
    }

    return [
      { key: formatNumber(baseUnitStr), label: formatNumber(baseUnitStr) },
      {
        key: formatNumber(doubleBaseUnitStr),
        label: formatNumber(doubleBaseUnitStr),
      },
      {
        key: formatNumber(fiveBaseUnitStr),
        label: formatNumber(fiveBaseUnitStr),
      },
      {
        key: formatNumber(tenBaseUnitStr),
        label: formatNumber(tenBaseUnitStr),
      },
      {
        key: formatNumber(hundredBaseUnitStr),
        label: formatNumber(hundredBaseUnitStr),
      },
      {
        key: formatNumber(thousandBaseUnitStr),
        label: formatNumber(thousandBaseUnitStr),
      },
    ]
  }

  // 根据币种价格和精度选项索引获取订阅参数
  const getSubscribeParamsByPriceAndIndex = (
    price: number,
    index: number,
  ): { nSigFigs: 2 | 3 | 4 | 5 | null; mantissa?: 2 | 5 } => {
    // 默认参数 - 用于价格为0或无效的情况
    const defaultParams: { nSigFigs: 2 | 3 | 4 | 5; mantissa?: 2 | 5 } = {
      nSigFigs: 5,
    }

    if (!price || price <= 0) {
      return defaultParams
    }

    // 获取价格对应的精度选项
    const options = getPrecisionOptionsByPrice(price)

    // 如果没有选项或索引超出范围，返回默认参数
    if (options.length === 0 || index < 0 || index >= options.length) {
      return defaultParams
    }

    // 获取选中的精度值
    const selectedPrecision = options[index].key
    const precisionValue = parseFloat(selectedPrecision)

    // 根据精度值确定订阅参数
    // 精度值越小，需要的有效数字越多
    if (precisionValue >= 1) {
      // 对于整数精度 (1, 2, 5, 10, 100, 1000)

      if (price >= 100000) {
        if (precisionValue === 1) {
          return { nSigFigs: null }
        } else if (precisionValue === 10) {
          return { nSigFigs: 5 }
        } else if (precisionValue === 20) {
          return { nSigFigs: 5, mantissa: 2 }
        } else if (precisionValue === 50) {
          return { nSigFigs: 5, mantissa: 5 }
        } else if (precisionValue === 100) {
          return { nSigFigs: 4 }
        } else if (precisionValue == 1000) {
          return { nSigFigs: 3 }
        } else if (precisionValue == 10000) {
          return { nSigFigs: 2 }
        }
      } else {
        if (precisionValue === 1) {
          return { nSigFigs: 5 }
        } else if (precisionValue === 2) {
          return { nSigFigs: 5, mantissa: 2 }
        } else if (precisionValue === 5) {
          return { nSigFigs: 5, mantissa: 5 }
        } else if (precisionValue === 10) {
          return { nSigFigs: 4 }
        } else if (precisionValue === 100) {
          return { nSigFigs: 3 }
        } else if (precisionValue === 1000) {
          return { nSigFigs: 2 }
        }
      }
    } else if (precisionValue >= 0.1) {
      // 对于0.1, 0.2, 0.5精度
      if (precisionValue === 0.1) {
        return { nSigFigs: 5 }
      } else if (precisionValue === 0.2) {
        return { nSigFigs: 5, mantissa: 2 }
      } else if (precisionValue === 0.5) {
        return { nSigFigs: 5, mantissa: 5 }
      }
    } else if (precisionValue >= 0.01) {
      // 对于0.01, 0.02, 0.05精度
      if (precisionValue === 0.01) {
        return { nSigFigs: 5 }
      } else if (precisionValue === 0.02) {
        return { nSigFigs: 5, mantissa: 2 }
      } else if (precisionValue === 0.05) {
        return { nSigFigs: 5, mantissa: 5 }
      }
    } else if (precisionValue >= 0.001) {
      // 对于0.001, 0.002, 0.005精度
      if (precisionValue === 0.001) {
        return { nSigFigs: 5 }
      } else if (precisionValue === 0.002) {
        return { nSigFigs: 5, mantissa: 2 }
      } else if (precisionValue === 0.005) {
        return { nSigFigs: 5, mantissa: 5 }
      }
    } else if (precisionValue >= 0.0001) {
      // 对于0.0001, 0.0002, 0.0005精度
      if (precisionValue === 0.0001) {
        return { nSigFigs: 5 }
      } else if (precisionValue === 0.0002) {
        return { nSigFigs: 5, mantissa: 2 }
      } else if (precisionValue === 0.0005) {
        return { nSigFigs: 5, mantissa: 5 }
      }
    } else {
      // 对于更小的精度
      if (precisionValue.toString().endsWith('1')) {
        return { nSigFigs: 5 }
      } else if (precisionValue.toString().endsWith('2')) {
        return { nSigFigs: 5, mantissa: 2 }
      } else if (precisionValue.toString().endsWith('5')) {
        return { nSigFigs: 5, mantissa: 5 }
      }
    }

    // 默认返回最高精度
    return { nSigFigs: 5 }
  }

  // 根据精度值获取选项索引
  const getPrecisionOptionIndex = (precisionValue: string): number => {
    // 获取当前币种价格
    const coinInfo = perpsStore.meta.getCoinInfo(perpsStore.init.coin)
    const price = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')

    // 获取当前价格对应的精度选项列表
    const options = getPrecisionOptionsByPrice(price)

    // 查找精度值在选项列表中的索引
    const index = options.findIndex((option) => option.key === precisionValue)

    // 如果找不到，返回0（第一个选项）
    return index >= 0 ? index : 0
  }

  // 获取当前币种的默认订阅参数 - 简化版，只记录第一个参数
  const getDefaultSubscribeParams = (
    coin: string,
  ): { coin: string; nSigFigs: 5 | null; mantissa?: 2 | 5 } => {
    // 获取币种信息和价格
    const coinInfo = perpsStore.meta.getCoinInfo(coin)
    const price = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')

    // 如果价格大于 100000，返回 nSigFigs: null
    if (price > 100000) {
      return { coin, nSigFigs: null }
    }

    // 简化后只返回币种和默认参数
    return { coin, nSigFigs: 5 }
  }

  // 根据价格自动确定最佳精度选项
  const determineOptimalPrecision = (price: number): string => {
    if (!price || price <= 0) {
      return '0.000001'
    }

    // 获取价格对应的精度选项
    const options = getPrecisionOptionsByPrice(price)

    // 返回第一个选项（最小精度）
    if (options.length > 0) {
      return options[0].key
    }

    // 如果没有选项，返回默认值
    return '0.000001'
  }

  // 处理精度变化
  const handlePrecisionChange = (value: string) => {
    if (value === precision) return

    const coinInfo = perpsStore.meta.getCoinInfo(perpsStore.init.coin)
    const price = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')
    const options = getPrecisionOptionsByPrice(price)
    const isPrecisionInOptions = options.some((option) => option.key === value)
    const finalValue = isPrecisionInOptions
      ? value
      : options.length > 0
        ? options[0].key
        : value

    setPrecision(finalValue)
    setUserSetPrecision(true)
    savePrecisionSetting(perpsStore.init.coin, finalValue)

    // 触发自定义事件
    const event = new CustomEvent(PRECISION_CHANGE_EVENT, {
      detail: { coin: perpsStore.init.coin, precision: finalValue },
    })
    window.dispatchEvent(event)
  }

  // 监听其他组件触发的精度变化事件
  useEffect(() => {
    const handlePrecisionChangeEvent = (e: CustomEvent) => {
      const { coin, precision: newPrecision } = e.detail

      // 只处理当前币种的事件，避免跨币种更新
      if (coin === perpsStore.init.coin && newPrecision !== precision) {
        setPrecision(newPrecision)
        setUserSetPrecision(true)

        // 获取当前币种价格
        const coinInfo = perpsStore.meta.getCoinInfo(coin)
        const price = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')

        // 设置加载状态
        setIsLoading(true)

        // 获取精度选项索引
        const optionIndex = getPrecisionOptionIndex(newPrecision)
        // 获取新的订阅参数
        const subscribeParams = getSubscribeParamsByPriceAndIndex(
          price,
          optionIndex,
        )

        // 构建订阅选项
        const subscribeOptions: any = {
          coin,
          nSigFigs: subscribeParams.nSigFigs,
        }

        // 只有当 mantissa 有有效值(2或5)时才添加到参数中
        if (subscribeParams.mantissa === 2 || subscribeParams.mantissa === 5) {
          subscribeOptions.mantissa = subscribeParams.mantissa
        }

        // 直接调用订阅方法，内部会自动处理取消订阅
        perpsStore.book.subscribeBook(subscribeOptions)
      }
    }

    // 添加事件监听器
    window.addEventListener(
      PRECISION_CHANGE_EVENT,
      handlePrecisionChangeEvent as EventListener,
    )

    // 清理事件监听器
    return () => {
      window.removeEventListener(
        PRECISION_CHANGE_EVENT,
        handlePrecisionChangeEvent as EventListener,
      )
    }
  }, [precision, perpsStore.init.coin])

  // 处理单位变化
  const handleUnitChange = (value: string) => {
    setUnit(value)
  }
  const depthLevel = 16 // 设置显示数量为16条

  // 使用 useEffect 监听订单簿数据变化
  useEffect(() => {
    // 当订单簿数据变化时，如果有数据且正在加载，则结束加载状态
    if (perpsStore.book.data?.levels && isLoading) {
      // 检查数据是否有效（至少有一个买单或卖单）
      const hasData =
        perpsStore.book.data.levels[0]?.length > 0 ||
        perpsStore.book.data.levels[1]?.length > 0

      if (hasData) {
        // 延迟更长时间再取消加载状态，使过渡更平滑
        setTimeout(() => {
          setIsLoading(false)
        }, 800) // 增加延迟时间，让过渡更平滑
      }
    }
  }, [perpsStore.book.data, isLoading])

  // 监听币种变化，更新精度设置
  useEffect(() => {
    if (perpsStore.init.coin) {
      const coin = perpsStore.init.coin

      // 获取币种信息和价格
      const coinInfo = perpsStore.meta.getCoinInfo(coin)
      const price = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')

      // 获取当前价格对应的精度选项列表
      const currentPrecisionOptions = getPrecisionOptionsByPrice(price)

      // 检查是否有保存的精度设置
      const savedPrecision = getStoredPrecision(coin)

      // 检查保存的精度是否在当前选项列表中
      const isPrecisionInOptions = currentPrecisionOptions.some(
        (option) => option.key === savedPrecision,
      )

      if (savedPrecision && isPrecisionInOptions) {
        // 如果有保存的设置且在当前选项列表中，使用保存的设置
        setPrecision(savedPrecision)
        setUserSetPrecision(true)
      } else {
        // 如果没有保存的设置或保存的设置不在当前选项列表中
        if (price > 0 && currentPrecisionOptions.length > 0) {
          // 使用第一个精度选项
          const firstPrecision = currentPrecisionOptions[0].key
          setPrecision(firstPrecision)
          // 保存设置
          savePrecisionSetting(coin, firstPrecision)
          setUserSetPrecision(true)

          // 获取默认订阅参数
          const defaultParams = getDefaultSubscribeParams(coin)

          // 如果已经订阅了订单簿，重新订阅以使用正确的参数
          if (perpsStore.book.data?.coin) {
            // 构建订阅选项 - 简化版，只使用币种和默认nSigFigs
            const subscribeOptions: any = {
              coin: defaultParams.coin,
              nSigFigs: defaultParams.nSigFigs,
            }

            // 只有当 mantissa 有有效值(2或5)时才添加到参数中
            if (defaultParams.mantissa === 2 || defaultParams.mantissa === 5) {
              subscribeOptions.mantissa = defaultParams.mantissa
            }

            // 直接调用订阅方法，内部会自动处理取消订阅
            // perpsStore.book.subscribeBook(subscribeOptions)
          }

          return
        }
        // 如果无法获取精度选项，重置用户设置标志，以便自动设置精度
        setUserSetPrecision(false)
      }

      if (unit != 'USD' && perpsStore.init.coin) {
        setUnit(perpsStore.init.coin.toUpperCase())
      }
    }
  }, [perpsStore.init.coin])

  // 格式化数量，保留5位小数，最后一位是0的话舍去
  const formatAmount = (amount: string) => {
    const num = parseFloat(amount)
    if (isNaN(num)) return '0'

    // 先格式化为5位小数
    let formatted = num.toFixed(5)
    // 移除末尾的零
    while (formatted.endsWith('0')) {
      formatted = formatted.slice(0, -1)
    }
    // 如果以小数点结尾，移除小数点
    if (formatted.endsWith('.')) {
      formatted = formatted.slice(0, -1)
    }
    return formatted
  }

  // 格式化价格，根据选择的精度显示
  const formatPrice = (price: string) => {
    const num = parseFloat(price)
    if (isNaN(num)) return '0'

    // 获取币种信息
    const coinInfo = perpsStore.meta.getCoinInfo(perpsStore.init.coin)
    const szDecimals = coinInfo?.szDecimals || 0

    // 使用 hlFormatPrice 格式化价格
    return hlFormatPrice({
      price: num.toString(),
      szDecimals,
      isSimple: true,
    })
  }

  // 自定义滚动条样式
  const scrollbarStyle = `
    .custom-scrollbar::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: transparent;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 6px;
      border: 2px solid transparent;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
    .custom-scrollbar::-webkit-scrollbar-corner {
      background: transparent;
    }
  `

  // 根据精度处理价格
  const processPriceByPrecision = (price: number): number => {
    // 提取精度值
    const precisionValue = parseFloat(precision)

    // 如果精度是整数（如1, 2, 5, 10, 100, 1000）
    if (Number.isInteger(precisionValue)) {
      return Math.round(price / precisionValue) * precisionValue
    }

    // 对于小数精度，计算对应的乘数
    const multiplier = 1 / precisionValue
    return Math.round(price * multiplier) / multiplier
  }

  // 合并相同价格的订单
  const mergeOrdersByPrice = (orders: any[], isAsk: boolean) => {
    if (!orders || orders.length === 0) return []

    // 创建价格映射
    const priceMap = new Map()

    // 遍历订单，按处理后的价格合并
    orders.forEach((order) => {
      const originalPrice = parseFloat(order.px)
      const processedPrice = processPriceByPrecision(originalPrice)
      const size = parseFloat(order.sz)

      if (priceMap.has(processedPrice)) {
        // 已存在该价格，累加数量
        const existingOrder = priceMap.get(processedPrice)
        existingOrder.sz = (parseFloat(existingOrder.sz) + size).toString()
        existingOrder.n += order.n // 累加订单数量
      } else {
        // 新价格
        priceMap.set(processedPrice, {
          px: processedPrice.toString(),
          sz: order.sz,
          n: order.n,
        })
      }
    })

    // 转换回数组并排序
    const mergedOrders = Array.from(priceMap.values())

    // 排序：卖单从低到高，买单从高到低
    if (isAsk) {
      mergedOrders.sort((a, b) => parseFloat(a.px) - parseFloat(b.px))
    } else {
      mergedOrders.sort((a, b) => parseFloat(b.px) - parseFloat(a.px))
    }

    // 限制显示数量
    return mergedOrders.slice(0, depthLevel)
  }

  // 订单数据将通过 useMemo 处理

  // 计算阴影宽度的函数，根据累计值返回合适的宽度百分比
  const calculateShadowWidth = (total: number, maxTotal: number) => {
    if (maxTotal === 0 || isNaN(total) || isNaN(maxTotal)) return 0
    if (total <= 0) return 0

    // 使用线性比例尺计算宽度，确保背景宽度与数据成正比
    const minWidth = 5 // 最小宽度为5%
    const maxWidth = 100 // 最大宽度为100%

    // 使用线性比例计算宽度，并确保至少有最小宽度
    const linearScale = total / maxTotal
    return Math.max(minWidth, maxWidth * linearScale)
  }

  // 计算卖单累计值（从下往上累加）
  const calculateAskTotals = (orders: any[]) => {
    const totals: number[] = []
    let runningTotal = 0

    // 从第一个开始累加（从下往上）
    for (let i = 0; i < orders.length; i++) {
      const size = parseFloat(orders[i].sz)
      runningTotal += size
      totals[i] = runningTotal
    }

    return totals
  }

  // 计算买单累计值（从下往上累加）
  const calculateBidTotals = (orders: any[]) => {
    const totals: number[] = []
    let runningTotal = 0

    // 从第一个开始累加（从下往上）
    for (let i = 0; i < orders.length; i++) {
      const size = parseFloat(orders[i].sz)
      runningTotal += size
      totals[i] = runningTotal
    }

    return totals
  }

  // 计算根据精度处理后的价差
  const calculatePriceDiff = useMemo(() => {
    if (
      !perpsStore.book.data?.levels ||
      perpsStore.book.data?.levels?.length !== 2
    ) {
      return { diff: '0', percentage: '0' }
    }

    // 获取原始的最低卖价和最高买价
    const rawLowestAsk = Number(perpsStore.book.data?.levels?.[1]?.[0]?.px || 0)
    const rawHighestBid = Number(
      perpsStore.book.data?.levels?.[0]?.[0]?.px || 0,
    )

    if (rawLowestAsk === 0 || rawHighestBid === 0) {
      return { diff: '0', percentage: '0' }
    }

    // 根据当前精度处理价格
    const lowestAsk = processPriceByPrecision(rawLowestAsk)
    const highestBid = processPriceByPrecision(rawHighestBid)

    // 计算价差
    const diff = lowestAsk - highestBid
    // 计算价差百分比
    const percentage = (diff / lowestAsk) * 100

    // 获取当前币种信息
    const coinInfo = perpsStore.meta.getCoinInfo(perpsStore.init.coin)

    // 获取当前价格的小数点尾数
    // 使用标记价格或预言机价格
    const currentPrice = Number(coinInfo?.markPx || coinInfo?.oraclePx || '0')

    // 将当前价格转为字符串并分割小数部分
    const priceStr = currentPrice.toString()
    const priceParts = priceStr.split('.')

    // 获取小数部分的长度
    const decimalPlaces = priceParts.length > 1 ? priceParts[1].length : 0

    // 使用与当前价格相同的小数位数格式化价差
    return {
      diff: diff.toFixed(decimalPlaces),
      percentage: percentage.toFixed(3),
    }
  }, [perpsStore.book.data, precision])

  // 使用 useMemo 缓存订单数据处理结果
  const {
    askOrders,
    bidOrders,
    askTotals,
    bidTotals,
    maxAskTotal,
    maxBidTotal,
  } = useMemo(() => {
    // 获取当前币种
    const currentCoinName = perpsStore.init.coin

    // 获取当前数据
    // 不再使用上一次的数据，始终使用最新数据
    const currentData = perpsStore.book.data

    // 如果没有有效数据，返回空数组
    if (!currentData?.levels) {
      return {
        askOrders: [],
        bidOrders: [],
        askTotals: [],
        bidTotals: [],
        maxAskTotal: 1,
        maxBidTotal: 1,
        displayCoin: currentCoinName,
      }
    }

    // 准备卖单数据 - 从原始数据处理并合并相同价格
    const askOrders = mergeOrdersByPrice(currentData.levels[1] || [], true)

    // 准备买单数据 - 从原始数据处理并合并相同价格
    const bidOrders = mergeOrdersByPrice(currentData.levels[0] || [], false)

    // 计算累计值
    const askTotals = calculateAskTotals(askOrders)
    const bidTotals = calculateBidTotals(bidOrders)

    // 获取最大累计值
    const maxAskTotal = askTotals.length > 0 ? Math.max(...askTotals) : 1
    const maxBidTotal = bidTotals.length > 0 ? Math.max(...bidTotals) : 1

    // 不再使用统一的最大值，买卖单各自使用自己的最大值
    return {
      askOrders,
      bidOrders,
      askTotals,
      bidTotals,
      maxAskTotal, // 使用卖单自己的最大值
      maxBidTotal, // 使用买单自己的最大值
      displayCoin: currentCoinName,
    }
  }, [
    perpsStore.book.data,
    perpsStore.book.data?.levels,
    perpsStore.book.data?.levels?.[0],
    perpsStore.book.data?.levels?.[1],
    perpsStore.init.coin,
    precision,
  ])

  // 不再使用单独的渲染行函数，直接在内容区域中渲染

  return (
    <div className="flex h-full w-full flex-col bg-[#131313] text-white">
      {/* 添加滚动条样式 */}
      <style>{scrollbarStyle}</style>

      {/* 顶部控制栏 */}
      <div className="flex items-center border-b border-[#303030] bg-[#1E1E1E] px-3 py-2">
        {/* 左侧区域 - 包含两个下拉框 */}
        <div className="flex w-1/2 items-center justify-between">
          {/* 精度选择下拉菜单 - 靠左 */}
          <ConfigProvider
            theme={{
              token: {
                colorBgContainer: 'inherit',
                colorBgElevated: '#1A1A1A',
                colorText: '#F9FAFB',
              },
              components: {
                Dropdown: {
                  colorBgElevated: '#1A1A1A',
                  controlItemBgHover: '#2A2A2A',
                  controlItemBgActive: '#2A2A2A',
                },
              },
            }}
          >
            <Dropdown
              menu={{
                onClick: (e) => handlePrecisionChange(e.key),
                selectedKeys: [precision],
                items: (() => {
                  // 获取当前币种价格
                  const coinInfo = perpsStore.meta.getCoinInfo(
                    perpsStore.init.coin,
                  )
                  const rawPrice = Number(
                    coinInfo?.markPx || coinInfo?.oraclePx || '0',
                  )

                  // 使用getPrecisionOptionsByPrice函数获取精度选项
                  const options = getPrecisionOptionsByPrice(rawPrice)

                  // 检查当前精度是否在选项列表中
                  const isPrecisionInOptions = options.some(
                    (option) => option.key === precision,
                  )

                  // 如果当前精度不在选项列表中，自动选择第一个选项
                  if (!isPrecisionInOptions && options.length > 0) {
                    // 使用setTimeout避免在渲染过程中更新状态
                    // setTimeout(() => {
                    //   handlePrecisionChange(options[0].key)
                    // }, 0)
                  }

                  return options
                })(),
              }}
              trigger={['click']}
              placement="bottomLeft"
              arrow={false}
              destroyPopupOnHide
            >
              <button className="flex cursor-pointer items-center rounded bg-[#131313] px-2 py-1 text-sm">
                <span>
                  {(() => {
                    // 获取当前币种价格
                    const coinInfo = perpsStore.meta.getCoinInfo(
                      perpsStore.init.coin,
                    )
                    const price = Number(
                      coinInfo?.markPx || coinInfo?.oraclePx || '0',
                    )

                    // 获取当前价格对应的精度选项列表
                    const options = getPrecisionOptionsByPrice(price)

                    // 检查当前精度是否在选项列表中
                    const isPrecisionInOptions = options.some(
                      (option) => option.key === precision,
                    )

                    // 如果在选项列表中，显示当前精度，否则显示第一个选项
                    return isPrecisionInOptions
                      ? precision
                      : options.length > 0
                        ? options[0].key
                        : precision
                  })()}
                </span>
                <DownOutlined className="ml-1 text-xs" />
              </button>
            </Dropdown>
          </ConfigProvider>

          {/* 单位选择下拉菜单 - 靠右 */}
          <ConfigProvider
            theme={{
              token: {
                colorBgContainer: 'inherit',
                colorBgElevated: '#1A1A1A',
                colorText: '#F9FAFB',
              },
              components: {
                Dropdown: {
                  colorBgElevated: '#1A1A1A',
                  controlItemBgHover: '#2A2A2A',
                  controlItemBgActive: '#2A2A2A',
                },
              },
            }}
          >
            <Dropdown
              menu={{
                onClick: (e) => handleUnitChange(e.key as string),
                selectedKeys: [unit],
                items: [
                  {
                    key: perpsStore.init.coin,
                    label: perpsStore.init.coin,
                  },
                  { key: 'USD', label: 'USD' },
                ],
              }}
              trigger={['click']}
              placement="bottomRight"
              arrow={false}
              destroyPopupOnHide
            >
              <button className="flex cursor-pointer items-center rounded bg-[#131313] px-2 py-1 text-sm">
                <span>
                  {unit === 'USD' ? 'USD' : perpsStore.init.coin || '--'}
                </span>
                <DownOutlined className="ml-1 text-xs" />
              </button>
            </Dropdown>
          </ConfigProvider>
        </div>

        {/* 右侧区域 - 价差显示 */}
        <div className="flex w-1/2 items-center">
          <div className="flex h-[22px] w-full items-center justify-between bg-[#40414A] px-4 text-sm">
            <span className="text-white">{i18n.t('book_spread')}</span>
            <span className="text-center text-white">
              {calculatePriceDiff.diff}
            </span>
            <span className="text-center text-white">
              {calculatePriceDiff.percentage + '%'}
            </span>
          </div>
        </div>
      </div>

      {/* 表格容器 */}
      <div className="flex w-full flex-1 flex-col overflow-hidden">
        {/* 表头 */}
        <div className="grid grid-cols-6 border-b border-[#303030] bg-[#1E1E1E] px-2 py-1 text-xs text-[#868686]">
          <div className="text-left">
            {i18n.t('book_total')}({unit})
          </div>
          <div className="text-center">{i18n.t('book_size')}</div>
          <div className="pr-1 text-right">{i18n.t('price')}</div>
          <div className="pl-1 text-left">{i18n.t('price')}</div>
          <div className="text-center">{i18n.t('book_size')}</div>
          <div className="text-right">
            {i18n.t('book_total')}({unit})
          </div>
        </div>

        {/* 整个内容区域可滚动 */}
        <div
          className="custom-scrollbar relative w-full flex-1 overflow-y-auto"
          style={{ height: 'calc(100% - 30px)', minHeight: '300px' }}
        >
          {/* 加载指示器 - 使用淡入淡出效果 */}
          <div
            className={`absolute inset-0 z-10 flex items-center justify-center bg-[#131313] transition-opacity duration-500 ${
              isLoading ? 'opacity-50' : 'pointer-events-none opacity-0'
            }`}
          >
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-[#303030] border-t-[#00D09C]"></div>
          </div>

          {/* 内容区域 - 始终保持可见，但在加载时降低不透明度 */}
          <div
            className="w-full transition-all duration-500"
            style={{
              opacity: isLoading ? 0.7 : 1,
              filter: isLoading ? 'blur(1px)' : 'none',
            }}
          >
            {/* 骨架屏 - 在加载时显示，或者当没有数据时显示 */}
            {(isLoading || askOrders.length === 0) &&
              Array(depthLevel)
                .fill(0)
                .map((_, index) => (
                  <div
                    key={`skeleton-${index}`}
                    className="grid grid-cols-6 px-[2px] py-[6px] text-xs"
                    style={{
                      backgroundColor: index % 2 === 0 ? '#131313' : '#1A1A1A',
                    }}
                  >
                    <div className="pl-1 text-left">
                      <div className="h-3 w-16 animate-pulse rounded bg-[#303030]"></div>
                    </div>
                    <div className="text-center">
                      <div className="mx-auto h-3 w-12 animate-pulse rounded bg-[#303030]"></div>
                    </div>
                    <div className="relative pr-1 text-right">
                      <div className="ml-auto h-3 w-14 animate-pulse rounded bg-[#303030]"></div>
                    </div>
                    <div className="relative pl-1 text-left">
                      <div className="h-3 w-14 animate-pulse rounded bg-[#303030]"></div>
                    </div>
                    <div className="text-center">
                      <div className="mx-auto h-3 w-12 animate-pulse rounded bg-[#303030]"></div>
                    </div>
                    <div className="text-right">
                      <div className="ml-auto h-3 w-16 animate-pulse rounded bg-[#303030]"></div>
                    </div>
                  </div>
                ))}

            {/* 实际数据 - 只在有数据且不在加载状态时显示 */}
            {!isLoading &&
              (askOrders.length > 0 || bidOrders.length > 0) &&
              Array.from({
                length: Math.max(askOrders.length, bidOrders.length),
              }).map((_, index) => {
                const askLevel = askOrders[index]
                const bidLevel = bidOrders[index]

                // 获取当前行的累计值（卖单）
                const askTotalToken = askLevel ? askTotals[index] : 0
                const askTotalUsd = askLevel
                  ? parseFloat(askLevel.px) * askTotalToken
                  : 0
                const askShadowWidth = calculateShadowWidth(
                  askTotalToken,
                  maxAskTotal,
                )

                // 获取当前行的累计值（买单）
                const bidTotalToken = bidLevel ? bidTotals[index] : 0
                const bidTotalUsd = bidLevel
                  ? parseFloat(bidLevel.px) * bidTotalToken
                  : 0
                const bidShadowWidth = calculateShadowWidth(
                  bidTotalToken,
                  maxBidTotal,
                )

                return (
                  <div
                    key={`row-${index}`}
                    className="grid grid-cols-2 px-[2px] py-[5px] text-xs"
                  >
                    {/* 卖单部分 */}
                    <div className="relative grid grid-cols-3">
                      {askLevel && (
                        <>
                          <div
                            className="absolute right-0 top-0 z-0 h-full bg-[#FF5C5C] opacity-10"
                            style={{ width: `${askShadowWidth}%` }}
                          ></div>
                          <div className="pl-1 text-left">
                            {unit === 'USD'
                              ? askTotalUsd.toLocaleString(undefined, {
                                  maximumFractionDigits: 0,
                                })
                              : formatAmount(askTotalToken.toString())}
                          </div>
                          <div className="text-center">
                            {unit === 'USD'
                              ? (
                                  parseFloat(askLevel.sz) *
                                  parseFloat(askLevel.px)
                                ).toLocaleString(undefined, {
                                  maximumFractionDigits: 2,
                                })
                              : formatAmount(askLevel.sz)}
                          </div>
                          <div className="relative pr-1 text-right">
                            <span
                              className="relative z-10 cursor-pointer text-[#FF5C5C] hover:font-bold"
                              onClick={() => {
                                setLimitPrice(formatPrice(askLevel.px))
                              }}
                            >
                              {formatPrice(askLevel.px)}
                            </span>
                          </div>
                        </>
                      )}
                      {!askLevel && (
                        <>
                          <div className="pl-1 text-left"></div>
                          <div className="text-center"></div>
                          <div className="relative pr-1 text-right"></div>
                        </>
                      )}
                    </div>

                    {/* 买单部分 */}
                    <div className="relative grid grid-cols-3">
                      {bidLevel && (
                        <>
                          <div
                            className="absolute left-0 top-0 z-0 h-full bg-[#00D09C] opacity-10"
                            style={{ width: `${bidShadowWidth}%` }}
                          ></div>
                          <div className="relative pl-1 text-left">
                            <span
                              className="relative z-10 cursor-pointer text-[#00D09C] hover:font-bold"
                              onClick={() => {
                                setLimitPrice(formatPrice(bidLevel.px))
                              }}
                            >
                              {formatPrice(bidLevel.px)}
                            </span>
                          </div>
                          <div className="text-center">
                            {unit === 'USD'
                              ? (
                                  parseFloat(bidLevel.sz) *
                                  parseFloat(bidLevel.px)
                                ).toLocaleString(undefined, {
                                  maximumFractionDigits: 2,
                                })
                              : formatAmount(bidLevel.sz)}
                          </div>
                          <div className="text-right">
                            {unit === 'USD'
                              ? bidTotalUsd.toLocaleString(undefined, {
                                  maximumFractionDigits: 0,
                                })
                              : formatAmount(bidTotalToken.toString())}
                          </div>
                        </>
                      )}
                      {!bidLevel && (
                        <>
                          <div className="relative pl-1 text-left"></div>
                          <div className="text-center"></div>
                          <div className="text-right"></div>
                        </>
                      )}
                    </div>
                  </div>
                )
              })}
          </div>
        </div>
      </div>
    </div>
  )
})
