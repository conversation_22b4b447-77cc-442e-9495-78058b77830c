import i18n from '@/i18n'
import { Modal } from 'antd'

interface CancelAllOrdersModalProps {
  open: boolean
  onClose: () => void
  onConfirm: () => void
  isLoading: boolean
}

export const CancelAllOrdersModal = ({
  open,
  onClose,
  onConfirm,
  isLoading,
}: CancelAllOrdersModalProps) => {
  return (
    <Modal
      title={null}
      open={open}
      onCancel={onClose}
      footer={null}
      width={400}
      centered
      wrapClassName="cancel-all-orders-modal"
      maskClosable={true}
      closable={true}
      closeIcon={<span className="text-lg">×</span>}
    >
      <div className="flex flex-col items-center px-4 py-6">
        <div className="mb-4 text-center text-xl font-medium">
          {i18n.t('cancel_all_orders')}
        </div>
        <p className="mb-6 text-center text-sm">
          {i18n.t('cancel_all_orders_desc')}
        </p>

        {/* 确认按钮 */}
        <button
          className="h-12 w-full rounded-md bg-black text-center text-base font-normal text-white hover:bg-opacity-90 disabled:cursor-not-allowed disabled:opacity-50"
          onClick={onConfirm}
          disabled={isLoading}
        >
          {isLoading ? `${i18n.t('processing')}...` : i18n.t('confirm')}
        </button>
      </div>
    </Modal>
  )
}
