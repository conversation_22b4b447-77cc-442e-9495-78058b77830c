import { EX_CANCEL_ORDER } from '@/api/interface/EX_CANCEL_ORDER'
import { ButtonBlack } from '@/components/Customize/Button'
import i18n from '@/i18n'
import { useOrder } from '@/pages/Perp/hooks/useOrder'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { TPSLTextCol } from '@/pages/Perp/module/modals/TPSLTextCol'
import { message } from 'antd'
import { observer } from 'mobx-react-lite'
import { useEffect, useState } from 'react'

export const TPSLPending = observer(({ coin }: { coin: string }) => {
  const { hlFormatPrice } = useTools()
  const {
    order,
    positionTpslProfitOrder,
    positionTpslLossOrder,
    profitToAmount,
    lossToAmount,
  } = useOrder({
    coin,
  })

  const [isProfitCanceling, setIsProfitCanceling] = useState(false)
  const [isLossCanceling, setIsLossCanceling] = useState(false)

  useEffect(() => {
    return () => {
      setIsProfitCanceling(false)
      setIsLossCanceling(false)
    }
  }, [])

  // 取消订单
  async function cancelOrder(type: 'profit' | 'loss') {
    const isProfit = type === 'profit'
    const isLoss = type === 'loss'

    if (isProfit && isLossCanceling) return
    if (isLoss && isProfitCanceling) return

    if (isProfit) setIsProfitCanceling(true)
    if (isLoss) setIsLossCanceling(true)

    const assetId = order?.assetid || 0
    const orderId = isProfit
      ? positionTpslProfitOrder?.oid
      : positionTpslLossOrder?.oid

    try {
      const response = await EX_CANCEL_ORDER({
        cancels: [
          {
            a: assetId,
            o: orderId,
          },
        ],
      })

      if (response.code === 200) {
        message.success(i18n.t('tpsl_order_cancel_success'))
      } else {
        message.error(response.message || i18n.t('tpsl_order_cancel_failed'))
      }
    } catch (error: any) {
      message.error(error?.message || i18n.t('tpsl_order_cancel_failed'))
    } finally {
      if (isProfit) setIsProfitCanceling(false)
      if (isLoss) setIsLossCanceling(false)
    }
  }

  return (
    <div className="flex flex-col gap-2 border-b pb-3">
      {positionTpslProfitOrder?.triggerPx && (
        <TPSLTextCol title="止盈">
          <div className="flex flex-col gap-1">
            <div className="flex items-center justify-between gap-2">
              <div>
                {i18n.t('tpsl_order_price_above', {
                  price: hlFormatPrice({
                    price: positionTpslProfitOrder.triggerPx,
                    szDecimals: order?.szDecimals,
                  }),
                })}
              </div>
              <ButtonBlack
                type="text"
                size="small"
                loading={isProfitCanceling}
                onClick={() => cancelOrder('profit')}
              >
                {i18n.t('cancel')}
              </ButtonBlack>
            </div>
            <div className="justify-center text-right text-xs font-normal text-zinc-400">
              {i18n.t('tpsl_order_expected_profit')}: {profitToAmount} USD
            </div>
          </div>
        </TPSLTextCol>
      )}

      {positionTpslLossOrder?.triggerPx && (
        <TPSLTextCol title={i18n.t('loss')}>
          <div className="flex flex-col gap-1">
            <div className="flex items-center justify-between gap-2">
              <div>
                {i18n.t('tpsl_order_price_below', {
                  price: hlFormatPrice({
                    price: positionTpslLossOrder.triggerPx,
                    szDecimals: order?.szDecimals,
                  }),
                })}
              </div>
              <ButtonBlack
                type="text"
                size="small"
                loading={isLossCanceling}
                onClick={() => cancelOrder('loss')}
              >
                {i18n.t('cancel')}
              </ButtonBlack>
            </div>
            <div className="justify-center text-right text-xs font-normal text-zinc-400">
              {i18n.t('tpsl_order_expected_loss')}: {lossToAmount} USD
            </div>
          </div>
        </TPSLTextCol>
      )}
    </div>
  )
})
