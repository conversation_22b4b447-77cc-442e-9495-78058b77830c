/* TPSLOrderModal 移动端适配样式 */

/* 基础样式 */
.tpsl-order-modal .ant-modal-content {
  max-width: 100%;
}

/* 确保按钮在所有屏幕尺寸下都能正常显示 */
.tpsl-order-modal .ant-modal-footer {
  padding: 10px 16px;
}

/* 移动端适配 */
@media screen and (max-width: 576px) {
  /* 调整模态框宽度 */
  .tpsl-order-modal .ant-modal-content {
    width: 95vw !important;
    margin: 0 auto;
  }

  /* 调整内容区域 */
  .tpsl-order-modal .ant-modal-body {
    padding: 16px 12px;
  }

  /* 调整标题区域 */
  .tpsl-order-modal .ant-modal-header {
    padding: 12px 16px;
  }

  /* 调整表单元素间距 */
  .tpsl-order-modal .flex-col.gap-2 {
    gap: 10px;
  }

  /* 调整输入框和滑块组件 */
  .tpsl-order-modal .ant-input-number,
  .tpsl-order-modal .ant-slider {
    width: 100%;
  }

  /* 调整复选框组 */
  .tpsl-order-modal .ant-checkbox-group {
    width: 100%;
  }

  /* 调整按钮 */
  .tpsl-order-modal .ant-btn {
    width: 100%;
  }

  /* 调整文本列布局 */
  .tpsl-order-modal .flex.justify-between {
    flex-wrap: wrap;
  }

  /* 确保文本不会溢出 */
  .tpsl-order-modal .text-xs,
  .tpsl-order-modal .text-sm {
    word-break: break-word;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 375px) {
  .tpsl-order-modal .ant-modal-content {
    width: 98vw !important;
  }

  .tpsl-order-modal .ant-modal-body {
    padding: 12px 8px;
  }

  /* 进一步减小字体大小 */
  .tpsl-order-modal .text-xs {
    font-size: 0.7rem;
  }

  /* 减小间距 */
  .tpsl-order-modal .gap-2 {
    gap: 6px;
  }

  /* 调整Tooltip图标大小 */
  .tpsl-order-modal .size-4 {
    width: 14px;
    height: 14px;
  }
}
