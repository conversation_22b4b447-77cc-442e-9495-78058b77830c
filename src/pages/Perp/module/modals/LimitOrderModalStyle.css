.limit-order-modal .ant-modal-content {
  padding: 12px;
}

.limit-order-modal .ant-modal-close {
  top: 12px;
  right: 12px;
}

/* 自定义下拉框样式 */
.unit-select .ant-select-selector {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  height: 36px !important;
  padding-right: 20px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unit-select .ant-select-selection-item {
  text-align: center;
  font-size: 14px;
  color: #000;
}

.unit-select .ant-select-arrow {
  color: #000;
  right: 8px;
}

/* 下拉菜单样式 */
.ant-select-dropdown {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ant-select-item {
  padding: 8px 12px;
}

.ant-select-item-option-selected {
  background-color: #f5f5f5;
  font-weight: 500;
}
