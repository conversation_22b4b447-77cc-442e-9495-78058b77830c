import i18n from '@/i18n'
import { Modal } from '@/components/Customize/Modal'
import { observer } from 'mobx-react-lite'
import { useOrder } from '@/pages/Perp/hooks/useOrder'
import { InputNumberPerp } from '@/components/Customize/InputNumber'
import { TPerpNumber } from '@/pages/Perp/module/types'
import { $ten, Decimal, isBlank } from '@/utils'
import { useEffect, useMemo, useState } from 'react'
import { perpsStore } from '@/store'
import {
  DropdownLightPerp,
  DropdownPerp,
} from '@/components/Customize/Dropdown'
import { MenuProps, message } from 'antd'
import { ButtonBlack } from '@/components/Customize/Button'
import { UPDATE_ISOLATED_MARGIN } from '@/api/interface/UPDATE_ISOLATED_MARGIN'
import { ModalOnPopup } from '@/components/ModalOnPopup'
import clsx from 'clsx'
import { useMedia } from '@/hooks/useMedia'

export const MarginModal = observer(
  ({
    modifyCoin,
    open,
    onClose,
  }: {
    open: boolean
    onClose: () => void
    modifyCoin: string
  }) => {
    const { isMd } = useMedia()
    const { withdrawable } = perpsStore.user || {}
    const [amount, setAmount] = useState<TPerpNumber | null>(null)
    const [coinType, setCoinType] = useState<string>('add')
    const [loading, setLoading] = useState(false)

    const items: MenuProps['items'] = [
      {
        label: i18n.t('add'),
        onClick: () => setCoinType('add'),
        key: 'add',
      },
      {
        label: i18n.t('remove'),
        onClick: () => setCoinType('remove'),
        key: 'remove',
      },
    ]

    const { order } = useOrder({
      coin: modifyCoin,
    })

    // 当前保证金
    const currentMargin = useMemo(() => order?.marginUsed || 0, [order])
    // 可添加保证金
    const addMargin = useMemo(() => withdrawable || 0, [withdrawable])

    const transferMarginRequired = useMemo(() => {
      if (!order?.positionValue || !order?.leverage) return 0
      // 初始保证金要求 = 仓位价值 / 杠杆
      const initialMarginRequired =
        Number(order.positionValue) / Number(order.leverage)
      // 仓位价值的10%
      const tenPercentOfPositionValue = Number(order.positionValue) * 0.1
      // 取两者较大值
      return Math.max(initialMarginRequired, tenPercentOfPositionValue)
    }, [order])

    // 可移除保证金
    const removeMargin = useMemo(() => {
      if (!order?.marginUsed || !order?.unrealizedPnl) return 0
      // 当前保证金（含未实现盈亏）
      const currentMarginWithPnL =
        Number(order.marginUsed) + Number(order.unrealizedPnl)
      // 可移除保证金 = 当前保证金 - 转移保证金要求
      const removable = currentMarginWithPnL - transferMarginRequired

      return Math.max(0, removable)
    }, [order, transferMarginRequired])

    const changeMargin = useMemo(() => {
      if (coinType === 'add') {
        // 添加保证金：最大值是可用余额
        const maxAddable = Math.max(0, Number(addMargin))
        return $ten.toFixed(maxAddable, 2)
      } else {
        // 移除保证金：最大值是可移除保证金
        const maxRemovable = Math.max(0, removeMargin)
        return $ten.toFixed(maxRemovable, 2)
      }
    }, [coinType, addMargin, removeMargin])

    const disabled = useMemo(() => {
      if (coinType === 'add') {
        return Number(amount) > Number(addMargin) || Number(amount) <= 0
      } else {
        return Number(amount) > Number(removeMargin) || Number(amount) <= 0
      }
    }, [coinType, amount, addMargin, removeMargin])

    useEffect(() => {
      if (coinType) setAmount(null)
    }, [coinType])

    function onMaxClick() {
      if (coinType === 'add') {
        // 添加保证金：最大值是可用余额
        const maxAmount = Math.max(0, Number(addMargin))
        setAmount($ten.toFixed(maxAmount, 2))
      } else {
        // 移除保证金：最大值是可移除保证金
        const maxAmount = Math.max(0, removeMargin)
        setAmount($ten.toFixed(maxAmount, 2))
      }
    }

    function handlePriceChange(value: TPerpNumber) {
      setAmount(value)
    }

    function handlePriceBlur(e: React.FocusEvent<HTMLInputElement>) {
      if (isBlank(e.target.value)) return
      setAmount($ten.toFixed(e.target.value, 2))
    }

    function onConfirm() {
      setLoading(true)

      const ntli = new Decimal(amount ?? 0)
        .mul(1000000)
        .mul(coinType === 'add' ? 1 : -1)
        .toString()

      UPDATE_ISOLATED_MARGIN({
        asset: order?.assetid ?? 0,
        ntli,
      })
        .then((res) => {
          if (res?.code !== 200) {
            message.error(res?.message || i18n.t('margin_modal_failed'))
            return
          }
          message.success(i18n.t('margin_modal_success'))
          onClose()
        })
        .catch(() => {
          message.error(i18n.t('margin_modal_failed'))
        })
        .finally(() => {
          setLoading(false)
        })
    }

    return (
      <ModalOnPopup
        title={
          <div className="flex w-full flex-col gap-2 md:items-center md:justify-center">
            <span>{i18n.t('margin_modal_title')}</span>
          </div>
        }
        intro={i18n.t('margin_modal_desc')}
        visible={open}
        onOk={onClose}
        onClose={onClose}
      >
        <div
          className={clsx(
            'flex flex-col gap-2',
            isMd ? 'mt-4 w-full px-4' : 'w-96',
          )}
        >
          {!isMd && (
            <span className="mb-4 text-xs font-normal text-gray-450 md:text-theme-secondary">
              {i18n.t('margin_modal_desc')}
            </span>
          )}
          <div className="mb-1 flex w-full items-center justify-between gap-2">
            <div className="flex-1">
              <InputNumberPerp
                theme={isMd ? 'dark' : 'light'}
                textAlign="left"
                value={amount}
                onChange={handlePriceChange}
                onBlur={handlePriceBlur}
                className="w-full"
                min={0}
                onFocus={() => {}}
                placeholder={`${i18n.t('quantity')}(USD)`}
                addonAfter={
                  <div
                    className="cursor-pointer justify-start text-right text-sm text-white md:text-black"
                    onClick={onMaxClick}
                  >
                    {i18n.t('max')}
                  </div>
                }
              />
            </div>
            <div>
              {isMd ? (
                <DropdownPerp
                  trigger={['click']}
                  menu={{
                    items,
                    selectable: true,
                    defaultSelectedKeys: [coinType],
                  }}
                >
                  <span className="text-center">
                    {coinType === 'add' ? i18n.t('add') : i18n.t('remove')}
                  </span>
                </DropdownPerp>
              ) : (
                <DropdownLightPerp
                  trigger={['click']}
                  menu={{
                    items,
                    selectable: true,
                    defaultSelectedKeys: [coinType],
                  }}
                >
                  <span className="text-center">
                    {coinType === 'add' ? i18n.t('add') : i18n.t('remove')}
                  </span>
                </DropdownLightPerp>
              )}
            </div>
          </div>

          <div className="mt-3 flex w-full items-center justify-between">
            <span className="text-xs text-gray-450 md:text-theme-secondary">
              {i18n.t('margin_modal_current_margin', {
                coin: `${modifyCoin}-USD`,
              })}
            </span>
            <span className="text-xs">
              {currentMargin ? `$${$ten.toFixed(currentMargin, 2)}` : 'N/A'}
            </span>
          </div>

          <div className="flex w-full items-center justify-between">
            <span className="text-xs text-gray-450 md:text-theme-secondary">
              {i18n.t('margin_modal_add_margin', {
                type: coinType === 'add' ? i18n.t('add') : i18n.t('remove'),
              })}
            </span>
            <span className="text-xs">
              {changeMargin ? `$${$ten.toFixed(changeMargin, 2)}` : 'N/A'}
            </span>
          </div>

          {/* 按钮 */}
          <div className="mt-2 flex w-full justify-center sm:justify-end md:mt-4">
            <ButtonBlack
              disabled={disabled}
              loading={loading}
              onClick={onConfirm}
              className={clsx('h-10 w-full', isMd && '!bg-white !text-black')}
            >
              <div className="">{i18n.t('confirm')}</div>
            </ButtonBlack>
          </div>
        </div>
      </ModalOnPopup>
    )
  },
)
