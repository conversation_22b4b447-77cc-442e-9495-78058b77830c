import { observer } from 'mobx-react-lite'
import React from 'react'

export const TPSLTextCol = observer(
  ({
    title,
    children,
  }: {
    title: React.ReactNode
    children: React.ReactNode
  }) => {
    return (
      <div className="flex items-center justify-between">
        <div className="justify-center text-xs font-normal text-[#5F606D]">
          {title}
        </div>
        <div className="justify-center text-xs font-normal text-zinc-900">
          {children}
        </div>
      </div>
    )
  },
)
