import { InputNumberPerp } from '@/components/Customize/InputNumber'
import { TPerpNumber } from '@/pages/Perp/module/types'
import { Slider } from '@/components/Slider'
import { $ten } from '@/utils'
import { observer } from 'mobx-react-lite'
import { useState, useEffect } from 'react'

export const TPSLSlider = observer(
  ({
    coin,
    maxAmount,
    szDecimals,
    onCallback,
  }: {
    coin: string
    maxAmount: string
    szDecimals: number
    onCallback: (amount: string | null) => void
  }) => {
    // 百分比
    const [pct, setPct] = useState<number>(0)
    // 数量
    const [amount, setAmount] = useState<string | null>(null)

    useEffect(() => {
      onCallback && onCallback(amount)
    }, [amount, onCallback])

    // 操作数量
    function onValueChange(value: number[]) {
      const percent = value[0]

      if (percent >= 100) {
        setAmount(maxAmount)
      } else if (percent <= 0) {
        setAmount(null)
      } else {
        setAmount($ten.toFixed($ten.mul(maxAmount, percent / 100), szDecimals))
      }

      setPct(percent)
    }

    // 操作百分比
    function handleChange(value: TPerpNumber) {
      if (Number(value) >= Number(maxAmount)) {
        setPct(100)
      } else if (Number(value) <= 0) {
        setPct(0)
      } else {
        const pct = $ten.toFixed(
          $ten.mul($ten.div(value || 0, maxAmount), 100),
          0,
        )
        setPct(Number(pct))
      }

      setAmount(value as string | null)
    }

    return (
      <div className="flex items-center justify-between gap-5">
        <div className="flex-1">
          <Slider
            value={[pct]}
            type="trade"
            theme="light"
            min={0}
            max={100}
            onValueChange={onValueChange}
            onValueCommit={onValueChange}
          />
        </div>
        <div className="w-32 py-1">
          <InputNumberPerp
            value={amount}
            onChange={handleChange}
            className="w-full"
            theme="light"
            max={maxAmount}
            min={0}
            precision={szDecimals}
            onFocus={() => {}}
            onBlur={() => {}}
            suffix={
              <span className="text-xs text-[#9293a0]">
                {coin.toUpperCase()}
              </span>
            }
          />
        </div>
      </div>
    )
  },
)
