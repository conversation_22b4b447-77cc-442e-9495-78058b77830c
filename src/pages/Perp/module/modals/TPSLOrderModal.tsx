import { observer } from 'mobx-react-lite'
import { Modal } from '@/components/Customize/Modal'
import { useEffect, useMemo, useState } from 'react'
import { perpsStore } from '@/store'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { BuyText, SellText } from '@/components/ColoringText'
import { TSide } from '@/store/perps/types'
import { isBlank } from '@/utils'
import { CheckboxLight } from '@/components/Customize/Checkbox'
import { TProfit, TLoss } from '@/pages/Perp/module/openOrder'
import { TPerpNumber, TPerpSubmitOrder } from '@/pages/Perp/module/types'
import { ButtonBlack } from '@/components/Customize/Button'
import { TPSLSlider } from '@/pages/Perp/module/modals/TPSLSlider'
import { TPSLLimit } from '@/pages/Perp/module/modals/TPSLLimit'
import { usePerpForm } from '@/pages/Perp/hooks/usePerpForm'
import { EX_PLACE_ORDER } from '@/api/interface/EX_PLACE_ORDER'
import { message, Tooltip } from 'antd'
import { useRoute } from '@/hooks/useRoute'
import { TPSLPending } from '@/pages/Perp/module/modals/TPSLPending'
import { TPSLTextCol } from '@/pages/Perp/module/modals/TPSLTextCol'
import { useOrder } from '@/pages/Perp/hooks/useOrder'
import { SvgIconQuestionCircle } from '@/imgs/icons'
import i18n from '@/i18n'
import './TPSLOrderModal.css'
export const TPSLOrderModal = observer(
  ({
    modifyCoin,
    open,
    onClose,
  }: {
    open: boolean
    onClose: () => void
    modifyCoin: string
  }) => {
    const { tpslCoinType } = perpsStore.init
    const { query } = useRoute()
    const { hlFormatPrice, hlFormatCoin, filterZero } = useTools()
    const { order, side, positionTpslProfitOrder, positionTpslLossOrder } =
      useOrder({
        coin: modifyCoin,
      })

    // 当前选择
    const [selected, setSelected] = useState<('amount' | 'limit')[]>([])
    // 选中配置金额
    const isAmount = useMemo(() => selected.includes('amount'), [selected])
    // 选中限价
    const isLimit = useMemo(() => selected.includes('limit'), [selected])

    // 止盈价
    const [profitPrice, setProfitPrice] = useState<TPerpNumber>(null)
    // 止损价
    const [lossPrice, setLossPrice] = useState<TPerpNumber>(null)
    // 配置金额
    const [amount, setAmount] = useState<TPerpNumber>(null)
    // 限价-止盈
    const [limitProfitPrice, setLimitProfitPrice] = useState<TPerpNumber>(null)
    // 限价-止损
    const [limitLossPrice, setLimitLossPrice] = useState<TPerpNumber>(null)

    // 提交状态
    const [loadingConfirm, setLoadingConfirm] = useState(false)

    // 限制止盈止损
    const disabled = useMemo(() => {
      // 当选择配置金额后，金额如果为空，不允许止盈止损以金额的形式输入
      if (isAmount && isBlank(amount) && tpslCoinType === 'amount') return true
      return false
    }, [isAmount, amount, tpslCoinType])

    // 限制提交
    const disabledConfirm = useMemo(() => {
      const isProfit = positionTpslProfitOrder?.triggerPx
      const isLoss = positionTpslLossOrder?.triggerPx

      // 空验证
      if (isProfit && !lossPrice && !limitProfitPrice) return true
      if (isLoss && !profitPrice && !limitLossPrice) return true

      // 止盈止损价都为空，不允许提交
      if (!profitPrice && !lossPrice) return true
      // 止盈价如果小于0，不允许提交
      if (profitPrice && Number(profitPrice) <= 0) return true
      // 止损价如果小于0，不允许提交
      if (lossPrice && Number(lossPrice) <= 0) return true
      // 配置金额时，金额为空，不允许提交
      if (isAmount && !amount) return true
      // 输入限价-止盈时, 如果没有设置止盈价，不允许提交
      if (isLimit && limitProfitPrice && !profitPrice) return true
      // 输入限价-止损时, 如果没有设置止损价，不允许提交
      if (isLimit && limitLossPrice && !lossPrice) return true

      return false
    }, [
      isAmount,
      isLimit,
      amount,
      profitPrice,
      lossPrice,
      limitProfitPrice,
      limitLossPrice,
      positionTpslProfitOrder,
      positionTpslLossOrder,
    ])

    // 获取订单数量
    const orderAmount = useMemo(() => {
      // 如果未配置金额，或者金额为空，则为 0，表示全部数量
      if (!isAmount || isBlank(amount)) return '0'
      // 如果配置了金额，则返回金额
      return filterZero(amount)
    }, [isAmount, amount, order?.szi])

    // 获取仓位数量
    const positionAmount = useMemo(() => {
      if (order?.assetid === null || order?.assetid === undefined) return '0'
      return hlFormatCoin(
        String(Math.abs(Number(order?.szi || '0'))),
        order?.szDecimals,
      )
    }, [order?.szi, order?.szDecimals])

    // 获取订单类型
    const mode = useMemo(() => {
      if (isLimit && (limitLossPrice || limitProfitPrice)) return 'limit'
      return 'market'
    }, [isLimit, limitLossPrice, limitProfitPrice])

    const { calculateSlippagePrice } = usePerpForm({
      mode,
      szDecimals: order?.szDecimals || 0,
    })

    useEffect(() => {
      if (positionTpslProfitOrder?.triggerPx) {
        setProfitPrice(positionTpslProfitOrder.triggerPx)
      }
      if (positionTpslLossOrder?.triggerPx) {
        setLossPrice(positionTpslLossOrder.triggerPx)
      }
    }, [positionTpslProfitOrder, positionTpslLossOrder])

    function onCallbackAmount(amount: string | null) {
      setAmount(amount)
    }

    function onCallbackLimit(profitPrice: TPerpNumber, lossPrice: TPerpNumber) {
      setLimitProfitPrice(profitPrice)
      setLimitLossPrice(lossPrice)
    }

    async function onConfirm() {
      if (disabledConfirm || loadingConfirm) return
      setLoadingConfirm(true)

      try {
        const orders = getParamsOrders()

        const res = await EX_PLACE_ORDER({
          orders,
          grouping: 'positionTpsl',
          roomId: query?.roomId,
        })

        if (res?.code !== 200) {
          message.error(res?.message)
          return
        }
        onClose()
        message.success(i18n.t('tpsl_order_success'))
      } catch (err: any) {
        console.error(err)
        message.error(err?.message || i18n.t('tpsl_order_failed'))
      } finally {
        setLoadingConfirm(false)
      }
    }

    // 请求数据整理
    function getParamsOrders(): TPerpSubmitOrder[] {
      const orders: TPerpSubmitOrder[] = []

      if (order?.assetid === null || order?.assetid === undefined) return []

      // 是否为买单
      const isBid = side === 'B'

      // 部分数据
      const before = {
        // 资产id
        a: order?.assetid,
        // 购买数量
        s: orderAmount,
      }

      // 带止损的订单
      if (lossPrice !== null) {
        const isLimitPrice = isLimit && limitLossPrice
        const triggerPx = filterZero(lossPrice)
        const price = isLimitPrice
          ? filterZero(limitLossPrice)
          : calculateSlippagePrice(lossPrice, side, true)

        orders.push({
          ...before,
          // 是否为买单，方向和普通订单相反
          b: !isBid,
          // 触发价格(下单价格)
          p: price,
          // 是否只挂单
          r: true,
          t: { trigger: { isMarket: !isLimitPrice, triggerPx, tpsl: 'sl' } },
        })
      }

      // 带止盈的订单
      if (profitPrice !== null) {
        const isLimitPrice = isLimit && limitProfitPrice
        const triggerPx = filterZero(profitPrice)
        const price = isLimitPrice
          ? filterZero(limitProfitPrice)
          : calculateSlippagePrice(triggerPx, side, true)

        orders.push({
          ...before,
          // 是否为买单，方向和普通订单相反
          b: !isBid,
          // 触发价格(下单价格)
          p: price,
          // 是否只挂单
          r: true,
          t: { trigger: { isMarket: !isLimitPrice, triggerPx, tpsl: 'tp' } },
        })
      }

      return orders
    }

    return (
      <Modal
        title={
          <div className="flex w-full items-center justify-center gap-1">
            <span>{i18n.t('tpsl_order_title')}</span>
            <TifTips />
          </div>
        }
        open={open}
        onOk={onClose}
        onCancel={onClose}
        className="tpsl-order-modal"
      >
        <div className="flex w-full flex-col gap-2 md:w-[383px]">
          {/* 合约 */}
          <TPSLTextCol title={i18n.t('tpsl_order_contract')}>
            {order?.name?.toUpperCase()}-USD {order?.leverage}x
          </TPSLTextCol>
          {/* 数量 */}
          <TPSLTextCol title={i18n.t('amount')}>
            {side === 'B' ? (
              <BuyText>
                {positionAmount} {order?.name?.toUpperCase()}
              </BuyText>
            ) : side === 'A' ? (
              <SellText>
                {positionAmount} {order?.name?.toUpperCase()}
              </SellText>
            ) : (
              <>
                {hlFormatCoin(order?.szi || '0', order?.szDecimals)}{' '}
                {order?.name?.toUpperCase()}
              </>
            )}
          </TPSLTextCol>
          {/* 开仓价格 */}
          <TPSLTextCol title={i18n.t('tpsl_order_open_price')}>
            {hlFormatPrice({
              price: order?.entryPx || '0',
              szDecimals: order?.szDecimals,
            })}
          </TPSLTextCol>
          {/* 标记价格 */}
          <TPSLTextCol title={i18n.t('tpsl_order_mark_price')}>
            {hlFormatPrice({
              price: order?.markPx || '0',
              szDecimals: order?.szDecimals,
            })}
          </TPSLTextCol>
          <TPSLPending coin={modifyCoin} />
          {/* 止盈止损 */}
          <div className="mt-1 flex w-full flex-col gap-2">
            {!positionTpslProfitOrder?.triggerPx && (
              <TProfit
                theme="light"
                amount={positionAmount}
                price={order?.entryPx || '0'}
                side={side as TSide}
                leverage={order?.leverage || 0}
                szDecimals={order?.szDecimals || 0}
                coin={order?.name || ''}
                coinType={'coin'}
                disabled={disabled}
                onPriceChange={(price) => setProfitPrice(price)}
              />
            )}
            {!positionTpslLossOrder?.triggerPx && (
              <TLoss
                theme="light"
                amount={positionAmount}
                price={order?.entryPx || '0'}
                side={side as TSide}
                leverage={order?.leverage || 0}
                szDecimals={order?.szDecimals || 0}
                coin={order?.name || ''}
                coinType={'coin'}
                disabled={disabled}
                onPriceChange={(price) => setLossPrice(price)}
              />
            )}
          </div>
          <CheckboxLight.Group
            className="flex w-full flex-col"
            onChange={(value) => setSelected(value)}
          >
            {/* 配置金额 */}
            <div>
              <CheckboxLight value="amount">
                <span className="justify-center text-xs font-normal text-[#5F606D]">
                  {i18n.t('tpsl_order_amount_config')}
                </span>
              </CheckboxLight>
            </div>
            {isAmount && (
              <div className="w-full">
                <TPSLSlider
                  maxAmount={positionAmount || '0'}
                  szDecimals={order?.szDecimals || 0}
                  coin={order?.name || ''}
                  onCallback={onCallbackAmount}
                />
              </div>
            )}
            {/* 配置限价 */}
            <div className="mb-1 text-xs text-gray-600 md:mb-0">
              <CheckboxLight value="limit">
                <span className="justify-center text-xs font-normal text-[#5F606D]">
                  {i18n.t('limit')}
                </span>
              </CheckboxLight>
            </div>
            {isLimit && (
              <div className="w-full">
                <TPSLLimit
                  szDecimals={order?.szDecimals || 0}
                  onCallback={onCallbackLimit}
                />
              </div>
            )}
          </CheckboxLight.Group>
          {/* 按钮 */}
          <div className="mt-2 flex w-full justify-center sm:justify-end md:mt-4">
            <ButtonBlack
              disabled={disabledConfirm}
              loading={loadingConfirm}
              onClick={onConfirm}
              className="h-10 w-full"
            >
              <div className="">{i18n.t('confirm')}</div>
            </ButtonBlack>
          </div>
        </div>
      </Modal>
    )
  },
)

function TifTips() {
  return (
    <Tooltip
      color="white"
      placement="bottom"
      title={
        <div className="flex flex-col gap-1 p-2 text-black">
          <span>{i18n.t('tpsl_order_desc01')}</span>
          <span>{i18n.t('tpsl_order_desc02')}</span>
        </div>
      }
    >
      <SvgIconQuestionCircle className="size-4" />
    </Tooltip>
  )
}
