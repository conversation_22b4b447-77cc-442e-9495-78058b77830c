import { Modal } from 'antd'
import { ButtonBlack } from '@/components/Customize/Button'
import { Radio } from 'antd'
import { useState } from 'react'
import i18n from '@/i18n'

interface CloseAllOrdersModalProps {
  open: boolean
  onClose: () => void
  onConfirm: (useMarketPrice: boolean) => void
}

export const CloseAllOrdersModal = ({
  open,
  onClose,
  onConfirm,
}: CloseAllOrdersModalProps) => {
  const [closeType, setCloseType] = useState<'market' | 'limit'>('market')

  const handleConfirm = () => {
    onConfirm(closeType === 'market')
  }

  return (
    <Modal
      title={i18n.t('close_all_orders')}
      open={open}
      onCancel={onClose}
      footer={null}
      width={400}
      centered
      wrapClassName="close-all-orders-modal"
    >
      <div className="flex flex-col gap-4 px-4 py-3">
        <p className="mb-2 text-center text-sm text-black">
          {i18n.t('close_all_orders_desc')}
        </p>

        {/* 选择平仓方式 */}
        <div className="flex flex-col">
          <div className="mb-2 text-sm text-black">
            {i18n.t('close_all_orders_type')}
          </div>
          <Radio.Group
            value={closeType}
            onChange={(e) => setCloseType(e.target.value)}
            className="flex flex-col gap-2"
          >
            <Radio value="market" className="text-sm text-black">
              {i18n.t('close_all_orders_market')}
            </Radio>
            <Radio value="limit" className="text-sm text-black">
              {i18n.t('close_all_orders_limit')}
            </Radio>
          </Radio.Group>
        </div>

        {/* 确认按钮 */}
        <ButtonBlack
          className="mt-2 h-10 w-full text-center text-base font-normal"
          onClick={handleConfirm}
        >
          {i18n.t('confirm')}
        </ButtonBlack>
      </div>
    </Modal>
  )
}
