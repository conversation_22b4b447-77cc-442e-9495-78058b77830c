import { useState, useEffect, useMemo } from 'react'
import { Modal } from '@/components/Customize/Modal'
import { ButtonBlack } from '@/components/Customize/Button'
import { Slider } from '@/components/Slider'
import { perpsStore, walletStore } from '@/store'
import { Select } from 'antd'
import { EX_PLACE_ORDER } from '@/api/interface/EX_PLACE_ORDER'
import { useRoute } from '@/hooks/useRoute'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { useSharedNotification } from '@/hooks/useSharedNotification'
import './LimitOrderModalStyle.css'
import i18n from '@/i18n'

interface LimitOrderModalProps {
  open: boolean
  onClose: () => void
  position?: any // 完整的持仓信息
}

export const LimitOrderModal = ({
  open,
  onClose,
  position,
}: LimitOrderModalProps) => {
  // 从position中解构出需要的属性

  console.log('position: ')
  console.log(position)
  const coin = position?.coin || ''
  const currentPrice = position?.entryPx || ''
  const positionSize = position?.szi || ''
  const isLong = position ? Number(position.szi) > 0 : true // 默认为多仓
  // 获取路由参数
  const { query } = useRoute()

  // 获取工具函数
  const { hlFormatPrice, filterZero } = useTools()
  // 获取通知函数
  const { openNotification, NotificationComponent } = useSharedNotification()
  // 价格
  const [price, setPrice] = useState<string>('')
  // 数量
  const [amount, setAmount] = useState<string>('')
  // 百分比
  const [percent, setPercent] = useState<number>(100)
  // 加载状态
  const [loading, setLoading] = useState<boolean>(false)
  // 币种单位
  const [unit, setUnit] = useState<string>('TOKEN')
  // 当前币种价格
  const [coinPrice, setCoinPrice] = useState<number>(0)
  // 未格式化的 midPrice
  const [rawPrice, setRawPrice] = useState<string>('')
  // 预估已关闭盈亏
  const [estimatedPnL, setEstimatedPnL] = useState<string>('')
  // 预估已关闭盈亏百分比
  const [, setEstimatedPnLPercent] = useState<string>('')

  // 设置输入框的最大精度（必须在unit声明之后）
  const maxPrecision = useMemo(() => {
    // 获取币种信息
    const coinInfo = perpsStore.meta.getCoinInfo(coin)
    // 如果是USD，使用2位小数；否则使用币种的szDecimals
    return unit === 'USD' ? 2 : coinInfo?.szDecimals || 6
  }, [unit, coin])

  // 初始化价格、数量和获取当前币种价格
  useEffect(() => {
    if (open) {
      // 清空价格输入框，不显示默认值
      setPrice('')

      // 设置数量为全部持仓量
      if (positionSize) {
        // 处理空仓情况（positionSize为负数）
        let initialAmount = positionSize
        if (!isLong && initialAmount.startsWith('-')) {
          // 如果是空仓且数量为负数，则取绝对值
          initialAmount = initialAmount.substring(1)
        }
        setAmount(initialAmount)
        // 设置百分比为100%，表示全部持仓
        setPercent(100)
      }

      // 如果有币种名称，更新单位状态
      if (coin) {
        setUnit('TOKEN')
      }

      // 获取当前币种价格（仍然需要获取价格用于计算）
      const coinInfo = perpsStore.meta.getCoinInfo(coin)
      const markPrice = coinInfo?.markPx || currentPrice
      const price = Number(markPrice)

      // 确保价格是有效数字且大于0
      if (!isNaN(price) && price > 0) {
        setCoinPrice(price)
      } else {
        // 如果无法获取有效价格，使用默认值
        setCoinPrice(1000) // 假设默认价格为1000 USD/SOL
      }
    }
  }, [open, currentPrice, positionSize, isLong])

  // 处理百分比变化
  const handlePercentChange = (value: number[]) => {
    const newPercent = value[0]
    setPercent(newPercent)

    try {
      // 获取当前输入框中的价格（如果有效）或使用存储的币种价格
      const currentPrice =
        Number(rawPrice) ||
        Number(price.toString().replace(/,/g, '')) ||
        coinPrice
      // 获取最大可用数量（从positionSize获取）
      let maxAmount = positionSize ? Number(positionSize) : 0.101

      // 处理空仓情况（positionSize为负数）
      if (!isLong && maxAmount < 0) {
        // 如果是空仓且数量为负数，则取绝对值
        maxAmount = Math.abs(maxAmount)
      }

      // 确保maxAmount是有效数字
      if (isNaN(maxAmount) || maxAmount <= 0) {
        maxAmount = 0.101 // 使用默认值
      }

      // 根据当前单位调整最大数量
      if (unit === 'USD') {
        maxAmount = maxAmount * currentPrice // 转换为USD
      }

      // 如果百分比为0，设置为空字符串
      if (newPercent === 0) {
        setAmount('')
        return
      }

      // 如果百分比为100%，直接使用最大值
      if (newPercent === 100) {
        if (unit === 'USD') {
          // USD使用2位小数
          setAmount(maxAmount.toFixed(2))
        } else {
          // 使用maxPrecision控制精度
          setAmount(filterZero(maxAmount.toFixed(maxPrecision)))
        }
        return
      }

      const calculatedAmount = (maxAmount * newPercent) / 100

      // 确保计算结果是有效数字
      if (!isNaN(calculatedAmount)) {
        // 根据单位设置不同的精度
        if (unit === 'USD') {
          // USD使用2位小数
          setAmount(calculatedAmount.toFixed(2))
        } else {
          // 使用maxPrecision控制精度，保持精确的数值表示
          const rawAmount = calculatedAmount.toFixed(maxPrecision)
          // 使用filterZero处理数值，去除末尾的0
          setAmount(filterZero(rawAmount))
        }
      }
    } catch (error) {
      console.error('百分比计算错误:', error)
    }
  }

  // 这里我们删除了未使用的函数

  // 计算预估已关闭盈亏
  const calculateEstimatedPnL = (closePrice: string) => {
    if (!closePrice || !currentPrice || !amount || !positionSize) {
      setEstimatedPnL('')
      setEstimatedPnLPercent('')
      return
    }

    try {
      // 转换为数字进行计算
      const closePriceNum = Number(closePrice.toString().replace(/,/g, ''))
      const entryPriceNum = Number(currentPrice.toString().replace(/,/g, ''))

      if (
        isNaN(closePriceNum) ||
        isNaN(entryPriceNum) ||
        closePriceNum <= 0 ||
        entryPriceNum <= 0
      ) {
        setEstimatedPnL('')
        setEstimatedPnLPercent('')
        return
      }

      // 计算当前选择的数量占总仓位的比例
      let positionSizeNum = Number(positionSize)
      if (!isLong && positionSizeNum < 0) {
        positionSizeNum = Math.abs(positionSizeNum)
      }

      // 计算选择的数量（币种单位）
      let selectedAmount = Number(amount)
      if (unit === 'USD') {
        selectedAmount = selectedAmount / closePriceNum
      }

      // 计算盈亏
      let pnl: number
      if (isLong) {
        // 多仓：平仓价格 - 开仓价格
        pnl = (closePriceNum - entryPriceNum) * selectedAmount
      } else {
        // 空仓：开仓价格 - 平仓价格
        pnl = (entryPriceNum - closePriceNum) * selectedAmount
      }

      // 计算盈亏百分比
      const pnlPercent = (pnl / (entryPriceNum * selectedAmount)) * 100

      // 格式化盈亏
      const formattedPnL = pnl.toFixed(2)
      const formattedPnLPercent = pnlPercent.toFixed(2)

      setEstimatedPnL(formattedPnL)
      setEstimatedPnLPercent(formattedPnLPercent)
    } catch (error) {
      console.error('计算预估盈亏错误:', error)
      setEstimatedPnL('')
      setEstimatedPnLPercent('')
    }
  }

  // 使用Mid价格
  const handleUseMidPrice = () => {
    const coin = position?.coin
    const cbbo = perpsStore.book.bbo?.[coin]
    const impactPxs = perpsStore.meta.getCoinInfo(coin)?.impactPxs || []
    const bid = cbbo?.bbo?.[0]?.px || impactPxs?.[0] || '0'
    const offer = cbbo?.bbo?.[1]?.px || impactPxs?.[1] || '0'
    const rawMidPrice = isLong ? offer : bid || currentPrice
    const szDecimals = perpsStore.meta.getCoinInfo(coin)?.szDecimals
    // 统一为不带逗号的纯数字
    const formattedPrice = hlFormatPrice({
      price: rawMidPrice,
      szDecimals,
      isSimple: true,
    })
    setPrice(formattedPrice)
    setRawPrice(rawMidPrice)
    if (position) {
      position.closePrice = formattedPrice
    }

    // 计算预估已关闭盈亏
    calculateEstimatedPnL(formattedPrice)
  }

  // 检查按钮是否禁用
  const isButtonDisabled = useMemo(() => {
    // 检查价格和数量是否有效
    if (!price || !amount) return true

    // 检查价格和数量是否为有效数字（去掉逗号）
    const priceNum = Number(price.toString().replace(/,/g, ''))
    const amountNum = Number(amount.toString().replace(/,/g, ''))

    if (isNaN(priceNum) || priceNum <= 0) return true
    if (isNaN(amountNum) || amountNum <= 0) return true

    // 如果有持仓数量限制，检查输入的数量是否超过持仓数量
    if (positionSize) {
      let positionSizeNum = Number(positionSize)

      // 处理空仓情况（positionSize为负数）
      if (!isLong && positionSizeNum < 0) {
        // 如果是空仓且数量为负数，则取绝对值
        positionSizeNum = Math.abs(positionSizeNum)
      }

      // 如果是代币单位，直接比较
      if (unit === 'TOKEN' && amountNum > positionSizeNum) return true
      // 如果是USD单位，需要转换后比较
      if (unit === 'USD') {
        const positionValueInUSD = positionSizeNum * coinPrice
        if (amountNum > positionValueInUSD) return true
      }
    }

    return false
  }, [price, amount, unit, positionSize, coinPrice, isLong])

  // 获取订单参数
  const getOrderParams = () => {
    // 获取当前币种的assetId
    const coinInfo = perpsStore.meta.getCoinInfo(coin)
    const { assetid: assetId } = coinInfo || {} // 使用getCoinInfo获取正确的assetId

    // 确定订单方向（平仓时，如果原始仓位是多仓，则平仓为卖出；如果原始仓位是空仓，则平仓为买入）
    // 多仓平仓 = 卖出(b: false)，空仓平仓 = 买入(b: true)
    const isBuy = !isLong // 多仓平仓为卖出(false)，空仓平仓为买入(true)

    // 处理数量（如果是USD单位，需要转换为币种单位）
    let orderAmount = amount
    if (unit === 'USD') {
      // 将USD金额转换为币种数量
      const currentPrice = Number(price)
      if (currentPrice > 0) {
        // 先使用maxPrecision控制精度，保持精确的数值表示
        const rawAmount = (Number(amount) / currentPrice).toFixed(maxPrecision)
        // 使用filterZero处理数值，去除末尾的0
        orderAmount = filterZero(rawAmount)
      }
    }

    // 确保数量为正数（无论是多仓还是空仓）
    if (orderAmount.startsWith('-')) {
      orderAmount = orderAmount.substring(1)
    }

    // 限价单不需要计算滑点，但需要格式化价格
    // 使用hlFormatPrice格式化价格，确保精度与币种的szDecimals一致
    const formattedPrice = hlFormatPrice({
      price,
      szDecimals: coinInfo?.szDecimals,
      isSimple: true,
    })

    // 使用filterZero处理价格，去除末尾的0
    const slippagePrice = filterZero(formattedPrice)

    // 构建订单参数
    return {
      orders: [
        {
          a: assetId, // 使用assetId或默认值
          b: isBuy, // 平多仓为卖出(false)，平空仓为买入(true)
          s: filterZero(orderAmount),
          p: slippagePrice,
          r: true, // 限价单
          t: { limit: { tif: 'Gtc' as const } },
        },
      ],
      grouping: 'na' as const,
      roomId: query?.roomId || '', // 从路由参数中获取roomId
    }
  }

  // 提交订单
  const handleSubmit = async () => {
    if (isButtonDisabled || loading) return

    setLoading(true)
    try {
      // 获取订单参数
      const orderParams = getOrderParams()

      // 发送下单请求
      const res = await EX_PLACE_ORDER(orderParams)

      if (res?.code !== 200) {
        openNotification(
          'position_notification',
          'error',
          res?.message || i18n.t('order_failed'),
        )
        return
      }

      openNotification(
        'position_notification',
        'success',
        i18n.t('limit_order_success'),
      )

      // 重新调用仓位接口，更新仓位信息
      try {
        // 获取当前钱包地址
        const address = walletStore.arbitrumAddress
        if (address) {
          await perpsStore.order.getPositionOrders(address as any)
          await perpsStore.order.getPendingOrder(address as any)
        }
      } catch (error) {
        console.error('更新仓位信息失败:', error)
      }

      onClose()
    } catch (error: any) {
      console.error('下单失败:', error)
      openNotification(
        'position_notification',
        'error',
        error?.message || i18n.t('order_failed'),
      )
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal
      title={i18n.t('limit_order_title')}
      open={open}
      onCancel={onClose}
      footer={null}
      width={400}
      centered
      wrapClassName="limit-order-modal"
    >
      <NotificationComponent />
      <div className="flex flex-col gap-4 px-4 py-3">
        <p className="mb-2 text-center text-sm text-black">
          {i18n.t('limit_order_desc')}
        </p>

        {/* 价格输入框 */}
        <div className="flex flex-col">
          <div className="w-full">
            <div className="relative flex w-full items-center">
              <input
                type="text"
                value={price === '' ? '' : price}
                onChange={(e) => {
                  // 获取输入值
                  const value = e.target.value

                  // 检查输入是否为有效数字格式（允许小数点和数字）
                  if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                    // 检查小数位数是否超过限制
                    const parts = value.split('.')
                    // 价格使用与币种相同的精度
                    const coinInfo = perpsStore.meta.getCoinInfo(coin)
                    const pricePrecision = coinInfo?.szDecimals || 6

                    if (
                      parts.length === 1 ||
                      parts[1].length <= pricePrecision
                    ) {
                      setPrice(value)
                      // 计算预估已关闭盈亏
                      calculateEstimatedPnL(value)
                    }
                  }
                }}
                className="w-full rounded border border-[#E5E7EB] bg-transparent px-3 py-1 text-black"
                style={{ height: '36px' }}
                placeholder={i18n.t('limit_order_price_placeholder')}
              />
              <div
                className="absolute right-3 cursor-pointer justify-start text-right text-sm text-yellow-500"
                onClick={handleUseMidPrice}
              >
                {i18n.t('tit_mid')}
              </div>
            </div>
          </div>
        </div>

        {/* 数量输入框 */}
        <div className="flex flex-col">
          <div className="w-full">
            <div className="flex w-full items-center">
              <div className="relative flex-1">
                <input
                  type="text"
                  value={
                    amount === ''
                      ? ''
                      : amount === '0'
                        ? amount
                        : filterZero(amount)
                  }
                  onChange={(e) => {
                    // 获取输入值
                    const value = e.target.value

                    // 检查输入是否为有效数字格式（允许小数点和数字，不允许负号）
                    if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                      // 检查小数位数是否超过限制
                      const parts = value.split('.')
                      if (
                        parts.length === 1 ||
                        parts[1].length <= maxPrecision
                      ) {
                        // 更新金额 - 允许空值
                        setAmount(value)
                        // 计算预估已关闭盈亏
                        calculateEstimatedPnL(price)

                        // 如果输入为空，设置百分比为0
                        if (value === '') {
                          setPercent(0)
                          return
                        }

                        // 计算并更新百分比
                        try {
                          // 获取最大可用数量
                          let maxAmount = positionSize
                            ? Number(positionSize)
                            : 0.101

                          // 处理空仓情况（positionSize为负数）
                          if (!isLong && maxAmount < 0) {
                            // 如果是空仓且数量为负数，则取绝对值
                            maxAmount = Math.abs(maxAmount)
                          }

                          // 确保maxAmount是有效数字
                          if (isNaN(maxAmount) || maxAmount <= 0) {
                            maxAmount = 0.101 // 使用默认值
                          }

                          // 根据当前单位调整最大数量
                          if (unit === 'USD') {
                            // 获取当前输入框中的价格（如果有效）或使用存储的币种价格
                            let currentPrice = coinPrice
                            const inputPrice = Number(price)
                            if (!isNaN(inputPrice) && inputPrice > 0) {
                              currentPrice = inputPrice
                            }
                            maxAmount = maxAmount * currentPrice // 转换为USD
                          }

                          // 计算百分比
                          const inputAmount = Number(value)
                          if (!isNaN(inputAmount) && maxAmount > 0) {
                            // 检查输入值是否超过最大值
                            if (value !== '' && inputAmount > maxAmount) {
                              // 如果输入超过最大值，则设置为最大值
                              const limitedAmount =
                                unit === 'USD'
                                  ? maxAmount.toFixed(2)
                                  : maxAmount.toFixed(maxPrecision)

                              setAmount(filterZero(limitedAmount))
                              setPercent(100)
                              return
                            }

                            // 正常计算百分比
                            const newPercent = Math.min(
                              100,
                              Math.round((inputAmount / maxAmount) * 100),
                            )
                            setPercent(newPercent)
                          }
                        } catch (error) {
                          console.error('百分比计算错误:', error)
                        }
                      }
                    }
                  }}
                  className="w-full rounded-l border border-[#E5E7EB] bg-transparent px-3 py-1 text-black"
                  style={{ height: '36px', borderRight: 'none' }}
                  placeholder={i18n.t('limit_order_amount_placeholder')}
                />
              </div>
              <Select
                value={unit}
                onChange={(value) => {
                  // 切换单位时转换数值
                  if (value !== unit) {
                    try {
                      const currentAmount = Number(amount)

                      // 获取当前输入框中的价格（如果有效）或使用存储的币种价格
                      let currentPrice = coinPrice
                      const inputPrice = Number(price)
                      if (!isNaN(inputPrice) && inputPrice > 0) {
                        currentPrice = inputPrice
                      }

                      // 确保金额是有效数字
                      if (!isNaN(currentAmount)) {
                        if (value === 'USD' && unit === 'TOKEN') {
                          // 代币 -> USD: 乘以当前价格
                          const newAmount = (
                            currentAmount * currentPrice
                          ).toFixed(2)
                          setAmount(filterZero(newAmount))
                        } else if (value === 'TOKEN' && unit === 'USD') {
                          // USD -> 代币: 除以当前价格
                          if (currentPrice > 0) {
                            // 先使用maxPrecision控制精度，保持精确的数值表示
                            const rawAmount = (
                              currentAmount / currentPrice
                            ).toFixed(maxPrecision)
                            // 使用filterZero处理数值，去除末尾的0
                            setAmount(filterZero(rawAmount))
                          }
                        }
                      }
                    } catch (error) {
                      console.error('单位转换错误:', error)
                    }

                    setUnit(value)
                    // 计算预估已关闭盈亏
                    calculateEstimatedPnL(price)
                  }
                }}
                className="unit-select"
                popupMatchSelectWidth={true}
                dropdownStyle={{ minWidth: '120px' }}
                style={{
                  width: '120px',
                  height: '36px',
                  border: '1px solid #E5E7EB',
                  borderLeft: 'none',
                  borderTopRightRadius: '4px',
                  borderBottomRightRadius: '4px',
                }}
              >
                <Select.Option value="TOKEN">{coin}</Select.Option>
                <Select.Option value="USD">USD</Select.Option>
              </Select>
            </div>
          </div>
        </div>

        {/* 百分比滑块 */}
        <div className="mt-1">
          <div className="flex items-center">
            <div className="flex-1">
              <Slider
                value={[percent]}
                type="trade"
                theme="light"
                min={0}
                max={100}
                onValueChange={handlePercentChange}
                onValueCommit={(value) => {
                  handlePercentChange(value)
                  // 计算预估已关闭盈亏
                  calculateEstimatedPnL(price)
                }}
              />
            </div>
            <div className="ml-2 rounded bg-white px-2 py-1 text-center text-sm text-black">
              {percent}%
            </div>
          </div>
        </div>

        {/* 预估已关闭盈亏 */}
        {price && amount && (
          <div className="flex items-center justify-between px-3 py-2">
            <div className="text-sm text-gray-600">
              {i18n.t('limit_order_estimated_pnl')}
            </div>
            {estimatedPnL ? (
              <div
                className={`text-sm font-medium ${Number(estimatedPnL) > 0 ? 'text-green-500' : Number(estimatedPnL) === 0 ? 'black' : 'text-red-500'}`}
              >
                ${Math.abs(Number(estimatedPnL)).toFixed(2)}
              </div>
            ) : (
              <div className="text-sm text-gray-400">--</div>
            )}
          </div>
        )}

        {/* 确认按钮 */}
        <ButtonBlack
          className="mt-2 h-10 w-full text-center text-base font-normal"
          loading={loading}
          disabled={isButtonDisabled}
          onClick={handleSubmit}
        >
          {i18n.t('confirm')}
        </ButtonBlack>
      </div>
    </Modal>
  )
}
