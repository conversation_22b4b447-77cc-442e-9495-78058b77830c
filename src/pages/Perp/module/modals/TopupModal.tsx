import { ButtonBlack } from '@/components/Customize/Button'
import i18n from '@/i18n'
import { SvgIconTArbitrum, SvgIconProfileCopy } from '@/imgs/icons'
import { WarningOutlined } from '@ant-design/icons'
import { usePerp } from '@/pages/Perp/module/contPerp/usePerp'
import { perpsStore } from '@/store'
import { QRCode, message } from 'antd'
import { observer } from 'mobx-react-lite'
import { Hex } from 'viem'
import { ModalOnPopup } from '@/components/ModalOnPopup'
import clsx from 'clsx'
import { useMedia } from '@/hooks/useMedia'

export const TopupModal = observer(() => {
  const { isMd } = useMedia()
  const { showTopUpModal, setShowTopUpModal, walletAddress } = usePerp()
  const { contractMinDepositAmount } = perpsStore.setting

  return (
    <ModalOnPopup
      title={i18n.t('top_up_usdc')}
      visible={showTopUpModal}
      onOk={() => setShowTopUpModal(false)}
      onClose={() => setShowTopUpModal(false)}
    >
      <div
        className={clsx(
          'flex flex-col items-center gap-2',
          isMd ? 'w-full px-4' : 'w-96',
        )}
      >
        <div
          className={clsx(
            'inline-flex items-center gap-1 rounded-sm px-2.5 py-1',
            isMd ? 'bg-zinc-800' : 'bg-gray-100',
          )}
        >
          <SvgIconTArbitrum className="size-4" />
          <div
            className={clsx(
              'justify-center text-base',
              isMd ? 'text-white' : 'text-zinc-900',
            )}
          >
            Arbitrum
          </div>
        </div>

        <div className="flex flex-col items-center gap-2">
          <div className={clsx(isMd && 'bg-white')}>
            <QRCode size={100} value={walletAddress as Hex} />
          </div>
          <div
            className={clsx(
              'justify-start self-stretch text-center text-xs font-normal leading-none',
              isMd ? 'text-gray-450' : 'text-theme-secondary',
            )}
          >
            {i18n.t('scan_qr_code_to_deposit')}
          </div>
        </div>

        <div className="mt-2 inline-flex w-full items-start justify-center gap-2 rounded-lg px-2.5 py-1.5 outline outline-1 outline-gray-300">
          {walletAddress}
        </div>

        <div className="inline-flex items-start justify-start gap-1 rounded-sm bg-yellow-500/10 px-2.5 py-1.5">
          <WarningOutlined className="size-3.5 text-red-600" />
          <div className="justify-start text-center text-xs font-normal text-red-600">
            {i18n.t('min_transfer_usdc', {
              amount: contractMinDepositAmount || 0,
            })}
          </div>
        </div>

        <div className="mt-5 flex w-full items-center">
          <ButtonBlack
            className={clsx('h-10 w-full', isMd && '!bg-white !text-black')}
            onClick={() => {
              try {
                navigator.clipboard.writeText(walletAddress as Hex)
                message.success(i18n.t('copy_success'))
              } catch (error) {
                message.error(i18n.t('copy_failed'))
                console.error('copy failed:', error)
              }
            }}
          >
            <div className="flex w-full cursor-pointer items-center justify-center gap-1">
              <SvgIconProfileCopy className="size-4" />
              {i18n.t('copy')}
            </div>
          </ButtonBlack>
        </div>
      </div>
    </ModalOnPopup>
  )
})
