import { InputNumberPerp } from '@/components/Customize/InputNumber'
import i18n from '@/i18n'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { TPerpNumber } from '@/pages/Perp/module/types'
import { isBlank } from '@/utils'
import { observer } from 'mobx-react-lite'
import { useEffect, useState } from 'react'

export const TPSLLimit = observer(
  ({
    szDecimals,
    onCallback,
  }: {
    szDecimals: number
    onCallback: (profitPrice: TPerpNumber, lossPrice: TPerpNumber) => void
  }) => {
    const { hlFormatPrice } = useTools()

    const [profitPrice, setProfitPrice] = useState<TPerpNumber>(null)
    const [lossPrice, setLossPrice] = useState<TPerpNumber>(null)

    useEffect(() => {
      onCallback && onCallback(profitPrice, lossPrice)
    }, [profitPrice, lossPrice, onCallback])

    function onProfitChange(value: TPerpNumber) {
      setProfitPrice(value)
    }

    function onLossChange(value: TPerpNumber) {
      setLossPrice(value)
    }

    function onProfitBlur(e: React.FocusEvent<HTMLInputElement>) {
      if (isBlank(e.target.value)) return

      const value = hlFormatPrice({
        price: e.target.value,
        szDecimals,
        isSimple: true,
      })

      setProfitPrice(value)
    }

    function onLossBlur(e: React.FocusEvent<HTMLInputElement>) {
      if (isBlank(e.target.value)) return

      const value = hlFormatPrice({
        price: e.target.value,
        szDecimals,
        isSimple: true,
      })

      setLossPrice(value)
    }

    return (
      <div className="flex w-full items-center justify-between gap-2">
        <div className="w-full md:w-auto md:py-1">
          <InputNumberPerp
            value={profitPrice}
            className="w-full"
            theme="light"
            min={0}
            onChange={onProfitChange}
            onBlur={onProfitBlur}
            prefix={
              <span className="text-10 text-[#9293a0]">
                {i18n.t('tpsl_profit_price')}
              </span>
            }
          />
        </div>
        <div className="w-full md:w-auto md:py-1">
          <InputNumberPerp
            value={lossPrice}
            className="w-full"
            theme="light"
            min={0}
            onChange={onLossChange}
            onBlur={onLossBlur}
            prefix={
              <span className="text-10 text-[#9293a0]">
                {i18n.t('tpsl_loss_price')}
              </span>
            }
          />
        </div>
      </div>
    )
  },
)
