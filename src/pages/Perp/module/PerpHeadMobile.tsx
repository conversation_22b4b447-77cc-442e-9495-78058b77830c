import { ColoringText } from '@/components/ColoringText'
import { SvgIconClose } from '@/imgs/icons'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { HeadCtx } from '@/pages/Perp/module/HeadCtx'
import { PerpHead } from '@/pages/Perp/module/PerpHead'
import { perpsStore } from '@/store'
import { observer } from 'mobx-react-lite'
import { useEffect, useMemo, useState } from 'react'
import numeral from 'numeral'
import { DownOutlined } from '@ant-design/icons'
import clsx from 'clsx'

export const PerpHeadMobile = observer(
  ({
    isModalOpen,
    setIsModalOpen,
  }: {
    isModalOpen: boolean
    setIsModalOpen: (isModalOpen: boolean) => void
  }) => {
    const { markPx, prevDayPx, szDecimals } = perpsStore.meta.activeMeta || {}
    const { hlFormatNumber, calculateCoinPrice, hlFormatPrice } = useTools()

    const [isOpen, setIsOpen] = useState(false)

    // 计算24h涨跌变化
    const dayChange = useMemo(() => {
      if (!markPx || !prevDayPx) return { amount: '0', percentage: '0' }
      return perpsStore.meta.dayChange(markPx, prevDayPx)
    }, [markPx, prevDayPx])

    const { amount, percentage } = dayChange

    // 展示24小时涨跌价格
    const viewPrice = useMemo(() => {
      const maxWidth = calculateCoinPrice(markPx, szDecimals)
      const amountPrice = numeral(amount).format(`0,0.${'0'.repeat(maxWidth)}`)
      if (parseFloat(amount) === 0) {
        return <ColoringText amount={amount}>{amountPrice}</ColoringText>
      }
      if (parseFloat(amount) >= 0) {
        return <ColoringText amount={amount}>+{amountPrice}</ColoringText>
      }
      return <ColoringText amount={amount}>{amountPrice}</ColoringText>
    }, [amount, markPx, szDecimals])

    // 展示24小时涨跌百分比
    const viewPercentage = useMemo(() => {
      if (parseFloat(amount) === 0) {
        return (
          <ColoringText amount={amount}>
            {hlFormatNumber(percentage, 2)}%
          </ColoringText>
        )
      }

      if (parseFloat(amount) >= 0) {
        return (
          <ColoringText amount={amount}>
            +{hlFormatNumber(percentage, 2)}%
          </ColoringText>
        )
      }

      return (
        <ColoringText amount={amount}>
          {hlFormatNumber(percentage, 2)}%
        </ColoringText>
      )
    }, [amount, percentage])

    // 展示标记价格
    const viewMarkPx = useMemo(() => {
      return markPx ? hlFormatPrice({ price: markPx }) : 'N/A'
    }, [hlFormatPrice, markPx])

    useEffect(() => {
      if (isModalOpen) {
        setIsOpen(false)
      }
    }, [isModalOpen])

    function onOpen() {
      if (isModalOpen) {
        setIsModalOpen(false)
      } else {
        setIsOpen(!isOpen)
      }
    }

    return (
      <div className="flex w-full flex-col">
        <div className="flex w-full shrink-0 items-center justify-between">
          <div className="flex items-center gap-2">
            <PerpHead />
            {isModalOpen && <div className="text-9">{viewPercentage}</div>}
          </div>
          {isModalOpen ? (
            <div
              className="flex cursor-pointer items-center justify-between"
              onClick={onOpen}
            >
              <SvgIconClose className="size-4 cursor-pointer text-white" />
            </div>
          ) : (
            <div
              className="flex cursor-pointer items-center gap-2"
              onClick={onOpen}
            >
              <div className="flex flex-col items-center text-9">
                {viewPrice}
                {viewPercentage}
              </div>
              <div className="flex items-center text-base font-medium">
                ${viewMarkPx}
              </div>
              <DownOutlined />
            </div>
          )}
        </div>

        <div
          className={clsx(
            'overflow-hidden transition-all duration-500',
            isOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0',
          )}
        >
          <HeadCtx />
        </div>
      </div>
    )
  },
)
