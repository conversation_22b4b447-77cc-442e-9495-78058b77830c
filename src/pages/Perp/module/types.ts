import { EX_PLACE_ORDER_PARAMS } from '@/api/interface/EX_PLACE_ORDER'
import { TSide } from '@/store/perps/types'
import { NotificationConfig } from 'antd/es/notification/interface'
import { Hex } from 'viem'

export type OpenOrderContextType = {
  // 市价/限价
  mode: TPerpMode
  setMode: (mode: TPerpMode) => void
  // 订单方向
  side: TSide
  setSide: (side: TSide) => void
  // 限价单价格
  price: TPerpNumber
  setPrice: (price: TPerpNumber) => void
  // 下单币种数量
  // 注意：这里受到 perpsStore.init.coinType 的影响
  // 如果 coinType = usdc，则 amount 为 usd 数量
  // 如果 coinType = coin，则 amount 为 coin 数量
  amount: string | null
  setAmount: (amount: string | null) => void
  // 下单币种数量(百分比)
  amountPercent: number
  setAmountPercent: (amountPercent: number) => void
  // 订单管理
  dealManage: TPerpDealManage
  setDealManage: (dealManage: TPerpDealManage) => void
  // 订单类型
  orderType: TPerpOrderType
  setOrderType: (orderType: TPerpOrderType) => void
  // 止盈价格
  profitPrice: TPerpNumber
  setProfitPrice: (profitPrice: TPerpNumber) => void
  // 止损价格
  lossPrice: TPerpNumber
  setLossPrice: (lossPrice: TPerpNumber) => void
}

export interface IUseOfficial {
  // 可用下单余额
  availableUsd: string
  // 最大可交易数量
  maxAmount: string
  // 最大交易数量精度
  maxPrecision: number
  // 是否只减仓且方向相同
  isReduceSameSide: boolean
  // 换算数量
  convertAmount: (value: string) => string
  // 换算金额
  convertAmountToUsd: (value: string) => string
}

export type TPerpNumber = string | number | null

// 市价/限价
export type TPerpMode = 'market' | 'limit'

// 订单管理 「reduce = 只减仓； profitLoss = 止盈/止损」
export type TPerpDealManage = 'reduce' | 'profitLoss' | undefined

// 订单提交
export type TPerpSubmitOrder = EX_PLACE_ORDER_PARAMS['orders'][0]

// 订单类型
export type TPerpOrderType = 'Gtc' | 'Ioc' | 'Alo'

export interface PerpContextType {
  // 打开通知
  openNotification: (
    key: string,
    type: 'success' | 'error' | 'loading',
    message?: React.ReactNode,
    duration?: number,
  ) => void
  // 限价
  limitPrice: TPerpNumber
  setLimitPrice: (limitPrice: TPerpNumber) => void
  // 钱包地址
  walletAddress: Hex | undefined
  // 是否显示充值弹窗
  showTopUpModal: boolean
  setShowTopUpModal: (showTopUpModal: boolean) => void
  // 移动端指定订单方向
  sideView: TSideView
  setSideView: (views: TSideView) => void
}

/**
 * 移动端指定订单方向
 */
export type TSideView = {
  side: TSide
  round: number
}
