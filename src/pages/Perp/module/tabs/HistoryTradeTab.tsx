import { observer } from 'mobx-react-lite'
import { perpsStore } from '@/store'
import { useState, useEffect, useRef } from 'react'
import { useTabContext } from '../context/TabContext'
import { useTools } from '../../hooks/useTools'
import { $ten } from '@/utils'
import clsx from 'clsx'
import i18n from '@/i18n'
import { TabComponentProps } from './TabComponentProps'
import { isMobile } from 'react-device-detect'

export const HistoryTradeTab = observer((props: TabComponentProps = {}) => {
  // 主题和简化模式
  const { theme = 'dark', simplified = false } = props

  // 根据主题确定样式
  const isDarkTheme = theme === 'dark'
  const bgColor = isDarkTheme ? 'bg-[#131313]' : 'bg-white'
  const textColor = isDarkTheme ? 'text-white' : 'text-black'
  const borderColor = isDarkTheme ? 'border-[#303030]' : 'border-[#F2F3F7]'
  const headerBgColor = isDarkTheme ? 'bg-[#131313]' : 'bg-white'
  const headerTextColor = isDarkTheme ? 'text-[#868686]' : 'text-[#9293A0]'
  const rowHoverColor = isDarkTheme
    ? 'hover:bg-[#1A1A1A]'
    : 'hover:bg-[#F9FAFB]'

  // 获取上下文
  const { setActiveTab } = useTabContext()
  // 获取工具函数
  const { hlFormatPrice, hlFormatAnyCoin, filterZero } = useTools()

  // 筛选器状态
  const [filterType, setFilterType] = useState<
    'all' | 'current' | 'long' | 'short'
  >('all')
  const [filterDropdownVisible, setFilterDropdownVisible] = useState(false)

  // 排序状态
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // 创建过滤器下拉菜单的引用
  const filterDropdownRef = useRef<HTMLDivElement>(null)

  // 处理过滤器切换
  const handleFilterChange = (type: 'all' | 'current' | 'long' | 'short') => {
    setFilterType(type)
    setFilterDropdownVisible(false)
  }

  // 切换过滤器下拉菜单显示状态
  const toggleFilterDropdown = () => {
    setFilterDropdownVisible(!filterDropdownVisible)
  }

  // 添加点击外部关闭下拉菜单的功能
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        filterDropdownRef.current &&
        !filterDropdownRef.current.contains(event.target as Node) &&
        filterDropdownVisible
      ) {
        setFilterDropdownVisible(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [filterDropdownVisible])
  // 自定义滚动条样式
  const scrollbarStyle = `
    .custom-scrollbar::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      position: absolute;
      right: 0;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: transparent;
      margin-top: 30px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 4px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }
    .custom-scrollbar::-webkit-scrollbar-corner {
      background: transparent;
    }
    .position-table-container {
      scrollbar-width: thin;
      scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
    }
    .position-table-container {
      width: calc(100vw - 840px);
      min-width: 500px; /* 确保在小屏幕上有最小宽度 */
      max-width: 100%; /* 确保不会超出父容器 */
      overflow-x: auto;
      -webkit-overflow-scrolling: touch; /* 增强移动端滚动体验 */
    }
    @media screen and (max-width: 1200px) {
      .position-table-container {
        width: calc(100vw - 600px);
      }
    }
    @media screen and (max-width: 992px) {
      .position-table-container {
        width: calc(100vw - 20px);
        min-width: 360px;
        margin: 0 auto;
      }
      .no-data-row {
        display: none !important; /* 在移动端隐藏无数据提示行 */
      }
    }
    @media screen and (max-width: 768px) {
      .position-table-container {
        width: calc(100vw - 20px);
        min-width: 320px;
      }
    }
  `

  // 过滤并排序历史成交数据
  const sortedOrders = [...perpsStore.order.historyOrders]
    .filter((order) => {
      // 获取当前活跃的币种
      const currentCoin = perpsStore.init.coin
      const isBuy = order.side === 'B'

      switch (filterType) {
        case 'current':
          return order.coin === currentCoin
        case 'long':
          return isBuy
        case 'short':
          return !isBuy
        case 'all':
        default:
          return true
      }
    })
    .sort((a, b) => {
      // 按照时间排序
      if (sortOrder === 'asc') {
        return a.time - b.time
      } else {
        return b.time - a.time
      }
    })

  // 切换排序方式
  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
  }

  // 格式化日期时间
  const formatDateTime = (timestamp: number) => {
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
  }

  // 跳转到区块浏览器查看交易详情
  const openExplorer = (txHash: string) => {
    if (!txHash) return
    // 使用环境变量中的域名构建区块浏览器URL
    const baseUrl = import.meta.env.VITE_APP_HL_SVG_URL || ''
    const explorerUrl = `${baseUrl}/explorer/tx/${txHash}`
    window.open(explorerUrl, '_blank')
  }

  return (
    <div className="relative flex w-full flex-col">
      {/* 添加滚动条样式 */}
      <style>{scrollbarStyle}</style>

      {/* 过滤器 - 只在非简化模式下显示 */}
      {!simplified && (
        <div className="absolute -top-8 right-0 mb-2 flex justify-end">
          <div className="relative" ref={filterDropdownRef}>
            <button
              className={`flex items-center rounded ${isDarkTheme ? 'bg-[#1A1A1A] text-white' : 'bg-[#F9FAFB] text-black'} px-3 py-1 text-[11px]`}
              onClick={toggleFilterDropdown}
            >
              {filterType === 'all' && i18n.t('all')}
              {filterType === 'current' && i18n.t('current')}
              {filterType === 'long' && i18n.t('long')}
              {filterType === 'short' && i18n.t('short')}
              <svg
                className={`ml-1 h-4 w-4 transform transition-transform ${
                  filterDropdownVisible ? 'rotate-180' : ''
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
            {filterDropdownVisible && (
              <div
                className={`absolute right-0 z-50 mt-1 w-20 rounded ${isDarkTheme ? 'bg-[#1A1A1A]' : 'bg-white'} py-1 shadow-lg`}
              >
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('all')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('all')}
                  </span>
                  {filterType === 'all' && <span className="ml-auto">✓</span>}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('current')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('current')}
                  </span>
                  {filterType === 'current' && (
                    <span className="ml-auto">✓</span>
                  )}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('long')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('long')}
                  </span>
                  {filterType === 'long' && <span className="ml-auto">✓</span>}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('short')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('short')}
                  </span>
                  {filterType === 'short' && <span className="ml-auto">✓</span>}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 表格容器 */}
      <div
        className={`position-table-container custom-scrollbar overflow-y-auto ${bgColor}`}
        style={{ height: '210px', position: 'relative' }}
      >
        <div className="w-full min-w-[600px]">
          <table className="w-full table-fixed border-collapse">
            <thead>
              <tr
                className={`border-b ${borderColor} text-xs ${headerTextColor} sticky top-0 z-10 ${headerBgColor}`}
              >
                <th
                  className="w-[15%] cursor-pointer px-1 py-1 text-left text-[11px]"
                  onClick={toggleSortOrder}
                >
                  {i18n.t('time')} {sortOrder === 'desc' ? '↓' : '↑'}
                </th>
                <th className="w-[8%] px-1 py-1 text-left text-[11px]">
                  {i18n.t('coin')}
                </th>
                <th className="w-[10%] px-1 py-1 text-left text-[11px]">
                  {i18n.t('direction')}
                </th>
                <th className="w-[10%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('price')}
                </th>
                <th className="w-[10%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('quantity')}
                </th>
                <th className="w-[12%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('trade_value')}
                </th>
                <th className="w-[12%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('fee')}
                </th>
                <th className="w-[15%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('realized_profit_loss')}
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedOrders.length > 0 ? (
                sortedOrders.map((order, index) => {
                  // 计算成交价值
                  const tradeValue = parseFloat(order.px) * parseFloat(order.sz)
                  // 确定方向文本
                  const directionText = order.dir || ''
                  // 根据方向确定颜色
                  const directionClass =
                    directionText === 'Open Long' ||
                    directionText === 'Close Short'
                      ? 'text-[#00D09C]'
                      : 'text-[#FF5C5C]'
                  return (
                    <tr
                      key={index}
                      className={`border-b ${borderColor} text-sm ${rowHoverColor}`}
                    >
                      <td className="w-[15%] px-1 py-1 text-left text-[11px] text-[#5F606D]">
                        {formatDateTime(order.time)}
                        <span className="ml-1 inline-block align-middle text-[#868686]">
                          <svg
                            width="12"
                            height="12"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            onClick={() => openExplorer(order.hash)}
                            className="cursor-pointer"
                          >
                            <path
                              d="M7 17L17 7"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M7 7H17V17"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </span>
                      </td>
                      <td className="w-[8%] px-1 py-1 text-left text-[11px]">
                        <span
                          className={clsx('cursor-pointer', directionClass)}
                          onClick={() => {
                            if (order?.coin) {
                              perpsStore.init.setCoin(order.coin)
                            }
                          }}
                        >
                          {order.coin}
                        </span>
                      </td>
                      <td className="w-[10%] px-1 py-1 text-left text-[11px]">
                        <span className={directionClass}>{directionText}</span>
                      </td>
                      <td className="w-[10%] px-1 py-1 text-right text-[11px] text-[#5F606D]">
                        {filterZero(
                          hlFormatPrice({
                            price: order.px,
                            szDecimals: perpsStore.meta.getCoinInfo(order.coin)
                              ?.szDecimals,
                            isSimple: true,
                          }),
                        )}
                      </td>
                      <td className="w-[10%] px-1 py-1 text-right text-[11px] text-[#5F606D]">
                        {parseFloat(order.sz) === 0
                          ? '--'
                          : filterZero(
                              hlFormatAnyCoin(
                                order.sz,
                                perpsStore.meta.getCoinInfo(order.coin)
                                  ?.szDecimals,
                              ),
                            )}{' '}
                        {order.coin}
                      </td>
                      <td className="w-[12%] px-1 py-1 text-right text-[11px] text-[#5F606D]">
                        ${$ten.toFixed(tradeValue.toString(), 2, true)}
                      </td>
                      <td className="w-[12%] px-1 py-1 text-right text-[11px] text-[#5F606D]">
                        $
                        {$ten.toFixed(
                          Math.abs(parseFloat(order.fee || '0')).toString(),
                          2,
                          true,
                        )}
                      </td>
                      <td className="w-[15%] px-1 py-1 text-right text-[11px]">
                        <span
                          className={
                            parseFloat(order.closedPnl || '0') -
                              parseFloat(order.fee || '0') >=
                            0
                              ? 'text-[#00D09C]'
                              : 'text-[#FF5C5C]'
                          }
                        >
                          {parseFloat(order.closedPnl || '0') -
                            parseFloat(order.fee || '0') >=
                          0
                            ? '$'
                            : '-$'}
                          {$ten.toFixed(
                            Math.abs(
                              parseFloat(order.closedPnl || '0') -
                                parseFloat(order.fee || '0'),
                            ).toString(),
                            2,
                            true,
                          )}
                        </span>
                      </td>
                    </tr>
                  )
                })
              ) : (
                <tr className={`text-sm ${rowHoverColor} no-data-row`}>
                  <td
                    colSpan={8}
                    className={`px-4 py-8 text-center ${headerTextColor}`}
                  >
                    {i18n.t('no_history_trade_data')}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
})
