// 通用的表格样式，用于所有 Tab 组件
export const getScrollbarStyle = (isInWalletPage: boolean) => `
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    position: absolute;
    right: 0;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    margin-top: 30px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }
  .custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }
  .position-table-container {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  }
  .position-table-container {
    ${isInWalletPage 
      ? 'width: 100%;' // 在钱包页面中使用100%宽度
      : `width: calc(100vw - 840px);
         @media screen and (max-width: 1200px) {
           width: calc(100vw - 600px);
         }
         @media screen and (max-width: 992px) {
           width: 400px;
         }`
    }
    min-width: ${isInWalletPage ? '100%' : '500px'}; /* 确保在小屏幕上有最小宽度 */
    max-width: 100%; /* 确保不会超出父容器 */
    overflow-x: auto;
    padding: 0 ${isInWalletPage ? '0' : '10px'};
  }
  .position-table th,
  .position-table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`
