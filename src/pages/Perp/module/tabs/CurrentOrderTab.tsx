import { observer } from 'mobx-react-lite'
import { perpsStore, walletStore } from '@/store'
import { useState, useRef, useEffect } from 'react'
import { message } from 'antd'
import { $ten } from '@/utils'
import { EX_CANCEL_ORDER } from '@/api/interface/EX_CANCEL_ORDER'
import { useTabContext } from '../context/TabContext'
import { CancelAllOrdersModal } from '../modals/CancelAllOrdersModal'
import { useTools } from '../../hooks/useTools'
import clsx from 'clsx'
import i18n from '@/i18n'
import { TabComponentProps } from './TabComponentProps'
import { isMobile } from 'react-device-detect'

export const CurrentOrderTab = observer((props: TabComponentProps = {}) => {
  // 主题和简化模式
  const { theme = 'dark', simplified = false } = props

  // 根据主题确定样式
  const isDarkTheme = theme === 'dark'
  const bgColor = isDarkTheme ? 'bg-[#131313]' : 'bg-white'
  const textColor = isDarkTheme ? 'text-white' : 'text-black'
  const borderColor = isDarkTheme ? 'border-[#303030]' : 'border-[#F2F3F7]'
  const headerBgColor = isDarkTheme ? 'bg-[#131313]' : 'bg-white'
  const headerTextColor = isDarkTheme ? 'text-[#868686]' : 'text-[#9293A0]'
  const rowHoverColor = isDarkTheme
    ? 'hover:bg-[#1A1A1A]'
    : 'hover:bg-[#F9FAFB]'

  // 获取上下文
  const { setActiveTab } = useTabContext()
  // 获取工具函数
  const { hlFormatPrice, hlFormatAnyCoin, filterZero } = useTools()

  // 筛选器状态
  const [filterType, setFilterType] = useState<
    'all' | 'current' | 'long' | 'short'
  >('all')
  const [filterDropdownVisible, setFilterDropdownVisible] = useState(false)

  // 创建过滤器下拉菜单的引用
  const filterDropdownRef = useRef<HTMLDivElement>(null)

  // 排序方式
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  // 排序字段
  const [sortField, setSortField] = useState<'time' | 'value'>('time')

  // 取消订单的加载状态，使用订单ID作为键
  const [cancelingOrders, setCancelingOrders] = useState<
    Record<string, boolean>
  >({})

  // 取消全部订单的加载状态
  const [isCancelingAll, setIsCancelingAll] = useState(false)

  // 取消全部订单弹窗状态
  const [cancelAllModalVisible, setCancelAllModalVisible] = useState(false)

  // 处理过滤器切换
  const handleFilterChange = (type: 'all' | 'current' | 'long' | 'short') => {
    setFilterType(type)
    setFilterDropdownVisible(false)
  }

  // 切换过滤器下拉菜单显示状态
  const toggleFilterDropdown = () => {
    setFilterDropdownVisible(!filterDropdownVisible)
  }

  // 添加点击外部关闭下拉菜单的功能
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        filterDropdownRef.current &&
        !filterDropdownRef.current.contains(event.target as Node) &&
        filterDropdownVisible
      ) {
        setFilterDropdownVisible(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [filterDropdownVisible])

  // 切换时间排序方式
  const toggleTimeSort = () => {
    if (sortField === 'time') {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField('time')
      setSortOrder('desc')
    }
  }

  // 切换价值排序方式
  const toggleValueSort = () => {
    if (sortField === 'value') {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField('value')
      setSortOrder('desc')
    }
  }

  // 取消单个订单
  const handleCancelOrder = async (assetId: number, orderId: number) => {
    // 设置当前订单为取消中状态
    setCancelingOrders((prev) => ({ ...prev, [`${assetId}-${orderId}`]: true }))

    try {
      const response = await EX_CANCEL_ORDER({
        cancels: [
          {
            a: assetId,
            o: orderId,
          },
        ],
      })

      if (response.code === 200) {
        message.success(i18n.t('tpsl_order_cancel_success'))

        // 重新获取当前委托订单
        const address = walletStore.arbitrumAddress
        if (address) {
          await perpsStore.order.getPendingOrder(address as any)
        }
      } else {
        message.error(`订单取消失败: ${response.message}`)
      }
    } catch (error: any) {
      console.error('取消订单失败:', error)
      message.error(error?.message || i18n.t('tpsl_order_cancel_failed'))
    } finally {
      // 清除当前订单的取消中状态
      setCancelingOrders((prev) => {
        const newState = { ...prev }
        delete newState[`${assetId}-${orderId}`]
        return newState
      })
    }
  }

  // 打开取消全部订单弹窗
  const showCancelAllModal = () => {
    setCancelAllModalVisible(true)
  }

  // 关闭取消全部订单弹窗
  const closeCancelAllModal = () => {
    setCancelAllModalVisible(false)
  }

  // 执行取消全部订单操作
  const executeCancelAllOrders = async () => {
    if (isCancelingAll) return // 防止重复点击

    setIsCancelingAll(true)
    try {
      // 准备所有订单的取消请求
      const cancelRequests = sortedOrders.map((order) => {
        const coinInfo = perpsStore.meta.getCoinInfo(order.coin)
        return {
          a: coinInfo?.assetid || 0,
          o: order.oid,
        }
      })

      if (cancelRequests.length === 0) {
        message.info(i18n.t('no_cancelable_orders'))
        setIsCancelingAll(false)
        closeCancelAllModal()
        return
      }

      // 调用取消订单接口
      const response = await EX_CANCEL_ORDER({
        cancels: cancelRequests,
      })

      if (response.code === 200) {
        message.success(i18n.t('cancel_all_orders_success'))

        // 重新获取当前委托订单
        const address = walletStore.arbitrumAddress
        if (address) {
          await perpsStore.order.getPendingOrder(address as any)
        }

        // 关闭弹窗
        closeCancelAllModal()
      } else {
        message.error(
          `${i18n.t('tpsl_order_cancel_failed')}: ${response.message}`,
        )
      }
    } catch (error: any) {
      console.error('取消全部订单失败:', error)
      message.error(error?.message || i18n.t('tpsl_order_cancel_failed_retry'))
    } finally {
      setIsCancelingAll(false)
    }
  }

  // 格式化日期时间
  const formatDateTime = (timestamp: number) => {
    const date = new Date(timestamp)

    // 格式化年月日
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    // 格式化时分秒
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
  }

  // 过滤委托订单数据
  const filteredOrders = perpsStore.order.pendingOrders.filter((order) => {
    // 获取当前活跃的币种
    const currentCoin = perpsStore.init.coin

    // 判断订单方向
    const isBuy = order.side === 'B'

    switch (filterType) {
      case 'current':
        return order.coin === currentCoin
      case 'long':
        return isBuy
      case 'short':
        return !isBuy
      case 'all':
      default:
        return true
    }
  })

  // 排序委托订单数据
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    // 计算订单价值
    const getOrderValue = (order: any) => {
      // 如果是市价单，价值为0
      if (
        order.orderType &&
        String(order.orderType).toLowerCase().includes('market')
      ) {
        return 0
      }
      return parseFloat(order.limitPx) * parseFloat(order.sz)
    }

    const valueA = getOrderValue(a)
    const valueB = getOrderValue(b)

    if (sortField === 'value') {
      // 按照订单价值排序
      if (sortOrder === 'asc') {
        // 价值相等时按时间倒序
        return valueA === valueB ? b.timestamp - a.timestamp : valueA - valueB
      } else {
        // 价值相等时按时间倒序
        return valueA === valueB ? b.timestamp - a.timestamp : valueB - valueA
      }
    } else {
      // 按照时间排序
      if (sortOrder === 'asc') {
        return a.timestamp - b.timestamp
      } else {
        return b.timestamp - a.timestamp
      }
    }
  })

  // 检查订单是否为止盈止损订单
  const isTpSlOrder = (order: any) => {
    // 检查是否存在触发条件 和/或 订单是否被标记为持仓TPSL订单
    return (
      (order.triggerCondition && order.triggerCondition !== 'N/A') ||
      order.isPositionTpsl
    )
  }

  // 自定义滚动条样式
  const scrollbarStyle = `
    .custom-scrollbar::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      position: absolute;
      right: 0;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: transparent;
      margin-top: 30px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 4px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }
    .custom-scrollbar::-webkit-scrollbar-corner {
      background: transparent;
    }
    .position-table-container {
      scrollbar-width: thin;
      scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
    }
    .position-table-container {
      width: calc(100vw - 840px);
      min-width: 500px; /* 确保在小屏幕上有最小宽度 */
      max-width: 100%; /* 确保不会超出父容器 */
      overflow-x: auto;
      -webkit-overflow-scrolling: touch; /* 优化移动端滚动体验 */
    }
    .position-table th,
    .position-table td {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    @media screen and (max-width: 1200px) {
      .position-table-container {
        width: calc(100vw - 600px);
      }
    }
    @media screen and (max-width: 992px) {
      .position-table-container {
        width: calc(100vw - 20px);
        min-width: 360px;
        margin: 0 auto;
      }
      .no-data-row {
        display: none !important; /* 在移动端隐藏无数据提示行 */
      }
    }

    /* 取消全部订单弹窗样式 */
    .cancel-all-orders-modal .ant-modal-content {
      padding: 0;
      overflow: hidden;
      border-radius: 12px;
      background-color: white;
    }
    .cancel-all-orders-modal .ant-modal-body {
      padding: 0;
    }
    .cancel-all-orders-modal .ant-modal-close {
      color: rgba(0, 0, 0, 0.5);
      top: 12px;
      right: 12px;
    }
  `

  return (
    <div className="relative flex w-full flex-col">
      {/* 添加滚动条和弹窗样式 */}
      <style>{scrollbarStyle}</style>

      {/* 取消全部订单弹窗 - 只在非简化模式下显示 */}
      {!simplified && (
        <CancelAllOrdersModal
          open={cancelAllModalVisible}
          onClose={closeCancelAllModal}
          onConfirm={executeCancelAllOrders}
          isLoading={isCancelingAll}
        />
      )}

      {/* 过滤器 - 只在非简化模式下显示 */}
      {!simplified && (
        <div className="absolute -top-8 right-0 mb-2 flex justify-end">
          <div className="relative" ref={filterDropdownRef}>
            <button
              className={`flex items-center rounded ${isDarkTheme ? 'bg-[#1A1A1A] text-white' : 'bg-[#F9FAFB] text-black'} px-3 py-1 text-[11px]`}
              onClick={toggleFilterDropdown}
            >
              {filterType === 'all' && i18n.t('all')}
              {filterType === 'current' && i18n.t('current')}
              {filterType === 'long' && i18n.t('long')}
              {filterType === 'short' && i18n.t('short')}
              <svg
                className={`ml-1 h-4 w-4 transform transition-transform ${
                  filterDropdownVisible ? 'rotate-180' : ''
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
            {filterDropdownVisible && (
              <div
                className={`absolute right-0 z-50 mt-1 w-20 rounded ${isDarkTheme ? 'bg-[#1A1A1A]' : 'bg-white'} py-1 shadow-lg`}
              >
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('all')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('all')}
                  </span>
                  {filterType === 'all' && <span className="ml-auto">✓</span>}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('current')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('current')}
                  </span>
                  {filterType === 'current' && (
                    <span className="ml-auto">✓</span>
                  )}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('long')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('long')}
                  </span>
                  {filterType === 'long' && <span className="ml-auto">✓</span>}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('short')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('short')}
                  </span>
                  {filterType === 'short' && <span className="ml-auto">✓</span>}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 表格容器 */}
      <div
        className={`position-table-container custom-scrollbar overflow-y-auto ${bgColor}`}
        style={{ height: '210px', position: 'relative' }}
      >
        <div className="min-w-[1040px]">
          {/* 使用单一表格结构确保表头和内容宽度一致，并在移动端同步滚动 */}
          <table className="position-table w-full table-fixed border-collapse">
            <thead
              className={`${!isMobile ? 'sticky top-0 z-10' : ''} ${headerBgColor}`}
            >
              <tr
                className={`border-b ${borderColor} text-xs ${headerTextColor}`}
              >
                <th
                  className="w-[12%] cursor-pointer px-1 py-1 text-left text-[11px]"
                  onClick={toggleTimeSort}
                >
                  {i18n.t('time')}
                  {sortField === 'time' && (sortOrder === 'desc' ? '↓' : '↑')}
                </th>
                <th className="w-[11%] px-1 py-1 text-left text-[11px]">
                  {i18n.t('type')}
                </th>
                <th className="w-[6%] px-1 py-1 text-left text-[11px]">
                  {i18n.t('coin')}
                </th>
                <th className="w-[8%] px-1 py-1 text-left text-[11px]">
                  {i18n.t('direction')}
                </th>
                <th className="w-[5%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('quantity')}
                </th>
                <th className="w-[10%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('original_size')}
                </th>
                <th
                  className="w-[8%] cursor-pointer px-1 py-1 text-right text-[11px]"
                  onClick={toggleValueSort}
                >
                  {i18n.t('order_value')}{' '}
                  {sortField === 'value' && (sortOrder === 'desc' ? '↓' : '↑')}
                </th>
                <th className="w-[7%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('tit_price')}
                </th>
                <th className="w-[5%] px-1 py-1 text-center text-[11px]">
                  {i18n.t('reduce_only')}
                </th>
                <th className="w-[14%] px-1 py-1 text-center text-[11px]">
                  {i18n.t('trigger_condition')}
                </th>
                <th className="w-[6%] px-1 py-1 text-center text-[11px]">
                  {i18n.t('tpsl_order')}
                </th>
                <th className="w-[9%] px-1 py-1 text-left text-[11px]">
                  {sortedOrders.length > 0 ? (
                    <button
                      className={`px-2 py-1 text-xs ${isDarkTheme ? 'text-[#FF5C5C] hover:text-[#ff8080]' : '!text-[#FF5C5C] hover:text-[#ff8080]'}`}
                      onClick={showCancelAllModal}
                      disabled={isCancelingAll}
                    >
                      {isCancelingAll
                        ? `${i18n.t('processing')}...`
                        : i18n.t('cancel_all')}
                    </button>
                  ) : (
                    i18n.t('operation')
                  )}
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedOrders.length > 0 ? (
                sortedOrders.map((order, index) => {
                  // 计算订单价值
                  const orderValue =
                    parseFloat(order.limitPx) * parseFloat(order.sz)

                  // 判断方向
                  const isBuy = order.side === 'B'

                  // 获取币种信息
                  const coinInfo = perpsStore.meta.getCoinInfo(order.coin)

                  return (
                    <tr
                      key={index}
                      className={`border-b ${borderColor} text-sm ${rowHoverColor}`}
                    >
                      <td className="w-[12%] px-1 py-1 text-left text-[11px] text-[#5F606D]">
                        {formatDateTime(order.timestamp)}
                      </td>
                      <td className="w-[11%] px-1 py-1 text-left text-[11px] text-[#5F606D]">
                        {order.orderType}
                      </td>
                      <td className="w-[6%] px-1 py-1 text-left text-[11px]">
                        <span
                          className={clsx(
                            'cursor-pointer',
                            isBuy ? 'text-[#00D09C]' : 'text-[#FF5C5C]',
                          )}
                          onClick={() => {
                            if (order?.coin) {
                              perpsStore.init.setCoin(order.coin)
                            }
                          }}
                        >
                          {order.coin}
                        </span>
                      </td>
                      <td className="w-[8%] px-1 py-1 text-left text-[11px]">
                        <span
                          className={
                            isBuy ? 'text-[#00D09C]' : 'text-[#FF5C5C]'
                          }
                        >
                          {order.reduceOnly
                            ? isBuy
                              ? 'Close Short'
                              : 'Close Long'
                            : isBuy
                              ? 'Long'
                              : 'Short'}
                        </span>
                      </td>
                      <td className="w-[5%] px-1 py-1 text-right text-[11px] text-[#5F606D]">
                        {parseFloat(order.sz) === 0
                          ? '--'
                          : hlFormatAnyCoin(
                              order.sz,
                              perpsStore.meta.getCoinInfo(order.coin)
                                ?.szDecimals,
                            )}
                      </td>
                      <td className="w-[10%] px-1 py-1 text-right text-[11px] text-[#5F606D]">
                        {parseFloat(order.origSz) === 0
                          ? '--'
                          : hlFormatAnyCoin(
                              order.origSz,
                              perpsStore.meta.getCoinInfo(order.coin)
                                ?.szDecimals,
                            )}
                      </td>
                      <td className="w-[8%] px-1 py-1 text-right text-[11px] text-[#5F606D]">
                        {order.orderType &&
                        String(order.orderType).toLowerCase().includes('market')
                          ? '--'
                          : Number(orderValue) === 0
                            ? '--'
                            : `$${$ten.toFixed(orderValue.toString(), 2, true)}`}
                      </td>
                      <td className="w-[7%] px-1 py-1 text-right text-[11px] text-[#5F606D]">
                        <span>
                          {order.orderType &&
                          String(order.orderType)
                            .toLowerCase()
                            .includes('market')
                            ? 'Market'
                            : hlFormatPrice({
                                price: order.limitPx,
                                szDecimals: perpsStore.meta.getCoinInfo(
                                  order.coin,
                                )?.szDecimals,
                                isSimple: true,
                              })}
                        </span>
                      </td>
                      <td className="w-[5%] px-1 py-1 text-center text-[11px] text-[#5F606D]">
                        {order.reduceOnly ? i18n.t('yes') : i18n.t('no')}
                      </td>
                      <td className="w-[14%] px-1 py-1 text-center text-[11px] text-[#5F606D]">
                        {order.triggerCondition
                          ? order.triggerCondition.includes('Above')
                            ? `Price Above ${filterZero(
                                hlFormatPrice({
                                  price: order.triggerPx,
                                  szDecimals: perpsStore.meta.getCoinInfo(
                                    order.coin,
                                  )?.szDecimals,
                                  isSimple: true,
                                }),
                              )}`
                            : order.triggerCondition.includes('Below')
                              ? `Price Below ${filterZero(
                                  hlFormatPrice({
                                    price: order.triggerPx,
                                    szDecimals: perpsStore.meta.getCoinInfo(
                                      order.coin,
                                    )?.szDecimals,
                                    isSimple: true,
                                  }),
                                )}`
                              : order.triggerCondition
                          : order.orderType === 'Limit'
                            ? '-'
                            : '--'}
                      </td>
                      <td className="w-[6%] px-1 py-1 text-center text-[11px] text-[#5F606D]">
                        {isTpSlOrder(order) ? i18n.t('yes') : '-'}
                      </td>
                      <td className="w-[9%] px-1 py-1">
                        <button
                          className={`rounded ${isDarkTheme ? 'bg-[#303030] hover:bg-[#404040]' : 'bg-[#F9FAFB] hover:bg-[#F2F3F7]'} px-2 py-1 text-xs ${isDarkTheme ? 'text-[#FF5C5C] hover:text-[#ff8080]' : '!text-[#FF5C5C] hover:text-[#ff8080]'} disabled:cursor-not-allowed disabled:opacity-50`}
                          onClick={() => {
                            handleCancelOrder(coinInfo?.assetid || 0, order.oid)
                          }}
                          disabled={
                            cancelingOrders[
                              `${coinInfo?.assetid || 0}-${order.oid}`
                            ] || isCancelingAll
                          }
                        >
                          {cancelingOrders[
                            `${coinInfo?.assetid || 0}-${order.oid}`
                          ]
                            ? `${i18n.t('processing')}...`
                            : i18n.t('cancel')}
                        </button>
                      </td>
                    </tr>
                  )
                })
              ) : (
                <tr className={`text-sm ${rowHoverColor} no-data-row`}>
                  <td
                    colSpan={12}
                    className={`px-4 py-8 text-center ${headerTextColor}`}
                  >
                    {i18n.t('no_current_order_data')}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
})
