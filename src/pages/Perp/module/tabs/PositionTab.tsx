import { observer } from 'mobx-react-lite'
import { perpsStore, walletStore } from '@/store'
import { useState, useEffect, useRef, useMemo } from 'react'
import { useTabContext } from '../context/TabContext'
import { useRoute } from '@/hooks/useRoute'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { useSharedNotification } from '@/hooks/useSharedNotification'
import { $ten } from '@/utils'
import { EX_PLACE_ORDER } from '@/api/interface/EX_PLACE_ORDER'
import { ColoringText } from '@/components/ColoringText'
import { TPSLOrderModal } from '@/pages/Perp/module/modals/TPSLOrderModal'
import { CloseAllOrdersModal } from '@/pages/Perp/module/modals/CloseAllOrdersModal'
import { LimitOrderModal } from '@/pages/Perp/module/modals/LimitOrderModal'
import { MarketOrderModal } from '@/pages/Perp/module/modals/MarketOrderModal'
import { IPositionOrder } from '@/store/perps/types'
import i18n from '@/i18n'
import { isMobile } from 'react-device-detect'

interface Order {
  triggerCondition: string
  side: 'S' | 'A' | 'B'
  triggerPx: string | number
}

import { TabComponentProps } from './TabComponentProps'

export const PositionTab = observer((props: TabComponentProps = {}) => {
  const { theme = 'dark', simplified = false } = props
  const { setActiveTab } = useTabContext()
  const { query } = useRoute() // 获取路由参数
  const { hlFormatPrice, hlFormatAnyCoin, filterZero } = useTools() // 获取格式化工具
  const { openNotification, NotificationComponent } = useSharedNotification() // 使用共享的通知hook

  // 根据主题确定样式
  const isDarkTheme = theme === 'dark'
  const bgColor = isDarkTheme ? 'bg-[#131313]' : 'bg-white'
  const textColor = isDarkTheme ? 'text-white' : 'text-black'
  const borderColor = isDarkTheme ? 'border-[#303030]' : 'border-[#F2F3F7]'
  const headerBgColor = isDarkTheme ? 'bg-[#131313]' : 'bg-white'
  const headerTextColor = isDarkTheme ? 'text-[#868686]' : 'text-[#9293A0]'
  const rowHoverColor = isDarkTheme
    ? 'hover:bg-[#1A1A1A]'
    : 'hover:bg-[#F9FAFB]'

  // 筛选器状态
  const [filterType, setFilterType] = useState<
    'all' | 'current' | 'long' | 'short'
  >('all')
  const [filterDropdownVisible, setFilterDropdownVisible] = useState(false)

  // 创建过滤器下拉菜单的引用
  const filterDropdownRef = useRef<HTMLDivElement>(null)

  const [limitModalOpen, setLimitModalOpen] = useState(false)
  const [marketModalOpen, setMarketModalOpen] = useState(false)
  const [closeAllModalOpen, setCloseAllModalOpen] = useState(false)
  const [tpslModalOpen, setTpslModalOpen] = useState(false)
  const [loading, setLoading] = useState(false)

  // 排序状态
  const [sortField, setSortField] = useState<'positionValue' | 'updateTime'>(
    'positionValue',
  )
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [isHovering, setIsHovering] = useState(false)
  const [isTimeHovering, setIsTimeHovering] = useState(false)

  // 切换排序方式
  const toggleSort = (field: 'positionValue' | 'updateTime') => {
    if (sortField === field) {
      // 如果已经按照该字段排序，则切换排序方向
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      // 如果是新的排序字段，设置为降序
      setSortField(field)
      setSortOrder('desc')
    }
  }

  // 处理过滤器切换
  const handleFilterChange = (type: 'all' | 'current' | 'long' | 'short') => {
    setFilterType(type)
    setFilterDropdownVisible(false)
  }

  // 切换过滤器下拉菜单显示状态
  const toggleFilterDropdown = () => {
    setFilterDropdownVisible(!filterDropdownVisible)
  }

  // 添加点击外部关闭下拉菜单的功能
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        filterDropdownRef.current &&
        !filterDropdownRef.current.contains(event.target as Node) &&
        filterDropdownVisible
      ) {
        setFilterDropdownVisible(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [filterDropdownVisible])

  const [selectedPosition, setSelectedPosition] = useState<{
    coin: string
    price: string
    size: string
    isLong: boolean // 是否为多仓
  } | null>(null)

  const [tpslCoin, setTpslCoin] = useState<string | null>(null)

  // 跳转到当前委托标签页
  const goToCurrentOrderTab = () => {
    setActiveTab('current')
  }

  // 打开限价订单弹窗
  const openLimitModal = (position: any) => {
    setSelectedPosition(position)
    setLimitModalOpen(true)
  }

  // 关闭限价订单弹窗
  const closeLimitModal = () => {
    setLimitModalOpen(false)
    setSelectedPosition(null)
  }

  // 打开市价订单弹窗
  const openMarketModal = (position: any) => {
    setSelectedPosition(position)
    setMarketModalOpen(true)
  }

  // 关闭市价订单弹窗
  const closeMarketModal = () => {
    setMarketModalOpen(false)
    setSelectedPosition(null)
  }

  // 打开关闭全部订单弹窗
  const openCloseAllModal = () => {
    setCloseAllModalOpen(true)
  }

  // 关闭关闭全部订单弹窗
  const closeCloseAllModal = () => {
    setCloseAllModalOpen(false)
  }

  // 打开止盈止损弹窗
  const openTpslModal = (coin: string) => {
    setTpslCoin(coin)
    setTpslModalOpen(true)
  }

  // 关闭止盈止损弹窗
  const closeTpslModal = () => {
    setTpslModalOpen(false)
    setTpslCoin(null)
  }

  // 处理关闭全部订单
  const handleCloseAllOrders = async (useMarketPrice: boolean) => {
    if (loading) return

    setLoading(true)
    try {
      // 获取当前所有持仓
      const positions = perpsStore.order.positionOrders

      if (positions.length === 0) {
        openNotification(
          'position_notification',
          'success',
          i18n.t('no_position_to_close'),
        )
        setCloseAllModalOpen(false)
        return
      }

      if (useMarketPrice) {
        // 市价平仓所有持仓
        const orders = positions.map((position) => {
          const coinInfo = perpsStore.meta.getCoinInfo(position.position.coin)
          const assetId = coinInfo?.assetid || 0
          const isBuy = Number(position.position.szi) < 0 // 如果是空仓，则平仓为买入

          const rawSize = Math.abs(Number(position.position.szi))
          // 使用toFixed保留小数位数，然后去除末尾的0
          const orderSize = rawSize
            .toFixed(coinInfo?.szDecimals || 6)
            .replace(/\.?0+$/, '')

          // 获取盘口 midPrice
          const cbbo = perpsStore.book.bbo?.[position.position.coin]
          const impactPxs = coinInfo?.impactPxs || []
          const bid = cbbo?.bbo?.[0]?.px || impactPxs?.[0] || '0'
          const offer = cbbo?.bbo?.[1]?.px || impactPxs?.[1] || '0'
          const midPrice = isBuy ? offer : bid || '0'

          // 从perpsStore.init中获取滑点值
          const { slippage } = perpsStore.init

          // 计算滑点价格
          let rawSlippagePrice
          if (isBuy) {
            // 买入（空仓平仓）- 价格略高
            rawSlippagePrice = (
              Number(midPrice) *
              (1 + slippage / 100)
            ).toString()
          } else {
            // 卖出（多仓平仓）- 价格略低
            rawSlippagePrice = (
              Number(midPrice) *
              (1 - slippage / 100)
            ).toString()
          }

          // 使用hlFormatPrice格式化价格，确保精度与币种的szDecimals一致
          const formattedPrice = hlFormatPrice({
            price: rawSlippagePrice,
            szDecimals: coinInfo?.szDecimals,
            isSimple: true,
          })

          // 只去除小数点后面的0，保留整数部分的0
          // 如果有小数点，则去除末尾的0；如果没有小数点，则保持原样
          const slippagePrice = formattedPrice.includes('.')
            ? formattedPrice.replace(/(\.\d*?)0+$/, '$1').replace(/\.$/, '')
            : formattedPrice

          return {
            a: assetId,
            b: isBuy,
            s: filterZero(orderSize),
            p: filterZero(slippagePrice),
            r: true, // 市价单
            t: { limit: { tif: 'FrontendMarket' as const } },
          }
        })

        // 发送市价平仓请求
        const res = await EX_PLACE_ORDER({
          orders,
          grouping: 'na',
          roomId: query?.roomId || '',
        })

        if (res?.code !== 200) {
          openNotification(
            'position_notification',
            'error',
            res?.message || i18n.t('close_all_position_failed'),
          )
          return
        }
        openNotification(
          'position_notification',
          'success',
          i18n.t('market_order_submitted'),
        )
        // openNotification(
        //   'form',
        //   'success',
        //   <div className="flex flex-col">
        //     <div>市价单已成交:</div>
        //     <div>
        //       {res?.data?.totalSz &&
        //         `${res?.data?.totalSz} 已平仓于平均价格: $${hlFormatPrice({ price: res?.data?.avgPx })}`}
        //     </div>
        //   </div>,
        // )
      } else {
        // 限价平仓 - 使用盘口 midPrice 并应用滑点
        const orders = positions.map((position) => {
          const coinInfo = perpsStore.meta.getCoinInfo(position.position.coin)
          const assetId = coinInfo?.assetid || 0
          const isBuy = Number(position.position.szi) < 0 // 如果是空仓，则平仓为买入

          const rawSize = Math.abs(Number(position.position.szi))
          // 使用toFixed保留小数位数，然后去除末尾的0
          const orderSize = rawSize
            .toFixed(coinInfo?.szDecimals || 6)
            .replace(/\.?0+$/, '')

          // 获取盘口 midPrice
          const cbbo = perpsStore.book.bbo?.[position.position.coin]
          const impactPxs = coinInfo?.impactPxs || []
          const bid = cbbo?.bbo?.[0]?.px || impactPxs?.[0] || '0'
          const offer = cbbo?.bbo?.[1]?.px || impactPxs?.[1] || '0'
          const midPrice = isBuy ? offer : bid || '0'

          // 从perpsStore.init中获取滑点值
          // const { slippage } = perpsStore.init

          // 计算滑点价格 - 即使是限价单也应用滑点以提高成交概率
          let rawLimitPrice
          if (isBuy) {
            // 买入（空仓平仓）- 价格略高
            rawLimitPrice = (Number(midPrice) * 1).toString()
          } else {
            // 卖出（多仓平仓）- 价格略低
            rawLimitPrice = (Number(midPrice) * 1).toString()
          }

          // 使用hlFormatPrice格式化价格，确保精度与币种的szDecimals一致
          const limitPrice = hlFormatPrice({
            price: rawLimitPrice,
            szDecimals: coinInfo?.szDecimals,
            isSimple: true,
          })

          return {
            a: assetId,
            b: isBuy,
            s: filterZero(orderSize),
            p: filterZero(limitPrice),
            r: true, // 限价单
            t: { limit: { tif: 'Gtc' as const } },
          }
        })

        // 发送限价平仓请求
        const res = await EX_PLACE_ORDER({
          orders,
          grouping: 'na',
          roomId: query?.roomId || '',
        })

        if (res?.code !== 200) {
          openNotification(
            'position_notification',
            'error',
            res?.message || i18n.t('close_all_position_failed'),
          )
          return
        }

        openNotification(
          'position_notification',
          'success',
          i18n.t('limit_order_submitted'),
        )
      }

      // 重新获取持仓和委托订单数据
      const address = walletStore.arbitrumAddress
      if (address) {
        await perpsStore.order.getPositionOrders(address as any)
        await perpsStore.order.getPendingOrder(address as any)
      }

      setCloseAllModalOpen(false)
    } catch (error: any) {
      console.error('关闭全部持仓失败:', error)
      openNotification(
        'position_notification',
        'error',
        error?.message || i18n.t('close_all_position_failed'),
      )
    } finally {
      setLoading(false)
    }
  }

  // 过滤持仓数据
  const filteredPositions = perpsStore.order.positionOrders.filter(
    (position) => {
      // 获取当前活跃的币种 - 直接从 perpsStore.init.coin 获取
      const currentCoin = perpsStore.init.coin
      const positionSzi = Number(position.position.szi)

      switch (filterType) {
        case 'current':
          return position.position.coin === currentCoin
        case 'long':
          return positionSzi > 0
        case 'short':
          return positionSzi < 0
        case 'all':
        default:
          return true
      }
    },
  )

  // 获取历史订单中的最新时间戳
  const getLatestTimestamp = (coin: string) => {
    // 从历史成交订单中查找该币种的最新成交时间
    const historyOrder = perpsStore.order.historyOrders
      .filter((order) => order.coin === coin)
      .sort((a, b) => b.time - a.time)[0]

    // 从历史委托订单中查找该币种的最新状态更新时间
    const historicalOrder = perpsStore.order.historicalOrders
      .filter((order) => order.order.coin === coin)
      .sort((a, b) => b.statusTimestamp - a.statusTimestamp)[0]

    // 返回最新的时间戳，如果没有则返回0
    const historyTime = historyOrder?.time || 0
    const historicalTime = historicalOrder?.statusTimestamp || 0

    return Math.max(historyTime, historicalTime)
  }

  // 排序后的仓位数据
  const sortedPositions = [...filteredPositions].sort((a, b) => {
    if (sortField === 'positionValue') {
      const valueA = Number(a.position.positionValue)
      const valueB = Number(b.position.positionValue)
      return sortOrder === 'asc' ? valueA - valueB : valueB - valueA
    } else if (sortField === 'updateTime') {
      const timeA = getLatestTimestamp(a.position.coin)
      const timeB = getLatestTimestamp(b.position.coin)
      return sortOrder === 'asc' ? timeA - timeB : timeB - timeA
    }
    return 0
  })

  // 自定义滚动条样式
  const scrollbarStyle = `
    .custom-scrollbar::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      position: absolute;
      right: 0;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: transparent;
      margin-top: 30px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 4px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }
    .custom-scrollbar::-webkit-scrollbar-corner {
      background: transparent;
    }
    .position-table-container {
      scrollbar-width: thin;
      scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
    }
    .position-table-container {
      width: calc(100vw - 840px);
      min-width: 500px; /* 确保在小屏幕上有最小宽度 */
      max-width: 100%; /* 确保不会超出父容器 */
      overflow-x: auto;
      padding:0 10px;
    }
    @media screen and (max-width: 1200px) {
      .position-table-container {
        width: calc(100vw - 600px);
      }
    }
    @media screen and (max-width: 992px) {
      .position-table-container {
        width: 100vw;
        min-width: 360px;
        margin: 0 auto;
      }
      .no-data-row {
        display: none !important; /* 在移动端隐藏无数据提示行 */
      }
    }
    .position-table th,
    .position-table td {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  `

  return (
    <div className="relative flex w-full flex-col">
      {/* 添加通知组件 */}
      <NotificationComponent />

      {/* 添加滚动条样式 */}
      <style>{scrollbarStyle}</style>

      {/* 过滤器 - 只在非简化模式下显示 */}
      {!simplified && (
        <div className="absolute -top-8 right-0 mb-2 flex justify-end">
          <div className="relative" ref={filterDropdownRef}>
            <button
              className={`flex items-center rounded ${isDarkTheme ? 'bg-[#1A1A1A] text-white' : 'bg-[#F9FAFB] text-black'} px-3 py-1 text-[11px]`}
              onClick={toggleFilterDropdown}
            >
              {filterType === 'all' && i18n.t('all')}
              {filterType === 'current' && i18n.t('current')}
              {filterType === 'long' && i18n.t('long')}
              {filterType === 'short' && i18n.t('short')}
              <svg
                className={`ml-1 h-4 w-4 transform transition-transform ${
                  filterDropdownVisible ? 'rotate-180' : ''
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
            {filterDropdownVisible && (
              <div
                className={`absolute right-0 z-50 mt-1 w-20 rounded ${isDarkTheme ? 'bg-[#1A1A1A]' : 'bg-white'} py-1 shadow-lg`}
              >
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('all')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('all')}
                  </span>
                  {filterType === 'all' && <span className="ml-auto">✓</span>}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('current')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('current')}
                  </span>
                  {filterType === 'current' && (
                    <span className="ml-auto">✓</span>
                  )}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('long')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('long')}
                  </span>
                  {filterType === 'long' && <span className="ml-auto">✓</span>}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('short')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('short')}
                  </span>
                  {filterType === 'short' && <span className="ml-auto">✓</span>}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 表格容器 */}
      <div
        className={`position-table-container custom-scrollbar overflow-y-auto ${bgColor}`}
        style={{ height: '210px', position: 'relative' }}
      >
        <div className="min-w-[1040px]">
          {/* 表头 - 固定不动 */}
          <div className={`sticky top-0 z-10 w-full ${headerBgColor}`}>
            <table className="w-full table-fixed border-collapse">
              <thead>
                <tr
                  className={`border-b ${borderColor} text-xs ${headerTextColor}`}
                >
                  <th
                    className="w-[8%] cursor-pointer px-1 py-1 text-left text-[11px]"
                    onClick={() => toggleSort('updateTime')}
                    onMouseEnter={() => setIsTimeHovering(true)}
                    onMouseLeave={() => setIsTimeHovering(false)}
                  >
                    {i18n.t('coin')}
                  </th>
                  <th className="w-[8%] px-1 py-1 text-left text-[11px]">
                    {i18n.t('quantity')}
                  </th>
                  <th
                    className="w-[10%] cursor-pointer px-1 py-1 text-left text-[11px]"
                    onClick={() => toggleSort('positionValue')}
                    onMouseEnter={() => setIsHovering(true)}
                    onMouseLeave={() => setIsHovering(false)}
                  >
                    {i18n.t('position_value')}
                    {sortField === 'positionValue' || isHovering
                      ? sortOrder === 'desc'
                        ? '↓'
                        : '↑'
                      : ''}
                  </th>
                  <th className="w-[9%] px-1 py-1 text-left text-[11px]">
                    {i18n.t('entry_price')}
                  </th>
                  <th className="w-[7%] px-1 py-1 text-left text-[11px]">
                    {i18n.t('mark_price')}
                  </th>
                  <th className="w-[9%] px-1 py-1 text-left text-[11px]">
                    {i18n.t('profit_loss_roe')}
                  </th>
                  <th className="w-[7%] px-1 py-1 text-left text-[11px]">
                    {i18n.t('liquidation_price')}
                  </th>
                  <th className="w-[11%] px-1 py-1 text-left text-[11px]">
                    {i18n.t('margin_used')}
                  </th>
                  <th className="w-[6%] px-1 py-1 text-left text-[11px]">
                    {i18n.t('funding_fee')}
                  </th>
                  <th className="w-[9%] px-1 py-1 text-left text-[11px]">
                    <div className="flex items-center">
                      <span
                        className={`ml-2 cursor-pointer ${filteredPositions.length === 0 ? 'text-[#868686]' : isDarkTheme ? 'text-[#FFB800]' : '!text-[#FFB800]'} ${filteredPositions.length === 0 ? 'cursor-not-allowed' : ''}`}
                        onClick={() => {
                          if (filteredPositions.length > 0) {
                            openCloseAllModal()
                          }
                        }}
                      >
                        {i18n.t('close_all_position')}
                      </span>
                    </div>
                  </th>
                  <th className="w-[14%] px-1 py-1 text-left text-[11px]">
                    TP/SL
                  </th>
                </tr>
              </thead>
            </table>
          </div>

          {/* 表格内容 */}
          <div className="w-full">
            <table className="w-full table-fixed border-collapse">
              <tbody>
                {sortedPositions.length > 0 ? (
                  sortedPositions.map((position, index) => (
                    <tr
                      key={index}
                      className={`border-b ${borderColor} text-xs ${rowHoverColor}`}
                    >
                      <td className="w-[8%] px-1 py-1">
                        <div
                          className="flex cursor-pointer items-center"
                          onClick={() => {
                            if (position.position?.coin) {
                              perpsStore.init.setCoin(position.position.coin)
                            }
                          }}
                        >
                          <span className="text-[11px] font-bold leading-normal">
                            <ColoringText amount={position.position.szi}>
                              {position.position.coin}{' '}
                              {position.position.leverage.value}x
                            </ColoringText>
                          </span>
                        </div>
                      </td>
                      <td className="w-[8%] px-1 py-1">
                        <ColoringText amount={position.position.szi}>
                          <span className="text-[11px]">
                            {hlFormatAnyCoin(
                              Math.abs(
                                Number(position.position.szi),
                              ).toString(),
                              perpsStore.meta.getCoinInfo(
                                position.position.coin,
                              )?.szDecimals,
                            )}
                            {position.position.coin}
                          </span>
                        </ColoringText>
                      </td>
                      <td className="w-[10%] px-1 py-1 text-[#5F606D]">
                        $
                        {$ten.toFixed(
                          position.position.positionValue,
                          2,
                          false,
                        )}
                      </td>
                      <td className="w-[9%] px-1 py-1 text-[#5F606D]">
                        {hlFormatPrice({
                          price: position.position.entryPx,
                          szDecimals: perpsStore.meta.getCoinInfo(
                            position.position.coin,
                          )?.szDecimals,
                          isSimple: true,
                        })}
                      </td>
                      <td className="w-[7%] px-1 py-1 text-[#5F606D]">
                        {hlFormatPrice({
                          price:
                            position.ctx?.markPx ||
                            perpsStore.meta.activeMeta?.markPx ||
                            '0',
                          szDecimals: perpsStore.meta.getCoinInfo(
                            position.position.coin,
                          )?.szDecimals,
                          isSimple: true,
                        })}
                      </td>
                      <td className="w-[9%] px-1 py-1">
                        <ColoringText amount={position.position.unrealizedPnl}>
                          {parseFloat(position.position.unrealizedPnl) >= 0
                            ? '$'
                            : '-$'}
                          {Math.abs(
                            parseFloat(position.position.unrealizedPnl),
                          ).toFixed(2)}
                          (
                          {(
                            parseFloat(position.position.returnOnEquity) * 100
                          ).toFixed(2)}
                          %)
                        </ColoringText>
                      </td>
                      <td className="w-[7%] px-1 py-1 text-[#5F606D]">
                        {position.position.liquidationPx
                          ? hlFormatPrice({
                              price: position.position.liquidationPx,
                              szDecimals: perpsStore.meta.getCoinInfo(
                                position.position.coin,
                              )?.szDecimals,
                              isSimple: true,
                            })
                          : 'N/A'}
                      </td>
                      <td className="w-[11%] px-1 py-1 text-[#5F606D]">
                        ${$ten.toFixed(position.position.marginUsed, 2, true)}(
                        {position.position.leverage.type})
                      </td>
                      <td className="w-[6%] px-1 py-1">
                        {position.position.cumFunding?.allTime ? (
                          <ColoringText
                            amount={(
                              parseFloat(position.position.cumFunding.allTime) *
                              -1
                            ).toString()}
                          >
                            {parseFloat(position.position.cumFunding.allTime) >=
                            0
                              ? '-$'
                              : '$'}
                            {$ten.toFixed(
                              Math.abs(
                                parseFloat(
                                  position.position.cumFunding.allTime,
                                ),
                              ),
                              2,
                              false,
                            )}
                          </ColoringText>
                        ) : (
                          'N/A'
                        )}
                      </td>
                      <td className="w-[9%] px-1 py-1">
                        <div className="flex items-center gap-2">
                          <span
                            className={`cursor-pointer ${isDarkTheme ? 'text-[#FFB800]' : 'text-[#FFB800]'} `}
                            onClick={() => {
                              // 传递完整的持仓信息
                              const positionData = position.position
                              openLimitModal(positionData)
                            }}
                          >
                            {i18n.t('limit')}
                          </span>
                          <span
                            className={`cursor-pointer ${isDarkTheme ? 'text-[#FFB800]' : 'text-[#FFB800]'}`}
                            onClick={() => {
                              // 传递完整的持仓信息
                              const positionData = position.position
                              openMarketModal(positionData)
                            }}
                          >
                            {i18n.t('market')}
                          </span>
                        </div>
                      </td>
                      <td className="w-[14%] px-1 py-1">
                        <div className="flex items-center">
                          <HasTriggerOrder
                            position={position}
                            onGoRoute={goToCurrentOrderTab}
                          />
                          <button
                            className="ml-2 flex items-center justify-center"
                            onClick={() => {
                              // 传递完整的持仓信息
                              const positionData = position.position
                              openTpslModal(positionData?.coin)
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="11"
                              height="11"
                              viewBox="0 0 11 11"
                              fill="none"
                            >
                              <path
                                d="M4.36924 5.11034C4.21285 5.26694 4.125 5.47923 4.125 5.70057V6.875H5.30662C5.52816 6.875 5.74063 6.78695 5.89725 6.63024L9.38054 3.14495C9.70649 2.81882 9.70649 2.29019 9.38054 1.96405L9.03643 1.61976C8.71019 1.29333 8.18107 1.29343 7.85495 1.61998L4.36924 5.11034Z"
                                stroke={isDarkTheme ? '#F9FAFB' : '#000000'}
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M9.625 5.5C9.625 7.44454 9.625 8.41682 9.02091 9.02091C8.41682 9.625 7.44454 9.625 5.5 9.625C3.55546 9.625 2.58318 9.625 1.97909 9.02091C1.375 8.41682 1.375 7.44454 1.375 5.5C1.375 3.55546 1.375 2.58318 1.97909 1.97909C2.58318 1.375 3.55546 1.375 5.5 1.375"
                                stroke={isDarkTheme ? '#F9FAFB' : '#000000'}
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr className={`text-sm ${rowHoverColor} no-data-row`}>
                    <td
                      colSpan={12}
                      className={`px-4 py-8 text-center ${headerTextColor}`}
                    >
                      {i18n.t('no_position_data')}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* 限价订单弹窗和市价订单弹窗 - 不受简化模式影响 */}
      {selectedPosition && (
        <>
          <LimitOrderModal
            open={limitModalOpen}
            onClose={closeLimitModal}
            position={selectedPosition}
          />
          <MarketOrderModal
            open={marketModalOpen}
            onClose={closeMarketModal}
            position={selectedPosition}
          />
        </>
      )}

      {/* 止盈止损弹窗 - 不受简化模式影响 */}
      {tpslCoin && (
        <TPSLOrderModal
          open={tpslModalOpen}
          onClose={closeTpslModal}
          modifyCoin={tpslCoin}
        />
      )}

      {/* 关闭全部订单弹窗 - 不受简化模式影响 */}
      <CloseAllOrdersModal
        open={closeAllModalOpen}
        onClose={closeCloseAllModal}
        onConfirm={handleCloseAllOrders}
      />
    </div>
  )
})

function HasTriggerOrder({
  position,
  onGoRoute,
}: {
  position: IPositionOrder
  onGoRoute: () => void
}) {
  const { hlFormatPrice } = useTools()
  const pendingOrders = perpsStore.order.pendingOrders
  const { coin } = position.position || {}
  const { szDecimals } = position.ctx || {}

  const profitPrice = useMemo(() => {
    if (!pendingOrders?.length) {
      return null
    }

    // 查找与当前币种匹配的全仓止盈订单
    const profitOrder = pendingOrders.find(
      (item) =>
        item.coin === coin &&
        item.isPositionTpsl &&
        item.orderType === 'Take Profit Market',
    )

    if (profitOrder) {
      return hlFormatPrice({
        price: profitOrder.triggerPx,
        szDecimals,
      })
    }

    return '--'
  }, [pendingOrders])

  const lossPrice = useMemo(() => {
    if (!pendingOrders?.length) {
      return null
    }

    const lossOrder = pendingOrders.find(
      (item) =>
        item.coin === coin &&
        item.isPositionTpsl &&
        item.orderType === 'Stop Market',
    )

    if (lossOrder) {
      return hlFormatPrice({
        price: lossOrder.triggerPx,
        szDecimals,
      })
    }

    return '--'
  }, [pendingOrders])

  // 检查是否有带触发条件的委托单
  const hasTriggerOrder = useMemo(() => {
    return pendingOrders.some(
      (order) =>
        order.coin === coin &&
        order.triggerCondition &&
        order.triggerCondition !== 'N/A',
    )
  }, [pendingOrders])

  if (hasTriggerOrder && !profitPrice && !lossPrice) {
    return (
      <div className="flex items-center">
        <a
          href="#"
          className="text-xs text-[#00D09C] hover:underline"
          onClick={(e) => {
            e.preventDefault()
            onGoRoute()
          }}
        >
          {i18n.t('view_order')}
        </a>
      </div>
    )
  }

  return (
    <div className="flex items-center">
      <span className="text-xs">
        {profitPrice || '--'}/{lossPrice || '--'}
      </span>
    </div>
  )
}
