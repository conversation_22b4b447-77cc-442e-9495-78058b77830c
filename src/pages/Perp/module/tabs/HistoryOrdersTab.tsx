import { observer } from 'mobx-react-lite'
import { perpsStore } from '@/store'
import { useState, useEffect, useRef } from 'react'
import { useTabContext } from '../context/TabContext'
import { useTools } from '../../hooks/useTools'
import { $ten } from '@/utils'
import clsx from 'clsx'
import i18n from '@/i18n'
import { TabComponentProps } from './TabComponentProps'
import { isMobile } from 'react-device-detect'

export const HistoryOrdersTab = observer((props: TabComponentProps = {}) => {
  // 主题和简化模式
  const { theme = 'dark', simplified = false } = props

  // 根据主题确定样式
  const isDarkTheme = theme === 'dark'
  const bgColor = isDarkTheme ? 'bg-[#131313]' : 'bg-white'
  const textColor = isDarkTheme ? 'text-white' : 'text-black'
  const borderColor = isDarkTheme ? 'border-[#303030]' : 'border-[#F2F3F7]'
  const headerBgColor = isDarkTheme ? 'bg-[#131313]' : 'bg-white'
  const headerTextColor = isDarkTheme ? 'text-[#868686]' : 'text-[#9293A0]'
  const rowHoverColor = isDarkTheme
    ? 'hover:bg-[#1A1A1A]'
    : 'hover:bg-[#F9FAFB]'

  // 获取上下文
  const { setActiveTab } = useTabContext()
  // 获取工具函数
  const { hlFormatPrice, hlFormatAnyCoin, filterZero } = useTools()

  // 筛选器状态
  const [filterType, setFilterType] = useState<
    'all' | 'current' | 'long' | 'short'
  >('all')
  const [filterDropdownVisible, setFilterDropdownVisible] = useState(false)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc') // 默认倒序
  const filterDropdownRef = useRef<HTMLDivElement>(null)

  // 处理过滤器切换
  const handleFilterChange = (type: 'all' | 'current' | 'long' | 'short') => {
    setFilterType(type)
    setFilterDropdownVisible(false)
  }

  // 切换过滤器下拉菜单显示状态
  const toggleFilterDropdown = () => {
    setFilterDropdownVisible(!filterDropdownVisible)
  }

  // 添加点击外部关闭下拉菜单的功能
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        filterDropdownRef.current &&
        !filterDropdownRef.current.contains(event.target as Node) &&
        filterDropdownVisible
      ) {
        setFilterDropdownVisible(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [filterDropdownVisible])

  // 过滤历史委托数据（恢复为真实接口数据）
  const filteredHistoricalOrders = perpsStore.order.historicalOrders.filter(
    (order) => {
      // 获取当前活跃的币种
      const currentCoin = perpsStore.init.coin
      const isBuy = order.order.side === 'B'

      switch (filterType) {
        case 'current':
          return order.order.coin === currentCoin
        case 'long':
          return isBuy
        case 'short':
          return !isBuy
        case 'all':
        default:
          return true
      }
    },
  )

  // 排序逻辑
  const sortedHistoricalOrders = [...filteredHistoricalOrders].sort((a, b) => {
    if (sortOrder === 'asc') {
      return a.statusTimestamp - b.statusTimestamp
    } else {
      return b.statusTimestamp - a.statusTimestamp
    }
  })

  // 表头点击切换排序
  const onTimeHeaderClick = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
  }

  // 自定义滚动条样式
  const scrollbarStyle = `
    .custom-scrollbar::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      position: absolute;
      right: 0;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: transparent;
      margin-top: 30px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 4px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }
    .custom-scrollbar::-webkit-scrollbar-corner {
      background: transparent;
    }
    .position-table-container {
      scrollbar-width: thin;
      scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
    }
    .position-table-container {
      width: calc(100vw - 840px);
      min-width: 500px; /* 确保在小屏幕上有最小宽度 */
      max-width: 100%; /* 确保不会超出父容器 */
      overflow-x: auto;
      -webkit-overflow-scrolling: touch; /* 增强移动端滚动体验 */
    }
    .history-table th,
    .history-table td {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    @media screen and (max-width: 1200px) {
      .position-table-container {
        width: calc(100vw - 600px);
      }
    }
    @media screen and (max-width: 992px) {
      .position-table-container {
        width: calc(100vw - 20px);
        min-width: 360px;
        margin: 0 auto;
      }
      .no-data-row {
        display: none !important; /* 在移动端隐藏无数据提示行 */
      }
    }
    @media screen and (max-width: 768px) {
      .position-table-container {
        width: calc(100vw - 20px);
        min-width: 320px;
      }
    }
  `

  return (
    <div className="relative flex w-full flex-col">
      {/* 添加滚动条样式 */}
      <style>{scrollbarStyle}</style>

      {/* 过滤器 - 只在非简化模式下显示 */}
      {!simplified && (
        <div className="absolute -top-8 right-0 mb-2 flex justify-end">
          <div className="relative" ref={filterDropdownRef}>
            <button
              className={`flex items-center rounded ${isDarkTheme ? 'bg-[#1A1A1A] text-white' : 'bg-[#F9FAFB] text-black'} px-3 py-1 text-[11px]`}
              onClick={toggleFilterDropdown}
            >
              {filterType === 'all' && i18n.t('all')}
              {filterType === 'current' && i18n.t('current')}
              {filterType === 'long' && i18n.t('long')}
              {filterType === 'short' && i18n.t('short')}
              <svg
                className={`ml-1 h-4 w-4 transform transition-transform ${
                  filterDropdownVisible ? 'rotate-180' : ''
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
            {filterDropdownVisible && (
              <div
                className={`absolute right-0 z-50 mt-1 w-20 rounded ${isDarkTheme ? 'bg-[#1A1A1A]' : 'bg-white'} py-1 shadow-lg`}
              >
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('all')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('all')}
                  </span>
                  {filterType === 'all' && <span className="ml-auto">✓</span>}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('current')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('current')}
                  </span>
                  {filterType === 'current' && (
                    <span className="ml-auto">✓</span>
                  )}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('long')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('long')}
                  </span>
                  {filterType === 'long' && <span className="ml-auto">✓</span>}
                </div>
                <div
                  className={`flex cursor-pointer items-center px-4 py-2 text-[11px] ${isDarkTheme ? 'text-white hover:bg-[#303030]' : 'text-black hover:bg-[#F9FAFB]'}`}
                  onClick={() => handleFilterChange('short')}
                >
                  <span className="inline-block min-w-[40px]">
                    {i18n.t('short')}
                  </span>
                  {filterType === 'short' && <span className="ml-auto">✓</span>}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 表格容器 */}
      <div
        className={`position-table-container custom-scrollbar overflow-y-auto ${bgColor}`}
        style={{ height: '210px', position: 'relative' }}
      >
        <div className="min-w-[1140px]">
          {/* 使用单一表格结构确保在移动端表头和内容同步滚动 */}
          <table className="history-table w-full table-fixed border-collapse">
            <thead
              className={`${!isMobile ? 'sticky top-0 z-10' : ''} ${headerBgColor}`}
            >
              <tr
                className={`border-b ${borderColor} text-xs ${headerTextColor}`}
              >
                <th
                  className="w-[10%] cursor-pointer select-none px-1 py-1 text-left text-[11px]"
                  onClick={onTimeHeaderClick}
                >
                  {i18n.t('time')} {sortOrder === 'desc' ? '↓' : '↑'}
                </th>
                <th className="w-[9%] px-1 py-1 text-left text-[11px]">
                  {i18n.t('type')}
                </th>
                <th className="w-[5%] px-1 py-1 text-left text-[11px]">
                  {i18n.t('coin')}
                </th>
                <th className="w-[6%] px-1 py-1 text-left text-[11px]">
                  {i18n.t('direction')}
                </th>
                <th className="w-[5%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('quantity')}
                </th>
                <th className="w-[5%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('filled_size')}
                </th>
                <th className="w-[6%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('order_value')}
                </th>
                <th className="w-[7%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('price')}
                </th>
                <th className="w-[7%] px-1 py-1 text-center text-[11px]">
                  {i18n.t('reduce_only')}
                </th>
                <th className="w-[11%] px-1 py-1 text-center text-[11px]">
                  {i18n.t('trigger_condition')}
                </th>
                <th className="w-[3%] px-1 py-1 text-center text-[11px]">
                  TP/SL
                </th>
                <th className="w-[9%] px-0 py-1 text-center text-[11px]">
                  {i18n.t('status')}
                </th>
                <th className="w-[8%] px-1 py-1 text-right text-[11px]">
                  {i18n.t('order_id')}
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedHistoricalOrders.length > 0 ? (
                sortedHistoricalOrders.map((order, index) => {
                  // 格式化日期
                  const orderDate = new Date(order.statusTimestamp)
                  const formattedDate = `${orderDate.getFullYear()}/${String(orderDate.getMonth() + 1).padStart(2, '0')}/${String(orderDate.getDate()).padStart(2, '0')} ${String(orderDate.getHours()).padStart(2, '0')}:${String(orderDate.getMinutes()).padStart(2, '0')}:${String(orderDate.getSeconds()).padStart(2, '0')}`
                  // 计算订单价值
                  const orderValue =
                    parseFloat(order.order.limitPx) * parseFloat(order.order.sz)
                  // 状态文本
                  let statusText = ''
                  if (String(order.status).toLowerCase() === 'filled') {
                    statusText = i18n.t('order_type_filled')
                  } else if (
                    String(order.status).toLowerCase() === 'canceled' ||
                    String(order.status).toLowerCase() === 'reduceonlycanceled'
                  ) {
                    statusText = i18n.t('order_type_canceled')
                  } else if (String(order.status).toLowerCase() === 'open') {
                    statusText = i18n.t('order_type_open')
                  } else if (
                    String(order.status).toLowerCase() === 'rejected'
                  ) {
                    statusText = i18n.t('order_type_rejected')
                  } else {
                    statusText = String(order.status)
                  }
                  // 方向文本
                  let directionText = ''
                  if (order.order.reduceOnly) {
                    directionText =
                      order.order.side === 'B' ? 'Close Short' : 'Close Long'
                  } else {
                    directionText = order.order.side === 'B' ? 'Long' : 'Short'
                  }
                  // 只减仓
                  const reduceOnlyText = order.order.reduceOnly
                    ? i18n.t('yes')
                    : i18n.t('no')
                  // 触发条件
                  let triggerCondition = order.order.triggerCondition || 'N/A'
                  if (order.order.orderType === 'Limit')
                    triggerCondition = 'N/A'
                  // TP/SL
                  const tpSlText = '--'
                  // 订单价格/价格
                  const isMarket =
                    order.order.orderType &&
                    String(order.order.orderType)
                      .toLowerCase()
                      .includes('market')
                  const avgPx = isMarket ? 'Market' : order.order.limitPx
                  // 数量
                  const qty = Math.abs(parseFloat(order.order.sz))
                  // 成交大小
                  let filledQty = '--'
                  if (String(order.status).toLowerCase() === 'filled') {
                    const absQty = Math.abs(
                      parseFloat(order.order.origSz) ||
                        parseFloat(order.order.sz),
                    )
                    filledQty = filterZero(
                      hlFormatAnyCoin(
                        absQty.toString(),
                        perpsStore.meta.getCoinInfo(order.order.coin)
                          ?.szDecimals,
                      ),
                    )
                  } else if (
                    String(order.status).toLowerCase() === 'open' &&
                    parseFloat(order.order.sz) === 0 &&
                    parseFloat(order.order.origSz) > 0
                  ) {
                    const absQty = Math.abs(parseFloat(order.order.origSz))
                    filledQty = filterZero(
                      hlFormatAnyCoin(
                        absQty.toString(),
                        perpsStore.meta.getCoinInfo(order.order.coin)
                          ?.szDecimals,
                      ),
                    )
                  }
                  return (
                    <tr
                      key={order.order.oid + '-' + index}
                      className={`border-b ${borderColor} text-sm ${rowHoverColor}`}
                    >
                      <td className="w-[10%] px-1 py-1 text-left text-[11px] text-[#5F606D]">
                        {formattedDate}
                      </td>
                      <td className="w-[9%] px-1 py-1 text-left text-[11px]">
                        {order.order.orderType === 'Limit'
                          ? 'Limit'
                          : order.order.orderType === 'Market'
                            ? 'Market'
                            : order.order.orderType === 'Stop Market'
                              ? 'Stop Market'
                              : order.order.orderType === 'Stop Limit'
                                ? 'Stop Limit'
                                : order.order.orderType || ''}
                      </td>
                      <td className="w-[5%] px-1 py-1 text-left text-[11px]">
                        <span
                          className={clsx(
                            'cursor-pointer',
                            order.order.side === 'B'
                              ? 'text-[#00D09C]'
                              : 'text-[#FF5C5C]',
                          )}
                          onClick={() => {
                            if (order?.order?.coin) {
                              perpsStore.init.setCoin(order.order.coin)
                            }
                          }}
                        >
                          {order.order.coin}
                        </span>
                      </td>
                      <td className="w-[6%] px-1 py-1 text-left text-[11px]">
                        <span
                          className={
                            order.order.side === 'B'
                              ? 'text-[#00D09C]'
                              : 'text-[#FF5C5C]'
                          }
                        >
                          {directionText}
                        </span>
                      </td>
                      <td className="w-[5%] px-1 py-1 text-right text-[11px]">
                        {qty === 0
                          ? '--'
                          : hlFormatAnyCoin(
                              qty.toString(),
                              perpsStore.meta.getCoinInfo(order.order.coin)
                                ?.szDecimals,
                            )}
                      </td>
                      <td className="w-[5%] px-1 py-1 text-right text-[11px]">
                        {filledQty}
                      </td>
                      <td className="w-[6%] px-1 py-1 text-right text-[11px]">
                        {isMarket || !orderValue
                          ? '--'
                          : `$${$ten.toFixed(orderValue.toString(), 2, true)}`}
                      </td>
                      <td className="w-[7%] px-1 py-1 text-right text-[11px]">
                        <span>
                          {isMarket
                            ? 'Market'
                            : filterZero(
                                hlFormatPrice({
                                  price: avgPx,
                                  szDecimals: perpsStore.meta.getCoinInfo(
                                    order.order.coin,
                                  )?.szDecimals,
                                  isSimple: true,
                                }),
                              )}
                        </span>
                      </td>
                      <td className="w-[7%] px-1 py-1 text-center text-[11px]">
                        {reduceOnlyText}
                      </td>
                      <td className="w-[11%] px-1 py-1 text-center text-[11px]">
                        {triggerCondition}
                      </td>
                      <td className="w-[3%] px-1 py-1 text-center text-[11px]">
                        {tpSlText}
                      </td>
                      <td className="w-[9%] px-0 py-1 text-center text-[11px]">
                        {statusText}
                      </td>
                      <td className="w-[8%] px-1 py-1 text-right text-[11px]">
                        {order.order.oid}
                      </td>
                    </tr>
                  )
                })
              ) : (
                <tr className={`text-sm ${rowHoverColor} no-data-row`}>
                  <td
                    colSpan={13}
                    className={`px-1 py-8 text-center ${headerTextColor}`}
                  >
                    {i18n.t('no_history_order_data')}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
})
