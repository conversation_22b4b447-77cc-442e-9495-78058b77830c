import { Widget } from '@/components/ChartKline'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { perpsStore } from '@/store'
import { observer } from 'mobx-react-lite'
import { LoadingOutlined } from '@ant-design/icons'

export const Mod<PERSON>line<PERSON>hart = observer(() => {
  const { calculateCoinPrice } = useTools()

  if (!perpsStore.meta.activeMeta?.name) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoadingOutlined className="text-5xl" spin />
      </div>
    )
  }

  return (
    <Widget
      symbol={perpsStore.init.coin}
      interval={perpsStore.init.interval}
      getKline={() => perpsStore.kline.lastData}
      getPrecision={() => {
        const { szDecimals, markPx } = perpsStore.meta.activeMeta || {}
        return calculateCoinPrice(markPx, szDecimals)
      }}
      setInterval={(interval) => {
        perpsStore.init.setInterval(interval)
      }}
      fetchKline={(coin, interval, startTime, endTime) => {
        return perpsStore.kline.getKlineHistory(
          coin,
          interval,
          startTime,
          endTime,
        )
      }}
      subscribeKline={(coin, interval) => {
        perpsStore.kline.subscribeCandleData(coin, interval)
      }}
      unsubscribeKline={() => {
        perpsStore.kline.unsubscribeCandleData()
      }}
    />
  )
})
