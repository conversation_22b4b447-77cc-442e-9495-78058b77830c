import { GET_ARBITRUM_ADDRESS } from '@/api/interface/GET_ARBITRUM_ADDRESS'
import { PerpContext } from '@/pages/Perp/module/contPerp/context'
import { GuideComponent } from '@/pages/Perp/module/contPerp/GuideComponent'
import { usePerpNotification } from '@/pages/Perp/module/contPerp/useNotification'
import { TopupModal } from '@/pages/Perp/module/modals/TopupModal'
import { TPerpNumber, TSideView } from '@/pages/Perp/module/types'
import { perpsStore, walletStore } from '@/store'
import { observer } from 'mobx-react-lite'
import { useEffect, useState } from 'react'
import { Hex } from 'viem'

export const ModPerpProvider = observer(
  ({ children }: { children: React.ReactNode }) => {
    const { openNotification, NotificationComponent } = usePerpNotification()
    // 写入限价
    const [limitPrice, setLimitPrice] = useState<TPerpNumber>(null)
    // 钱包地址
    const [walletAddress, setWalletAddress] = useState<Hex | undefined>(
      undefined,
    )
    // 是否显示充值弹窗
    const [showTopUpModal, setShowTopUpModal] = useState(false)
    // 移动端指定订单方向
    const [sideView, setSideView] = useState<TSideView>({
      side: 'B',
      round: Math.random(),
    })

    useEffect(() => {
      setLimitPrice(null)
    }, [perpsStore.init.coin])

    useEffect(() => {
      const coin = perpsStore.init.coin
      if (walletAddress) {
        perpsStore.init.subscribeWebData2(walletAddress)
        perpsStore.meta.subscribeAssetCtxs(coin, walletAddress)
        perpsStore.fee.getUserFees(walletAddress)
      }

      perpsStore.book.subscribeBbo(coin)
      perpsStore.setting.getMinDepositAmount()

      return () => {
        perpsStore.book.unsubscribeBbo()
        perpsStore.meta.unsubscribeAssetCtxs()
      }
    }, [walletAddress, perpsStore.init.coin])

    // 获取Arbitrum地址，优先从 walletStore 中获取
    useEffect(() => {
      const fetchArbitrumAddress = async () => {
        try {
          // 先从 walletStore 中获取地址
          const cachedAddress = walletStore.arbitrumAddress

          if (cachedAddress) {
            setWalletAddress(cachedAddress as Hex)
            return
          }

          // 如果 walletStore 中没有地址，则从 API 中获取
          const response = await GET_ARBITRUM_ADDRESS()

          if (response.code === 200 && response.data) {
            setWalletAddress(response.data as Hex)
            // 将地址存储在 walletStore 中
            walletStore.setArbitrumAddress(response.data)
          }
        } catch (error) {
          console.error('PerpPage - 获取Arbitrum地址失败:', error)
        }
      }

      fetchArbitrumAddress()
    }, [])

    return (
      <PerpContext.Provider
        value={{
          openNotification,
          limitPrice,
          setLimitPrice,
          walletAddress,
          showTopUpModal,
          setShowTopUpModal,
          sideView,
          setSideView,
        }}
      >
        {children}
        <NotificationComponent />
        <TopupModal />
        <GuideComponent />
      </PerpContext.Provider>
    )
  },
)
