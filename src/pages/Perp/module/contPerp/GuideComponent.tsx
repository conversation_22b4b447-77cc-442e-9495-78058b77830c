import { ButtonBlack } from '@/components/Customize/Button'
import { useMedia } from '@/hooks/useMedia'
import i18n from '@/i18n'
import { SvgIconClose } from '@/imgs/icons'
import { guideStore } from '@/store'
import clsx from 'clsx'
import { observer } from 'mobx-react-lite'
import { useEffect, useMemo, useState } from 'react'
import Joyride, {
  ACTIONS,
  CallBackProps,
  LIFECYCLE,
  STATUS,
  Step,
  TooltipRenderProps,
} from 'react-joyride'

// 自定义引导提示框组件
const CustomTooltip = ({
  index,
  step,
  closeProps,
  primaryProps,
  skipProps,
  tooltipProps,
  size,
}: TooltipRenderProps) => {
  const { content, target } = step as Step & { title?: string }
  const isLast = index === size - 1
  const isMobileTop = target === '.wallet-area-mobile'

  return (
    <div
      {...tooltipProps}
      className={clsx(
        '-mb-[1px] overflow-hidden rounded bg-white p-2.5 pt-1.5 shadow-xl',
        isMobileTop && 'ml-2',
      )}
    >
      {/* 头部区域 */}
      <div className="flex items-center justify-end">
        <button
          {...closeProps}
          className="rounded-full p-1 text-black/80 transition-colors hover:bg-white/10 hover:text-black/40"
        >
          <SvgIconClose className="size-5" />
        </button>
      </div>
      {/* 内容区域 */}
      <div>
        <div className="leading-relaxed text-gray-700">{content}</div>
        <div className="mt-5">
          <div className="flex items-center justify-between">
            {/* 进度条 */}
            <div className="justify-start text-right text-xs font-normal leading-3 text-theme-primary">
              {index + 1} / <span className="text-theme-secondary">{size}</span>
            </div>
            {/* 右侧主要操作 */}
            <div className="flex items-center space-x-3">
              {!isLast && (
                <button
                  {...skipProps}
                  className="justify-start text-sm font-semibold text-theme-primary"
                >
                  {i18n.t('skip')}
                </button>
              )}
              <ButtonBlack {...primaryProps}>
                {isLast ? i18n.t('done') : i18n.t('guide_next')}
              </ButtonBlack>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export const GuideComponent = observer(() => {
  const [stepIndex, setStepIndex] = useState(0)
  const [isReady, setIsReady] = useState(false)
  const { isMd } = useMedia()

  // 延迟加载引导
  useEffect(() => {
    if (guideStore.isPerpGuide) {
      const timer = setTimeout(() => {
        setIsReady(true)
      }, 1000) // 延迟1秒，可以根据需要调整

      return () => clearTimeout(timer)
    }
  }, [guideStore.isPerpGuide])

  function onExit() {
    guideStore.setPerpMark(true)
  }

  // 引导步骤配置
  const steps: (Step & { title?: string })[] = useMemo(() => {
    return [
      {
        target: '.chart-area',
        content: (
          <div className="-mt-4 min-w-52 max-w-96 justify-start pr-7 text-sm font-semibold text-theme-primary">
            {i18n.t('guide_select_contract_pair')}
          </div>
        ),
        placement: 'bottom',
        disableBeacon: true,
        floaterProps: {
          styles: {
            arrow: {
              length: 6, // 箭头长度
              spread: 16, // 箭头宽度
            },
          },
        },
      },
      {
        target: isMd ? '.wallet-area-mobile' : '.wallet-area',
        content: (
          <div className="-mt-4 min-w-52 max-w-96 justify-start pr-7 text-sm font-semibold text-theme-primary">
            {i18n.t('guide_top_up')}
          </div>
        ),
        placement: isMd ? 'top' : 'left',
        disableBeacon: true,
        isFixed: true,
        floaterProps: {
          styles: {
            arrow: {
              length: 6, // 箭头长度
              spread: 16, // 箭头宽度
            },
          },
        },
      },
      {
        target: '.order-area',
        content: (
          <div className="-mt-4 min-w-52 max-w-96 justify-start pr-7 text-sm font-semibold text-theme-primary">
            {i18n.t('guide_view_position')}
          </div>
        ),
        placement: 'top',
        disableBeacon: true,
        floaterProps: {
          styles: {
            arrow: {
              length: 6, // 箭头长度
              spread: 16, // 箭头宽度
            },
          },
        },
      },
    ]
  }, [isMd])

  // 处理引导回调
  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status, type, action, index, lifecycle } = data

    if (action === ACTIONS.UPDATE && lifecycle === LIFECYCLE.TOOLTIP) {
      window.dispatchEvent(new Event('resize'))
    }

    if (
      status === STATUS.FINISHED ||
      status === STATUS.SKIPPED ||
      action === 'close'
    ) {
      // 引导完成或跳过
      onExit()
      setStepIndex(0)
      guideStore.setPerpMark(true)
    } else if (type === 'step:after') {
      // 步骤切换后
      setStepIndex(index + 1)
    }
  }

  if (!guideStore.isPerpGuide || !isReady) return null

  return (
    <>
      <Joyride
        steps={steps}
        run={guideStore.isPerpGuide}
        stepIndex={stepIndex}
        callback={handleJoyrideCallback}
        continuous
        showProgress={false}
        showSkipButton={false}
        scrollToFirstStep
        scrollOffset={0} // 设置滚动偏移为0
        disableScrolling={true} // 完全禁用滚动
        disableOverlayClose
        tooltipComponent={CustomTooltip}
        floaterProps={{
          disableAnimation: true,
          hideArrow: false,
        }}
        styles={{
          spotlight: isMd
            ? {}
            : {
                borderRadius: '12px',
                border: '3px solid #facc15',
              },
          overlay: {
            position: 'fixed',
          },
        }}
      />
    </>
  )
})
