import { observer } from 'mobx-react-lite'
import { HeadCtx } from '@/pages/Perp/module/HeadCtx'
import { FullscreenToggleButton } from '@/components/LocalFullsreen'
import { HeadSearch } from '@/pages/Perp/module/HeadSearch'
import { useMedia } from '@/hooks/useMedia'

export const PerpHead = observer(() => {
  const { isMd } = useMedia()
  return (
    <div className="flex w-full items-center justify-between gap-2.5">
      <div className="flex items-center">
        <HeadSearch />
      </div>
      <div className="flex items-center">
        {!isMd && <HeadCtx />}
        {!isMd && <FullscreenToggleButton />}
      </div>
    </div>
  )
})
