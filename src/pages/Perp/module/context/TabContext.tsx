import React, { createContext, useContext } from 'react'

// 定义一个全局变量，用于存储 Wallet 的 TabContext
let WalletTabContext: React.Context<any> | null = null

// 提供一个函数，用于注册 Wallet 的 TabContext
export function registerWalletTabContext(context: React.Context<any>) {
  WalletTabContext = context
}

interface TabContextType {
  setActiveTab: (tabId: string) => void
  activeTab?: string
  theme?: 'dark' | 'light'
  simplified?: boolean
}

export const TabContext = createContext<TabContextType | undefined>(undefined)

export const useTabContext = () => {
  // 首先尝试从 Perp 的 TabContext 获取上下文
  const context = useContext(TabContext)

  // 如果找到上下文，直接返回
  if (context !== undefined) {
    return context
  }

  // 如果没有找到上下文，尝试从 Wallet 的 TabContext 获取
  if (WalletTabContext) {
    const walletContext = useContext(WalletTabContext)
    if (walletContext) {
      return walletContext
    }
  }

  // 如果两个上下文都没有找到，抛出错误
  throw new Error('useTabContext must be used within a TabContextProvider')
}
