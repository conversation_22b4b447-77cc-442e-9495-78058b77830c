import { BuyText, ColoringText, SellText } from '@/components/ColoringText'
import { ViewRow, ViewItem, ViewValue } from '@/pages/Perp/module/styles'
import { perpsStore } from '@/store'
import numeral from 'numeral'
import { observer } from 'mobx-react-lite'
import { useMemo } from 'react'
import i18n from '@/i18n'
import { useMedia } from '@/hooks/useMedia'

export const Overview = observer(() => {
  const {
    marginRatio,
    maintenanceMarginUsd,
    accountLeverage,
    unrealizedPnlUsd,
    balance,
  } = perpsStore.user.data

  const { isMd } = useMedia()

  const unrealizedPnlUsdValue = useMemo(() => {
    return numeral(unrealizedPnlUsd).format('$0,0.00')
  }, [unrealizedPnlUsd])

  return (
    <div className="flex w-full flex-col gap-2">
      {!isMd && (
        <div className="justify-start text-sm font-normal text-gray-50">
          {i18n.t('perp_overview')}
        </div>
      )}
      <ViewRow>
        <ViewItem>{i18n.t('balance')}</ViewItem>
        <ViewValue>${balance?.accountValue}</ViewValue>
      </ViewRow>
      <ViewRow>
        <ViewItem>{i18n.t('unrealized_profit_loss')}</ViewItem>
        <ViewValue>
          <ColoringText amount={unrealizedPnlUsd}>
            {unrealizedPnlUsdValue}
          </ColoringText>
        </ViewValue>
      </ViewRow>
      <ViewRow>
        <ViewItem>{i18n.t('margin_ratio')}</ViewItem>
        <ViewValue>
          {parseFloat(marginRatio) <= 50 ? (
            <BuyText>{marginRatio}%</BuyText>
          ) : (
            <SellText>{marginRatio}%</SellText>
          )}
        </ViewValue>
      </ViewRow>
      <ViewRow>
        <ViewItem>{i18n.t('maintenance_margin')}</ViewItem>
        <ViewValue>${maintenanceMarginUsd}</ViewValue>
      </ViewRow>
      <ViewRow>
        <ViewItem>{i18n.t('account_leverage')}</ViewItem>
        <ViewValue>{accountLeverage}x</ViewValue>
      </ViewRow>
    </div>
  )
})
