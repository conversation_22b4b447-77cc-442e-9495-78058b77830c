import { InputSearch } from '@/components/Customize/InputSearch'
import { SearchPopover } from '@/components/Customize/PKPopover'
import { PopupDown } from '@/components/PopupDown'
import { useMedia } from '@/hooks/useMedia'
import i18n from '@/i18n'
import { SvgIconDelete, SvgIconZoomOut } from '@/imgs/icons'
import { SvgIconNoResults } from '@/imgs/other'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { HlAvatar, DayChange } from '@/pages/Perp/module/utils'
import { perpsStore } from '@/store'
import { IActiveMetaCtx } from '@/store/perps/types'
import { STORAGE_PERPS_SEARCH_HISTORY } from '@/utils/STORAGE_REG'
import { useDebounce, useLocalStorageState } from 'ahooks'
import { Col, Divider, Row } from 'antd'
import clsx from 'clsx'
import { observer } from 'mobx-react-lite'
import { useMemo, useState } from 'react'
import styled from 'styled-components'
import tw from 'twin.macro'

const Rth = styled(Col)`
  ${tw`justify-start text-xs font-medium text-zinc-400`}
`

export const HeadSearch = observer(() => {
  const { allData } = perpsStore.meta
  const { isMd } = useMedia()
  const [visible, setVisible] = useState(false)

  const dataMap = useMemo(() => {
    if (!visible) return []
    const visibleArray = perpsStore.meta.getVisibleAssetNamesArray()
    return allData.filter((item) => visibleArray.includes(item.name))
  }, [visible, allData])

  if (isMd) {
    return (
      <>
        <div className="chart-area" onClick={() => setVisible(true)}>
          <SearchChips />
        </div>
        <PopupDown
          visible={visible}
          title={i18n.t('search_coin')}
          onClose={() => setVisible(false)}
        >
          <div className="px-4">
            <SearchContent setVisible={setVisible} dataMap={dataMap} />
          </div>
        </PopupDown>
      </>
    )
  }
  return (
    <SearchPopover
      destroyTooltipOnHide
      align={{ offset: [-5, 10] }}
      content={<SearchContent setVisible={setVisible} dataMap={dataMap} />}
      open={visible}
      onOpenChange={setVisible}
    >
      <div className="chart-area" onClick={() => setVisible(true)}>
        <SearchChips />
      </div>
    </SearchPopover>
  )
})

const SearchChips = observer(() => {
  const { isMd } = useMedia()
  return (
    <div className="flex cursor-pointer items-center gap-1.5">
      <HlAvatar
        coin={perpsStore.init.coin}
        className={clsx(isMd ? 'size-7' : 'size-10')}
      />
      <div
        className={clsx(
          'justify-start font-medium leading-tight text-white',
          isMd ? 'text-sm' : 'text-xl',
        )}
      >
        <span>{perpsStore.init.coin}-USD</span>
      </div>
      <SvgIconZoomOut className="size-3.5 text-white" />
    </div>
  )
})

const SearchContent = observer(
  ({
    setVisible,
    dataMap,
  }: {
    setVisible: (visible: boolean) => void
    dataMap: IActiveMetaCtx[]
  }) => {
    const { isMd } = useMedia()
    const [history, setHistory] = useLocalStorageState<string[]>(
      STORAGE_PERPS_SEARCH_HISTORY,
      { defaultValue: [] },
    )
    const [search, setSearch] = useState('')
    const debouncedSearch = useDebounce(search, { wait: 200 })

    // 选择币种
    function selectCoin(name: string) {
      perpsStore.init.setCoin(name)
      setHistory([...new Set([name, ...(history || [])])].slice(0, 10))
      setVisible(false)
    }

    // 删除搜索历史
    function deleteHistory() {
      setHistory([])
    }

    // 筛选币种
    const filteredData = useMemo(() => {
      if (!debouncedSearch) return dataMap
      return dataMap.filter((item) =>
        item.name.toLowerCase().includes(debouncedSearch.trim().toLowerCase()),
      )
    }, [debouncedSearch, dataMap])

    const isShowHistory = !!history?.length
    const isShowRecommend = !!filteredData?.length

    return (
      <div className={clsx('text-white', isMd ? 'w-full' : 'w-96')}>
        <div className="mb-2.5 w-full">
          <InputSearch
            placeholder={i18n.t('search_coin')}
            prefix={
              <div className="pr-1">
                <SvgIconZoomOut className="size-3.5 text-[#5F606D]" />
              </div>
            }
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        {isShowHistory && !search && (
          <>
            <div className="flex w-full items-center justify-between">
              <SearchH5>{i18n.t('recents_search')}</SearchH5>
              <SearchDelete onClick={deleteHistory} />
            </div>
            <div className="flex w-full flex-wrap gap-2.5 px-2 pt-3">
              {history?.map((name, index) => {
                return (
                  <div
                    className="cursor-pointer justify-start text-sm font-medium leading-tight text-gray-50"
                    onClick={() => selectCoin(name)}
                    key={index}
                  >
                    {name}-USD
                  </div>
                )
              })}
            </div>
            <Divider className="my-2.5 w-full bg-zinc-800" />
          </>
        )}
        <div className="w-full">
          {!search && <SearchH5>{i18n.t('recommend_coin')}</SearchH5>}
          {isShowRecommend && (
            <Row className="my-1 w-full items-center py-0.5">
              <Rth span={10}>{i18n.t('symbol')}</Rth>
              <Rth span={4}>{i18n.t('last_price')}</Rth>
              <Rth span={10} className="text-right">
                {i18n.t('change_24h')}
              </Rth>
            </Row>
          )}
          <div
            className={clsx(
              'w-full overflow-y-auto overflow-x-hidden no-scroll',
              isMd ? 'h-[40vh]' : 'h-44',
            )}
          >
            {isShowRecommend ? (
              filteredData?.map((item) => (
                <SearchMap
                  item={item}
                  selectCoin={selectCoin}
                  key={item.name}
                />
              ))
            ) : (
              <div className="flex h-full flex-col items-center justify-center gap-4">
                <SvgIconNoResults className="w-32" />
                <div className="text-sm font-normal leading-tight text-[#9293a0]">
                  {i18n.t('no_match_result')}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  },
)

function SearchH5({ children }: { children: React.ReactNode }) {
  return (
    <div className="h-5 justify-start text-sm font-medium leading-tight text-white">
      {children}
    </div>
  )
}

function SearchDelete({ onClick }: { onClick: () => void }) {
  return (
    <div
      className="flex cursor-pointer items-center justify-center"
      onClick={onClick}
    >
      <SvgIconDelete className="size-4 text-[#9293A0]" />
      <div className="justify-start text-xs font-normal leading-none text-zinc-400">
        {i18n.t('clear')}
      </div>
    </div>
  )
}

function SearchMap({
  item,
  selectCoin,
}: {
  item: IActiveMetaCtx
  selectCoin: (name: string) => void
}) {
  const { hlFormatPrice } = useTools()

  return (
    <Row
      className="my-1 w-full cursor-pointer items-center py-0.5 hover:bg-slate-500/5"
      onClick={() => selectCoin(item.name)}
    >
      <Col span={10} className="flex items-center gap-1">
        <div className="justify-start text-sm font-normal text-gray-50">
          {item.name}-USD
        </div>
        <div className="justify-start bg-green-500/10 px-0.5 text-right text-sm font-medium text-emerald-500">
          {item.maxLeverage}x
        </div>
      </Col>
      <Col span={4} className="flex items-center">
        {hlFormatPrice({ price: item.markPx, szDecimals: item.szDecimals })}
      </Col>
      <Col span={10} className="flex items-center justify-end">
        <DayChange
          markPx={item.markPx}
          prevDayPx={item.prevDayPx}
          szDecimals={item.szDecimals}
        />
      </Col>
    </Row>
  )
}
