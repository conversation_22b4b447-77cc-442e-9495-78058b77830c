import { SegmentedPerps } from '@/components/Customize/Segmented'
import { useMedia } from '@/hooks/useMedia'
import i18n from '@/i18n'
import { usePerpOpenOrder } from '@/pages/Perp/module/openOrder/context'
import { TSide } from '@/store/perps/types'
import clsx from 'clsx'
import { observer } from 'mobx-react-lite'

export const DealTabs = observer(() => {
  const { isMd } = useMedia()
  const { side, setSide } = usePerpOpenOrder()
  const item = [
    {
      label: i18n.t('buy_long'),
      value: 'B',
    },
    {
      label: i18n.t('sell_short'),
      value: 'A',
    },
  ]

  function handleChange(value: string | number) {
    setSide(value as TSide)
  }

  return (
    <SegmentedPerps
      className={clsx('w-full', isMd && '!p-0')}
      options={item}
      value={side}
      onChange={handleChange}
    />
  )
})
