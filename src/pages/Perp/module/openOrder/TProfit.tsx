import { InputNumberPerp } from '@/components/Customize/InputNumber'
import i18n from '@/i18n'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { useTpsl } from '@/pages/Perp/hooks/useTpsl'
import { DropdownAfter } from '@/pages/Perp/module/openOrder/DropdownAfter'
import { TPerpNumber } from '@/pages/Perp/module/types'
import { perpsStore } from '@/store'
import { TCoinType, TSide } from '@/store/perps/types'
import { $ten, isBlank } from '@/utils'
import { observer } from 'mobx-react-lite'
import { useEffect, useMemo, useState } from 'react'

export const TProfit = observer(
  ({
    theme,
    amount,
    price,
    side,
    leverage,
    szDecimals,
    coin,
    coinType,
    disabled,
    onPriceChange,
  }: {
    // 主题
    theme?: 'dark' | 'light'
    // 下单数量
    amount: TPerpNumber
    // 预期开仓价格
    price: string
    // 持仓方向
    side: TSide
    // 杠杆
    leverage: number
    // 币种数量精度
    szDecimals: number
    // 交易对
    coin: string
    // 交易货币类型
    coinType: TCoinType
    // 是否禁用止盈输入
    disabled: boolean
    // 回调
    onPriceChange: (profitPrice: TPerpNumber) => void
  }) => {
    const { tpslCoinType } = perpsStore.init
    const { hlFormatPrice } = useTools()
    const { priceToPct, pctToPrice, priceToAmount, amountToPrice } = useTpsl()

    // 止盈价格
    const [profitPrice, setProfitPrice] = useState<TPerpNumber>(null)
    // 止盈比例 / 金额
    const [profitPct, setProfitPct] = useState<TPerpNumber>(null)
    // 记录是否修正过止盈价格
    const [isProfitFocused, setIsProfitFocused] = useState(false)

    // 是否是亮色主题
    const isLight = theme === 'light'
    // 是否是买单
    const isBid = side === 'B'
    // 是否是金额
    const isAmount = useMemo(() => tpslCoinType === 'amount', [tpslCoinType])

    useEffect(() => {
      onPriceChange && onPriceChange(profitPrice)
    }, [profitPrice])

    useEffect(() => {
      setTimeout(() => {
        setProfitPrice(null)
        setProfitPct(null)
      })
    }, [tpslCoinType, coin, side])

    useEffect(() => {
      const isPct = tpslCoinType === 'percentage'
      const isAmount = tpslCoinType === 'amount'
      if (profitPct && isAmount) {
        setProfitPrice(
          amountToPrice({
            exitAmount: profitPct,
            entryPx: price,
            amount,
            coinType,
            side,
            szDecimals,
            direction: isBid ? '1' : '-1',
          }),
        )
      }

      if (profitPct && isPct) {
        setProfitPrice(
          pctToPrice({
            roePct: profitPct,
            entryPx: price,
            side,
            leverage,
            szDecimals,
            direction: isBid ? '-1' : '1',
          }),
        )
      }
    }, [amount, price])

    useEffect(() => {
      if (profitPct && price && !isProfitFocused) {
        onProfitAmountChange(profitPct, 'auto')
      }
    }, [price, profitPct, isProfitFocused])

    function onProfitPriceChange(value: TPerpNumber) {
      setProfitPrice(value)
      setIsProfitFocused(true)

      try {
        const exitPx = value // 止盈价格
        const entryPx = price // 开仓均价
        const direction = isBid ? '1' : '-1'

        if (isAmount) {
          setProfitPct(
            $ten.toFixed(
              priceToAmount({
                exitPx,
                entryPx,
                amount,
                coinType,
                side,
                direction,
              }) || '0',
              0,
            ),
          )
        } else {
          setProfitPct(priceToPct({ exitPx, entryPx, direction, leverage }))
        }
      } catch (error) {
        console.error(error)
      }
    }

    function onProfitAmountChange(value: TPerpNumber, type: 'auto' | 'manual') {
      if (type === 'manual') {
        setProfitPct(value)
        setIsProfitFocused(false)
      }

      try {
        const entryPx = price
        const direction = isBid ? '-1' : '1'

        if (isAmount) {
          setProfitPrice(
            amountToPrice({
              exitAmount: value,
              entryPx,
              amount,
              coinType,
              side,
              szDecimals,
              direction,
            }),
          )
        } else {
          setProfitPrice(
            pctToPrice({
              roePct: value,
              entryPx,
              side,
              leverage,
              szDecimals,
              direction,
            }),
          )
        }
      } catch (error) {
        console.error(error)
      }
    }

    function handleProfitPriceBlur(e: React.FocusEvent<HTMLInputElement>) {
      if (isBlank(e.target.value)) return

      const value = hlFormatPrice({
        price: e.target.value,
        szDecimals,
        isSimple: true,
      })

      setProfitPrice(value)
    }

    return (
      <div className="flex w-full items-center justify-between gap-2">
        <div className="flex-1">
          <InputNumberPerp
            theme={isLight ? 'light' : 'dark'}
            value={profitPrice}
            onChange={onProfitPriceChange}
            onBlur={handleProfitPriceBlur}
            className="w-full"
            prefix={
              <span className="text-10 text-[#9293a0]">
                {i18n.t('tpsl_profit_price')}
              </span>
            }
            disabled={disabled}
          />
        </div>
        <div className="flex-1">
          <InputNumberPerp
            theme={isLight ? 'light' : 'dark'}
            value={profitPct}
            onChange={(value) => onProfitAmountChange(value, 'manual')}
            className="w-full"
            precision={2}
            prefix={
              <span className="text-10 text-[#9293a0]">
                {i18n.t('profitPct')}
              </span>
            }
            addonAfter={<DropdownAfter theme={theme} />}
            disabled={disabled}
          />
        </div>
      </div>
    )
  },
)
