import { CateTabs } from '@/pages/Perp/module/openOrder/CateTabs'
import { PerpOpenOrderContext } from '@/pages/Perp/module/openOrder/context'
import { DealAmount } from '@/pages/Perp/module/openOrder/DealAmount'
import { DealAmountSlider } from '@/pages/Perp/module/openOrder/DealAmountSlider'
import { DealManage } from '@/pages/Perp/module/openOrder/DealManage'
import { DealTabs } from '@/pages/Perp/module/openOrder/DealTabs'
import { FormBtn } from '@/pages/Perp/module/openOrder/FormBtn'
import { LeverBtn } from '@/pages/Perp/module/openOrder/LeverBtn'
import { OrderConver } from '@/pages/Perp/module/openOrder/OrderConver'
import { PosBtn } from '@/pages/Perp/module/openOrder/PosBtn'
import { Position } from '@/pages/Perp/module/openOrder/Position'
import { Overview } from '@/pages/Perp/module/Overview'
import { WalletTag } from '@/pages/Perp/module/openOrder/WalletTag'
import {
  TPerpDealManage,
  TPerpMode,
  TPerpNumber,
  TPerpOrderType,
} from '@/pages/Perp/module/types'
import { perpsStore } from '@/store/perps'
import { TSide } from '@/store/perps/types'
import { Divider } from 'antd'
import { observer } from 'mobx-react-lite'
import { useEffect, useState } from 'react'
import { usePerp } from '@/pages/Perp/module/contPerp/usePerp'
import { useMedia } from '@/hooks/useMedia'

export const ModOpenOrder = observer(() => {
  const { limitPrice, sideView } = usePerp()
  const { isMd } = useMedia()
  // 市价/限价
  const [mode, setMode] = useState<TPerpMode>('market')
  // 订单方向
  const [side, setSide] = useState<TSide>(isMd ? sideView.side : 'B')
  // 限价单价格
  const [price, setPrice] = useState<TPerpNumber>(null)
  // 下单币种数量
  // 注意：这里受到 perpsStore.init.coinType 的影响
  // 如果 coinType = usdc，则 amount 为 usd 数量
  // 如果 coinType = coin，则 amount 为 coin 数量
  const [amount, setAmount] = useState<string | null>(null)
  // 下单币种数量(百分比)
  const [amountPercent, setAmountPercent] = useState<number>(0)
  // 订单管理
  const [dealManage, setDealManage] = useState<TPerpDealManage>(undefined)
  // 订单类型
  const [orderType, setOrderType] = useState<TPerpOrderType>('Gtc')
  // 止盈价格
  const [profitPrice, setProfitPrice] = useState<TPerpNumber>(null)
  // 止损价格
  const [lossPrice, setLossPrice] = useState<TPerpNumber>(null)

  useEffect(() => {
    return () => {
      reset()
    }
  }, [])

  useEffect(() => {
    setMode('limit')
    setPrice(limitPrice)
  }, [limitPrice])

  useEffect(() => {
    if (isMd) setSide(sideView.side)
  }, [sideView, isMd])

  useEffect(() => {
    reset()
  }, [perpsStore.init.coin])

  // 重置数据
  function reset() {
    setMode('market')
    setSide(isMd ? sideView.side : 'B')
    setAmount(null)
  }

  return (
    <PerpOpenOrderContext.Provider
      value={{
        mode,
        setMode,
        side,
        setSide,
        price,
        setPrice,
        amount,
        setAmount,
        amountPercent,
        setAmountPercent,
        dealManage,
        setDealManage,
        orderType,
        setOrderType,
        profitPrice,
        setProfitPrice,
        lossPrice,
        setLossPrice,
      }}
    >
      <div className="flex flex-col gap-2">
        {/* 钱包充值 */}
        <div className="w-full">
          <WalletTag />
        </div>
        {!isMd && (
          <>
            {/* 杠杆和仓位 */}
            <div className="flex h-7 w-full items-center justify-between gap-2">
              <LeverBtn />
              <PosBtn />
            </div>
            {/* 订单类型 */}
            <div className="w-full">
              <CateTabs />
            </div>
          </>
        )}
        {isMd && (
          <div className="flex h-7 w-full items-center justify-between gap-2">
            <div>
              <CateTabs />
            </div>
            <div>
              <LeverBtn />
            </div>
            <div>
              <PosBtn />
            </div>
          </div>
        )}
        {/* 订单方向 */}
        <div className="w-full">
          <DealTabs />
        </div>
        {/* 仓位 */}
        <div className="flex w-full flex-col gap-2.5">
          <Position />
          <DealAmount />
          <DealAmountSlider />
        </div>
        {/* 订单管理 */}
        <div className="w-full">
          <DealManage />
        </div>
        {/* 订单转换 */}
        {isMd && (
          <>
            <div className="w-full">
              <OrderConver />
            </div>
            <Divider className="m-0 bg-zinc-900" />
          </>
        )}
        {/* 下单按钮 */}
        <div className="w-full">
          <FormBtn />
        </div>
        {/* 订单转换 */}
        {!isMd && (
          <>
            <Divider className="m-0 bg-zinc-900" />
            <div className="w-full">
              <OrderConver />
            </div>
          </>
        )}
        {/* 概览 */}
        {!isMd && (
          <>
            <Divider className="m-0 bg-zinc-900" />
            <div className="w-full">
              <Overview />
            </div>
          </>
        )}
      </div>
    </PerpOpenOrderContext.Provider>
  )
})
