import { DropdownPerp } from '@/components/Customize/Dropdown'
import { TabsTheme } from '@/components/Customize/Tabs'
import { useMedia } from '@/hooks/useMedia'
import i18n from '@/i18n'
import { usePerpOpenOrder } from '@/pages/Perp/module/openOrder/context'
import { TPerpMode } from '@/pages/Perp/module/types'
import { observer } from 'mobx-react-lite'

export const CateTabs = observer(() => {
  const { isMd } = useMedia()
  const { mode, setMode } = usePerpOpenOrder()

  const items = [
    {
      key: 'market',
      onClick: () => setMode('market'),
      label: i18n.t('market'),
    },
    {
      key: 'limit',
      onClick: () => setMode('limit'),
      label: i18n.t('limit'),
    },
  ]

  const handleChange = (key: string) => {
    setMode(key as TPerpMode)
  }

  if (isMd) {
    return (
      <DropdownPerp
        trigger={['click']}
        menu={{ items, selectable: true, defaultSelectedKeys: [mode] }}
      >
        {mode === 'market' ? i18n.t('market') : i18n.t('limit')}
      </DropdownPerp>
    )
  }

  return (
    <TabsTheme
      theme="light"
      items={items}
      activeKey={mode}
      onChange={handleChange}
    />
  )
})
