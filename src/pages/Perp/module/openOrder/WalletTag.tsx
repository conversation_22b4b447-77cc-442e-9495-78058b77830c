import { cutToken } from '@/utils'
import CopyToClipboard from '@/components/CopyToClipboard'
import { observer } from 'mobx-react-lite'
import { message } from 'antd'
import i18n from '@/i18n'
import { userStore } from '@/store'
import { IconRoomFollowRoom } from '@/imgs/icons'
import { LoadingOutlined } from '@ant-design/icons'
import { usePerp } from '@/pages/Perp/module/contPerp/usePerp'

export const WalletTag = observer(() => {
  const { walletAddress, setShowTopUpModal } = usePerp()

  if (!walletAddress) {
    return (
      <div className="flex w-full items-center justify-center py-2">
        <LoadingOutlined />
      </div>
    )
  }

  return (
    <>
      <div className="flex items-center justify-between gap-2 rounded-lg bg-[#1a1b1e] px-2 py-2.5">
        <div className="flex items-center gap-2">
          <img
            className="size-5 rounded-full object-cover"
            src={userStore.info?.profile?.photos[0]?.path}
            alt="avatar"
          />
          <div>
            <CopyToClipboard
              text={walletAddress}
              onCopy={() => {
                message.success(i18n.t('copy_success'))
              }}
            >
              <div>{cutToken(walletAddress || '', '...')}</div>
            </CopyToClipboard>
          </div>
        </div>
        {/* 充值按钮 */}
        <div className="flex cursor-pointer items-center gap-2">
          <IconRoomFollowRoom
            className="size-4"
            onClick={() => setShowTopUpModal(true)}
          />
        </div>
      </div>
    </>
  )
})
