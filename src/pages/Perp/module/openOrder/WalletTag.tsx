import { cutToken } from '@/utils'
import CopyToClipboard from '@/components/CopyToClipboard'
import { observer } from 'mobx-react-lite'
import { message } from 'antd'
import i18n from '@/i18n'
import { IconRoomFollowRoom } from '@/imgs/icons'
import { LoadingOutlined } from '@ant-design/icons'
import { usePerp } from '@/pages/Perp/module/contPerp/usePerp'
import { useMedia } from '@/hooks/useMedia'

export const WalletTag = observer(() => {
  const { walletAddress, setShowTopUpModal } = usePerp()
  const { isMd } = useMedia()

  if (!walletAddress) {
    return (
      <div className="flex w-full items-center justify-center py-2">
        <LoadingOutlined />
      </div>
    )
  }

  return (
    <>
      <div className="wallet-area flex items-center justify-between gap-2 rounded-lg bg-[#1a1b1e] px-2 py-1 md:py-2.5">
        <div className="flex items-center gap-2">
          {/* <img
            className="size-5 rounded-full object-cover"
            src={userStore.info?.profile?.photos[0]?.path}
            alt="avatar"
          /> */}
          <div>
            <CopyToClipboard
              text={walletAddress}
              onCopy={() => {
                message.success(i18n.t('copy_success'))
              }}
            >
              <div>{cutToken(walletAddress || '', '...', isMd ? 12 : 16)}</div>
            </CopyToClipboard>
          </div>
        </div>
        {/* 充值按钮 */}
        <div className="flex cursor-pointer items-center gap-2">
          <IconRoomFollowRoom
            className="size-3 md:size-4"
            onClick={() => setShowTopUpModal(true)}
          />
        </div>
      </div>
    </>
  )
})
