import { observer } from 'mobx-react-lite'
import { Slider } from '@/components/Slider'
import { usePerpOpenOrder } from '@/pages/Perp/module/openOrder/context'
import { InputNumberPerp } from '@/components/Customize/InputNumber'
import { $ten } from '@/utils'
import { TPerpNumber } from '@/pages/Perp/module/types'
import { useOfficial } from '@/pages/Perp/module/openOrder/useOfficial'

export const DealAmountSlider = observer(() => {
  const { amountPercent, setAmountPercent, setAmount } = usePerpOpenOrder()
  const { maxAmount, maxPrecision, isReduceSameSide } = useOfficial()

  function onValueChange(value: number[]) {
    const percent = value[0]
    setAmountPercent(percent)
    convertAmount(percent)
  }

  function onValueInput(value: TPerpNumber) {
    const percent = Number(value)
    setAmountPercent(percent)
    convertAmount(percent)
  }

  // 换算数量
  function convertAmount(percent: number) {
    if (isReduceSameSide) {
      setAmount(null)
    } else if (percent >= 100) {
      setAmount($ten.toFixed(maxAmount, maxPrecision))
    } else if (percent <= 0) {
      setAmount(null)
    } else {
      setAmount($ten.toFixed($ten.mul(maxAmount, percent / 100), maxPrecision))
    }
  }

  return (
    <div className="flex items-center justify-between gap-5">
      <div className="flex-1">
        <Slider
          value={[amountPercent]}
          type="trade"
          theme="dark"
          min={0}
          max={100}
          onValueChange={onValueChange}
          onValueCommit={onValueChange}
        />
      </div>
      <div className="w-16 py-1">
        <InputNumberPerp
          value={amountPercent}
          onChange={onValueInput}
          className="w-full"
          max={100}
          min={0}
          precision={0}
          onFocus={() => {}}
          onBlur={() => {}}
          suffix={<span className="text-xs text-[#9293a0]">%</span>}
        />
      </div>
    </div>
  )
})
