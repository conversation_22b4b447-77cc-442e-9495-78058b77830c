import { usePerpOpenOrder } from '@/pages/Perp/module/openOrder/context'
import { IUseOfficial } from '@/pages/Perp/module/types'
import { perpsStore } from '@/store'
import { $ten, Decimal } from '@/utils'
import { useMemo } from 'react'

export const useOfficial = (): IUseOfficial => {
  const { activeMeta, activeAsset } = perpsStore.meta
  const { bestPrice } = perpsStore.book
  const { assetPosition, accountValue } = perpsStore.user
  const { coinType, coin } = perpsStore.init
  const { positionOrders, pendingOrders } = perpsStore.order
  const { mode, side, dealManage, price } = usePerpOpenOrder()

  // 可用下单余额
  const availableUsd = useMemo(() => {
    // 当前杠杆
    const leverage = activeAsset?.leverage?.value ?? 0

    // 持仓已用保证金
    const posMargin = { used: 0, margin: 0, side: 'B' }
    positionOrders.map((pos) => {
      if (!leverage) return 0

      const position = pos.position
      const sziSide = new Decimal(position.szi).gte(0) ? 'B' : 'A'

      if (coin !== position.coin) return 0

      const margin = new Decimal(position.marginUsed)
      const marginAdjust = sziSide === side ? margin : margin.mul(-1)

      posMargin.used = marginAdjust.toNumber()
      posMargin.margin = margin.toNumber()
      posMargin.side = sziSide
    })

    // 挂单占用保证金
    const openMarginUsed = pendingOrders.reduce((sum, pos) => {
      if (!leverage) return 0
      if (pos.side !== side) return sum

      // 订单价值 = 订单数量 * 限价
      const orderValue = new Decimal(pos.sz).mul(pos.limitPx)
      // 保证金 = 订单价值 / 杠杆
      const margin = new Decimal(orderValue).div(leverage)

      return new Decimal(sum).add(margin).toNumber()
    }, 0)

    // 结算订单
    let openOrdersMarginUsed = openMarginUsed
    if (posMargin.side !== side && posMargin.margin > openMarginUsed) {
      openOrdersMarginUsed = 0
    }

    // 可用下单余额 = 可用余额 - 绝对值总和安全阀值 - 逐仓已用保证金
    const total = $ten.sub(
      $ten.sub(accountValue, posMargin.used),
      openOrdersMarginUsed,
    )

    const totalDecimal = new Decimal(total)

    return $ten.toFixed(
      totalDecimal.isNegative() || totalDecimal.lessThan(0.1) ? 0 : total,
      2,
    )
  }, [
    positionOrders,
    pendingOrders,
    assetPosition,
    accountValue,
    activeAsset,
    bestPrice,
    side,
  ])

  // 最大可交易数量
  const maxAmount = useMemo(() => {
    const isUsdc = coinType === 'usdc'
    const isCoin = coinType === 'coin'
    const isReduce = dealManage === 'reduce'
    const positionValue = assetPosition?.value
    const leverage = activeAsset?.leverage?.value || 0

    // 只减仓
    if (isReduce && isUsdc) return convertAmountToUsd(positionValue)
    if (isReduce && isCoin) return positionValue

    // 普通交易
    if (isCoin) {
      return $ten.mul(convertAmount(availableUsd), leverage)
    }

    return $ten.mul(availableUsd, leverage)
  }, [
    availableUsd,
    activeMeta,
    assetPosition,
    dealManage,
    coinType,
    activeAsset,
    side,
    bestPrice,
    price,
    mode,
    coin,
  ])

  // 最大交易数量精度
  const maxPrecision = useMemo(() => {
    return coinType === 'usdc' ? 2 : activeMeta?.szDecimals
  }, [activeMeta, coinType])

  // 只减仓且方向相同
  const isReduceSameSide = useMemo(() => {
    return dealManage === 'reduce' && ['N', side].includes(assetPosition?.side)
  }, [dealManage, side, assetPosition])

  // 换算数量
  function convertAmount(value: string): string {
    if (!value) return '0'
    const isMarket = mode === 'market'
    const bidPrice = isMarket ? bestPrice?.bid : price || bestPrice?.bid
    const offerPrice = isMarket ? bestPrice?.offer : price || bestPrice?.offer
    const szDecimals = activeMeta?.szDecimals

    // 多头
    if (side === 'B') return $ten.toFixed($ten.div(value, bidPrice), szDecimals)
    // 空头
    if (side === 'A')
      return $ten.toFixed($ten.div(value, offerPrice), szDecimals)
    return '0'
  }

  // 换算金额
  function convertAmountToUsd(value: string): string {
    if (!value) return '0'
    const isMarket = mode === 'market'
    const bidPrice = isMarket ? bestPrice?.bid : price || bestPrice?.bid
    const offerPrice = isMarket ? bestPrice?.offer : price || bestPrice?.offer

    // 多头
    if (side === 'B') return $ten.toFixed($ten.mul(value, bidPrice), 2)
    // 空头
    if (side === 'A') return $ten.toFixed($ten.mul(value, offerPrice), 2)
    return '0'
  }

  return {
    availableUsd,
    maxAmount,
    maxPrecision,
    isReduceSameSide,
    convertAmount,
    convertAmountToUsd,
  }
}
