import { InputNumberPerp } from '@/components/Customize/InputNumber'
import { perpsStore } from '@/store'
import { MenuProps } from 'antd'
import { observer } from 'mobx-react-lite'
import { useEffect, useState } from 'react'
import { DropdownPerp } from '@/components/Customize/Dropdown'
import { usePerpOpenOrder } from '@/pages/Perp/module/openOrder/context'
import { TPerpNumber } from '@/pages/Perp/module/types'
import { $ten, isBlank } from '@/utils'
import { useOfficial } from '@/pages/Perp/module/openOrder/useOfficial'
import { useTools } from '@/pages/Perp/hooks/useTools'
import i18n from '@/i18n'

export const DealAmount = observer(() => {
  const { coinType } = perpsStore.init
  const { bestPrice } = perpsStore.book
  const { activeMeta } = perpsStore.meta
  const { mode, side, price, setPrice, amount, setAmount, setAmountPercent } =
    usePerpOpenOrder()
  const { maxAmount, maxPrecision, convertAmount, convertAmountToUsd } =
    useOfficial()
  const { hlFormatPrice } = useTools()

  const isLimit = mode === 'limit'

  useEffect(() => {
    if (isLimit) {
      onMidClick()
    }
  }, [isLimit])

  useEffect(() => {
    if (amount && coinType === 'coin') {
      setAmount(convertAmount(amount))
    }
    if (amount && coinType === 'usdc') {
      setAmount(convertAmountToUsd(amount))
    }
  }, [coinType])

  function handleChange(value: TPerpNumber) {
    setAmount(value as string | null)

    if (Number(value) >= Number(maxAmount)) {
      setAmountPercent(100)
    } else if (Number(value) <= 0) {
      setAmountPercent(0)
    } else {
      const pct = $ten.toFixed(
        $ten.mul($ten.div(value || 0, maxAmount), 100),
        0,
      )
      setAmountPercent(Number(pct))
    }
  }

  function handlePriceChange(value: TPerpNumber) {
    setPrice(value)
  }

  function handlePriceBlur(e: React.FocusEvent<HTMLInputElement>) {
    if (isBlank(e.target.value)) return

    const value = hlFormatPrice({
      price: e.target.value,
      szDecimals: activeMeta?.szDecimals,
      isSimple: true,
    })

    setPrice(value)
  }

  function onMidClick() {
    const isBid = side === 'B'
    const price = isBid ? bestPrice?.bid : bestPrice?.offer

    const value = hlFormatPrice({
      price,
      szDecimals: activeMeta?.szDecimals,
      isSimple: true,
    })

    setPrice(value)
  }

  return (
    <div className="w-full">
      {mode === 'limit' && (
        <div className="mb-1 w-full">
          <InputNumberPerp
            value={price}
            onChange={handlePriceChange}
            onBlur={handlePriceBlur}
            className="w-full"
            min={0}
            onFocus={() => {}}
            prefix={
              <span className="text-xs text-[#9293a0]">
                {i18n.t('tit_price')}(USD)
              </span>
            }
            addonAfter={
              <div
                className="cursor-pointer justify-start text-right text-sm text-yellow-500"
                onClick={onMidClick}
              >
                {i18n.t('tit_mid')}
              </div>
            }
          />
        </div>
      )}
      <InputNumberPerp
        value={amount}
        onChange={handleChange}
        className="w-full"
        min={0}
        precision={maxPrecision}
        onFocus={() => {}}
        onBlur={() => {}}
        prefix={
          <span className="text-xs text-[#9293a0]">{i18n.t('amount')}</span>
        }
        addonAfter={<DropdownAmount />}
      />
    </div>
  )
})

const DropdownAmount = observer(() => {
  const { coin, coinType } = perpsStore.init
  const [items, setItems] = useState<MenuProps['items']>([])

  useEffect(() => {
    setItems([
      {
        label: 'USD',
        onClick: () => perpsStore.init.setCoinType('usdc'),
        key: 'usdc',
      },
      {
        label: coin.toUpperCase(),
        onClick: () => perpsStore.init.setCoinType('coin'),
        key: 'coin',
      },
    ])
  }, [coin])

  return (
    <DropdownPerp
      trigger={['click']}
      menu={{ items, selectable: true, defaultSelectedKeys: [coinType] }}
    >
      {coinType === 'usdc' ? 'USD' : coin.toUpperCase()}
    </DropdownPerp>
  )
})
