import { DropdownPerp } from '@/components/Customize/Dropdown'
import { RadioPerp } from '@/components/Customize/Radio'
import i18n from '@/i18n'
import { usePerpOpenOrder } from '@/pages/Perp/module/openOrder/context'
import { TLoss } from '@/pages/Perp/module/openOrder/TLoss'
import { TProfit } from '@/pages/Perp/module/openOrder/TProfit'
import { TPerpDealManage, TPerpNumber } from '@/pages/Perp/module/types'
import { perpsStore } from '@/store'
import { MenuProps, Tooltip } from 'antd'
import { observer } from 'mobx-react-lite'
import { useState, useEffect, useMemo } from 'react'

export const DealManage = observer(() => {
  const { bestPrice } = perpsStore.book
  const { coinType, coin, tpslCoinType } = perpsStore.init
  const { activeMeta, activeAsset } = perpsStore.meta

  const {
    dealManage,
    setDealManage,
    mode,
    side,
    amount,
    price,
    orderType,
    setOrderType,
    setProfitPrice,
    setLossPrice,
  } = usePerpOpenOrder()

  const [items, setItems] = useState<MenuProps['items']>([])

  // 是否禁用止盈止损输入
  const disabled = useMemo(() => {
    return tpslCoinType === 'amount' && !amount
  }, [tpslCoinType, amount])

  const handleChange = (newValue: TPerpDealManage) => {
    if (dealManage === newValue) {
      setDealManage(undefined)
    } else {
      setDealManage(newValue)
    }
  }

  useEffect(() => {
    setItems([
      {
        label: 'GTC',
        onClick: () => setOrderType('Gtc'),
        key: 'Gtc',
      },
      {
        label: 'IOC',
        onClick: () => setOrderType('Ioc'),
        key: 'Ioc',
      },
      {
        label: 'ALO',
        onClick: () => setOrderType('Alo'),
        key: 'Alo',
      },
    ])
  }, [coin])

  function onProfitPrice(profitPrice: TPerpNumber) {
    setProfitPrice(profitPrice)
  }

  function onLossPrice(lossPrice: TPerpNumber) {
    setLossPrice(lossPrice)
  }

  // 入参传递
  const TPSL_PROPS = useMemo(() => {
    const isLimit = mode === 'limit'
    const currentPrice = isLimit
      ? price
      : side === 'B'
        ? bestPrice?.bid
        : bestPrice?.offer
    return {
      side,
      amount,
      coin,
      coinType,
      leverage: activeAsset?.leverage?.value || 0,
      szDecimals: activeMeta?.szDecimals || 0,
      price: currentPrice,
      disabled,
    }
  }, [
    side,
    mode,
    amount,
    coin,
    coinType,
    price,
    activeAsset,
    activeMeta,
    bestPrice,
    bestPrice,
    disabled,
  ])

  return (
    <div className="flex w-full flex-col gap-2">
      <div className="flex w-full justify-between gap-2">
        <RadioPerp.Group className="flex flex-col gap-2" value={dealManage}>
          <RadioPerp value="reduce" onClick={() => handleChange('reduce')}>
            {i18n.t('reduce')}
          </RadioPerp>
          <RadioPerp
            value="profitLoss"
            onClick={() => handleChange('profitLoss')}
          >
            {i18n.t('profitLoss')}
          </RadioPerp>
        </RadioPerp.Group>
        {mode === 'limit' && (
          <div className="flex items-start text-white">
            <div className="flex items-center gap-1">
              <TifTips />
              <DropdownPerp
                trigger={['click']}
                menu={{
                  items,
                  selectable: true,
                  defaultSelectedKeys: [orderType],
                }}
              >
                {orderType?.toUpperCase()}
              </DropdownPerp>
            </div>
          </div>
        )}
      </div>
      {dealManage === 'profitLoss' && (
        <div className="mt-1 flex w-full flex-col gap-2">
          <TProfit {...TPSL_PROPS} onPriceChange={onProfitPrice} />
          <TLoss {...TPSL_PROPS} onPriceChange={onLossPrice} />
        </div>
      )}
    </div>
  )
})

function TifTips() {
  return (
    <Tooltip
      title={
        <div className="flex flex-col gap-1 p-2">
          <span>{i18n.t('tit_tif')}</span>
          <span>{i18n.t('tif_gtc')}</span>
          <span>{i18n.t('tif_ioc')}</span>
          <span>{i18n.t('tif_alo')}</span>
        </div>
      }
    >
      <span className="text-xs text-[#9293a0]">TIF</span>
    </Tooltip>
  )
}
