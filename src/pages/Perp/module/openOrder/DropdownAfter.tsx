import {
  DropdownLightPerp,
  DropdownPerp,
} from '@/components/Customize/Dropdown'
import { perpsStore } from '@/store'
import { MenuProps } from 'antd'
import { observer } from 'mobx-react-lite'

export const DropdownAfter = observer(
  ({ theme }: { theme?: 'light' | 'dark' }) => {
    const { tpslCoinType } = perpsStore.init
    const items: MenuProps['items'] = [
      {
        label: '$',
        onClick: () => perpsStore.init.setTpslCoinType('amount'),
        key: 'amount',
      },
      {
        label: '%',
        onClick: () => perpsStore.init.setTpslCoinType('percentage'),
        key: 'percentage',
      },
    ]

    return theme === 'light' ? (
      <DropdownLightPerp
        trigger={['click']}
        menu={{ items, selectable: true, defaultSelectedKeys: [tpslCoinType] }}
      >
        <span className="w-3 text-center">
          {tpslCoinType === 'amount' ? '$' : '%'}
        </span>
      </DropdownLightPerp>
    ) : (
      <DropdownPerp
        trigger={['click']}
        menu={{ items, selectable: true, defaultSelectedKeys: [tpslCoinType] }}
      >
        <span className="w-3 text-center">
          {tpslCoinType === 'amount' ? '$' : '%'}
        </span>
      </DropdownPerp>
    )
  },
)
