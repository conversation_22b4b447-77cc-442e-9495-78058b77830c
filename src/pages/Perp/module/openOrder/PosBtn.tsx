import { DownOutlined, WarningOutlined } from '@ant-design/icons'
import { ButtonBlack } from '@/components/Customize/Button'
import { <PERSON>lider } from '@/components/Slider'
import { useEffect, useMemo, useState } from 'react'
import { perpsStore } from '@/store'
import { EX_UPDATE_LEVERAGE } from '@/api/interface/EX_UPDATE_LEVERAGE'
import { message } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'
import { useMedia } from '@/hooks/useMedia'
import { observer } from 'mobx-react-lite'
import i18n from '@/i18n'
import { ModalOnPopup } from '@/components/ModalOnPopup'
import clsx from 'clsx'

export const PosBtn = observer(() => {
  const { isMd } = useMedia()
  const { activeAsset, activeMeta } = perpsStore.meta
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [leverage, setLeverage] = useState<number>(20)
  const [moveLeverage, setMoveLeverage] = useState<number>(20)

  // 是否为全仓模式
  const isCross = activeAsset?.leverage?.type === 'cross'

  useEffect(() => {
    setLeverage(activeAsset?.leverage?.value)
  }, [activeAsset?.leverage])

  useEffect(() => {
    setMoveLeverage(activeAsset?.leverage?.value)
  }, [open])

  const leverageLoading = useMemo(
    () => !!activeAsset?.leverage?.value || !!leverage,
    [activeAsset?.leverage, leverage],
  )

  async function onConfirm() {
    setLoading(true)
    EX_UPDATE_LEVERAGE({
      asset: activeMeta?.assetid,
      leverage: moveLeverage,
      isCross,
    })
      .then((res) => {
        if (res?.code !== 200) {
          setLoading(false)
          message.error(res?.message)
          return
        }

        const checkLeverage = () => {
          if (perpsStore.meta.activeAsset?.leverage?.value === moveLeverage) {
            setOpen(false)
            setLoading(false)
            setLeverage(moveLeverage)
            message.success(i18n.t('set_leverage_success'))
          } else {
            setTimeout(checkLeverage, 100)
          }
        }
        checkLeverage()
      })
      .catch((err) => {
        setLoading(false)
        message.error(err?.message)
      })
  }

  function onValueChange(value: number[]) {
    setMoveLeverage(value?.[0] ?? 0)
  }

  return (
    <>
      {isMd ? (
        <div
          className="flex cursor-pointer items-center gap-1"
          onClick={() => setOpen(true)}
        >
          {leverageLoading ? (
            <div>{activeAsset?.leverage?.value}x</div>
          ) : (
            <div>
              <LoadingOutlined />
            </div>
          )}
          <DownOutlined />
        </div>
      ) : (
        <ButtonBlack
          className="h-full w-full rounded-sm !bg-[#28292f]"
          onClick={() => setOpen(true)}
        >
          {leverageLoading ? (
            <div>{activeAsset?.leverage?.value}x</div>
          ) : (
            <div>
              <LoadingOutlined />
            </div>
          )}
        </ButtonBlack>
      )}

      <ModalOnPopup
        title={i18n.t('adjust_leverage')}
        visible={open}
        onOk={() => setOpen(false)}
        onClose={() => setOpen(false)}
      >
        <div
          className={clsx('flex flex-col gap-2', isMd ? 'w-full px-4' : 'w-96')}
        >
          <div className="justify-start self-stretch text-sm text-gray-450">
            {i18n.t('adjust_leverage_desc', {
              coin: activeMeta?.name,
              leverage: activeMeta?.maxLeverage,
            })}
          </div>
          <div
            className={clsx(
              'inline-flex items-center justify-start gap-[5px] rounded-sm bg-yellow-500/10 px-2 py-1',
            )}
          >
            <WarningOutlined className="size-3.5 text-red-600" />
            <div className="justify-start text-center text-xs font-normal text-red-600">
              {i18n.t('adjust_leverage_warning')}
            </div>
          </div>
          <div className="my-5 flex items-center gap-4">
            <div className="flex-1">
              <Slider
                value={[moveLeverage]}
                type="number"
                theme={isMd ? 'dark' : 'light'}
                max={activeMeta?.maxLeverage}
                onValueChange={onValueChange}
                onValueCommit={onValueChange}
              />
            </div>
            <div
              className={clsx(
                'w-12 justify-start rounded-sm py-1 text-center text-sm',
                isMd ? 'bg-zinc-800 text-white' : 'bg-gray-100',
              )}
            >
              {moveLeverage}x
            </div>
          </div>
          <ButtonBlack
            loading={loading}
            className={clsx(
              'h-10 w-full',
              isMd && '!bg-white !text-black',
              Number(perpsStore.user.rawBalance.balanceUsd) <= 0 &&
                'cursor-not-allowed !text-black opacity-50',
            )}
            onClick={onConfirm}
            disabled={Number(perpsStore.user.rawBalance.balanceUsd) <= 0}
          >
            <div>
              {Number(perpsStore.user.rawBalance.balanceUsd) > 0
                ? i18n.t('confirm')
                : i18n.t('deposit_required')}
            </div>
          </ButtonBlack>
        </div>
      </ModalOnPopup>
    </>
  )
})
