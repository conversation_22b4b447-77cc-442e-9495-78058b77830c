import { useTools } from '@/pages/Perp/hooks/useTools'
import { perpsStore } from '@/store'
import { observer } from 'mobx-react-lite'
import { ViewRow, ViewItem, ViewValue } from '@/pages/Perp/module/styles'
import { BuyText, SellText } from '@/components/ColoringText'
import i18n from '@/i18n'
import { useMedia } from '@/hooks/useMedia'
import { useOfficial } from '@/pages/Perp/module/openOrder/useOfficial'

export const Position = observer(() => {
  const { coin } = perpsStore.init
  const { assetPosition } = perpsStore.user
  const { hlFormatCoin } = useTools()
  const { availableUsd } = useOfficial()
  const { isMd } = useMedia()

  return (
    <div className="flex w-full flex-col gap-1">
      <ViewRow>
        <ViewItem>{i18n.t('available')}</ViewItem>
        <ViewValue>${availableUsd}</ViewValue>
      </ViewRow>
      {!isMd && (
        <ViewRow>
          <ViewItem>{i18n.t('current_position')}</ViewItem>
          <ViewValue>
            {assetPosition?.side === 'B' ? (
              <BuyText>
                {hlFormatCoin(assetPosition?.value)} {coin.toUpperCase()}
              </BuyText>
            ) : assetPosition?.side === 'A' ? (
              <SellText>
                {hlFormatCoin(assetPosition?.value)} {coin.toUpperCase()}
              </SellText>
            ) : (
              <>
                {hlFormatCoin(assetPosition?.value)} {coin.toUpperCase()}
              </>
            )}
          </ViewValue>
        </ViewRow>
      )}
    </div>
  )
})
