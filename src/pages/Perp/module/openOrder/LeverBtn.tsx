import { EX_UPDATE_LEVERAGE } from '@/api/interface/EX_UPDATE_LEVERAGE'
import { ButtonBlack } from '@/components/Customize/Button'
import { TabsTheme } from '@/components/Customize/Tabs'
import { useMedia } from '@/hooks/useMedia'
import i18n from '@/i18n'
import { perpsStore } from '@/store'
import { THlPerpType } from '@/store/perps/types'
import { message, TabsProps } from 'antd'
import { observer } from 'mobx-react-lite'
import { useEffect, useState } from 'react'
import { DownOutlined } from '@ant-design/icons'
import { ModalOnPopup } from '@/components/ModalOnPopup'
import clsx from 'clsx'

export const LeverBtn = observer(() => {
  const { isMd } = useMedia()
  const { coin } = perpsStore.init
  const { activeMeta, activeAsset } = perpsStore.meta
  const { positionOrders } = perpsStore.order
  const [type, setType] = useState<THlPerpType>(
    activeAsset?.leverage?.type || 'cross',
  )
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)

  const isCross = activeAsset?.leverage?.type === 'cross'

  useEffect(() => {
    setType(activeAsset?.leverage?.type || 'cross')
  }, [activeAsset?.leverage?.type, open])

  const onChange = (key: string) => {
    setType(key as THlPerpType)
  }

  async function onConfirm() {
    const sheet = positionOrders?.filter(
      (order) => order?.position?.coin === coin,
    )

    if (sheet?.length > 0) {
      message.error(i18n.t('cannot_switch_leverage_in_position'))
      return
    }

    setLoading(true)
    EX_UPDATE_LEVERAGE({
      asset: activeMeta?.assetid,
      leverage: activeAsset?.leverage?.value,
      isCross: type === 'cross',
    })
      .then((res) => {
        if (res?.code !== 200) {
          message.error(res?.message)
          return
        }
        setOpen(false)
        message.success(i18n.t('switch_leverage_success'))
      })
      .catch((err) => {
        message.error(err?.message)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const items: TabsProps['items'] = [
    {
      key: 'cross',
      label: i18n.t('cross'),
      children: <CrossContent />,
    },
    {
      key: 'isolated',
      label: i18n.t('isolated'),
      children: <IsolatedContent />,
    },
  ]

  return (
    <>
      {isMd ? (
        <div
          className="flex cursor-pointer items-center gap-1"
          onClick={() => setOpen(true)}
        >
          {isCross ? i18n.t('cross') : i18n.t('isolated')}
          <DownOutlined />
        </div>
      ) : (
        <ButtonBlack
          className="h-full w-full rounded-sm !bg-[#28292f]"
          onClick={() => setOpen(true)}
        >
          <div>{isCross ? i18n.t('cross') : i18n.t('isolated')}</div>
        </ButtonBlack>
      )}

      <ModalOnPopup
        title={i18n.t('margin_mode')}
        visible={open}
        onOk={() => setOpen(false)}
        onClose={() => setOpen(false)}
      >
        <div
          className={clsx('flex flex-col gap-2', isMd ? 'w-full px-4' : 'w-96')}
        >
          <div className="">
            <TabsTheme
              theme={isMd ? 'light' : 'dark'}
              activeKey={type}
              defaultActiveKey={type}
              centered
              items={items}
              onChange={onChange}
            />
          </div>

          <ButtonBlack
            className={clsx(
              'h-10 w-full',
              isMd && '!bg-white !text-black',
              Number(perpsStore.user.rawBalance.balanceUsd) <= 0 &&
                'cursor-not-allowed !text-black opacity-50',
            )}
            loading={loading}
            onClick={onConfirm}
            disabled={Number(perpsStore.user.rawBalance.balanceUsd) <= 0}
          >
            <div>
              {Number(perpsStore.user.rawBalance.balanceUsd) > 0
                ? i18n.t('confirm')
                : i18n.t('deposit_required')}
            </div>
          </ButtonBlack>
        </div>
      </ModalOnPopup>
    </>
  )
})

function CrossContent() {
  const { isMd } = useMedia()
  return (
    <div
      className={clsx(
        'justify-start self-stretch py-5 text-sm font-normal leading-tight',
        isMd ? 'text-gray-450' : 'text-gray-600',
      )}
    >
      {i18n.t('cross_desc')}
    </div>
  )
}

function IsolatedContent() {
  const { isMd } = useMedia()
  return (
    <div
      className={clsx(
        'justify-start self-stretch py-5 text-sm font-normal leading-tight',
        isMd ? 'text-gray-450' : 'text-gray-600',
      )}
    >
      {i18n.t('isolated_desc')}
    </div>
  )
}
