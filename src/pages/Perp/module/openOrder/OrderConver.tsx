import { perpsStore } from '@/store'
import { $ten, Decimal } from '@/utils'
import { observer } from 'mobx-react-lite'
import { ViewRow, ViewItem, ViewValue } from '@/pages/Perp/module/styles'
import { usePerpOpenOrder } from '@/pages/Perp/module/openOrder/context'
import { useOfficial } from '@/pages/Perp/module/openOrder/useOfficial'
import { useMemo } from 'react'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { useOrder } from '@/pages/Perp/hooks/useOrder'
import i18n from '@/i18n'
import { useMedia } from '@/hooks/useMedia'
import {
  maxToMaintenanceLeverage,
  getIsolatedLiquidationPrice,
  getCrossLiquidationPrice,
} from '@/pages/Perp/module/calculate'
import { IsolatedLeverage } from '@/store/perps/types'

export const OrderConver = observer(() => {
  const { isMd } = useMedia()
  const { accountValue, maintenanceMarginUsd, assetPosition } =
    perpsStore.user || {}
  const { activeMeta, activeAsset } = perpsStore.meta
  const { bestPrice } = perpsStore.book
  const { coin, coinType } = perpsStore.init
  const { userFees } = perpsStore.fee || {}

  const { amount, mode, side, price } = usePerpOpenOrder()
  const { convertAmount } = useOfficial()
  const { hlFormatNumber, hlFormatPrice, calculateCoinPrice } = useTools()
  const { order } = useOrder({ coin })

  // 标记价格
  const markPx = useMemo(
    // () => (side === 'B' ? bestPrice.bid : bestPrice.offer) ?? 0,
    () => activeMeta?.markPx ?? 0,
    [side, bestPrice, activeMeta],
  )

  // 标的价格
  const markPrice = useMemo(() => {
    return mode === 'limit' ? (price ?? 0) : (markPx ?? 0)
  }, [coinType, markPx, mode, price])

  // 当前币种最大杠杆
  const maxLeverage = useMemo(() => activeMeta?.maxLeverage, [activeMeta])

  // 订单价值 = 订单数量 * 标记价格
  const orderValue = useMemo(() => {
    let result = '0'
    if (!amount || !markPrice) return result
    if (coinType === 'usdc') {
      result = $ten.toFixed($ten.mul(convertAmount(amount), markPrice), 2)
    }
    if (coinType === 'coin') {
      result = $ten.toFixed($ten.mul(amount, markPrice), 2)
    }
    return result
  }, [amount, markPrice, coinType, convertAmount])

  // 所需保证金 = 订单价值 / 杠杆
  const requiredMargin = useMemo(() => {
    let result = '0'
    if (!orderValue || !activeAsset?.leverage?.value) return result
    result = $ten.toFixed($ten.div(orderValue, activeAsset?.leverage?.value), 2)
    return result
  }, [orderValue, activeAsset?.leverage?.value])

  // 获取当前仓位的爆仓价
  const currentLiqPrice = useMemo(() => {
    if (!amount) {
      return !order?.liquidationPx ? null : order.liquidationPx
    } else {
      return null
    }
  }, [order, amount])

  // 是否为逐仓模式
  const isolated = useMemo(
    () => activeAsset?.leverage?.type === 'isolated',
    [activeAsset?.leverage],
  )

  // 订单方向 = 买多 = 1；买空 = -1;
  const floatSide = useMemo(() => (side === 'B' ? 1 : -1), [side])

  // 当前持仓
  const positionSzi = useMemo(() => assetPosition?.value, [assetPosition])

  // 用户预计下单持仓
  const userSz = useMemo(() => {
    let result = '0'
    if (!amount || !markPrice) return result
    if (coinType === 'usdc') result = convertAmount(amount)
    if (coinType === 'coin') result = amount
    return result
  }, [amount, markPrice, coinType, floatSide, convertAmount])

  // 最终的总持仓 = 当前持仓 + (订单方向 * 用户预计下单持仓)
  const updatedPosition = useMemo(() => {
    return $ten.add(positionSzi, $ten.mul(floatSide, userSz))
  }, [positionSzi, floatSide, userSz])

  // 最终的总持仓绝对值
  const absUpdatedPosition = useMemo(
    () => $ten.abs(updatedPosition),
    [updatedPosition],
  )

  // 剩余维持保证金 = 账户价值 - 维持保证金 - (总持仓绝对值 * 标记价格)
  const crossMaintenanceMarginRemaining = useMemo(() => {
    const s = $ten.sub(accountValue, maintenanceMarginUsd)
    const t = $ten.div(
      $ten.mul($ten.abs(positionSzi), markPx),
      maxToMaintenanceLeverage(maxLeverage),
    )
    return $ten.add(s, t)
  }, [accountValue, maintenanceMarginUsd, markPx, maxLeverage])

  // 修改的标记价格
  const midPrice = useMemo(() => {
    if (!price) return markPx
    const isBuyOrder = side === 'B'
    return Number(price) > Number(markPx) !== isBuyOrder ? price : markPx
  }, [price, markPx, side])

  // 总持仓价值 = 订单价格 * 总持仓绝对值
  const totalNtlPos = useMemo(() => {
    return $ten.mul(midPrice, absUpdatedPosition)
  }, [price, midPrice, absUpdatedPosition, side])

  // 计算理论强平价格
  const liquiPrice = useMemo(() => {
    // 如果当前仓位的爆仓价存在，则直接返回当前仓位的爆仓价
    if (currentLiqPrice) return currentLiqPrice

    let result = null
    if (isolated) {
      result = getIsolatedLiquidationPrice(
        Number(midPrice),
        floatSide,
        activeAsset?.leverage as IsolatedLeverage,
        Number(positionSzi),
        Number(userSz),
        Number(totalNtlPos),
        Number(updatedPosition),
        maxLeverage,
      )
    } else {
      const leverage = activeAsset?.leverage?.value ?? 0

      result = getCrossLiquidationPrice(
        Number(midPrice),
        floatSide,
        Math.max(
          Number(crossMaintenanceMarginRemaining),
          Number(totalNtlPos) / leverage,
        ),
        Number(totalNtlPos),
        Number(absUpdatedPosition),
        maxLeverage,
      )
    }

    if (result === null) return '0'
    const coinPrecision = calculateCoinPrice(String(result))
    return $ten.toFixed(result, coinPrecision, true)
  }, [
    isolated,
    midPrice,
    floatSide,
    userSz,
    maxLeverage,
    currentLiqPrice,
    activeAsset,
    totalNtlPos,
    updatedPosition,
    absUpdatedPosition,
    crossMaintenanceMarginRemaining,
  ])

  return (
    <div className="flex w-full flex-col gap-2">
      <ViewRow>
        <ViewItem>{i18n.t('liquidation_price')}</ViewItem>
        <ViewValue>
          {Number(liquiPrice)
            ? hlFormatPrice({ price: String(liquiPrice) })
            : 'N/A'}
        </ViewValue>
      </ViewRow>
      <ViewRow>
        <ViewItem>{i18n.t('order_price')}</ViewItem>
        <ViewValue>
          {Number(orderValue) ? `$${hlFormatNumber(orderValue)}` : 'N/A'}
        </ViewValue>
      </ViewRow>
      <ViewRow>
        <ViewItem>{i18n.t('required_margin')}</ViewItem>
        <ViewValue>
          {Number(requiredMargin)
            ? `$${hlFormatNumber(requiredMargin)}`
            : 'N/A'}
        </ViewValue>
      </ViewRow>
      {mode === 'market' && (
        <ViewRow>
          <ViewItem>{i18n.t('slippage')}</ViewItem>
          <ViewValue>{`${$ten.toFixed(perpsStore.init.slippage, 2)}%`}</ViewValue>
        </ViewRow>
      )}
      {!isMd && (
        <ViewRow>
          <ViewItem>{i18n.t('fee')}</ViewItem>

          <ViewValue>
            {!userFees?.userCrossRate || !userFees?.userAddRate
              ? `N/A`
              : `${userFees?.userCrossRate}% / ${userFees?.userAddRate}%`}
          </ViewValue>
        </ViewRow>
      )}
    </div>
  )
})
