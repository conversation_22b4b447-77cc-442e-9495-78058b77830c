import { InputNumberPerp } from '@/components/Customize/InputNumber'
import i18n from '@/i18n'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { useTpsl } from '@/pages/Perp/hooks/useTpsl'
import { DropdownAfter } from '@/pages/Perp/module/openOrder/DropdownAfter'
import { TPerpNumber } from '@/pages/Perp/module/types'
import { perpsStore } from '@/store'
import { TCoinType, TSide } from '@/store/perps/types'
import { $ten, isBlank } from '@/utils'
import { observer } from 'mobx-react-lite'
import { useEffect, useMemo, useState } from 'react'

export const TLoss = observer(
  ({
    theme,
    amount,
    price,
    side,
    leverage,
    szDecimals,
    coin,
    coinType,
    disabled,
    onPriceChange,
  }: {
    // 主题
    theme?: 'dark' | 'light'
    // 下单数量
    amount: TPerpNumber
    // 预期开仓价格
    price: string
    // 持仓方向
    side: TSide
    // 杠杆
    leverage: number
    // 币种数量精度
    szDecimals: number
    // 交易对
    coin: string
    // 交易货币类型
    coinType: TCoinType
    // 是否禁用止损输入
    disabled: boolean
    // 回调
    onPriceChange: (lossPrice: TPerpNumber) => void
  }) => {
    const { tpslCoinType } = perpsStore.init
    const { hlFormatPrice } = useTools()
    const { priceToPct, pctToPrice, priceToAmount, amountToPrice } = useTpsl()

    // 止损价格
    const [lossPrice, setLossPrice] = useState<TPerpNumber>(null)
    // 止损比例 / 金额
    const [lossPct, setLossPct] = useState<TPerpNumber>(null)
    // 记录是否修正过止损价格
    const [isLossFocused, setIsLossFocused] = useState(false)

    // 是否是亮色主题
    const isLight = theme === 'light'
    // 是否是买单
    const isBid = side === 'B'
    // 是否是卖单
    const isAsk = side === 'A'
    // 是否是金额
    const isAmount = useMemo(() => tpslCoinType === 'amount', [tpslCoinType])

    useEffect(() => {
      onPriceChange && onPriceChange(lossPrice)
    }, [lossPrice])

    useEffect(() => {
      setTimeout(() => {
        setLossPrice(null)
        setLossPct(null)
      })
    }, [tpslCoinType, coin, side])

    useEffect(() => {
      const isPct = tpslCoinType === 'percentage'
      const isAmount = tpslCoinType === 'amount'
      if (lossPct && isAmount) {
        setLossPrice(
          amountToPrice({
            exitAmount: lossPct,
            entryPx: price,
            amount,
            coinType,
            side,
            szDecimals,
            direction: isBid ? '-1' : '1',
          }),
        )
      }

      if (lossPct && isPct) {
        setLossPrice(
          pctToPrice({
            roePct: lossPct,
            entryPx: price,
            side,
            leverage,
            szDecimals,
            direction: isBid ? '1' : '-1',
          }),
        )
      }
    }, [amount, price])

    useEffect(() => {
      if (lossPct && price && isAmount && !isLossFocused) {
        onLossAmountChange(lossPct, 'auto')
      }
    }, [price, lossPct, isAmount, isLossFocused])

    function onLossPriceChange(value: TPerpNumber) {
      setLossPrice(value)
      setIsLossFocused(true)

      try {
        const exitPx = value // 止损价格
        const entryPx = price // 开仓均价
        const direction = isBid ? '-1' : '1'

        if (isAmount) {
          setLossPct(
            $ten.toFixed(
              priceToAmount({
                exitPx,
                entryPx,
                amount,
                coinType,
                side,
                direction,
              }) || '0',
              0,
            ),
          )
        } else {
          setLossPct(priceToPct({ exitPx, entryPx, direction, leverage }))
        }
      } catch (error) {
        console.error(error)
      }
    }

    function onLossAmountChange(value: TPerpNumber, type: 'auto' | 'manual') {
      if (type === 'manual') {
        setLossPct(value)
        setIsLossFocused(false)
      }

      try {
        const entryPx = price
        const direction = isBid ? '1' : '-1'

        if (isAmount) {
          setLossPrice(
            amountToPrice({
              exitAmount: value,
              entryPx,
              amount,
              coinType,
              side,
              szDecimals,
              direction,
            }),
          )
        } else {
          setLossPrice(
            pctToPrice({
              roePct: value,
              entryPx,
              side,
              leverage,
              szDecimals,
              direction,
            }),
          )
        }
      } catch (error) {
        console.error(error)
      }
    }

    function handleLossPriceBlur(e: React.FocusEvent<HTMLInputElement>) {
      if (isBlank(e.target.value)) return

      const value = hlFormatPrice({
        price: e.target.value,
        szDecimals,
        isSimple: true,
      })

      setLossPrice(value)
    }

    return (
      <div className="flex w-full items-center justify-between gap-2">
        <div className="flex-1">
          <InputNumberPerp
            theme={isLight ? 'light' : 'dark'}
            value={lossPrice}
            onChange={onLossPriceChange}
            onBlur={handleLossPriceBlur}
            className="w-full"
            prefix={
              <span className="text-10 text-[#9293a0]">
                {i18n.t('tpsl_loss_price')}
              </span>
            }
            disabled={disabled}
          />
        </div>
        <div className="flex-1">
          <InputNumberPerp
            theme={isLight ? 'light' : 'dark'}
            value={lossPct}
            onChange={(value) => onLossAmountChange(value, 'manual')}
            className="w-full"
            precision={2}
            prefix={
              <span className="text-10 text-[#9293a0]">
                {i18n.t('lossPct')}
              </span>
            }
            addonAfter={<DropdownAfter theme={theme} />}
            disabled={disabled}
          />
        </div>
      </div>
    )
  },
)
