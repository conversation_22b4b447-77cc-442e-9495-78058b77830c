import { EX_PLACE_ORDER } from '@/api/interface/EX_PLACE_ORDER'
import {
  ButtonPrimaryDown,
  ButtonPrimaryStatus,
  ButtonPrimaryUp,
} from '@/components/Customize/Button'
import { useRoute } from '@/hooks/useRoute'
import i18n from '@/i18n'
import { usePerpForm } from '@/pages/Perp/hooks/usePerpForm'
import { useTools } from '@/pages/Perp/hooks/useTools'
import { usePerp } from '@/pages/Perp/module/contPerp/usePerp'
import { usePerpOpenOrder } from '@/pages/Perp/module/openOrder/context'
import { useOfficial } from '@/pages/Perp/module/openOrder/useOfficial'
import { TPerpSubmitOrder } from '@/pages/Perp/module/types'
import { perpsStore } from '@/store'
import { walletStore } from '@/store/wallet/wallet'
import { observer } from 'mobx-react-lite'
import { useMemo, useState } from 'react'
import { Hex } from 'viem'

export const FormBtn = observer(() => {
  const { coinType, coin } = perpsStore.init
  const { bestPrice } = perpsStore.book
  const { activeMeta } = perpsStore.meta
  const { assetPosition } = perpsStore.user

  const {
    mode,
    side,
    price,
    amount,
    orderType,
    dealManage,
    profitPrice,
    lossPrice,
  } = usePerpOpenOrder()
  const { availableUsd, maxAmount, convertAmount } = useOfficial()
  const { calculateSlippagePrice } = usePerpForm({
    mode,
    szDecimals: activeMeta?.szDecimals,
  })

  const { filterZero, hlFormatPrice } = useTools()
  const { openNotification, setShowTopUpModal } = usePerp()
  const { query } = useRoute()
  const [loading, setLoading] = useState(false)

  // 是否勾选了只减仓
  const isReduce = useMemo(() => dealManage === 'reduce', [dealManage])
  // 是否勾选了止盈止损
  const isTrigger = useMemo(() => dealManage === 'profitLoss', [dealManage])

  // 是否需要开启充值弹窗
  const needShowTopUpModal = useMemo(() => {
    // 是否为市价单
    const isMarket = mode === 'market'
    // 是否为限价单
    const isLimit = mode === 'limit'

    const isUsd = Number(availableUsd) <= 0
    const isCoin = Number(assetPosition?.value) <= 0

    if (isReduce) {
      return isUsd && isCoin
    }

    if (!Number(maxAmount ?? 0)) return true

    if (isMarket) {
      return !(Number(amount) <= Number(maxAmount))
    }

    if (isLimit) {
      return !(Number(amount) <= Number(maxAmount))
    }

    return isUsd
  }, [availableUsd, assetPosition, isReduce, mode, amount, price, maxAmount])

  // 是否禁用
  const disabled = useMemo(() => {
    // 是否为市价单
    const isMarket = mode === 'market'
    // 是否为限价单
    const isLimit = mode === 'limit'

    if (isReduce && assetPosition?.side === side) {
      return true
    }

    if (isMarket) {
      if (!Number(amount ?? 0)) return true
      return !(Number(amount) <= Number(maxAmount))
    }

    if (isLimit) {
      if (!Number(price ?? 0)) return true
      if (!Number(amount ?? 0)) return true
      return !(Number(amount) <= Number(maxAmount))
    }

    return false
  }, [amount, price, maxAmount, mode, side, isReduce, assetPosition])

  // 订单数量
  const orderAmount = useMemo(() => {
    if (!amount) return '0'
    if (coinType === 'coin') return amount

    return filterZero(convertAmount(amount))
  }, [amount, convertAmount, coinType, bestPrice])

  async function onFormBtnClick() {
    if (disabled || loading) return

    setLoading(true)

    const isBid = side === 'B'
    const isMarket = mode === 'market'
    const isLimit = mode === 'limit'

    if (isMarket) {
      openNotification('form', 'loading', i18n.t('form_loading'))
    }
    if (isLimit) {
      openNotification('form', 'loading', i18n.t('form_loading_limit'))
    }

    try {
      const orders = getParamsOrders()

      const res = await EX_PLACE_ORDER({
        orders,
        grouping: isTrigger ? 'normalTpsl' : 'na',
        roomId: query?.roomId,
      })

      if (res?.code !== 200) {
        openNotification('form', 'error', res?.message)
        return
      }

      if (isMarket) {
        openNotification(
          'form',
          'success',
          <div className="flex flex-col">
            <div>{i18n.t('market_order_success')}:</div>
            <div>
              {i18n.t('market_order_success_info', {
                amount: res?.data?.totalSz,
                coin: coin.toUpperCase(),
                isLong: isBid ? i18n.t('buy_long') : i18n.t('sell_short'),
                avgPrice: hlFormatPrice({ price: res?.data?.avgPx }),
              })}
            </div>
          </div>,
        )
      }

      if (isLimit) {
        openNotification('form', 'success', i18n.t('limit_order_success'))
      }

      // 重新调用仓位接口，更新仓位信息
      try {
        // 获取当前钱包地址
        const address = walletStore.arbitrumAddress
        if (address) {
          await perpsStore.order.getPositionOrders(address as Hex)
          await perpsStore.order.getPendingOrder(address as Hex)
        }
      } catch (error) {
        console.error('更新仓位信息失败:', error)
      }
    } catch (err: any) {
      console.error(err)
      openNotification('form', 'error', err?.message || i18n.t('order_failed'))
    } finally {
      setLoading(false)
    }
  }

  // 请求数据整理
  function getParamsOrders(): TPerpSubmitOrder[] {
    const orders: TPerpSubmitOrder[] = []

    // 是否为买单
    const isBid = side === 'B'
    // 是否为市价单
    const isMarket = mode === 'market'
    // 是否为限价单
    const isLimit = mode === 'limit'

    // 部分数据
    const before = {
      // 资产id
      a: activeMeta?.assetid,
      // 购买数量
      s: orderAmount,
    }

    // 市价订单
    if (isMarket) {
      const price = isBid ? bestPrice?.bid : bestPrice?.offer
      const slippagePrice = calculateSlippagePrice(price, side, false)

      orders.push({
        ...before,
        // 是否为买单
        b: isBid,
        // 触发价格(下单价格)
        p: slippagePrice,
        // 是否只挂单
        r: isReduce,
        t: { limit: { tif: 'FrontendMarket' } },
      })
    }

    // 限价订单
    if (isLimit) {
      const slippagePrice = calculateSlippagePrice(price || 0, side, false)

      orders.push({
        ...before,
        // 是否为买单
        b: isBid,
        // 触发价格(下单价格)
        p: slippagePrice,
        // 是否只挂单
        r: isReduce,
        t: { limit: { tif: orderType } },
      })
    }

    // 带止损的订单
    if (isTrigger && !!lossPrice) {
      const triggerPx = filterZero(lossPrice)
      const price = calculateSlippagePrice(lossPrice, side, isTrigger)

      orders.push({
        ...before,
        // 是否为买单，方向和普通订单相反
        b: !isBid,
        // 触发价格(下单价格)
        p: price,
        // 是否只挂单
        r: true,
        t: { trigger: { isMarket: true, triggerPx, tpsl: 'sl' } },
      })
    }

    // 带止盈的订单
    if (isTrigger && !!profitPrice) {
      const triggerPx = filterZero(profitPrice)
      const price = calculateSlippagePrice(triggerPx, side, isTrigger)

      orders.push({
        ...before,
        // 是否为买单，方向和普通订单相反
        b: !isBid,
        // 触发价格(下单价格)
        p: price,
        // 是否只挂单
        r: true,
        t: { trigger: { isMarket: true, triggerPx, tpsl: 'tp' } },
      })
    }

    return orders
  }

  // 判断是否可用余额不够
  if (needShowTopUpModal) {
    return side === 'B' ? (
      <ButtonPrimaryUp
        onClick={() => setShowTopUpModal(true)}
        className="h-9 w-full rounded-sm"
      >
        <div className="text-xs">{i18n.t('top_up')}</div>
      </ButtonPrimaryUp>
    ) : (
      <ButtonPrimaryDown
        onClick={() => setShowTopUpModal(true)}
        className="h-9 w-full rounded-sm"
      >
        <div className="text-xs">{i18n.t('top_up')}</div>
      </ButtonPrimaryDown>
    )
  }

  // 是否因为只减仓导致余额不够
  if (isReduce && disabled) {
    return (
      <ButtonPrimaryStatus disabled className="h-9 w-full rounded-sm">
        <div className="text-xs">{i18n.t('reduce_too_large')}</div>
      </ButtonPrimaryStatus>
    )
  }

  return side === 'B' ? (
    <ButtonPrimaryUp
      loading={loading}
      disabled={disabled}
      onClick={onFormBtnClick}
      className="h-9 w-full rounded-sm"
    >
      <div className="text-xs">{i18n.t('buy_long')}</div>
    </ButtonPrimaryUp>
  ) : (
    <ButtonPrimaryDown
      loading={loading}
      disabled={disabled}
      onClick={onFormBtnClick}
      className="h-9 w-full rounded-sm"
    >
      <div className="text-xs">{i18n.t('sell_short')}</div>
    </ButtonPrimaryDown>
  )
})
