import { IsolatedLeverage } from '@/store/perps/types'

/**
 * 逐仓强平价格
 **/
export function getIsolatedLiquidationPrice(
  // 标记价格
  mid: number,
  // 交易方向「买=1；卖=-1；」
  floatSide: 1 | -1,
  // 杠杆信息
  leverage: IsolatedLeverage,
  // 当前持仓
  positionSzi: number,
  // 用户预计的下单数量
  userSz: number,
  // 根据 userSz，计算的当前总持仓价值
  totalNtlPos: number,
  // 更新后的持仓数量
  updatedPosition: number,
  // 最大杠杆
  maxLeverage: number,
): number | null {
  let userSzi = floatSide * userSz
  let rawUsd = Number(leverage.rawUsd)
  {
    const isTradeLong = userSzi > 0
    const isPosLong = positionSzi > 0
    const isOffsetting = isTradeLong !== isPosLong
    if (positionSzi !== 0 && isOffsetting) {
      const decreaseSz = Math.min(Math.abs(userSzi), Math.abs(positionSzi))
      const decreaseSzi = userSzi < 0 ? -decreaseSz : decreaseSz
      const originalPosAbs = Math.abs(positionSzi)
      const ntli = mid * positionSzi
      const adjustment = (rawUsd + ntli) * (decreaseSz / originalPosAbs)

      rawUsd -= adjustment

      userSzi -= decreaseSzi
      positionSzi += decreaseSzi
      rawUsd -= mid * decreaseSzi
    }
  }

  {
    const isTradeLong = userSzi > 0
    const isPosLong = positionSzi > 0
    const isIncreasingPos = isTradeLong === isPosLong
    if (positionSzi === 0 || isIncreasingPos) {
      const ntl = Math.abs(mid * userSzi)
      const margin = ntl / leverage.value
      rawUsd += margin
      positionSzi += userSzi
      rawUsd -= mid * userSzi
    }
  }

  if (positionSzi === 0) {
    rawUsd = 0
  }
  const ntli = updatedPosition * mid
  const accountValue = ntli + rawUsd
  const updatedPosSideFloat = updatedPosition > 0 ? 1.0 : -1.0
  const correction = 1 - floatSide / maxToMaintenanceLeverage(maxLeverage)
  const liquidationPrice =
    mid -
    (updatedPosSideFloat *
      (accountValue - totalNtlPos / maxToMaintenanceLeverage(maxLeverage))) /
      Math.abs(updatedPosition) /
      correction

  if (
    liquidationPrice <= 0 ||
    liquidationPrice > 1e15 ||
    updatedPosition === 0
  ) {
    return null
  } else {
    return liquidationPrice
  }
}

/**
 * 全仓强平价格
 **/
export function getCrossLiquidationPrice(
  // 标记价格
  markPx: number,
  // 交易方向「买=1；卖=-1；」
  floatSide: 1 | -1,
  // 当前账户价值
  liveAccountValue: number,
  // 根据 userSz，计算的当前总持仓价值
  totalNtlPos: number,
  // 更新后的持仓数量
  absPosition: number,
  // 最大杠杆
  maxLeverage: number,
): number | null {
  const correction = 1 - floatSide / maxToMaintenanceLeverage(maxLeverage)
  const liquidationPrice =
    markPx -
    (floatSide *
      (liveAccountValue -
        totalNtlPos / maxToMaintenanceLeverage(maxLeverage))) /
      absPosition /
      correction

  if (liquidationPrice <= 0 || liquidationPrice > 1e15 || absPosition === 0) {
    return null
  } else {
    return liquidationPrice
  }
}

/**
 * 最大杠杆转换为维持保证金
 * @param maxLeverage 最大杠杆
 * @returns 维持保证金
 **/
export function maxToMaintenanceLeverage(maxLeverage: number): number {
  return maxLeverage * 2
}
