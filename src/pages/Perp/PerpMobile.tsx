import {
  ButtonPrimaryUp,
  ButtonPrimaryDown,
  ButtonBlack,
} from '@/components/Customize/Button'
import { PopupDown } from '@/components/PopupDown'
import { SafeAreaApp } from '@/components/SafeAreaApp'
import i18n from '@/i18n'
import { SvgIconBlank } from '@/imgs/icons'
import { usePerp } from '@/pages/Perp/module/contPerp/usePerp'
import { ModOrderTabs } from '@/pages/Perp/module/ModOrderTabs'
import { ModTopTabs } from '@/pages/Perp/module/ModTopTabs'
import { ModOpenOrder } from '@/pages/Perp/module/openOrder'
import { Overview } from '@/pages/Perp/module/Overview'
import { PerpHeadMobile } from '@/pages/Perp/module/PerpHeadMobile'
import clsx from 'clsx'
import { observer } from 'mobx-react-lite'
import { useState } from 'react'
import { ModOrderBookMobile } from '@/pages/Perp/module/ModOrderBookMobile'
import { useRoute } from '@/hooks/useRoute'

export const PerpMobile = observer(() => {
  const { setShowTopUpModal, setSideView } = usePerp()
  const { location } = useRoute()
  const [isAccount, setIsAccount] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)

  function onBlank() {
    setIsAccount(true)
  }

  function onBuyLong() {
    setSideView({ side: 'B', round: Math.random() })
    setIsModalOpen(true)
  }

  function onSellShort() {
    setSideView({ side: 'A', round: Math.random() })
    setIsModalOpen(true)
  }

  return (
    <>
      <div
        className={clsx(
          'overflow-x-auto overflow-y-hidden no-scroll',
          isModalOpen ? 'h-[calc(100vh-132px)]' : 'h-[calc(100vh-132px-48px)]',
        )}
      >
        {/* 头部固定高度 */}
        <div className="min-h-10 border-b border-[#303030] px-3 py-2">
          <PerpHeadMobile
            isModalOpen={isModalOpen}
            setIsModalOpen={setIsModalOpen}
          />
        </div>
        {/* 交易区域 */}
        <div className={clsx('w-full', !isModalOpen && 'hidden')}>
          {/*下单区域*/}
          <div className="flex w-full justify-between gap-1 p-2">
            <div className="w-36 text-xs">
              <ModOrderBookMobile />
            </div>
            <div className="flex-1">
              <ModOpenOrder />
            </div>
          </div>
        </div>
        <div
          className={clsx(
            'h-[calc(100%-200px)] w-full',
            isModalOpen && 'hidden',
          )}
        >
          {/* 图表/订单簿/最新成交区域 */}
          <ModTopTabs />
        </div>

        {/* 订单区域 */}
        <div className="border-r border-[#303030]">
          <ModOrderTabs />
        </div>
        {/* 操作区域 */}
        {!isModalOpen ? (
          <div
            className={`fixed left-0 w-full bg-[#131313] ${location.pathname === '/trade' ? 'bottom-16 z-0' : 'bottom-0 z-50'}`}
          >
            <div className="flex h-7 items-center justify-between gap-2">
              <div
                className="flex h-full items-center justify-center bg-[#28292F] px-2"
                onClick={onBlank}
              >
                <SvgIconBlank className="w-4" />
              </div>
              <ButtonPrimaryUp
                className="h-full w-full rounded-sm"
                onClick={onBuyLong}
              >
                <div className="text-xs">{i18n.t('buy_long')}</div>
              </ButtonPrimaryUp>
              <ButtonPrimaryDown
                className="h-full w-full rounded-sm"
                onClick={onSellShort}
              >
                <div className="text-xs">{i18n.t('sell_short')}</div>
              </ButtonPrimaryDown>
            </div>
            <SafeAreaApp />
          </div>
        ) : null}
      </div>
      <PopupDown
        label={i18n.t('perp_overview')}
        visible={isAccount}
        onClose={() => setIsAccount(false)}
      >
        <div className="w-full px-4">
          <Overview />
          <ButtonBlack
            className="mt-4 h-10 w-full !bg-white !text-black"
            onClick={() => {
              setShowTopUpModal(true)
              setIsAccount(false)
            }}
          >
            <div>{i18n.t('top_up')}</div>
          </ButtonBlack>
        </div>
      </PopupDown>
    </>
  )
})
