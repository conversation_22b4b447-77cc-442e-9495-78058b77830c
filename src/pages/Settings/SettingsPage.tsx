import { useEffect, useState, useMemo } from 'react'
import { useRoute } from '@/hooks/useRoute'
import { IconLogout, SvgIconProfileBack, SvgIconArrowRight } from '@/imgs/icons'
import { Dropdown, message } from 'antd'
import { observer } from 'mobx-react-lite'
import i18n from '@/i18n'
import '@/pages/User/css/user.css'
import type { MenuProps } from 'antd'
import { chatroomManager } from '@/managers/chatroomManager'
import { setupStore, userStore } from '@/store'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { usePrivy } from '@privy-io/react-auth'
import {
  defaultLang,
  langRouting,
  supportedLanguage,
} from '@/config/default/setting.config'
import './css/settings.css'
import { ButtonSecondary } from '@/components/Customize/Button'
import PKSwitch from '@/components/Customize/PKSwtich'
import { USERINFO, UPDATE_SETTING } from '@/api/interface/USERINFO'
import { INoticeBroadcastTransactions } from '@/api/interface/QUERY_USERS_INFO'
import { HTTP_CODE } from '@/api/_http'

export const Component = observer(() => {
  const store = useStore()
  const { logout } = usePrivy()
  const { path, goBack } = useRoute()
  const [currentLang, setCurrentLang] = useState(i18n.language || defaultLang)
  const [noticeBroadcastTransactions, setNoticeBroadcastTransactions] =
    useState<INoticeBroadcastTransactions>(null)
  const [isBroadcastLoading, setIsBroadcastLoading] = useState(false)
  const [isInitializing, setIsInitializing] = useState(true)

  // 转换为 boolean 用于 UI 显示
  const broadcastTrades = useMemo(() => {
    return noticeBroadcastTransactions === 0 ? false : true
  }, [noticeBroadcastTransactions])

  // 获取用户设置
  const fetchUserSettings = async () => {
    try {
      const res = await USERINFO()

      if (res.code === HTTP_CODE.SUCCESS_CODE) {
        setNoticeBroadcastTransactions(
          res.data.preference?.noticeBroadcastTransactions ?? null,
        )
      }
    } catch (error) {
      console.error('获取用户设置失败:', error)
    } finally {
      setIsInitializing(false)
    }
  }

  useEffect(() => {
    fetchUserSettings()
  }, [])

  // 获取当前语言名称的函数
  const getCurrentLanguageName = () => {
    const langCode = i18n.language
    const foundLang = langRouting.find((item) => item.lang === langCode)
    if (foundLang) {
      return foundLang.label
    }

    switch (langCode) {
      case 'en_US':
        return 'English'
      case 'zh_CN':
        return '简体中文'
      case 'zh_TW':
        return '繁體中文'
      case 'vi_VN':
        return 'Tiếng Việt'
      case 'id_ID':
        return 'Bahasa Indonesia'
      case 'th_TH':
        return 'ภาษาไทย'
      case 'ko_KR':
        return '한国语'
      case 'ja_JP':
        return '日本語'
      case 'es_ES':
        return 'Español'
      case 'tr_TR':
        return 'Türkçe'
      default:
        return 'English'
    }
  }

  const languageItems: MenuProps['items'] = langRouting.map((lang) => ({
    key: lang.lang,
    label: lang.label,
  }))

  const handleLogout = async () => {
    userStore.reset()
    chatroomManager.logout()
    await store.leaveRoom()
    logout()
    path('/')
  }

  const handleBroadcastChange = async (checked: boolean) => {
    const newValue = checked ? 1 : 0
    try {
      setIsBroadcastLoading(true)
      const res = await UPDATE_SETTING({
        noticeBroadcastTransactions: newValue,
      })

      if (res.code === HTTP_CODE.SUCCESS_CODE) {
        setNoticeBroadcastTransactions(newValue)
        message.success(i18n.t('update_success'))
      }
    } catch (error) {
      console.error('更新设置失败:', error)
    } finally {
      setIsBroadcastLoading(false)
    }
  }

  return (
    <main>
      <header className="fixed left-0 top-0 z-10 w-full bg-[#F8F9FB] px-4 py-3 md:left-16">
        <nav>
          <button
            type="button"
            className="inline-flex cursor-pointer items-center"
            onClick={() => goBack()}
            aria-label={i18n.t('back_to_previous')}
          >
            <span className="text-xl">
              <SvgIconProfileBack className="mr-2 size-8 md:size-10" />
            </span>
            <span className="ml-2 text-base font-bold md:text-lg">
              {i18n.t('profile_settings')}
            </span>
          </button>
        </nav>
      </header>

      <section className="mb-20 mt-12 h-[calc(100vh-64px)] overflow-y-auto overflow-x-hidden px-4 pt-6 no-scroll md:ml-20 md:mr-20 md:px-20 md:pt-10">
        <div className="mb-5 w-full rounded-[8px] bg-white px-4 py-4 md:px-20 md:py-6">
          {/* 语言部分 */}
          <section className="mb-6" aria-labelledby="language-settings">
            <h2
              id="language-settings"
              className="mb-4 text-18 font-700 text-[#333]"
            >
              {i18n.t('language')}
            </h2>
            <Dropdown
              menu={{
                items: languageItems,
                onClick: ({ key }) => {
                  i18n.changeLanguage(key)
                  setCurrentLang(key)
                  setupStore.setLang(key as supportedLanguage)
                  localStorage.setItem('i18nextLng', key)
                },
                selectedKeys: [currentLang],
                style: { padding: '8px 0' },
              }}
              trigger={['click']}
              overlayClassName="language-dropdown"
            >
              <button
                type="button"
                className="flex w-full cursor-pointer items-center justify-between rounded-md border border-[#DADBE1] bg-[#F9FAFB] p-3"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <span className="text-base text-[#1A1B1E]">
                  {getCurrentLanguageName()}
                </span>
                <SvgIconArrowRight className="size-5" aria-hidden="true" />
              </button>
            </Dropdown>
          </section>

          {/* 隐私部分 */}
          <section className="mb-6" aria-labelledby="privacy-settings">
            <h2
              id="privacy-settings"
              className="mb-4 text-18 font-700 text-[#333]"
            >
              {i18n.t('privacy')}
            </h2>
            <div
              className="flex items-center justify-between rounded-md border border-[#DADBE1] bg-[#F9FAFB] p-3"
              role="group"
              aria-labelledby="broadcast-trades-label"
            >
              <label
                id="broadcast-trades-label"
                className="cursor-pointer text-base text-[#1A1B1E]"
                htmlFor="broadcast-trades-switch"
              >
                {i18n.t('broadcast_my_trades')}
              </label>
              <PKSwitch
                id="broadcast-trades-switch"
                checked={broadcastTrades}
                loading={isInitializing || isBroadcastLoading}
                onChange={handleBroadcastChange}
                disabled={isInitializing}
                aria-describedby="broadcast-trades-label"
              />
            </div>
          </section>
        </div>

        <footer className="flex w-full justify-end md:justify-end">
          <ButtonSecondary
            onClick={handleLogout}
            className="h-9"
            noBorder
            aria-label={i18n.t('logout')}
          >
            <IconLogout className="size-[14px]" aria-hidden="true" />
            <span>{i18n.t('logout')}</span>
          </ButtonSecondary>
        </footer>
      </section>
    </main>
  )
})

Component.displayName = 'SettingsPage'
export default Component
