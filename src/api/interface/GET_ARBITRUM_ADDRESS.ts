/**
 * @description 获取Arbitrum地址
 */
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'
import { fetch } from '@/api/fetch'

/**
 * @description 基本信息
 */
export const API_GET_ARBITRUM_ADDRESS = {
  name: 'GET_ARBITRUM_ADDRESS',
  url: '/hy/arbAddress',
}

/**
 * @description 回包(只记录关键信息)
 */
export interface GET_ARBITRUM_ADDRESS_RETURN {
  message: string
  time: string
  code: number
  data: string // Arbitrum地址
}

/**
 * @description 获取Arbitrum地址
 */
export const GET_ARBITRUM_ADDRESS =
  (): Promise<GET_ARBITRUM_ADDRESS_RETURN> => {
    // 开发环境下使用mock数据
    // if (process.env.NODE_ENV === 'development') {
    //   // 模拟网络延迟
    //   return new Promise((resolve) => {
    //     setTimeout(() => {
    //       resolve({
    //         message: 'Success',
    //         time: new Date().toISOString().replace('T', ' ').substring(0, 19),
    //         code: 200,
    //         data: '0x8EaEA55F57A69bFfa1ab18ceA3a9A19d87f6575F',
    //       })
    //     }, 500) // 模拟500ms的网络延迟
    //   })
    // }

    // 生产环境下调用真实API
    return fetch(API_GET_ARBITRUM_ADDRESS, HTTP_01, {
      headers: {
        token: myToken(),
      },
      type: 'GET',
    }) as Promise<GET_ARBITRUM_ADDRESS_RETURN>
  }
