/**
 * @description 合约取消订单
 */
import { fetch } from '@/api/fetch.ts'
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'

/* 接口名称和URL */
export const NAME_EX_CANCEL_ORDER = {
  name: 'EX_CANCEL_ORDER',
  url: '/hy/ex/cancelOrder',
}

/**
 * @description 入参
 */
export interface EX_CANCEL_ORDER_PARAMS {
  cancels: {
    // 资产id
    a: number
    // 订单id
    o: number
  }[]
}

/**
 * @description 回包(只记录关键信息)
 */
export interface EX_CANCEL_ORDER_RETURN {
  code: number
  data: any
  message: string
  time: string
}

export const EX_CANCEL_ORDER = (
  params: EX_CANCEL_ORDER_PARAMS,
): Promise<EX_CANCEL_ORDER_RETURN> => {
  return fetch(NAME_EX_CANCEL_ORDER, HTTP_01, {
    headers: {
      token: myToken(),
    },
    params,
    type: 'POST',
  }) as Promise<EX_CANCEL_ORDER_RETURN>
}
