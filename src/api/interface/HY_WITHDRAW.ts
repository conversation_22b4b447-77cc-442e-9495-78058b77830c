/**
 * @description 提现接口
 */
import { fetch } from '@/api/fetch.ts'
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '../_http'

/* 提现接口 */
export const NAME_HY_WITHDRAW = {
  name: 'NAME_HY_WITHDRAW',
  url: '/hy/withdraw',
}

/**
 * @description 入参
 */
export interface WITHDRAW_PARAMS {
  amount: number | string // 提现金额
  address: string // 提现地址
}

/**
 * @description 回包(只记录关键信息)
 */
export interface WITHDRAW_RETURN {
  code: number
  data: {
    txHash?: string // 交易哈希
    fee?: number // 手续费
  }
  message: string
  time: string
}

export const HY_WITHDRAW = (
  params: WITHDRAW_PARAMS,
): Promise<WITHDRAW_RETURN> => {
  return fetch(NAME_HY_WITHDRAW, HTTP_01, {
    headers: {
      token: myToken(),
    },
    params: params,
    type: 'POST',
  }) as Promise<WITHDRAW_RETURN>
}
