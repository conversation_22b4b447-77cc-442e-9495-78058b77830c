import { fetch } from '@/api/fetch.ts'
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'

export const NAME_GET_ALL_TOKEN_BALANCE = {
  name: 'GET_ALL_TOKEN_BALANCE',
  url: '/dex/asset/allTokenBalanceByAddress',
}

export interface GET_ALL_TOKEN_BALANCE_RETURN {
  message: string
  time: string
  code: number
  data: {
    tokens: Array<{
      symbol: string
      balance: string
      value: string
    }>
  }
}

export type ChainId = 'ethereum' | 'bsc' | 'base' | 'solana'
export type ChainIdName = 'ETH' | 'BSC' | 'Base' | 'Solana'

export const GET_ALL_TOKEN_BALANCE = (
  address: string,
  chainId: ChainId = 'solana',
): Promise<GET_ALL_TOKEN_BALANCE_RETURN> => {
  return fetch(NAME_GET_ALL_TOKEN_BALANCE, HTTP_01, {
    headers: {
      token: myToken(),
    },
    params: {
      chainId,
      address,
    },
    type: 'GET',
  }) as Promise<GET_ALL_TOKEN_BALANCE_RETURN>
}
