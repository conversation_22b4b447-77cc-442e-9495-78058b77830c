import { fetch } from '@/api/fetch.ts'
import { HTTP_01 } from '@/api/_http'

/**
 * @description privy登录
 */
export const API_TRIPARTITE_LOGIN: { name: string; url: string } = {
  name: 'TRIPARTITE_LOGIN',
  url: '/tripartite/login',
}

export interface TRIPARTITE_LOGIN_PARAMS {
  // 平台 token
  tripartiteToken: string
  // 平台 5=privy
  platform?: number
  // 客户端 ID
  clientId?: string
  // 邀请码
  inviter?: string
  // 渠道
  channel?: string
}

export interface TRIPARTITE_LOGIN_RETURN {
  code: number
  data: any
  message: string
}

export const TRIPARTITE_LOGIN = (
  params: TRIPARTITE_LOGIN_PARAMS,
): Promise<TRIPARTITE_LOGIN_RETURN> => {
  return fetch(API_TRIPARTITE_LOGIN, HTTP_01, {
    params,
    type: 'POST',
  }) as Promise<TRIPARTITE_LOGIN_RETURN>
}
