import { fetch } from '@/api/fetch.ts'
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'

export const NAME_SHARE_SETTING = {
  name: 'SHARE_SETTING',
  url: '/dex/transaction/share/setting',
}

export interface SHARE_SETTING_RETURN {
  message: string
  time: string
  code: number
  data: {
    // 主键
    _id: string
    // 用户分享是否开启
    userShareEnabled: boolean
    // 平台手续费率
    platformFeeRate: number
    // 房间分享率
    roomShareRate: number
    // 上级分享率
    superiorShareRate: number
    // 版本
    v: string
    // 最小eth充值金额
    ehtreceiveMinAmount: number
    // 最小sol充值金额
    solreceiveMinAmount: number
    // 最小bnb充值金额
    bnbreceiveMinAmount: number
    // 合约上级分享率
    contractSuperiorShareRate: number
    // 合约房间分享率
    contractRoomShareRate: number
    // 合约最小充值金额
    contractMinDepositAmount: number
    // 合约最小提现金额
    contractMinReceiveAmount: number
    contractExteriorSuperiorShareRate: number
    exteriorSuperiorShareRate: number
  }
}

export const SHARE_SETTING = (): Promise<SHARE_SETTING_RETURN> => {
  return fetch(NAME_SHARE_SETTING, HTTP_01, {
    headers: {
      token: myToken(),
    },
    type: 'GET',
  }) as Promise<SHARE_SETTING_RETURN>
}
