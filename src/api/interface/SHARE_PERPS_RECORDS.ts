import { fetch } from '@/api/fetch.ts'
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'

export const NAME_SHARE_PERPS_RECORDS = {
  name: 'SHARE_PERPS_RECORDS',
  url: '/hy/ex/shareRecords',
}

interface Record {
  createTime: number
  userId: string
  nickName: string
  amount: number
  netWork: string
  shareTypeEnum: string
  s: string
}

export interface SHARE_PERPS_RECORDS_PARAMS {
  page: number
  size: number
}

export interface SHARE_PERPS_RECORDS_RETURN {
  message: string
  time: string
  code: number
  data: {
    page: number
    size: number
    records: Record[]
    last: boolean
  }
}

export const SHARE_PERPS_RECORDS = (
  params: SHARE_PERPS_RECORDS_PARAMS,
): Promise<SHARE_PERPS_RECORDS_RETURN> => {
  return fetch(NAME_SHARE_PERPS_RECORDS, HTTP_01, {
    headers: {
      token: myToken(),
    },
    params,
    type: 'GET',
  }) as Promise<SHARE_PERPS_RECORDS_RETURN>
}
