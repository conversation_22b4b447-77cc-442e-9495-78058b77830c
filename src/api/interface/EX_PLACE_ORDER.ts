/**
 * @description 合约下单/平仓
 */
import { fetch } from '@/api/fetch.ts'
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'

/* 个人信息获取 */
export const NAME_EX_PLACE_ORDER = {
  name: 'EX_PLACE_ORDER',
  url: '/hy/ex/placeOrder',
}

/**
 * @description 入参
 */
export interface EX_PLACE_ORDER_PARAMS {
  orders: {
    // 资产id
    a: number
    // 是否为买单
    b: boolean
    // 触发价格(下单价格)（数值中如果有小数，末尾不能有0）
    p: string
    // 购买数量（数值中如果有小数，末尾不能有0）
    s: string
    // 是否只挂单(市价单/限价单=false；其他=true，当市价或限价单勾选了只减仓位时=true)
    r: boolean
    // 订单类型信息
    t:
      | {
          // 市价/限价单信息
          limit: {
            // 限价单
            // Gtc =（有效直至取消）订单没有特殊行为
            // Ioc =（立即或取消）将取消未完成的部分
            // Alo =（仅添加流动性，即“仅发布”）将被取消，而不是立即匹配
            // FrontendMarket = 市价下单
            tif: 'Gtc' | 'Ioc' | 'Alo' | 'FrontendMarket'
          }
        }
      | {
          // 止盈止损单信息
          trigger: {
            // 是否为做市单，使用止盈止损时，isMarket = true
            isMarket: boolean
            // 触发价格（数值中如果有小数，末尾不能有0）
            triggerPx: string
            // tp = 止盈; sl = 止损;
            tpsl: 'tp' | 'sl'
          }
        }
  }[]
  // 止盈止损类型
  // na = 不使用止盈止损，仅作为常规限价单或市价单执行
  // normalTpsl = 使用止盈止损，但触发仅针对该笔订单的成交部分，不与整体持仓关联
  // positionTpsl = 使用止盈止损，作用于整个持仓（即同一合约的所有仓位），而非单笔订单
  grouping: 'na' | 'normalTpsl' | 'positionTpsl'
  // 房间id
  roomId: string
}

/**
 * @description 回包(只记录关键信息)
 */
export interface EX_PLACE_ORDER_RETURN {
  code: number
  data: any
  message: string
}

export const EX_PLACE_ORDER = (
  params: EX_PLACE_ORDER_PARAMS,
): Promise<EX_PLACE_ORDER_RETURN> => {
  return fetch(NAME_EX_PLACE_ORDER, HTTP_01, {
    headers: {
      token: myToken(),
    },
    params,
    type: 'POST',
  }) as Promise<EX_PLACE_ORDER_RETURN>
}
