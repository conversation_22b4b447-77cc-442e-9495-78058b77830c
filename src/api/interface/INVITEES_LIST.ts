import { fetch } from '@/api/fetch.ts'
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'

// INVITEES 接口名称与地址定义
export const NAME_INVITEES = {
  name: 'INVITEES_LIST',
  url: '/ic/v2/invitees',
}

// 返回结构类型定义
export interface INVITEES_RETURN {
  message: string
  time: string
  code: number
  data: Array<{
    id: string
    userId: string
    username: string
    nickname: string
    thumbnail: string
    status: number
    delStatus: number
    createTime: number
  }>
}

/**
 * @returns Promise<INVITEES_RETURN>
 */
export const INVITEES_LIST = (): Promise<INVITEES_RETURN> => {
  return fetch(NAME_INVITEES, HTTP_01, {
    headers: {
      token: myToken(),
    },
    type: 'GET',
  }) as Promise<INVITEES_RETURN>
}
