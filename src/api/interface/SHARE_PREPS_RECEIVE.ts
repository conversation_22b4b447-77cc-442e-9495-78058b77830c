import { fetch } from '@/api/fetch.ts'
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'

// SHARE_PREPS_RECEIVE 接口名称与地址定义
export const NAME_SHARE_PREPS_RECEIVE = {
  name: 'SHARE_PREPS_RECEIVE',
  url: '/hy/ex/shareReceive',
}

// 返回结构类型定义
export interface SHARE_PREPS_RECEIVE_RETURN {
  message: string
  time: string
  code: number
  data: null
}

/**
 * @returns Promise<SHARE_PREPS_RECEIVE_RETURN>
 */
export const SHARE_PREPS_RECEIVE = (): Promise<SHARE_PREPS_RECEIVE_RETURN> => {
  return fetch(NAME_SHARE_PREPS_RECEIVE, HTTP_01, {
    headers: {
      token: myToken(),
    },
    type: 'POST',
  }) as Promise<SHARE_PREPS_RECEIVE_RETURN>
}
