/**
 * @description 获取最后交易代币信息
 */
import { HTTP_01 } from '@/api/_http'
import { fetch } from '@/api/fetch'

export const API_GET_LAST_TRADE_TOKEN = {
  name: 'API_GET_LAST_TRADE_TOKEN',
  url: '/dex/last-trade-token',
}

/**
 * @description 回包
 */
export interface GET_LAST_TRADE_TOKEN_RETURN {
  code: number
  message: string
  time: string
  data: {
    chainId: string
    address: string
  }
}

export const GET_LAST_TRADE_TOKEN =
  (): Promise<GET_LAST_TRADE_TOKEN_RETURN> => {
    return fetch(API_GET_LAST_TRADE_TOKEN, HTTP_01, {
      type: 'GET',
    }) as Promise<GET_LAST_TRADE_TOKEN_RETURN>
  }
