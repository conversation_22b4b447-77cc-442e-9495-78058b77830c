/**
 * @description 房主获取房间详情
 */
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'
import { fetch } from '@/api/fetch'

/**
 * @description 基本信息
 */
export const API_QUERY_ROOM_DETIAL_BY_ID: { name: string; url: string } = {
  name: 'API_QUERY_ROOM_DETIAL_BY_ID',
  url: '/imConnector/chatRoom/queryChatRoomDetailsByRoomId',
}

/**
 * @description 入参
 */
export interface QUERY_ROOM_DETIAL_BY_ID_PARAMS {
  roomId: string
  pwd?: string | null
}

export interface ROOM_DETIAL {
  createTime?: number
  hot?: number
  id?: string
  isFollow?: boolean
  isPw?: boolean
  num?: number
  prettyRoomId?: string
  roomContent?: string
  roomId?: string
  roomPic?: string
  roomRole?: string
  roomTitle?: string
  s?: number
  conciseUserId?: string // 房主ID
  locationChatRoomId?: string // 互踢使用（表示，登录的账号是否已经存在于直播间）
}

/**
 * @description 回包(只记录关键信息)
 */
export interface QUERY_ROOM_DETIAL_BY_ID_RETURN {
  code: number
  data: ROOM_DETIAL
  message: string
}

export const QUERY_ROOM_DETIAL_BY_ID = (
  params: QUERY_ROOM_DETIAL_BY_ID_PARAMS,
): Promise<QUERY_ROOM_DETIAL_BY_ID_RETURN> => {
  return fetch(API_QUERY_ROOM_DETIAL_BY_ID, HTTP_01, {
    headers: {
      token: myToken(),
    },
    params,
    type: 'GET',
  }) as Promise<QUERY_ROOM_DETIAL_BY_ID_RETURN>
}
