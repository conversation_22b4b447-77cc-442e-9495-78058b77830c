/**
 * @description 修改逐仓保证金

 */
import { fetch } from '@/api/fetch.ts'
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'

/* 个人信息获取 */
export const NAME_UPDATE_ISOLATED_MARGIN = {
  name: 'NAME_UPDATE_ISOLATED_MARGIN',
  url: '/hy/ex/updateIsolatedMargin',
}

/**
 * @description 入参
 */
interface UPDATE_ISOLATED_MARGIN_PARAMS {
  // 资产 ID
  asset: number
  // 表示要添加或删除的金额（带 6 位小数），例如 1 美元表示 1000000
  // 正数表示添加，负数表示删除
  ntli: string
}

/**
 * @description 回包(只记录关键信息)
 */
export interface UPDATE_ISOLATED_MARGIN_RETURN {
  code: number
  data: any
  message: string
}

export const UPDATE_ISOLATED_MARGIN = (
  params: UPDATE_ISOLATED_MARGIN_PARAMS,
): Promise<UPDATE_ISOLATED_MARGIN_RETURN> => {
  return fetch(NAME_UPDATE_ISOLATED_MARGIN, HTTP_01, {
    headers: {
      token: myToken(),
    },
    params,
    type: 'POST',
  }) as Promise<UPDATE_ISOLATED_MARGIN_RETURN>
}
