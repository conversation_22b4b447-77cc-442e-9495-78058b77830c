/**
 * @description 修改杠杆/修改为逐仓模式/修改逐仓杠杆
 */
import { fetch } from '@/api/fetch.ts'
import { myToken } from '@/api/_myToken'
import { HTTP_01 } from '@/api/_http'

export const NAME_EX_UPDATE_LEVERAGE = {
  name: 'EX_UPDATE_LEVERAGE',
  url: '/hy/ex/updateLeverage',
}

/**
 * @description 入参
 */
export interface EX_UPDATE_LEVERAGE_PARAMS {
  // 资产id
  asset: string | number
  // 是否为全仓模式
  isCross: boolean
  // 杠杆倍数
  leverage: number
}

/**
 * @description 回包(只记录关键信息)
 */
export interface EX_UPDATE_LEVERAGE_RETURN {
  message: string
  time: string
  code: number
  data: string
}

export const EX_UPDATE_LEVERAGE = (
  params: EX_UPDATE_LEVERAGE_PARAMS,
): Promise<EX_UPDATE_LEVERAGE_RETURN> => {
  return fetch(NAME_EX_UPDATE_LEVERAGE, HTTP_01, {
    headers: {
      token: myToken(),
    },
    params,
    type: 'POST',
  }) as Promise<EX_UPDATE_LEVERAGE_RETURN>
}
