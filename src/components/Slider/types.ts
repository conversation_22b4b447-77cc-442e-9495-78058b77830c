import { SliderProps } from '@radix-ui/react-slider'

export interface ISliderProps extends SliderProps {
  // 主题
  theme?: 'light' | 'dark'
  // 类型
  type: 'number' | 'trade'
  // 名称
  name?: string
  // 是否禁用
  disabled?: boolean
  // 方向
  orientation?: React.AriaAttributes['aria-orientation']
  // 方向
  dir?: 'ltr' | 'rtl'
  // 最小值
  min?: number
  // 最大值
  max?: number
  // 步长
  step?: number
  // 最小步长
  minStepsBetweenThumbs?: number
  // 值
  value: number[]
  // 默认值
  defaultValue?: number[]
  // 值变化(拖拽)
  onValueChange(value: number[]): void
  // 值提交(松开)
  onValueCommit?(value: number[]): void
  // 是否反转
  inverted?: boolean
}
