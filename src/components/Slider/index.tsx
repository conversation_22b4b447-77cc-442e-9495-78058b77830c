import * as SliderPrimitive from '@radix-ui/react-slider'
import styled from 'styled-components'
import tw from 'twin.macro'
import { ISliderProps } from '@/components/Slider/types'
import clsx from 'clsx'

const Scale = styled.span`
  ${tw`absolute top-1/3 h-[5px] w-[3px] -translate-y-1/2 rounded-[65px] outline outline-1`}
`

const ScaleLight = styled(Scale)<{ active?: boolean }>`
  ${tw`outline-gray-50`}
  ${({ active }) => (active ? tw`bg-black` : tw`bg-[#D9D9D9]`)}
`

const ScaleDark = styled(Scale)<{ active?: boolean }>`
  ${tw`outline-[#121212]`}
  ${({ active }) => (active ? tw`bg-white` : tw`bg-[#40414A]`)}
`

const Slider = ({
  value,
  disabled,
  type,
  theme,
  onValueChange,
  onValueCommit,
  ...rest
}: ISliderProps) => {
  const isLight = theme === 'light'
  const isDark = theme === 'dark'
  const marks = type === 'trade' ? [0, 25, 50, 75, 100] : [0, 100]

  return (
    <div className="w-full">
      <SliderPrimitive.Root
        value={value}
        defaultValue={value}
        disabled={disabled}
        onValueChange={onValueChange}
        onValueCommit={onValueCommit}
        min={1}
        step={1}
        className="group relative flex h-3 w-full touch-none select-none items-center overflow-visible"
        {...rest}
      >
        <SliderPrimitive.Track className="relative h-full w-full">
          <div
            className={clsx(
              'absolute top-1/2 h-[2.5px] w-full -translate-y-1/2 border-b-2',
              isLight && 'border-[#D9D9D9]',
              isDark && 'border-[#40414A]',
            )}
          />
          <SliderPrimitive.Range
            className={clsx(
              'absolute top-1/2 h-[2.5px] -translate-y-1/2 bg-black',
              isLight && 'bg-black',
              isDark && 'bg-white',
            )}
          />

          {isLight &&
            marks.map((percent) => (
              <ScaleLight
                key={percent}
                style={{ left: `${percent === 100 ? 99 : percent}%` }}
                active={value?.[0] !== undefined && value?.[0] >= percent}
              />
            ))}
          {isDark &&
            marks.map((percent) => (
              <ScaleDark
                key={percent}
                style={{ left: `${percent === 100 ? 99 : percent}%` }}
                active={value?.[0] !== undefined && value?.[0] >= percent}
              />
            ))}
        </SliderPrimitive.Track>
        <SliderPrimitive.Thumb
          className={clsx(
            'relative z-20 block h-3.5 w-2.5 rounded-[5px] outline outline-2',
            isLight && 'bg-gray-100 outline-zinc-900',
            isDark && 'bg-[#40414A] outline-[#F9FAFB]',
          )}
        />
      </SliderPrimitive.Root>
    </div>
  )
}

export { Slider }
