import { Modal } from '@/components/Customize/Modal'
import { PopupDown } from '@/components/PopupDown'
import { SafeAreaApp } from '@/components/SafeAreaApp'
import { useMedia } from '@/hooks/useMedia'
import { observer } from 'mobx-react-lite'

export const ModalOnPopup = observer(
  ({
    children,
    visible,
    title,
    intro,
    onOk,
    onClose,
  }: {
    children: React.ReactNode
    visible: boolean
    title: string
    intro?: string
    onOk: () => void
    onClose: () => void
  }) => {
    const { isMd } = useMedia()

    if (isMd) {
      return (
        <PopupDown
          label={title}
          intro={intro}
          visible={visible}
          onClose={onClose}
        >
          {children}
          <SafeAreaApp />
        </PopupDown>
      )
    }

    return (
      <Modal open={visible} title={title} onOk={onOk} onCancel={onClose}>
        {children}
      </Modal>
    )
  },
)
