export type IRefObject = React.RefObject<HTMLDivElement | null>
export type IRefWidget = React.RefObject<ITwWidget | null>
type HasKeys<K> = { [P in keyof K]: any }

export interface ChartKlineContextType {
  // 图表容器
  chartRef: IRefObject
  // 图表实例
  widgetRef: IRefWidget
}

export interface ChartKlineProps<
  T extends HasKeys<IKlineBas> = HasKeys<IKlineBas>,
> {
  // 当前币种
  symbol: string
  // 当前周期
  interval: IOutsideInterval
  // 接口：获取 K 线历史
  fetchKline: (
    coin: string,
    interval: IOutsideInterval,
    startTime: number,
    endTime?: number,
  ) => Promise<T[]>
  // 接口：订阅 K 线数据
  subscribeKline: (coin: string, interval: IOutsideInterval) => void
  // 接口：取消订阅 K 线数据
  unsubscribeKline: () => void
  // 方法：设置周期
  setInterval: (interval: IOutsideInterval) => void
  // 方法：获取最新K线数据
  getKline: () => T
  // 方法：获取价格精度
  getPrecision: () => number
}

export interface IDatafeedProps
  extends Omit<
    ChartKlineProps,
    'symbol' | 'interval' | 'setInterval' | 'getHistoryKline'
  > {
  onBarsCallback?: (historyKline: IKlineBas[]) => void
}

export type ICreateDatafeedProps = Omit<
  ChartKlineProps,
  'symbol' | 'interval' | 'setInterval' | 'getHistoryKline' | 'unsubscribeKline'
> & {
  onBarsCallback?: (historyKline: IKlineBas[]) => void
}

export interface IUseDatafeed {
  datafeed: IDatafeedReturn
}

export interface IDatafeedReturn {
  // 初始化
  onReady: (callback: (config: any) => void) => void
  // 搜索符号
  searchSymbols: (
    userInput: string,
    exchange: string,
    symbolType: string,
  ) => void
  // 解析符号
  resolveSymbol: (
    symbolName: string,
    onSymbolResolvedCallback: (symbolInfo: any) => void,
    onResolveErrorCallback: (error: string) => void,
  ) => void
  // 获取历史K线数据
  getBars: (
    symbolInfo: any,
    resolution: IInsideInterval,
    periodParams: any,
    onHistoryCallback: (bars: any[], meta: { noData?: boolean }) => void,
    onErrorCallback: (error: string) => void,
  ) => void
  // 订阅K线数据
  subscribeBars: (
    symbolInfo: any,
    resolution: IInsideInterval,
    onRealtimeCallback: (bar: any) => void,
    subscriberUID: string,
    _onResetCacheNeededCallback: () => void,
  ) => void
  // 取消订阅K线数据
  unsubscribeBars: (subscriberUID: string) => void
}

export interface IUseStudy {
  symbol: string
  widgetRef: IRefWidget
  setInterval: (interval: IOutsideInterval) => void
  getPrecision: () => number
}

export interface IKlineBas {
  // 时间戳
  time: number
  // 开盘价
  open: number
  // 最高价
  high: number
  // 最低价
  low: number
  // 收盘价
  close: number
  // 成交量
  volume: number
}

export enum IIntervalMap {
  '1m' = '1',
  '3m' = '3',
  '5m' = '5',
  '15m' = '15',
  '30m' = '30',
  '1h' = '60',
  '2h' = '120',
  '4h' = '240',
  '8h' = '480',
  '12h' = '720',
  '1d' = '1D',
  '3d' = '3D',
  '1w' = '1W',
  '1M' = '1M',
}

/**
 * @desc 外部时间周期
 */
export type IOutsideInterval = keyof typeof IIntervalMap

/**
 * @desc TradingView 时间周期
 */
export type IInsideInterval = (typeof IIntervalMap)[IOutsideInterval]

export interface ITwWidget {
  remove: () => void
  subscribe: (event: string, callback: (data: any) => void) => void
  onChartReady: (callback: () => void) => void
  activeChart: () => any
  [key: string]: any
}
