import overrides from '@/components/ChartKline/module/options/overrides'
import custom_themes from '@/components/ChartKline/module/options/customThemes'

export const options = (isMobile?: boolean) => {
  return {
    disabled_features: [
      'header_compare',
      // 'header_resolutions',
      // 'header_screenshot',
      // 'header_settings',
      'header_symbol_search',
      // 'symbol_search_hot_key',
      // 'header_undo_redo',
      // 'header_quick_search',
      'header_saveload',
      'timeframes_toolbar',
      'datasource_copypaste',
      'compare_symbol',
      'timezone_menu',
      'border_around_the_chart',
      'symbol_info',
      'chart_crosshair_menu',
      'control_bar',
      'main_series_scale_menu',
      'display_market_status',
      'remove_library_container_border',
      ...(isMobile ? ['header_widget'] : []),
      // ...(isMobile ? ['header_widget', 'create_volume_indicator_by_default'] : []),
      // ...(isMobile ? ['legend_widget', 'header_widget'] : []),
      // 指标按钮
      // 'header_indicators',
      // 禁用本地存储设置.开发专用
      // 'use_localstorage_for_settings',
      'save_chart_properties_to_local_storage',
    ],

    enabled_features: [
      'dont_show_boolean_study_arguments',
      'hide_last_na_study_output',
      ...(isMobile ? ['hide_left_toolbar_by_default'] : []),
    ],

    theme: 'dark',

    loading_screen: {
      backgroundColor: '#131313',
      foregroundColor: '#FFFFFF',
    },

    overrides,

    custom_themes,
  }
}
