import { fromTVResolution } from '@/components/ChartKline/module/utils'
import {
  ICreateDatafeedProps,
  IDatafeedProps,
  IDatafeedReturn,
  IInsideInterval,
  IIntervalMap,
  IKlineBas,
  IUseDatafeed,
} from '@/components/ChartKline/type'
import { useEffect, useMemo, useRef } from 'react'

export const useDatafeed = ({
  fetchKline,
  subscribeKline,
  unsubscribeKline,
  getKline,
  getPrecision,
  onBarsCallback,
}: IDatafeedProps): IUseDatafeed => {
  const finalCleanupRef = useRef<boolean>(false)

  useEffect(() => {
    finalCleanupRef.current = true
    return () => {
      if (finalCleanupRef.current) unsubscribeKline()
    }
  }, [])

  const datafeed = useMemo(
    () =>
      createDatafeed({
        fetchKline,
        subscribeKline,
        getPrecision,
        getKline,
        onBarsCallback,
      }),
    [fetchKline, subscribeKline, getKline, getPrecision, onBarsCallback],
  )

  return { datafeed }
}

function createDatafeed({
  fetchKline,
  subscribeKline,
  getPrecision,
  getKline,
  onBarsCallback,
}: ICreateDatafeedProps): IDatafeedReturn {
  const supported_resolutions = [...Object.values(IIntervalMap)]
  const timers: Record<string, any> = {}

  const datafeed: IDatafeedReturn = {} as IDatafeedReturn

  datafeed.onReady = (callback: (config: any) => void) => {
    setTimeout(() => {
      callback({
        supported_resolutions,
        supports_time: true,
        supports_marks: false,
        supports_timescale_marks: false,
      })
    }, 0)
  }

  datafeed.resolveSymbol = (
    symbolName: string,
    onSymbolResolvedCallback: (symbolInfo: any) => void,
    _onResolveErrorCallback: (error: string) => void,
  ) => {
    try {
      const precision = getPrecision() || 0
      if (typeof precision !== 'number' || isNaN(precision)) {
        _onResolveErrorCallback('Invalid precision')
        return
      }
      const pricescale = Math.pow(10, precision)

      const symbolInfo = {
        name: symbolName,
        description: '',
        type: 'crypto',
        session: '24x7',
        ticker: `${symbolName}USD`,
        minmov: 1,
        pricescale,
        volume_precision: 2,
        has_intraday: true,
        supported_resolutions,
        data_status: 'streaming',
        has_seconds: true,
        // seconds_multipliers: [],
      }
      onSymbolResolvedCallback(symbolInfo)
    } catch (error) {
      console.error(`[Datafeed] Error in resolveSymbol:`, error)
      _onResolveErrorCallback(
        `Error resolving symbol: ${error instanceof Error ? error.message : String(error)}`,
      )
    }
  }

  // 添加历史数据
  datafeed.getBars = async (
    symbolInfo: any,
    resolution: IInsideInterval,
    periodParams: any,
    onHistoryCallback: (bars: any[], meta: { noData?: boolean }) => void,
    onErrorCallback: (error: string) => void,
  ) => {
    const { from, to, firstDataRequest } = periodParams

    try {
      const data = await fetchKline(
        symbolInfo.name,
        fromTVResolution(resolution),
        from * 1000,
        to * 1000,
      )

      if (!Array.isArray(data)) {
        // 仅检查是否为数组，允许空数组表示没有数据
        console.error(
          'Historical data is not available or not in the expected format',
        )
        onErrorCallback('Historical data is not in the expected array format')
        return
      }

      const bars = data.map((bar) => filterBars(bar))

      if (bars.length === 0) {
        onHistoryCallback([], { noData: true })
      } else {
        onHistoryCallback(bars, { noData: false })
      }
      // 首次请求数据时，调用回调
      if (firstDataRequest && onBarsCallback) {
        onBarsCallback(bars)
      }
    } catch (error: any) {
      onErrorCallback(error.message)
      // 1秒后重试
      setTimeout(() => {
        datafeed.getBars(
          symbolInfo,
          resolution,
          periodParams,
          onHistoryCallback,
          onErrorCallback,
        )
      }, 2000)
    }
  }

  // 更新实时数据
  datafeed.subscribeBars = (
    symbolInfo: any,
    resolution: IInsideInterval,
    onRealtimeCallback: (bar: any) => void,
    subscriberUID: string,
    _onResetCacheNeededCallback: () => void,
  ) => {
    // 订阅数据
    subscribeKline(symbolInfo.name, fromTVResolution(resolution))
    const timer = setInterval(() => {
      // 推送最新数据
      const lastBar = getKline() as IKlineBas & { coin: string }
      if (symbolInfo.name !== lastBar?.coin) return
      onRealtimeCallback(filterBars(lastBar))
    }, 1000)
    timers[subscriberUID] = timer
  }

  datafeed.unsubscribeBars = (subscriberUID: string) => {
    clearInterval(timers[subscriberUID])
    delete timers[subscriberUID]
  }

  datafeed.searchSymbols = () => {}

  const filterBars = <T extends IKlineBas>(bar: T): IKlineBas => {
    return {
      time: bar?.time,
      open: Number(bar?.open || 0),
      high: Number(bar?.high || 0),
      low: Number(bar?.low || 0),
      close: Number(bar?.close || 0),
      volume: Number(bar?.volume || 0),
    }
  }

  return datafeed
}
