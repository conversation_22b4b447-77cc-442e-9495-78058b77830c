import {
  IInsideInterval,
  IIntervalMap,
  IOutsideInterval,
} from '@/components/ChartKline/type'

const reverseIntervalMap = Object.fromEntries(
  Object.entries(IIntervalMap).map(([k, v]) => [v, k]),
) as Record<IInsideInterval, IOutsideInterval>

/**
 * @desc 时间周期转换：hyperliquid  => TradingView
 * @param interval
 * @returns {IInsideInterval}
 */
export const toTVResolution = (interval: IOutsideInterval): IInsideInterval => {
  return IIntervalMap[interval]
}

/**
 * @desc 时间周期转换：TradingView => hyperliquid
 * @param resolution
 * @returns {IOutsideInterval}
 */
export const fromTVResolution = (
  resolution: IInsideInterval,
): IOutsideInterval => {
  return reverseIntervalMap[resolution]
}

/**
 * @desc 将语言转换为 TW 语言
 * @param lang
 * @returns {string}
 */
export const toTWLocale = (lang: string): string => {
  switch (lang) {
    case 'en':
      return 'en' // 英语
    case 'vn':
      return 'vi' // 越南语
    case 'kr':
      return 'ko' // 韩语
    case 'jp':
      return 'ja' // 日语
    case 'zh_CN':
      return 'zh' // 简体中文
    case 'zh_TW':
      return 'zh_TW' // 繁体中文（台湾）
    case 'id':
      return 'id_ID' // 印度尼西亚语
    case 'tr':
      return 'tr' // 土耳其语
    case 'es':
      return 'es' // 西班牙语
    case 'pt':
      return 'pt' // 葡萄牙语
    case 'ru':
      return 'ru' // 俄语
    case 'th':
      return 'th' // 俄语
    // 如果没有匹配的语言，默认使用英语
    default:
      return 'en' // 默认英语
  }
}
