import { deleteStudyDom } from '@/components/ChartKline/module/study/method'

const PROP_DICT_MA = [
  // {
  //   name: 'Moving Average',
  //   plot: 5,
  //   style: {
  //     'plot.color.0': '#bd67ee',
  //     'plot.transparency': 40,
  //     'plot.trackprice': true,
  //   },
  // },
  // {
  //   name: 'Moving Average',
  //   plot: 10,
  //   style: {
  //     'plot.color.0': '#e2c41c',
  //     'plot.transparency': 40,
  //     'plot.trackprice': true,
  //   },
  // },
  {
    name: 'Moving Average',
    plot: 5,
    style: {
      'plot.color.0': '#00BD9A',
      'plot.transparency': 40,
      'plot.trackprice': true,
    },
  },
  {
    name: 'Moving Average',
    plot: 10,
    style: {
      'plot.color.0': '#e2c41c',
      'plot.transparency': 40,
      'plot.trackprice': true,
    },
  },
  {
    name: 'Moving Average',
    plot: 20,
    style: {
      'plot.color.0': '#bd67ee',
      'plot.transparency': 40,
      'plot.trackprice': true,
    },
  },
  {
    name: 'Moving Average',
    plot: 50,
    style: {
      'plot.color.0': '#209edc',
      'plot.transparency': 40,
      'plot.trackprice': true,
    },
  },
]

export default function SET_MA(
  widgetRef: React.RefObject<any>,
  type: 'mobile' | 'desktop',
  getPrecision: () => number,
) {
  try {
    if (!widgetRef.current) return

    const isMobile = type === 'mobile'
    const chart = widgetRef.current.activeChart()

    // 先删除所有现有的 MA 指标
    const existingMAs = chart
      .getAllStudies()
      .filter((v: any) => v.name === 'Moving Average')

    // 如果存在 MA 指标，全部移除
    existingMAs.forEach((study: any) => {
      chart.removeEntity(study.id)
    })

    const precision = getPrecision()

    // 重新创建 MA 指标
    for (const item of PROP_DICT_MA) {
      const studyPromise = chart.createStudy(
        'Moving Average',
        0,
        0,
        {
          length: item.plot,
          source: 'close',
        },
        {
          ...item.style,
          showLabelsOnPriceScale: false,
          precision,
        },
      )

      // 使用 applyOverrides 方法来确保设置被应用
      chart.applyOverrides(studyPromise, {
        showLastValue: false,
      })

      // 移动端下删除 MA 标题
      if (isMobile) {
        studyPromise.then((id: string) => {
          deleteStudyDom(chart, [id])
        })
      }
    }
  } catch (e) {
    console.error('Error setting MA:', e)
  }
}
