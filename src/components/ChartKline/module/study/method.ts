// 操作 TW DOM
export const deleteStudyDom = (
  // 图表实例
  widget: any,
  // 指标 id 队列
  studyIds: string[] = [],
  // 是否启用指标标题和值
  isPanePropertiesShowStudy: boolean = false,
) => {
  setTimeout(() => {
    // 获取 iframe 元素
    const chartContainer = document.getElementById('chartTWContainer')
    const iframe = chartContainer
      ? (chartContainer.querySelector('iframe') as HTMLIFrameElement)
      : null

    if (iframe && iframe.contentDocument) {
      studyIds.forEach((studyId) => {
        // 在 iframe 内查找指定的 legend 元素
        const legendElement = iframe.contentDocument?.querySelector(
          `[data-name="legend-source-item"][data-entity-id="${studyId}"]`,
        )
        if (legendElement) {
          // 找到父元素并删除整个 legend 项
          const legendItem = legendElement.closest('.item')
          if (legendItem) {
            legendItem.remove()
          } else {
            // 如果找不到父元素，直接隐藏当前元素
            ;(legendElement as HTMLElement).style.display = 'none'
          }
        }
      })
    }

    if (isPanePropertiesShowStudy) {
      // 完成后，重新启用其他指标的标题和值显示
      widget.applyOverrides({
        'paneProperties.legendProperties.showStudyTitles': true,
        'paneProperties.legendProperties.showStudyValues': true,
      })
    }
  }, 100)
}
