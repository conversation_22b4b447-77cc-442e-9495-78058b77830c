import { IKlineBas, IRefWidget } from '@/components/ChartKline/type'
import { useEffect, useRef } from 'react'

export const useCreateShape = ({ widgetRef }: { widgetRef: IRefWidget }) => {
  const klineRef = useRef<any>(null)
  const tickRef = useRef<any>(null)
  const timerRef = useRef<any>(null)

  useEffect(() => {
    return () => {
      try {
        if (tickRef.current) {
          widgetRef.current?.unsubscribe('onTick')
        }
      } catch (e) {
        console.error('清理 useCreateShape 时出错:', e)
      }
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }, [])

  function createKline({ open, close }: { open: number; close: number }) {
    if (!widgetRef.current) return
    const widget = widgetRef.current

    const price = close
    const isUp = open === close
    const isDown = open > close
    const color = isUp ? '#727376' : isDown ? '#9a0a0a' : '#007447'
    const options = { price, channel: 'close' }

    try {
      if (klineRef.current) widget.activeChart().removeEntity(klineRef.current)
      klineRef.current = widget.activeChart().createShape(options, {
        shape: 'horizontal_line',
        showInObjectsTree: true,
        disableSelection: true,
        disableSave: true,
        filled: true,
        zOrder: 'top',
        overrides: {
          linecolor: color,
          linestyle: 1,
          linewidth: 1,
          showLabel: true,
          showSymbolLabels: true,
          fontsize: 10,
          bold: true,
          horzLabelsAlign: 'right',
          vertLabelsAlign: 'top',
          labelBorderWidth: 1,
          textcolor: '#FFFFFF',
          fillBackground: '#FFFFFF',
        },
      })
    } catch (e) {
      console.error('创建Shape失败:', e)
    }
  }

  function onFirstTick(data: IKlineBas) {
    if (!data?.close || data.open === undefined) return
    clearInterval(timerRef.current)

    timerRef.current = setInterval(() => {
      createKline({
        open: data.open,
        close: data.close,
      })
    }, 1000)
  }

  function onTick(data: IKlineBas) {
    if (!data?.close || data.open === undefined) return
    clearInterval(timerRef.current)
    createKline({
      open: data.open,
      close: data.close,
    })
  }

  function onSetSeries(getHistoryKline: IKlineBas[]) {
    const historyKline = getHistoryKline?.sort((a, b) => a.time - b.time) || []
    const lastData = historyKline[historyKline?.length - 1] || {}

    onFirstTick(lastData)

    if (tickRef.current) {
      widgetRef.current?.unsubscribe('onTick', onTick)
    }

    widgetRef.current?.subscribe('onTick', onTick)
    tickRef.current = true
  }

  return {
    onSetSeries,
  }
}
