import { deleteStudyDom } from '@/components/ChartKline/module/study/method'
import { IRefWidget } from '@/components/ChartKline/type'

export default function SET_DEFAULT_STUDY(
  widgetRef: IRefWidget,
  type: 'mobile' | 'desktop',
) {
  try {
    if (!widgetRef.current) return

    const isMobile = type === 'mobile'

    const chart = widgetRef.current.activeChart()

    // 获取所有的研究/指标
    const studies = chart.getAllStudies()
    // 收集需要删除 legend 的指标 ID
    const studyIdsToDelete: string[] = []
    // 查找所有 Volume、MA 指标
    const volumeStudies = studies.filter((study: any) =>
      ['Volume', 'Moving Average'].includes(study.name),
    )

    volumeStudies.forEach((study: any) => {
      const studyId = study.id
      // 修改商品指标的设置
      chart.getStudyById(studyId).applyOverrides({
        showLabelsOnPriceScale: false,
        visible: !isMobile,
      })

      if (study.name === 'Volume') studyIdsToDelete.push(studyId)
      // 移动端下如果 MA 指标存在，则删除
      if (study.name === 'Moving Average' && isMobile)
        studyIdsToDelete.push(studyId)
    })

    setTimeout(() => {
      deleteStudyDom(chart, studyIdsToDelete, true)
    }, 0)
  } catch (e) {
    console.error('Error setting SET_DEFAULT_STUDY:', e)
  }
}
