import SET_DEFAULT_STUDY from '@/components/ChartKline/module/study/default'
import SET_MA from '@/components/ChartKline/module/study/ma'
import { fromTVResolution } from '@/components/ChartKline/module/utils'
import { IInsideInterval, IUseStudy } from '@/components/ChartKline/type'
import { useEffect } from 'react'
import { isMobile } from 'react-device-detect'

export const useStudy = ({
  symbol,
  widgetRef,
  setInterval,
  getPrecision,
}: IUseStudy) => {
  useEffect(() => {
    if (!widgetRef.current) return
    widgetRef.current.onChartReady(async () => {
      const type = isMobile ? 'mobile' : 'desktop'
      // 添加 MA 指标
      SET_MA(widgetRef, type, getPrecision)
    })
  }, [symbol])

  function onChartReady() {
    if (!widgetRef.current) return

    widgetRef.current.onChartReady(async () => {
      const chart = widgetRef.current?.activeChart()
      const type = isMobile ? 'mobile' : 'desktop'

      // 添加 MA 指标
      SET_MA(widgetRef, type, getPrecision)
      // 修改默认指标
      SET_DEFAULT_STUDY(widgetRef, 'desktop')
      // 监听周期变化
      chart.onIntervalChanged().subscribe(null, (interval: IInsideInterval) => {
        setInterval(fromTVResolution(interval))
      })
    })
  }

  return { onChartReady }
}
