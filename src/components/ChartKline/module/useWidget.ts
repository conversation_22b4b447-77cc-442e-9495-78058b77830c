import { toTVResolution } from '@/components/ChartKline/module/utils'
import {
  IOutsideInterval,
  IRefObject,
  ITwWidget,
} from '@/components/ChartKline/type'
import { toTWLocale } from '@/components/ChartKline/module/utils'
import { options } from '@/components/ChartKline/module/options'
import { useDevice } from '@/hooks/useDevice'
import { useRef } from 'react'

export const useWidget = ({ chartRef }: { chartRef: IRefObject }) => {
  const { isMobile } = useDevice()
  const widgetRef = useRef<ITwWidget | null>(null)

  function createWidget({
    symbol,
    interval,
    locale,
    datafeed,
  }: {
    symbol: string
    interval: IOutsideInterval
    locale: string
    datafeed: any
  }) {
    if (!chartRef?.current) {
      throw new Error('chartRef is not found')
    }

    widgetRef.current = new window.TradingView.widget({
      ...options(isMobile),
      ...{
        //   debug: true, // uncomment this line to see Library errors and warnings in the console
        //   fullscreen: true,
        // timeframe: '1m',
        autosize: true,
        container: chartRef.current,
        datafeed,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        library_path: '/charting_library/',
        custom_css_url: `../charting_library-custom/style.css`,
        symbol,
        interval: toTVResolution(interval),
        locale: toTWLocale(locale),
        favorites: {
          intervals: ['60'],
        },
      },
    })
  }

  return { widgetRef, createWidget }
}
