import { useStudy } from '@/components/ChartKline/module/study'
import { useCreateShape } from '@/components/ChartKline/module/study/useCreateShape'
import { useDatafeed } from '@/components/ChartKline/module/useDatafeed'
import { useWidget } from '@/components/ChartKline/module/useWidget'
import {
  toTVResolution,
  toTWLocale,
} from '@/components/ChartKline/module/utils'
import { ChartKlineProps } from '@/components/ChartKline/type'
import { useDevice } from '@/hooks/useDevice'
import { setupStore } from '@/store'
import { observer } from 'mobx-react-lite'
import { useEffect, useRef, useState } from 'react'

export const Widget = observer((props: ChartKlineProps) => {
  const { symbol, interval, setInterval, getPrecision } = props
  const { isMobile } = useDevice()

  const chartRef = useRef<HTMLDivElement>(null)
  const { widgetRef, createWidget } = useWidget({ chartRef })
  const { onSetSeries } = useCreateShape({ widgetRef })
  const { datafeed } = useDatafeed({ ...props, onBarsCallback: onSetSeries })
  const { onChartReady } = useStudy({
    symbol,
    widgetRef,
    setInterval,
    getPrecision,
  })

  const [backMonitor, setBackMonitor] = useState(0)

  useEffect(() => {
    if (widgetRef.current) {
      widgetRef.current.setSymbol(symbol, toTVResolution(interval))
    }
  }, [symbol])

  useEffect(() => {
    function handleVisibilityChange() {
      if (document.visibilityState === 'visible') {
        // 页面从后台返回时刷新
        setBackMonitor(Math.round(Math.random() * 1000000))
      }
    }
    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  useEffect(() => {
    const locale = toTWLocale(setupStore.lang)

    createWidget({ symbol, interval, locale, datafeed })
    onChartReady()

    return () => {
      // 组件卸载时删除图表
      if (widgetRef.current) {
        try {
          widgetRef.current.remove()
          widgetRef.current = null
        } catch (error) {
          console.error('保存图表状态时出错:', error)
        }
      }
    }
  }, [isMobile, setupStore.lang, backMonitor])

  return (
    <div className="relative h-full w-full">
      <div id="chartTWContainer" className="h-full w-full" ref={chartRef}></div>
    </div>
  )
})
