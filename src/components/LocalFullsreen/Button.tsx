import { useFullscreen } from '@/components/LocalFullsreen/context'
import { ArrowsAltOutlined, ShrinkOutlined } from '@ant-design/icons'

/**
 * @desc 切换按钮
 */
export const FullscreenToggleButton = () => {
  const { isFullscreen, toggleFullscreen } = useFullscreen()
  return (
    <div
      className="flex h-full cursor-pointer items-center justify-center"
      onClick={toggleFullscreen}
    >
      {isFullscreen ? (
        <ShrinkOutlined style={{ fontSize: '20px' }} />
      ) : (
        <ArrowsAltOutlined style={{ fontSize: '20px' }} />
      )}
    </div>
  )
}
