import clsx from 'clsx'
import { useState, useRef, useEffect, useCallback } from 'react'
import screenfull from 'screenfull'
import { FullscreenContext } from '@/components/LocalFullsreen/context'

/**
 * @desc 局部全局组件
 */

export const LocalFullscreen = ({
  className,
  children,
}: {
  className?: string
  children?: React.ReactNode
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!screenfull.isEnabled) return

    const handler = () => {
      const isNowFullscreen = screenfull.isFullscreen
      setIsFullscreen(isNowFullscreen)
    }

    screenfull.on('change', handler)
    return () => screenfull.off('change', handler)
  }, [])

  const toggleFullscreen = useCallback(() => {
    if (screenfull.isEnabled) {
      screenfull.toggle(document.body)
      setIsFullscreen(!isFullscreen)
    }
  }, [isFullscreen])

  return (
    <FullscreenContext.Provider value={{ isFullscreen, toggleFullscreen }}>
      <div
        ref={containerRef}
        className={clsx(
          isFullscreen
            ? 'fixed inset-0 z-50 h-screen w-screen'
            : 'relative h-full w-full',
          className || 'bg-[#1a1a1a]',
        )}
      >
        {children}
      </div>
    </FullscreenContext.Provider>
  )
}
