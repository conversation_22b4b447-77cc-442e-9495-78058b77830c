import type { FullscreenContextType } from '@/components/LocalFullsreen/type'
import { createContext, useContext } from 'react'

export const FullscreenContext = createContext<FullscreenContextType | null>(
  null,
)

export const useFullscreen = () => {
  const context = useContext(FullscreenContext)
  if (!context) {
    throw new Error('useFullscreen must be used within LocalFullscreen')
  }
  return context
}
