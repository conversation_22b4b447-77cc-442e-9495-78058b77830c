import styled from 'styled-components'
import { Popover, ConfigProvider, PopoverProps } from 'antd'

const StyledPopover = styled(Popover)``

export const SearchPopover = ({ children, ...props }: PopoverProps) => {
  return (
    <ConfigProvider>
      <StyledPopover
        arrow={false}
        color="#1A1B1E"
        placement="bottomLeft"
        trigger="click"
        {...props}
      >
        {children}
      </StyledPopover>
    </ConfigProvider>
  )
}
