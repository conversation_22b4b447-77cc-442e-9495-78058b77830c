import { ConfigProvider, Input, InputProps } from 'antd'
import styled from 'styled-components'

const AntdInput = styled(Input)``

export function InputSearch(props: InputProps) {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorBgBase: '#ffffff',
          colorPrimaryActive: '#28292f',
          colorBgElevated: '#28292f',
          colorBorder: '#28292f',
          colorText: '#ffffff',
          borderRadius: 2,
          colorTextPlaceholder: '#5f606d',
          controlHeight: 40,
        },
        components: {
          Input: {
            activeShadow: '#28292f',
            activeBorderColor: '#28292f',
            hoverBorderColor: '#28292f',
            hoverBg: '#28292f',
            activeBg: '#28292f',
          },
        },
      }}
    >
      <AntdInput className="bg-[#28292f] text-white" {...props} />
    </ConfigProvider>
  )
}
