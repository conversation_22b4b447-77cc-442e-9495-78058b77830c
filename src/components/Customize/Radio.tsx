import { Radio } from 'antd'
import styled from 'styled-components'
import tw from 'twin.macro'

export const RadioPerp = styled(Radio)`
  &.ant-radio-wrapper {
    ${tw`flex items-center gap-2 text-sm font-normal text-white`}
    margin-inline-start: 0px !important;
  }

  .ant-radio {
    ${tw`self-center`}
  }

  .ant-radio-inner {
    ${tw`size-5 rounded-md border`}
    background-color: black !important;
    border-color: #4a4a58 !important;

    &::after {
      display: none !important;
    }
  }

  &.ant-radio-wrapper-checked {
    .ant-radio-inner {
      background-color: white !important;
      border-color: white !important;

      &::after {
        content: '';
        display: block;
        position: relative;
        ${tw`left-1.5 top-1.5 m-0 h-[10px] w-[6px] -translate-x-1/2 -translate-y-1/2`}
        border: solid black;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg) translate(-1px, -2px);
        display: block !important;
        background-color: transparent !important;
      }
    }
  }
`
