import { DropdownProps, Dropdown, ConfigProvider } from 'antd'
import { DownOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import tw from 'twin.macro'

const DropdownRender = styled.div`
  ${tw`mt-1`}
  .ant-dropdown-menu-item {
    ${tw`!px-5`}
    &:hover {
      ${tw`bg-gray-50/10 !text-zinc-400`}
    }
  }
  .ant-dropdown-menu-item.ant-dropdown-menu-item-selected {
    ${tw`bg-gray-50/10 !text-zinc-400`}
    &:hover {
      ${tw`bg-gray-50/10 !text-zinc-400`}
    }
  }
`

const DropdownLightRender = styled.div`
  ${tw`mt-1`}
  .ant-dropdown-menu-item {
    ${tw`!px-5`}
    &:hover {
      ${tw`bg-zinc-400/10 !text-black`}
    }
  }
  .ant-dropdown-menu-item.ant-dropdown-menu-item-selected {
    ${tw`bg-zinc-400/10 !text-black`}
    &:hover {
      ${tw`bg-zinc-400/10 !text-black`}
    }
  }
`

export const DropdownPerp = (props: DropdownProps) => {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorBgContainer: 'inherit',
          colorBgElevated: '#1A1B1E',
          colorText: '#F9FAFB',
        },
      }}
    >
      <Dropdown
        dropdownRender={(menu) => <DropdownRender>{menu}</DropdownRender>}
        placement="bottomRight"
        {...props}
      >
        <div className="flex cursor-pointer items-center gap-1 text-white">
          {props.children}
          <DownOutlined className="size-3" />
        </div>
      </Dropdown>
    </ConfigProvider>
  )
}

export const DropdownLightPerp = (props: DropdownProps) => {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorBgContainer: 'inherit',
          colorBgElevated: '#F9FAFB',
          colorText: '#1A1B1E',
        },
      }}
    >
      <Dropdown
        dropdownRender={(menu) => (
          <DropdownLightRender>{menu}</DropdownLightRender>
        )}
        placement="bottomRight"
        {...props}
      >
        <div className="flex cursor-pointer items-center gap-1 text-black">
          {props.children}
          <DownOutlined className="size-3" />
        </div>
      </Dropdown>
    </ConfigProvider>
  )
}
