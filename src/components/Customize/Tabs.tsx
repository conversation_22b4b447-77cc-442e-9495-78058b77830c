import { ConfigProvider, Tabs, TabsProps } from 'antd'
import styled, { css } from 'styled-components'
import tw from 'twin.macro'

export const TabsDefault = styled(Tabs)<{ beforeBorderColor?: string }>`
  ${tw`text-sm`}
  .ant-tabs-nav {
    ${tw`m-0`}
  }

  .ant-tabs-tab-active {
    ${tw`font-bold`}
  }
  .ant-tabs-nav:before {
    ${({ beforeBorderColor }) =>
      beforeBorderColor &&
      css`
        border-color: ${beforeBorderColor};
      `}
  }
`

export const TabsTheme = (props: TabsProps & { theme: 'light' | 'dark' }) => {
  const { theme, ...rest } = props
  const isLight = theme === 'light'
  return (
    <ConfigProvider
      theme={{
        components: {
          Tabs: isLight
            ? {
                inkBarColor: '#fff',
                itemActiveColor: '#fff',
                itemSelectedColor: '#fff',
                itemHoverColor: '#fff',
                itemColor: '#8C8E9C',
              }
            : {
                inkBarColor: '#000',
                itemActiveColor: '#000',
                itemSelectedColor: '#000',
                itemHoverColor: '#000',
                itemColor: '#5F606D',
              },
        },
      }}
    >
      <TabsDefault beforeBorderColor={isLight ? '#1a1b1e' : ''} {...rest} />
    </ConfigProvider>
  )
}
