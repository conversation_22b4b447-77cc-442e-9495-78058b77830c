import { ButtonBlack } from '@/components/Customize/Button'
import { Modal } from '@/components/Customize/Modal'
import { observer } from 'mobx-react-lite'
import { useMemo } from 'react'
import ReactMarkdown from 'react-markdown'
import CANNY_DATA from '@/components/CannyModal/data.json'
import { guideStore, setupStore, userStore } from '@/store'
import dayjs from 'dayjs'
import i18n from '@/i18n'

export const CannyModal = observer(() => {
  const cannyData = useMemo(() => {
    const langId = setupStore.getLang(setupStore.lang)?.cannyId
    return (
      CANNY_DATA.entries.filter((entry) => {
        return entry.labels.some((label) => label.id === langId)
      })?.[0] || {}
    )
  }, [CANNY_DATA])

  const showCanny = useMemo(() => {
    return (
      userStore.isLogin &&
      cannyCreatedTime(cannyData.created) !== guideStore.cannyTimeMark
    )
  }, [userStore.isLogin, cannyData, guideStore.cannyTimeMark])

  function onClose() {
    guideStore.setShowCannyTime(cannyCreatedTime(cannyData?.created))
  }

  function cannyCreatedTime(time: string) {
    return dayjs(time).format('YYYY-MM-DD')
  }

  if (!cannyData?.id) return null

  return (
    <Modal
      open={showCanny}
      onCancel={onClose}
      footer={null}
      closable={true}
      maskClosable={false}
      title={null}
      centered
      destroyOnClose
    >
      <div className="h-full w-full md:w-[403px]">
        {/* 头部标题区域 */}
        <div className="flex items-center justify-center gap-2">
          <span className="text-2xl">✨</span>
          <span className="text-xl font-bold">{i18n.t('guide_update')}</span>
        </div>

        <div className="">
          {/* 更新内容直接展示 */}
          <div className="mb-2 overflow-hidden rounded-lg">
            <div
              key={cannyData.id}
              className="border-b border-gray-200 pb-4 last:border-b-0"
            >
              <h3 className="text-lg font-semibold">{cannyData.title}</h3>

              <div className="prose prose-sm max-w-none">
                <ReactMarkdown
                  components={{
                    img: ({ src, alt }) => (
                      <img
                        src={src}
                        alt={alt}
                        className="my-2 h-auto max-w-full rounded-lg shadow-sm"
                        loading="lazy"
                      />
                    ),
                    p: ({ children }) => (
                      <p className="mb-2 text-gray-700">{children}</p>
                    ),
                  }}
                >
                  {cannyData.markdownDetails}
                </ReactMarkdown>
              </div>
            </div>
          </div>

          {/* 底部按钮 */}
          <ButtonBlack onClick={onClose} className="h-12 w-full">
            <div className="text-sm">{i18n.t('guide_update_content')}</div>
          </ButtonBlack>

          {/* 底部链接 */}
          <div className="mt-3 flex items-center justify-between text-sm text-gray-400">
            <a
              href="https://canny.io"
              className="text-gray-400"
              target="_blank"
            >
              {i18n.t('guide_update_link')} ↗
            </a>
            <a
              href={`${cannyData?.url}?labels=${cannyData?.langName}`}
              target="_blank"
            >
              {i18n.t('guide_update_link_all')} ↗
            </a>
          </div>
        </div>
      </div>
    </Modal>
  )
})
