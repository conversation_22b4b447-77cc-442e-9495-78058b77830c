/**
 * @description 文字涂色器
 * @param amount 涨跌金额
 * @param children 子元素
 */

import styled from 'styled-components'
import tw from 'twin.macro'

export const SellText = styled.span`
  ${tw`text-red-600`}
`

export const BuyText = styled.span`
  ${tw`text-emerald-600`}
`

export const ColoringText = ({
  amount,
  children,
}: {
  amount: string
  children: React.ReactNode
}) => {
  if (parseFloat(amount) < 0) {
    return <SellText>{children}</SellText>
  }

  if (parseFloat(amount) >= 0) {
    return <BuyText>{children}</BuyText>
  }

  return <span className="text-white">{children}</span>
}
