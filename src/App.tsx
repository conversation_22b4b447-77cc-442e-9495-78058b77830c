import { useEffect } from 'react'
import { ConfigProvider, App as AntApp, theme } from 'antd'
import { RouterProvider } from 'react-router-dom'
import '@ant-design/v5-patch-for-react-19'
import { PrivyProvider } from '@privy-io/react-auth'
import { toSolanaWalletConnectors } from '@privy-io/react-auth/solana'
import { router } from '@/router'
import { bscOverride, mainnetOverride, baseOverride } from '@/utils/wallet'
import { setupStore, userStore } from '@/store'
import { chatroomManager } from '@/managers/chatroomManager'
import { useStore } from '@/pages/ChatRoom/ChatRoomComponent'
import { usePrivy } from '@privy-io/react-auth'
import { MessageProvider } from '@/components/ToastMessage/MessageProvider'
import { UserInteractionProvider } from '@/pages/ChatRoom/hooks/useUserInteracted'
import { supportedLanguage } from '@/config'

function AppContent() {
  const store = useStore()
  const { logout } = usePrivy()

  useEffect(() => {
    const storedLang = localStorage.getItem('i18nextLng')

    setupStore.setLang(storedLang as supportedLanguage)

    const handleLogout = async (event: any) => {
      if (!userStore.IS_LOGIN()) {
        return
      }

      try {
        console.log('收到登出事件', event.detail)
        userStore.reset()
        chatroomManager.logout()
        if (store) store.leaveRoom()

        if (logout) {
          await logout()
        }

        window.location.href = '/'
      } catch {
        // 如果发生错误，仍然进行页面跳转
        window.location.href = '/'
      }
    }

    window.addEventListener('pumpkin-logout', handleLogout)
    return () => {
      window.removeEventListener('pumpkin-logout', handleLogout)
    }
  }, [])

  return <RouterProvider router={router} />
}

function App() {
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.compactAlgorithm,
        token: {
          fontFamily:
            'Lexend, Inter, system-ui, Avenir, Helvetica, Arial, sans-serif',
        },
      }}
    >
      <MessageProvider>
        <UserInteractionProvider>
          <PrivyProvider
            appId={import.meta.env.VITE_PRIVY_APP_ID}
            config={{
              appearance: {
                accentColor: '#6A6FF5',
                theme: '#FFFFFF',
                showWalletLoginFirst: false,
                logo: '/login-logo.png',
                walletChainType: 'ethereum-and-solana',
                walletList: ['okx_wallet', 'phantom', 'detected_wallets'],
              },
              embeddedWallets: {
                createOnLogin: 'users-without-wallets',
                requireUserPasswordOnCreate: false,
                showWalletUIs: true,
                ethereum: {
                  createOnLogin: 'users-without-wallets',
                },
                solana: {
                  createOnLogin: 'users-without-wallets',
                },
              },
              supportedChains: [bscOverride, mainnetOverride, baseOverride],
              externalWallets: {
                solana: { connectors: toSolanaWalletConnectors() },
              },
              loginMethods: ['email', 'wallet'],
            }}
          >
            <AntApp className="h-full w-full">
              <AppContent />
            </AntApp>
          </PrivyProvider>
        </UserInteractionProvider>
      </MessageProvider>
    </ConfigProvider>
  )
}

export default App
