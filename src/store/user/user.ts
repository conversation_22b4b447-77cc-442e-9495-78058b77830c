import { action, computed, makeObservable, observable } from 'mobx'
import { setupLocalStorageSync } from '@/store/_setupSync'
import { getStore, isBlank, removeStore } from '@/utils'
import { STORAGE_TOKEN } from '@/utils/STORAGE_REG'
import { LOGIN_RETURN_MAIN } from '@/api/interface/LOGIN'
import { USERINFO_RETURN } from '@/api/interface/USERINFO'
import { usersManager } from '@/managers/usersManager'
import { ChatroomErrorCode, chatroomManager } from '@/managers/chatroomManager'
// 导入 walletStore 用于重置
import { walletStore } from '@/store/wallet/wallet'

type InfoData = LOGIN_RETURN_MAIN & USERINFO_RETURN['data']

class UserStore {
  // 用户token
  info: InfoData = {} as InfoData
  token: string = ''
  imToken: string = ''

  constructor() {
    makeObservable(this, {
      token: observable,
      imToken: observable,
      isLogin: computed,
      info: observable,
      reset: action,
      setToken: action,
    })

    setupLocalStorageSync(this, 'user.pumpkin', ['token', 'info', 'imToken'])
    setTimeout(() => {
      usersManager.fetchFriends()
      chatroomManager
        .connect(userStore.imToken)
        .then((res) => {
          if (
            res.code === ChatroomErrorCode.PK_SUCCESS ||
            res.code === ChatroomErrorCode.RC_SUCCESS
          ) {
            chatroomManager.fetchConversationList()
          }
        })
        .catch((err) => {
          console.log('UserStore connect', err)
        })
    }, 1000)
  }

  // 重置所有数据
  reset() {
    this.setToken('')
    this.setIMToken('')
    this.info = {} as InfoData

    removeStore(STORAGE_TOKEN)
    removeStore('info')
    removeStore('imToken')

    // 重置钱包数据
    walletStore.reset()

    chatroomManager.logout()
  }

  setToken(token: string) {
    this.token = token
  }

  setIMToken(imToken: string) {
    this.imToken = imToken
    chatroomManager.connect(this.imToken)
  }

  setInfo(data: InfoData) {
    this.info = {
      ...this.info,
      ...data,
    }
  }

  // 验证是否登录(可追踪)
  get isLogin() {
    return !isBlank(this.token)
  }

  // 验证是否登录
  IS_LOGIN() {
    const token = getStore(STORAGE_TOKEN)
    return !isBlank(this.token) || !isBlank(token)
  }
}

const userStore = new UserStore()

export { userStore }
