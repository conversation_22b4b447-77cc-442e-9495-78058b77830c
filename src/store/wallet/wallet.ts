import { action, makeObservable, observable } from 'mobx'
import { setupSessionStorageSync } from '@/store/_setupSync'

class WalletStore {
  // 钱包地址信息
  solanaAddress: string = ''
  solanaConnectorType: string = ''
  evmAddress: string = ''
  evmConnectorType: string = ''
  arbitrumAddress: string = ''

  constructor() {
    makeObservable(this, {
      solanaAddress: observable,
      solanaConnectorType: observable,
      evmAddress: observable,
      evmConnectorType: observable,
      arbitrumAddress: observable,
      setSolanaAddress: action,
      setEvmAddress: action,
      setArbitrumAddress: action,
      reset: action,
    })

    // 同步到 sessionStorage，保证刷新页面后数据不丢失，但在会话结束时清除
    setupSessionStorageSync(this, 'wallet.pumpkin', [
      'solanaAddress',
      'solanaConnectorType',
      'evmAddress',
      'evmConnectorType',
      'arbitrumAddress',
    ])
  }

  setSolanaAddress(address: string, connectorType: string = '') {
    this.solanaAddress = address
    this.solanaConnectorType = connectorType
  }

  setEvmAddress(address: string, connectorType: string = '') {
    this.evmAddress = address
    this.evmConnectorType = connectorType
  }

  setArbitrumAddress(address: string) {
    this.arbitrumAddress = address
  }

  reset() {
    this.solanaAddress = ''
    this.solanaConnectorType = ''
    this.evmAddress = ''
    this.evmConnectorType = ''
    this.arbitrumAddress = ''

    // 清除 sessionStorage 中的钱包数据
    try {
      sessionStorage.removeItem('wallet.pumpkin')
    } catch (error) {
      console.error('清除钱包数据失败:', error)
    }
  }

  // 获取当前网络的地址
  getAddressByNetwork(network: string): string {
    if (network.toLowerCase() === 'solana') {
      return this.solanaAddress
    } else if (network.toLowerCase() === 'arbitrum') {
      return this.arbitrumAddress
    } else {
      return this.evmAddress
    }
  }

  // 获取地址缓存对象，兼容现有代码
  getAddressCache() {
    return {
      solana: this.solanaAddress
        ? {
            address: this.solanaAddress,
            connectorType: this.solanaConnectorType,
          }
        : undefined,
      evm: this.evmAddress
        ? {
            address: this.evmAddress,
            connectorType: this.evmConnectorType,
          }
        : undefined,
    }
  }
}

const walletStore = new WalletStore()

export { walletStore }
