import { setupLocalStorageSync } from '@/store/_setupSync'
import { hyperSocketClient } from '@/store/perps/api'
import {
  IPerpInitStore,
  IPerpsStore,
  TCoinType,
  TKlineInterval,
  TTPSLType,
} from '@/store/perps/types'
import {
  AssetPosition,
  FrontendOrder,
  Hex,
  PerpsAssetCtx,
  PerpsClearinghouseState,
  PerpsMeta,
  WsWebData2,
} from '@nktkas/hyperliquid'
import { computed, makeAutoObservable, runInAction } from 'mobx'

/**
 * @desc 初始化
 * @returns {IPerpInitStore} 开放接口
 */
export class PerpsInit implements IPerpInitStore {
  // 当前活跃的币种
  public coin = 'BTC'
  // 时间选择的时间间隔
  public interval: TKlineInterval = '1h'
  // 交易滑点 「默认 8%，最小 1，最大 100」
  public slippage = 8
  // 交易货币类型
  public coinType: TCoinType = 'usdc'
  // 止盈止损类型
  public tpslCoinType: TTPSLType = 'percentage'

  // 账户全量信息
  public webData2: WsWebData2 | null = null
  // 存储账户全量信息订阅对象
  private webData2Subscription: any = null

  constructor(_rootStore: IPerpsStore) {
    makeAutoObservable(this, {
      meta: computed,
    })

    setupLocalStorageSync(this, 'perps.pumpkin', ['coin', 'interval'])
  }

  /**
   * @desc 设置当前活跃的币种
   * @param coin 币种
   */
  public setCoin(coin: string) {
    if (!coin) return
    this.coin = coin
  }

  /**
   * @desc 设置当前 K 线周期
   * @param interval 周期
   */
  public setInterval(interval: TKlineInterval) {
    if (!interval) return
    this.interval = interval
  }

  /**
   * @desc 设置交易滑点
   * @param slippage 滑点
   */
  public setSlippage(slippage: number) {
    if (!slippage) return
    if (slippage < 5) {
      this.slippage = 5
    } else if (slippage > 100) {
      this.slippage = 100
    } else {
      this.slippage = slippage
    }
  }

  /**
   * @desc 设置交易货币类型
   * @param coinType 货币类型
   */
  public setCoinType(coinType: TCoinType) {
    if (!coinType) return
    this.coinType = coinType
  }

  /**
   * @desc 设置止盈止损类型
   * @param tpslCoinType 止盈止损类型
   */
  public setTpslCoinType(tpslCoinType: TTPSLType) {
    if (!tpslCoinType) return
    this.tpslCoinType = tpslCoinType
  }

  /**
   * 订阅账户全量信息
   * @param user 用户钱包地址
   */
  async subscribeWebData2(user: Hex) {
    try {
      // 取消之前的订阅
      this.unsubscribeWebData2()

      // 创建新的订阅
      const subscription = await hyperSocketClient.webData2(
        { user },
        (data) => {
          if (!data) return
          runInAction(() => {
            this.webData2 = data as WsWebData2
          })
        },
      )
      this.webData2Subscription = subscription
    } catch {
      // 检查是否已经有订阅数据，如果有则不重试
      if (this.webData2Subscription) return
      // 如果没有订阅数据，则进行重试
      setTimeout(() => {
        this.subscribeWebData2(user)
      }, 1000)
    }
  }

  /**
   * 取消订阅账户全量信息更新
   */
  async unsubscribeWebData2() {
    if (this.webData2Subscription) {
      await this.webData2Subscription.unsubscribe()
      runInAction(() => {
        this.webData2Subscription = null
        this.webData2 = null
      })
    }
  }

  /**
   * @desc 获取账户全量信息
   * @returns {PerpsClearinghouseState | null} 账户全量信息
   */
  public get clearinghouseState(): PerpsClearinghouseState | null {
    return this.webData2?.clearinghouseState || null
  }

  /**
   * @desc 获取币种源数据
   * @returns {PerpsMeta | null} 币种源数据
   */
  public get meta(): PerpsMeta | null {
    return this.webData2?.meta || null
  }

  /**
   * @desc 获取币种 CTX 资产数据
   * @returns {PerpsAssetCtx[] | null} CTX
   */
  public get assetCtxs(): PerpsAssetCtx[] | null {
    return this.webData2?.assetCtxs || null
  }

  /**
   * @desc 获取当前持仓订单
   * @returns {AssetPosition[] | null} 当前持仓订单
   */
  public get assetPositions(): AssetPosition[] | null {
    return this.webData2?.clearinghouseState?.assetPositions || null
  }

  /**
   * @desc 获取当前委托订单
   * @returns {FrontendOrder[] | null} 当前委托订单
   */
  public get openOrders(): FrontendOrder[] | null {
    return this.webData2?.openOrders || null
  }
}
