import { hyperSocketClient, wsTransport } from '@/store/perps/api'
import { IPerpsStore, IPerpTradesStore, TWsTrade } from '@/store/perps/types'
import { makeAutoObservable, runInAction } from 'mobx'

/**
 * 最新成交
 * @returns {IPerpTradesStore} 开放接口
 */
export class PerpTradesStore implements IPerpTradesStore {
  // 最新成交数据
  public data: TWsTrade[] = []
  // 存储订阅对象，用于后续取消订阅
  protected tradesSubscription: any = null

  constructor(_rootStore: IPerpsStore) {
    makeAutoObservable(this)
  }

  /**
   * 订阅最新成交
   * @param coin 币种 「例：ETH」
   */
  async subscribeTrades(coin: string) {
    try {
      // 取消之前的订阅
      this.unsubscribeTrades()

      // 创建新的订阅
      const subscription = await hyperSocketClient.trades({ coin }, (data) => {
        if (!data || data.length === 0) return

        const existingIds = new Set(this.data.map((item) => item.tid))
        // 过滤数据
        const newTrades = data.filter((trade) => !existingIds.has(trade.tid))
        // 有新交易时才更新
        runInAction(() => {
          if (newTrades.length > 0) {
            // 直接追加到现有数据末尾
            this.data = [...this.data, ...newTrades]
            // 限定数据长度
            if (this.data.length > 100) {
              this.data = this.data.slice(-100)
            }
          }
        })
      })
      this.tradesSubscription = subscription
    } catch {
      // 检查是否已经有订阅数据，如果有则不重试
      if (this.tradesSubscription) return
      // 如果没有订阅数据，则进行重试
      setTimeout(() => {
        this.subscribeTrades(coin)
      }, 1000)
    }
  }

  /**
   * 取消订阅最新成交
   */
  async unsubscribeTrades() {
    if (this.tradesSubscription) {
      await this.tradesSubscription.unsubscribe()
      runInAction(() => {
        this.tradesSubscription = null
        this.data = []
      })
    }
  }
}
