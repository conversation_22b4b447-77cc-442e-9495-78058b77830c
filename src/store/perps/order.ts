import { hyperHttpClient } from '@/store/perps/api'
import {
  IAssetPosition,
  IFill,
  IFrontendOrder,
  IOrderStatusFrontendOrder,
  IPerpInitStore,
  IPerpMetaStore,
  IPerpOrderStore,
  IPerpsStore,
  IPositionOrder,
  IUserFundingUpdate,
} from '@/store/perps/types'
import { Hex } from '@nktkas/hyperliquid'
import { makeAutoObservable, reaction, runInAction } from 'mobx'

/**
 * @desc 订单(持仓订单、委托订单、历史成交订单、资金费历史、历史委托订单)
 * @property {IPositionOrder[]} positionOrders 当前持仓订单
 * @property {IFrontendOrder[]} pendingOrders 当前委托订单
 * @property {Fill[]} historyOrders 历史成交订单
 * @property {UserFundingUpdate[]} fundingHistory 资金费历史
 * @property {OrderStatus<FrontendOrder>[]} historicalOrders 历史委托订单
 *
 * @function getPositionOrders - 获取当前持仓订单
 * @function getPendingOrder - 获取当前委托订单
 * @function getHistoryOrder - 获取历史成交订单
 * @function getFundingHistory - 获取资金费历史
 * @function getHistoricalOrders - 获取历史委托订单
 */
export class PerpOrderStore implements IPerpOrderStore {
  // 当前委托订单
  public pendingOrders: IFrontendOrder[] = []
  // 历史成交订单
  public historyOrders: IFill[] = []
  // 资金费历史
  public fundingHistory: IUserFundingUpdate[] = []
  // 历史委托订单
  public historicalOrders: IOrderStatusFrontendOrder[] = []

  // 源数据
  _initStore: IPerpInitStore
  _metaStore: IPerpMetaStore
  _assetPositions: IAssetPosition[] | null = null

  constructor(rootStore: IPerpsStore) {
    makeAutoObservable(this)

    // 初始化
    this._initStore = rootStore.init
    this._metaStore = rootStore.meta
    // 监听
    reaction(
      () => [this._initStore.assetPositions, this._initStore.openOrders],
      ([assetPositions, openOrders]) => {
        if (assetPositions && openOrders) {
          runInAction(() => {
            this._assetPositions = assetPositions as IAssetPosition[]
            this.pendingOrders = openOrders as IFrontendOrder[]
          })
        }
      },
    )
  }

  /**
   * @desc 当前持仓订单
   * @return {IPositionOrder[]} 增强后的持仓订单
   */
  public get positionOrders(): IPositionOrder[] {
    if (!this._assetPositions) return []

    return this._assetPositions.map((position) => {
      const coin = position.position.coin
      return {
        ...position,
        ctx: this._metaStore?.getCoinInfo(coin) || {},
      }
    })
  }

  /**
   * 获取当前持仓订单
   * @param user 用户钱包地址
   */
  async getPositionOrders(user: Hex): Promise<IAssetPosition[]> {
    try {
      const result = await hyperHttpClient.clearinghouseState({ user })
      runInAction(() => {
        this._assetPositions = result?.assetPositions || null
      })

      return result?.assetPositions || []
    } catch (error) {
      console.error('获取当前持仓订单失败', error)
      return []
    }
  }

  /**
   * 获取当前委托订单
   * @param user 用户钱包地址
   */
  async getPendingOrder(user: Hex): Promise<IFrontendOrder[]> {
    try {
      const result = await hyperHttpClient.frontendOpenOrders({ user })
      runInAction(() => {
        this.pendingOrders = result as IFrontendOrder[]
      })

      return result as IFrontendOrder[]
    } catch (error) {
      console.error('获取当前委托订单失败', error)
      return []
    }
  }

  /**
   * 获取历史成交订单
   * @param user 用户钱包地址
   * @param aggregateByTime 是否按照时间聚合数据
   */
  async getHistoryOrder(
    user: Hex,
    aggregateByTime?: boolean,
  ): Promise<IFill[]> {
    try {
      const rawOrders = await hyperHttpClient.userFills({
        user,
        aggregateByTime,
      })

      runInAction(() => {
        this.historyOrders = rawOrders as IFill[]
      })

      return rawOrders
    } catch (error) {
      console.error('获取历史订单失败', error)
      return []
    }
  }

  /**
   * 获取资金费历史
   * @param user 用户钱包地址
   * @param startTime 起始时间（可选，如果没有提供起始时间，使用3天前）
   */
  async getFundingHistory(
    user: Hex,
    startTime?: number,
  ): Promise<IUserFundingUpdate[]> {
    try {
      const defaultStartTime = startTime || Date.now() - 3 * 24 * 60 * 60 * 1000

      const fundingHistory = await hyperHttpClient.userFunding({
        user,
        startTime: defaultStartTime,
      })

      runInAction(() => {
        this.fundingHistory = fundingHistory as IUserFundingUpdate[]
      })

      return fundingHistory as IUserFundingUpdate[]
    } catch (error) {
      console.error('获取资金费历史失败', error)
      return []
    }
  }

  /**
   * 获取历史委托订单
   * @desc 同成交历史调用相同，只是过滤条件不同
   * @param user 用户钱包地址
   */
  async getHistoricalOrders(user: Hex): Promise<IOrderStatusFrontendOrder[]> {
    try {
      const allOrders = await hyperHttpClient.historicalOrders({ user })

      runInAction(() => {
        this.historicalOrders = allOrders as IOrderStatusFrontendOrder[]
      })

      return allOrders as IOrderStatusFrontendOrder[]
    } catch (error) {
      console.error('获取历史委托失败', error)
      return []
    }
  }
}
