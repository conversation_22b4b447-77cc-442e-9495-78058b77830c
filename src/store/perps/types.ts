import {
  AssetPosition,
  Book,
  BookLevel,
  Candle,
  CandleSnapshotParameters,
  Fill,
  FillLiquidation,
  FrontendOrder,
  FundingUpdate,
  Hex,
  L2BookParameters,
  Order,
  OrderProcessingStatus,
  OrderStatus,
  OrderType,
  PerpsAssetCtx,
  PerpsClearinghouseState,
  PerpsMeta,
  PerpsUniverse,
  PredictedFunding,
  TIF,
  UserFees,
  UserFundingUpdate,
  WsActiveAssetData,
  WsBbo,
  WsTrade,
  WsWebData2,
} from '@nktkas/hyperliquid'

/**
 * Perps Store 接口
 */
export interface IPerpsStore {
  // 初始化
  init: IPerpInitStore
  // 图表
  kline: IPerpKlineStore
  // 订单簿
  book: IPerpBookStore
  // 最新成交
  trades: IPerpTradesStore
  // 币种数据
  meta: IPerpMetaStore
  // 用户账户信息
  user: IPerpUserStore
}

/**
 * init Store 接口
 */
export interface IPerpInitStore {
  // 交易对
  coin: string
  // 周期
  interval: TKlineInterval
  // 交易滑点
  slippage: number
  // 交易货币类型
  coinType: TCoinType
  // 止盈止损类型
  tpslCoinType: TTPSLType
  // 账户全量信息
  webData2: WsWebData2 | null
  // 账户全量信息
  clearinghouseState: PerpsClearinghouseState | null
  // 币种源数据
  meta: PerpsMeta | null
  // 币种 CTX 资产数据
  assetCtxs: PerpsAssetCtx[] | null
  // 当前持仓订单
  assetPositions: AssetPosition[] | null
  // 当前委托订单
  openOrders: FrontendOrder[] | null

  // 订阅账户全量信息
  subscribeWebData2: (user: Hex) => void
  // 取消订阅账户全量信息
  unsubscribeWebData2: () => void
  // 设置当前活跃的币种
  setCoin: (coin: string) => void
  // 设置当前 K 线周期
  setInterval: (interval: TKlineInterval) => void
  // 设置交易滑点
  setSlippage: (slippage: number) => void
  // 设置交易货币类型
  setCoinType: (coinType: TCoinType) => void
  // 设置止盈止损类型
  setTpslCoinType: (tpslCoinType: TTPSLType) => void
}
/**
 * Kline Store 接口
 */
export interface IPerpKlineStore {
  // 历史 K 线数据
  historyData: IKlineData[]
  // 最新 K 线数据
  lastData: IKlineData
  // 获取 K 线历史数据
  getKlineHistory: (
    // 币种
    coin: string,
    // 周期
    interval: TKlineInterval,
    // 开始时间
    startTime: number,
    // 结束时间
    endTime?: number,
  ) => Promise<IKlineData[]>
  // 订阅 K 线数据
  subscribeCandleData: (coin: string, interval: TKlineInterval) => void
  // 取消订阅 K 线数据
  unsubscribeCandleData: () => void
}

/**
 * fee Store 接口
 */
export interface IFundingRateStore {
  // 所有币种资金费率数据
  allFundingRate: IFundingRateMap[]
  // 活跃币种资金费率数据
  activeFundingRate: IFundingData | null
  // 用户手续费率数据
  userFees: IUserFees | null
  // 请求所有币种资金费率数据
  getFundingHistory: () => void
  // 获取单个币种资金费率数据
  getCoinFundingRate: (coin: string) => IFundingRateMap
  // 获取用户的手续费率的详细信息
  getUserFees: (user: Hex) => void
}

/**
 * Book Store 接口
 */
export interface IPerpBookStore {
  // 订单簿数据
  data: IBook
  // 价差
  priceDiff: IPriceDiff
  // 买卖盘最优价格
  bbo: IBbo
  // 买卖盘最优价格
  bestPrice: { bid: string; offer: string }
  // 获取订单簿
  getBook: (req: L2BookParameters) => Promise<IBook>
  // 订阅订单簿
  subscribeBook: (params: L2BookParameters) => void
  // 取消订阅订单簿
  unsubscribeBook: () => void
  // 订阅买卖盘最优价格
  subscribeBbo: (coin: string) => void
  // 取消订阅买卖盘最优价格
  unsubscribeBbo: () => void
  // 转换金额，专供修改计价币种时，ui 层的展示数据
  convertAmount: (
    quoteCurrency: string,
    amount: string,
    price: string,
    nSigFigs?: number,
  ) => string
}

/**
 * Trades Store 接口
 */
export interface IPerpTradesStore {
  // 最新成交数据
  data: TWsTrade[]
  // 订阅最新成交
  subscribeTrades: (coin: string) => void
  // 取消订阅最新成交
  unsubscribeTrades: () => void
}

/**
 * Meta Store 接口
 */
export interface IPerpMetaStore {
  // 币种源数据
  // allData 并没有过滤不活跃的币种
  // 如需过滤，需要检索使用 getVisibleAssetNamesArray 方法，生成快照进一步过滤数据。
  // 例如：
  // const visibleArray = getVisibleAssetNamesArray()
  // const visibleArray = allData.filter((item) => visibleArray.includes(item.name))
  allData: IActiveMetaCtx[]
  // 活跃币种基础数据
  activeMeta: IActiveMetaCtx
  // 活跃币种 ctx 数据
  activeAsset: IWsActiveAssetData
  // 获取所有币种信息
  getAllCoinsInfo: () => void
  // 计算24h资金变化
  dayChange: (markPx: string, prevDayPx: string) => IDayChange
  // 获取单个币种数据
  getCoinInfo: (coin: string) => IActiveMetaCtx
  // 订阅活跃币种 ctx 数据
  subscribeAssetCtxs: (coin: string, user: Hex) => void
  // 取消订阅活跃币种 ctx 数据
  unsubscribeAssetCtxs: () => void
  // 计算合约持仓量 = 合约成交量 * 预言机价格
  calculateOpenPositions: (dayNtlVlm: string, oraclePx: string) => string
  // 计算资金费率
  calculateFunding: (funding: string) => string
  // 获取活跃的币种名称数组
  getVisibleAssetNamesArray: () => string[]
}

/**
 * User Store 接口
 */
export type TPerpPolyUser = IPerpUserBalance & Omit<IPerpUserData, 'balance'>
export interface IPerpUserStore extends TPerpPolyUser {
  // 集合：合约概览
  data: IPerpUserData
  // 集合：用户余额
  rawBalance: IPerpUserBalance
  // 集合：当前活跃币种仓位数据
  assetPosition: {
    // 持仓数量
    value: string
    // 持仓方向
    side: TSide | 'N'
  }
  // 获取账户概览
  getAccountOverview: (user: Hex) => void
}

/**
 * Order Store 接口
 */
export interface IPerpOrderStore {
  // 当前持仓订单
  positionOrders: IPositionOrder[]
  // 当前委托订单
  pendingOrders: IFrontendOrder[]
  // 历史成交订单
  historyOrders: IFill[]
  // 资金费历史
  fundingHistory: IUserFundingUpdate[]
  // 历史委托订单
  historicalOrders: IOrderStatusFrontendOrder[]
  // 获取当前持仓订单
  getPositionOrders: (user: Hex) => Promise<IAssetPosition[]>
  // 获取当前委托订单
  getPendingOrder: (user: Hex) => Promise<IFrontendOrder[]>
  // 获取历史成交订单
  getHistoryOrder: (user: Hex, aggregateByTime?: boolean) => Promise<IFill[]>
  // 获取资金费历史
  getFundingHistory: (
    user: Hex,
    startTime?: number,
  ) => Promise<IUserFundingUpdate[]>
  // 获取历史委托订单
  getHistoricalOrders: (user: Hex) => Promise<IOrderStatusFrontendOrder[]>
}

/**
 * Setting Store 接口
 */
export interface ISettingStore {
  // 合约最小充值金额
  contractMinDepositAmount: number
  // 合约最小提现金额
  contractMinReceiveAmount: number
  // 获取最小充值金额
  getMinDepositAmount: () => void
}

/**
 * 时间间隔周期
 */
export type TKlineInterval = CandleSnapshotParameters['interval']

/**
 * K线数据接口
 * @desc 获取 K 线历史数据返回的数据结构
 * @see https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/info-endpoint#candle-snapshot
 */

export interface IKlineData {
  // 开始时间戳
  time: number
  // 结束时间戳
  endTime: number
  // 收盘价
  close: string
  // 最高价
  high: string
  // 周期
  interval: string
  // 最低价
  low: string
  // 交易笔数
  n: number
  // 开盘价
  open: string
  // 交易对
  coin: string
  // 成交量
  volume: string
}

export interface IKCandle extends Candle {
  // 开始时间戳
  t: number
  // 结束时间戳
  T: number
  // 收盘价
  c: string
  // 最高价
  h: string
  // 周期
  i: string
  // 最低价
  l: string
  // 交易笔数
  n: number
  // 开盘价
  o: string
  // 交易对
  s: string
  // 成交量
  v: string
}

export type TOrderBookData = L2BookParameters

/**
 * 订单簿数据接口
 * @desc 获取订单簿数据返回的数据结构
 * @see https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/info-endpoint#l2-book-snapshot
 */
export interface IBook extends Book {
  // 交易对
  coin: string
  // 快照时间戳
  time: number
  // 订单簿数据 [bids, asks]
  levels: [IBookLevel[], IBookLevel[]]
}

export interface IBookLevel extends BookLevel {
  // 价格
  px: string
  // 总数量(合计)
  sz: string
  // 订单数量(数量)
  n: number
}

/**
 * 最新成交数据接口
 * @desc 获取最新成交数据返回的数据结构
 */
export interface TWsTrade extends WsTrade {
  // 交易对
  coin: string
  // 交易方向
  side: TSide
  // 价格
  px: string
  // 数量
  sz: string
  // 哈希
  hash: Hex
  // 时间戳
  time: number
  // 资产唯一ID
  tid: number
  // [买方, 卖方]
  users: [Hex, Hex]
}

/**
 * 价差
 */
export interface IPriceDiff {
  // 价差
  diff: string
  // 价差百分比
  percentage: string
}

/**
 * 24h资金变化
 */
export interface IDayChange {
  // 涨跌金额
  amount: string
  // 涨跌幅
  percentage: string
}

/**
 * 资产明细数据接口
 * @desc 获取特定资产数据返回的数据结构
 * @desc 24h资金变化: 涨跌金额 = 标记价格(最新价格) - 前一天收盘价
 * @desc 24h资金变化: 涨跌幅 = (标记价格(最新价格) - 前一天收盘价) / 前一天收盘价 * 100%
 * @desc 资金费倒计时 = 是固定时间，需要前端自行计算，UTC每1小时一次，详情见下方文档
 * @see https://hyperliquid.gitbook.io/hyperliquid-docs/trading/funding#overview
 */
export interface IActiveMetaCtx extends IPerpsUniverse, IPerpsAssetCtx {
  // 币种资产ID（下单需要用）
  assetid: number
}

/**
 * Meta 数据
 * @description
 * 价格精度「MAX_DECIMALS - szDecimals」
 * 小数点后不能超过 MAX_DECIMALS - szDecimals 个位数
 * 其中 MAX_DECIMALS 对于永续合约为 6 位，对于现货合约为 8 位。
 * 无论有效数字位数多少，价格始终允许使用整数。例如，即使 12345.6 无效， 123456.0 也是有效价格
 * @see https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/tick-and-lot-size?utm_source=chatgpt.com
 */
export interface IPerpsUniverse extends PerpsUniverse {
  // 币种数量精度（合法小数位数）「例 2 = 10.00; 0 = 10」
  szDecimals: number
  // 交易对
  name: string
  // 最大杠杆
  maxLeverage: number
  // 是否只允许逐仓交易
  onlyIsolated?: true
  // 是否下架
  isDelisted?: true
}

/**
 * Ctx 数据
 */
export interface IPerpsAssetCtx extends PerpsAssetCtx {
  // 最大杠杆
  maxLeverage: number
  // 资金费率
  funding: string
  // 总持仓量（本位币）
  openInterest: string
  // 溢价
  premium: string | null
  // 预言机价格
  oraclePx: string
  // 冲击价格
  impactPxs: string[] | null
  // 日基础成交量
  dayBaseVlm: string
  // 前一天收盘价
  prevDayPx: string
  // 24h成交量
  dayNtlVlm: string
  // 标记价格(最新价格)
  markPx: string
  // 中间价
  midPx: string | null
}

/**
 * 用户账户信息
 */
export interface IPerpUserData {
  // 全仓保证金比率
  marginRatio: string
  // 账户历史净值
  historyCapitalUsd: string
  // 未实现盈亏
  unrealizedPnlUsd: string
  // 维持保证金
  maintenanceMarginUsd: string
  // 全仓账户杠杆
  accountLeverage: string
  // 用户余额(格式化后的数据)
  balance: IPerpUserBalance
}

/**
 * 用户余额
 */
export interface IPerpUserBalance {
  // 可用余额
  balanceUsd: string
  // 可提现金额
  withdrawable: string
  // 账户总价值
  accountValue: string
  // 已占用保证金
  totalMarginUsed: string
}

/**
 * 持仓订单 增强数据
 */
export interface IPositionOrder extends IAssetPosition {
  ctx: IActiveMetaCtx
}

/**
 * 持仓订单 源数据
 */
export interface IAssetPosition extends AssetPosition {
  // 持仓类型
  type: 'oneWay'
  // 持仓详情
  position: {
    // 交易对
    coin: string
    // 持仓数量
    szi: string
    // 杠杆详情
    leverage: TLeverage
    // 平均入场价格
    entryPx: string
    // 持仓价值
    positionValue: string
    // 未实现盈亏
    unrealizedPnl: string
    // 回报率
    returnOnEquity: string
    // 强平价格
    liquidationPx: string | null
    // 已使用保证金
    marginUsed: string
    // 最大杠杆
    maxLeverage: number
    // 累计资金费
    cumFunding: {
      // 累计资金费
      allTime: string
      // 持仓以来资金费
      sinceOpen: string
      // 持仓以来资金费
      sinceChange: string
    }
  }
}

/**
 * @desc 订单
 * @extends IOrder Order 类型注释
 */
export interface IFrontendOrder extends FrontendOrder {
  // 触发条件
  triggerCondition: string
  // 是否为触发订单
  isTrigger: boolean
  // 触发价格
  triggerPx: string
  // 子订单
  children: IFrontendOrder[]
  // 是否为持仓TP/SL订单
  isPositionTpsl: boolean
  // 是否只减仓
  reduceOnly: boolean
  // 订单类型
  orderType: OrderType
  // 时间-在-force 选项
  tif: TIF | null
  // 客户端订单ID
  cloid: Hex | null
}

/**
 * 订单 源数据
 */
export interface IOrder extends Order {
  // 交易对
  coin: string
  // 交易方向
  side: TSide
  // 限价
  limitPx: string
  // 数量
  sz: string
  // 订单ID
  oid: number
  // 订单创建时间戳
  timestamp: number
  // 订单原始数量
  origSz: string
  // 客户端订单ID
  cloid?: Hex
  // 是否只减仓
  reduceOnly?: true
}

/**
 * 历史成交订单
 */
export interface IFill extends Fill {
  // 交易对
  coin: string
  // 价格
  px: string
  // 数量
  sz: string
  // 交易方向
  side: TSide
  // 交易时间戳
  time: number
  // 起始持仓数量
  startPosition: string
  // 前端显示方向
  dir: string
  // 已实现盈亏
  closedPnl: string
  // 交易哈希
  hash: Hex
  // 订单ID
  oid: number
  // 是否为成交订单
  crossed: boolean
  // 手续费
  fee: string
  // 唯一交易标识
  tid: number
  // 客户端订单ID
  cloid?: Hex
  // 清算详情
  liquidation?: IFillLiquidation
  // 手续费币种
  feeToken: string
}

/**
 * 清算详情 */
export interface IFillLiquidation extends FillLiquidation {
  // 清算用户地址
  liquidatedUser: Hex
  // 清算价格
  markPx: string
  // 清算方式「market: 市价, backstop: 强平」
  method: 'market' | 'backstop'
}

/**
 * 资金费率更新 */
export interface IUserFundingUpdate extends UserFundingUpdate {
  // 更新时间戳
  time: number
  // 交易哈希
  hash: Hex
  // 更新详情
  delta: IFundingUpdate
}

/**
 *  资金费率更新详情 */
export interface IFundingUpdate extends FundingUpdate {
  // 更新类型
  type: 'funding'
  // 交易对
  coin: string
  // 转移的 USDC 金额
  usdc: string
  // 持仓数量
  szi: string
  // 应用的资金费率
  fundingRate: string
  // 样本数量
  nSamples: number | null
}

/**
 * 资金费率更新
 */
export interface IOrderStatusFrontendOrder extends OrderStatus<FrontendOrder> {
  // 订单详情
  order: IFrontendOrder
  // 订单处理状态
  status: OrderProcessingStatus
  // 订单状态最后更新时间戳
  statusTimestamp: number
}

/**
 * 资金费率
 */
export type IPredictedFunding = PredictedFunding &
  [
    // 资产符号「例：BTC」
    string,
    // 预测资金费率数据
    [
      // 交易所符号(合约交易主要用 HlPerp)
      IFundingPlatform,
      // 预测资金费率数据
      IFundingData,
    ][],
  ]
// 交易所符号(合约交易主要用 HlPerp)
export type IFundingPlatform = string | 'BinPerp' | 'HlPerp' | 'BybitPerp'
// 预测资金费率数据
export type IFundingData = {
  // 预测资金费率
  fundingRate: string
  // 下一个资金费率时间戳
  nextFundingTime: number
  // 资金费率周期
  fundingIntervalHours?: number
} | null

/**
 * 资金费率数据
 */
export interface IFundingRateMap {
  [platform: IFundingPlatform]: IFundingData
}

/**
 * 用户手续费率数据
 */
export interface IHlUserFees extends UserFees {
  // 每日用户交易量
  dailyUserVlm: {
    // 日期 YYYY-M-D
    date: `${number}-${number}-${number}`
    // 用户交叉交易量
    userCross: string
    // 用户添加流动性量
    userAdd: string
    // 交易所总交易量
    exchange: string
  }[]
  // 手续费率信息
  feeSchedule: {
    // 交叉交易手续费率
    cross: string
    // 添加流动性手续费率
    add: string
    // 现货交叉交易手续费率
    spotCross: string
    // 现货添加流动性手续费率
    spotAdd: string
    // 手续费等级信息
    tiers: {
      // VIP 手续费等级信息
      vip: {
        // 名义交易量截止
        ntlCutoff: string
        // 交叉交易手续费率
        cross: string
        // 添加流动性手续费率
        add: string
        // 现货交叉交易手续费率
        spotCross: string
        // 现货添加流动性手续费率
        spotAdd: string
      }[]
      // 市场做市商手续费等级信息
      mm: {
        // 做市商手续费等级截止
        makerFractionCutoff: string
        // 添加流动性手续费率
        add: string
      }[]
    }
    // 推荐折扣率
    referralDiscount: string
  }
  // 合约交叉交易率
  userCrossRate: string
  // 合约添加流动性率
  userAddRate: string
  // 现货交叉交易率
  userSpotCrossRate: string
  // 现货添加流动性率
  userSpotAddRate: string
  // 活跃推荐折扣率
  activeReferralDiscount: string
  // 试用详情
  trial: unknown | null
  // 试用奖励
  feeTrialReward: string
  // 下次试用时间戳
  nextTrialAvailableTimestamp: unknown | null
}

/**
 * 用户手续费率数据
 */
export interface IUserFees {
  // 合约交叉交易率
  userCrossRate: string
  // 合约添加流动性率
  userAddRate: string
  // 活跃推荐折扣率
  activeReferralDiscount: string
}

/**
 * @desc 交易货币类型
 * @example
 * usdc = usd;
 * coin = 当前币种;
 */
export type TCoinType = 'usdc' | 'coin'

/**
 * @desc 止盈止损类型
 * @example
 * amount = 金额;
 * percentage = 百分比;
 */
export type TTPSLType = 'amount' | 'percentage'

/**
 * 买卖盘最优价格
 */
export interface IBbo {
  [key: string]: IWsBbo
}

export interface IWsBbo extends WsBbo {
  // 交易对
  coin: string
  // 时间戳
  time: number
  // 买卖盘最优价格 [bid = 最优买价, offer = 最优卖价]
  bbo: [IBookLevel | undefined, IBookLevel | undefined]
}

export interface IBookLevel {
  // 价格
  px: string
  // 总数量
  sz: string
  // 订单数量
  n: number
}

/**
 * 交易方向 「"B" = Bid/Buy, "A" = Ask/Sell」
 */
export type TSide = 'B' | 'A'

/**
 * 活跃币种 ctx 数据
 */
export interface IWsActiveCtx {
  [key: string]: IWsActiveAssetData
}

export interface IWsActiveAssetData extends WsActiveAssetData {
  // 用户地址
  user: Hex
  // 币种
  coin: string
  // 杠杆配置
  leverage: TLeverage
  // 最大交易量范围 [min, max]
  maxTradeSzs: [string, string]
  // 可交易量范围 [min, max]
  availableToTrade: [string, string]
}

/**
 * 杠杆配置
 */
export type THlPerpType = 'isolated' | 'cross'
export type TLeverage = IsolatedLeverage | ICrossLeverage

/**
 * 逐仓信息
 */
export type IsolatedLeverage = {
  // 杠杆类型: 逐仓
  type: 'isolated'
  // 杠杆值
  value: number
  // 杠杆金额
  rawUsd: string
}

/**
 * 全仓信息
 */
export type ICrossLeverage = {
  // 杠杆类型: 全仓
  type: 'cross'
  // 杠杆值
  value: number
}
