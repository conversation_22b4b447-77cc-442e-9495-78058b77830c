import {
  hyperHttpClient,
  hyperSocketClient,
  wsTransport,
} from '@/store/perps/api'
import {
  TKlineInterval,
  IKlineData,
  IPerpsStore,
  IPerpKlineStore,
  IKCandle,
} from '@/store/perps/types'
import { makeAutoObservable, runInAction } from 'mobx'

/**
 * @desc 图表
 * @returns {IPerpKlineStore} 开放接口
 */

export class PerpKlineStore implements IPerpKlineStore {
  // K线历史数据
  public historyData: IKlineData[] = []
  // k线最新数据
  public lastData: IKlineData = {} as IKlineData
  // 存储订阅对象，用于后续取消订阅
  protected candleSubscription: any = null

  constructor(_rootStore: IPerpsStore) {
    makeAutoObservable(this, {
      transformData: false,
    })
  }

  /**
   * 获取 K 线历史数据
   * @desc 固定获取约 1000 条数据, 最大仅支持最近 5000 条数据
   * @param coin 币种 「例：ETH」
   * @param interval 周期 「例如 1m | 5m」
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  async getKlineHistory(
    coin: string,
    interval: TKlineInterval,
    startTime: number,
    endTime?: number,
  ): Promise<IKlineData[]> {
    try {
      const result = await hyperHttpClient.candleSnapshot({
        coin,
        interval,
        startTime,
        endTime,
      })

      if (result?.length === 0) return []

      const data = result.map((item) => this.transformData(item))

      runInAction(() => {
        this.historyData = data
      })

      return data
    } catch (error) {
      console.error('获取K线历史数据失败', error)
      return []
    }
  }

  /**
   * 订阅 K 线数据
   * @param coin 币种 「例：ETH」
   * @param interval 周期 「例如 1m | 5m」
   */
  async subscribeCandleData(coin: string, interval: TKlineInterval) {
    try {
      // 取消之前的订阅
      this.unsubscribeCandleData()

      // 创建新的订阅
      const subscription = await hyperSocketClient.candle(
        { coin, interval },
        (data) => {
          if (!data) return
          runInAction(() => {
            this.lastData = this.transformData(data as IKCandle)
          })
        },
      )
      this.candleSubscription = subscription
    } catch {
      // 检查是否已经有订阅数据，如果有则不重试
      if (this.candleSubscription) return
      // 如果没有订阅数据，则进行重试
      setTimeout(() => {
        this.subscribeCandleData(coin, interval)
      }, 1000)
    }
  }

  /**
   * 取消订阅 K 线数据
   */
  async unsubscribeCandleData() {
    if (this.candleSubscription) {
      await this.candleSubscription.unsubscribe()
      runInAction(() => {
        this.candleSubscription = null
        this.lastData = {} as IKlineData
      })
    }
  }

  /**
   * 数据字段转换
   */
  transformData(item: IKCandle): IKlineData {
    if (!item) return {} as IKlineData

    return {
      ...item,
      endTime: item.T,
      time: item.t,
      interval: item.i,
      coin: item.s,
      close: item.c,
      high: item.h,
      low: item.l,
      open: item.o,
      volume: item.v,
    }
  }
}
