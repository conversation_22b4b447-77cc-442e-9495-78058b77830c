import { setupLocalStorageSync } from '@/store/_setupSync'
import { hyperHttpClient, hyperSocketClient } from '@/store/perps/api'
import {
  IActiveMetaCtx,
  IDayChange,
  IPerpInitStore,
  IPerpMetaStore,
  IPerpsStore,
  IsolatedLeverage,
  IWsActiveAssetData,
  IWsActiveCtx,
} from '@/store/perps/types'
import { $ten, Decimal } from '@/utils'
import {
  Hex,
  PerpsAssetCtx,
  PerpsClearinghouseState,
  PerpsMeta,
} from '@nktkas/hyperliquid'
import { makeAutoObservable, reaction, runInAction } from 'mobx'

/**
 * @desc 币种列表数据
 * @returns {IPerpMetaStore} 开放接口
 */
export class PerpMetaStore implements IPerpMetaStore {
  // 数据
  asset: IWsActiveCtx = {} as IWsActiveCtx
  // 源数据
  _initStore: IPerpInitStore
  _clearinghouseState: PerpsClearinghouseState | undefined = undefined

  _meta: PerpsMeta | null = null
  _assetCtxs: PerpsAssetCtx[] | null = null
  // 存储订阅对象，用于后续取消订阅
  private assetCtxsSubscription: any = null

  constructor(rootStore: IPerpsStore) {
    makeAutoObservable(this, {
      dayChange: false,
      getCoinInfo: false,
      calculateOpenPositions: false,
      calculateFunding: false,
    })

    setupLocalStorageSync(this, 'perp.meta', ['asset'])

    // 初始化
    this._initStore = rootStore.init
    // 监听
    reaction(
      () => this._initStore.clearinghouseState,
      (newData) => {
        if (newData) {
          runInAction(() => {
            this._clearinghouseState = newData
          })
        }
      },
    )
    // 监听
    reaction(
      () => [this._initStore.meta, this._initStore.assetCtxs],
      ([meta, assetCtxs]) => {
        runInAction(() => {
          this._meta = meta as PerpsMeta
          this._assetCtxs = assetCtxs as PerpsAssetCtx[]
        })
      },
    )
  }

  /**
   * @desc 获取所有币种数据
   * @return {IActiveMetaCtx[]} 所有币种数据
   */
  public get allData(): IActiveMetaCtx[] {
    return Object.values(this._dataMap)
  }

  // 获取活跃币种基础数据
  public get activeMeta(): IActiveMetaCtx {
    if (!this._initStore.coin) return {} as IActiveMetaCtx
    return this._dataMap[this._initStore.coin]
  }

  // 获取活跃币种基础数据
  public get activeAsset(): IWsActiveAssetData {
    const coin = this._initStore.coin
    if (!coin) return {} as IWsActiveAssetData
    return this.asset[coin]
  }

  /**
   * @desc 计算24h资金变化，用户 ui 展示时使用
   * @param markPx 标记价格
   * @param prevDayPx 前一天收盘价
   *
   * @return {string} amount 「涨跌金额 = 标记价格 - 前一天收盘价」
   * @return {string} percentage 「涨跌幅 = (标记价格 - 前一天收盘价) / 前一天收盘价 * 100%」
   */
  public dayChange(markPx: string, prevDayPx: string): IDayChange {
    const amount = $ten.sub(markPx, prevDayPx)
    const percentage = $ten.mul(
      $ten.div($ten.sub(markPx, prevDayPx), prevDayPx),
      '100',
    )
    return { amount, percentage }
  }

  /**
   * @desc 计算合约持仓量
   * @param openInterest 总持仓量（本位币）
   * @param oraclePx 预言机价格
   * @return {string} 合约持仓量 = 总持仓量（本位币） * 预言机价格
   */
  public calculateOpenPositions(
    openInterest: string,
    oraclePx: string,
  ): string {
    return $ten.toFixed($ten.mul(openInterest, oraclePx), 6, true)
  }

  /**
   * @desc 计算资金费率
   * @param funding 资金费率
   * @return {string} 资金费率
   */
  public calculateFunding(funding: string): string {
    return $ten.toFixed($ten.mul(funding, '100'), 4, true)
  }

  // 获取所有币种的完整信息
  async getAllCoinsInfo() {
    try {
      // meta - 所有合约币种的元数据
      // assetCtxs - 所有合约币种的资产 CTX
      const [meta, assetCtxs] = await hyperHttpClient.metaAndAssetCtxs()

      runInAction(() => {
        this._meta = meta
        this._assetCtxs = assetCtxs
      })
    } catch (error) {
      console.error('获取所有币种信息失败', error)
    }
  }

  /**
   * 获取指定币种的资产 CTX
   * @param coin 币种 「例：ETH」
   * @param user 用户地址
   */
  async subscribeAssetCtxs(coin: string, user: Hex) {
    try {
      // 取消之前的订阅
      this.unsubscribeAssetCtxs()

      // 创建新的订阅
      const subscription = await hyperSocketClient.activeAssetData(
        { coin, user },
        (data) => {
          if (!data?.coin) return
          runInAction(() => {
            this.asset[data?.coin] = data as IWsActiveAssetData
          })
        },
      )
      this.assetCtxsSubscription = subscription
    } catch {
      // 检查是否已经有订阅数据，如果有则不重试
      if (this.assetCtxsSubscription) return
      // 如果没有订阅数据，则进行重试
      setTimeout(() => {
        this.subscribeAssetCtxs(coin, user)
      }, 1000)
      return {}
    }
  }

  /**
   * 取消订阅订单簿
   */
  async unsubscribeAssetCtxs() {
    if (this.assetCtxsSubscription) {
      await this.assetCtxsSubscription.unsubscribe()
      runInAction(() => {
        this.assetCtxsSubscription = null
        this.asset = {} as IWsActiveCtx
      })
    }
  }

  /**
   * @desc 所有币种的完整信息(源数据拼接)
   * @return {Record<string, IActiveMetaCtx>} 所有币种的完整信息
   */
  get _dataMap(): Record<string, IActiveMetaCtx> {
    const result: Record<string, IActiveMetaCtx> = {}
    const meta = this._meta
    const assetCtxs = this._assetCtxs

    if (!meta || !assetCtxs) return {}

    for (let i = 0; i < meta.universe.length; i++) {
      // 过滤下架的币种，这是第一步。
      // 过滤不活跃的币种，需要检索使用 getVisibleAssetNamesArray 方法，生成快照手动隐藏。
      if (meta?.universe[i]?.isDelisted && !meta.universe[i]?.onlyIsolated) {
        continue
      }

      const asset = {
        ...meta.universe[i],
        ...assetCtxs[i],
        assetid: i,
      }
      result[meta.universe[i].name] = asset
    }

    return result
  }

  /**
   * @desc 获取单个币种的完整信息
   * @param coin 币种名称
   * @return {IActiveMetaCtx} 单个币种的完整信息
   */
  public getCoinInfo(coin: string): IActiveMetaCtx {
    return this._dataMap[coin]
  }

  /**
   * @desc 获取活跃的币种名称数组
   * @return {string[]} 活跃的币种名称数组
   */
  public getVisibleAssetNamesArray(): string[] {
    const allData = Object.values(this._dataMap)
    const visibleArray = new Set<string>()

    const shouldHideAsset = (data: IActiveMetaCtx) => {
      // 下架的币种
      if (data?.isDelisted && !data.onlyIsolated) {
        return true
      }

      if (
        // 盘口没有数据
        data.dayNtlVlm !== '0.0' &&
        // 总持仓量为空
        data.openInterest !== '0.0' &&
        // 影响价格为空
        data.impactPxs !== null &&
        // 溢价为空
        data.premium !== null
      ) {
        return false
      }

      // 价格无效
      if (data?.markPx && data?.prevDayPx) {
        const dayVal = this.dayChange(data.markPx, data.prevDayPx)

        const amount = new Decimal(dayVal.amount)
        const percentage = new Decimal(dayVal.percentage)

        if (amount.isZero() && percentage.isZero()) return true
      }

      return false
    }

    for (let i = 0; i < allData.length; i++) {
      const data = allData[i]
      if (shouldHideAsset(data)) continue
      visibleArray.add(data.name)
    }

    return Array.from(visibleArray)
  }
}
