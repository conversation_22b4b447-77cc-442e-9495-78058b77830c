import * as hl from '@nktkas/hyperliquid'

/**
 * 初始化 WebSocket 传输层
 */
export const wsTransport = new hl.WebSocketTransport({
  url: import.meta.env.VITE_APP_HL_WS_URL,
})

/**
 * 初始化 HTTP 传输层
 */
const httpTransport = new hl.HttpTransport({
  // 设为 true 则使用测试网，反之则是主网
  isTestnet: import.meta.env.VITE_APP_HL_HTTP_TYPE === 'testnet',
  // 请求超时时间（毫秒）
  timeout: 10000,
})

/**
 * 创建 WebSocket 客户端
 */
const hyperSocketClient = new hl.EventClient({ transport: wsTransport })

/**
 * 创建 HTTP 客户端
 */
const hyperHttpClient = new hl.PublicClient({ transport: httpTransport })

export { hyperSocketClient, hyperHttpClient }
