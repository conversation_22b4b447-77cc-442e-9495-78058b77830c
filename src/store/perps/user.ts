import { hyperHttpClient } from '@/store/perps/api'
import {
  IPerpInitStore,
  IPerpMetaStore,
  IPerpsStore,
  IPerpUserBalance,
  IPerpUserData,
  IPerpUserStore,
  TSide,
} from '@/store/perps/types'
import { $ten, Decimal } from '@/utils'
import { PerpsClearinghouseState } from '@nktkas/hyperliquid'
import { makeAutoObservable, reaction, runInAction } from 'mobx'
import { Hex } from 'viem'

/**
 * @desc 用户账户信息
 * @returns {IPerpUserStore} 开放接口
 */
export class PerpUserStore implements IPerpUserStore {
  // 账户元数据
  _initStore: IPerpInitStore
  _metaStore: IPerpMetaStore
  _clearinghouseState: PerpsClearinghouseState | undefined = undefined

  constructor(rootStore: IPerpsStore) {
    makeAutoObservable(this)
    // 初始化
    this._initStore = rootStore.init
    this._metaStore = rootStore.meta
    // 监听
    reaction(
      () => this._initStore.clearinghouseState,
      (newData) => {
        if (newData) {
          runInAction(() => {
            this._clearinghouseState = newData
          })
        }
      },
    )
  }

  /**
   * @desc 集合：合约概览
   * @returns {object} 合约概览
   */
  public get data(): IPerpUserData {
    return {
      marginRatio: this.marginRatio,
      historyCapitalUsd: $ten.toFixed(this.historyCapitalUsd, 2, true),
      unrealizedPnlUsd: $ten.toFixed(this.unrealizedPnlUsd, 2, true),
      maintenanceMarginUsd: $ten.toFixed(this.maintenanceMarginUsd, 2, true),
      accountLeverage: this.accountLeverage,
      balance: this.formatBalance(this.rawBalance),
    }
  }

  // 集合：用户余额
  public get rawBalance(): IPerpUserBalance {
    return {
      balanceUsd: this.balanceUsd,
      withdrawable: this.withdrawable,
      accountValue: this.accountValue,
      totalMarginUsed: this.totalMarginUsed,
    }
  }

  /**
   * 获取账户概览数据
   * @param user 用户钱包地址
   */
  async getAccountOverview(user: Hex) {
    try {
      // 获取账户状态
      const accountState = await hyperHttpClient.clearinghouseState({ user })
      this._clearinghouseState = accountState
    } catch (error) {
      console.error('获取账户概览失败', error)
    }
  }

  /**
   * @desc 账户历史净值
   * @returns {string} 账户历史净值 = 账户总价值 - 未实现盈亏
   */
  public get historyCapitalUsd(): string {
    const accountValue = this.accountValue
    const unrealizedPnl = Math.abs(Number(this.unrealizedPnlUsd))
    const total = $ten.add(accountValue, unrealizedPnl)
    return $ten.toFixed(total, 6)
  }

  /**
   * @desc 账户总价值
   * @returns {string} 账户总价值 = 存入资金 + 已实现盈亏 + 累计资金费 + 未实现盈亏
   */
  public get accountValue(): string {
    const isolated = this._metaStore.activeAsset?.leverage?.type === 'isolated'

    if (isolated) {
      return this._clearinghouseState?.marginSummary?.accountValue ?? '0'
    }

    return this._clearinghouseState?.crossMarginSummary?.accountValue ?? '0'
  }

  /**
   * @desc 逐仓保证金
   * @returns {string}
   */
  public get isolatedMarginUsd(): string {
    // 累加所有逐仓持仓的 rawUsd 字段
    const positions = this._clearinghouseState?.assetPositions || []
    const total = positions.reduce((sum, pos) => {
      // 只统计逐仓持仓
      if (pos?.position?.leverage?.type === 'isolated') {
        return sum + Number(pos?.position?.leverage?.rawUsd ?? 0)
      }
      return sum
    }, 0)
    return $ten.toFixed(total, 6)
  }

  /**
   * @desc 已使用保证金
   * @returns {string} 已使用保证金
   */
  public get totalMarginUsed(): string {
    return this._clearinghouseState?.crossMarginSummary?.totalMarginUsed ?? '0'
  }

  /**
   * @desc 可用余额
   * @returns {string} 可用余额 = 账户总价值 - 已使用保证金
   */
  public get balanceUsd(): string {
    const data = this._clearinghouseState?.crossMarginSummary
    const accountValue = data?.accountValue ?? '0'
    const totalMarginUsed = data?.totalMarginUsed ?? '0'
    const total = $ten.sub(accountValue, totalMarginUsed)
    const totalDecimal = new Decimal(total)

    return $ten.toFixed(totalDecimal.isNegative() ? 0 : total, 6)
  }

  /**
   * @desc 可提取金额
   * @returns {string} 可提取金额
   */
  public get withdrawable(): string {
    return this._clearinghouseState?.withdrawable ?? '0'
  }

  /**
   * @desc 未实现盈亏
   * @returns {string} 未实现盈亏 = 所有持仓的未实现盈亏总和
   */
  public get unrealizedPnlUsd(): string {
    // 计算所有持仓的未实现盈亏总和
    const positions = this._clearinghouseState?.assetPositions || []
    const total = positions.reduce((sum, pos) => {
      const pnl = Number(pos?.position?.unrealizedPnl ?? 0)
      return sum + pnl
    }, 0)

    return $ten.toFixed(total, 6)
  }

  /**
   * @desc 全仓保证金比率
   * @returns {string} 全仓保证金比率 = 全仓保证金 / 全仓持仓总价值
   */
  public get marginRatio(): string {
    const data = this._clearinghouseState
    const maintenanceMargin = data?.crossMaintenanceMarginUsed ?? 0
    const accountValue = data?.crossMarginSummary?.accountValue ?? 0

    if (!accountValue) return '0'

    const ratio = $ten.mul($ten.div(maintenanceMargin, accountValue), 100)
    return $ten.toFixed(ratio, 2, true)
  }

  /**
   * @desc 维持保证金
   * @returns {string} 维持保证金
   */
  public get maintenanceMarginUsd(): string {
    const margin = this._clearinghouseState?.crossMaintenanceMarginUsed ?? 0
    return $ten.toFixed(margin, 6)
  }

  /**
   * @desc 全仓账户杠杆
   * @returns {string} 全仓账户杠杆 = 全仓持仓总价值 / 全仓保证金
   */
  public get accountLeverage(): string {
    const data = this._clearinghouseState?.crossMarginSummary
    const totalPosition = data?.totalNtlPos ?? 0
    const accountValue = data?.accountValue ?? 0

    if (!accountValue) return '0'

    const leverage = $ten.div(totalPosition, accountValue)
    return $ten.toFixed(leverage, 2, true)
  }

  /**
   * @desc 当前活跃币种仓位数据
   * @returns {string} - value 持仓数量
   * @returns {TSide | 'N'} - side 持仓方向
   */
  public get assetPosition(): { value: string; side: TSide | 'N' } {
    const positions = this._clearinghouseState?.assetPositions || []
    // 找到当前活跃币种的仓位
    const activePosition = positions.find(
      (pos) => pos?.position?.coin === this._initStore.coin,
    )

    // 获取原始持仓数量 szi
    const szi = activePosition?.position?.szi ?? '0'
    const total = Number(szi)

    let side: TSide | 'N' = 'N'

    if (total > 0) {
      side = 'B' // 多头
    } else if (total < 0) {
      side = 'A' // 空头
    }

    return {
      value: $ten.toFixed(Math.abs(total), 6),
      side: side,
    }
  }

  /**
   * @desc 格式化余额
   * @param balance 余额
   * @returns {IPerpUserBalance} 格式化后的余额
   */
  private formatBalance(balance: IPerpUserBalance): IPerpUserBalance {
    return Object.entries(balance).reduce((acc, [key, value]) => {
      acc[key as keyof IPerpUserBalance] = $ten.toFixed(
        value,
        2,
        key !== 'balanceUsd',
      )
      return acc
    }, {} as IPerpUserBalance)
  }
}
