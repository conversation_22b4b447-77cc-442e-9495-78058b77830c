import { hyperHttpClient } from '@/store/perps/api'
import {
  IFundingData,
  IFundingRateMap,
  IFundingRateStore,
  IHlUserFees,
  IPerpInitStore,
  IPerpsStore,
  IPredictedFunding,
  IUserFees,
} from '@/store/perps/types'
import { $ten } from '@/utils'
import { makeAutoObservable, runInAction } from 'mobx'
import { Hex } from 'viem'

/**
 * @desc 币种资金费率数据
 * @returns {IFundingRateStore} 开放接口
 */
export class FundingRateStore implements IFundingRateStore {
  // 源数据
  _initStore: IPerpInitStore
  _fundings: IPredictedFunding[] | null = null
  _fees: IHlUserFees | null = null

  constructor(rootStore: IPerpsStore) {
    makeAutoObservable(this, {
      getCoinFundingRate: false,
    })

    // 初始化
    this._initStore = rootStore.init

    // 请求所有币种资金费率数据
    this.getFundingHistory()
  }

  /**
   * @desc 获取所有币种资金费率数据
   * @return {IActiveMetaCtx[]} 所有币种资金费率数据
   */
  public get allFundingRate(): IFundingRateMap[] {
    return Object.values(this._dataMap)
  }

  // 获取活跃币种资金费率数据
  public get activeFundingRate(): IFundingData | null {
    if (!this._initStore.coin) return null
    return this._dataMap[this._initStore.coin]?.['HlPerp'] || null
  }

  /**
   * @desc 获取用户手续费率数据
   * @return {IUserFees} 用户手续费率数据
   */
  public get userFees(): IUserFees | null {
    if (!this._fees) return null

    const fees = this._fees
    const userCrossRate = $ten.toFixed($ten.mul(fees?.userCrossRate, 100), 4)
    const userAddRate = $ten.toFixed($ten.mul(fees?.userAddRate, 100), 4)
    const activeReferralDiscount = $ten.toFixed(
      $ten.mul(fees?.activeReferralDiscount, 100),
      4,
    )
    return {
      userCrossRate,
      userAddRate,
      activeReferralDiscount,
    }
  }

  /**
   * @desc 获取资金费率的详细信息
   */
  async getFundingHistory() {
    try {
      const result = await hyperHttpClient.predictedFundings()
      runInAction(() => {
        this._fundings = result
      })
    } catch (error) {
      console.error('获取资金费率失败', error)
    }
  }

  /**
   * @desc 获取用户的手续费率的详细信息
   */
  async getUserFees(user: Hex) {
    try {
      const result = await hyperHttpClient.userFees({ user })
      runInAction(() => {
        this._fees = result
      })
    } catch (error) {
      console.error('获取手续费率失败', error)
    }
  }

  /**
   * @desc 所有币种的完整费率信息(源数据拼接)
   * @return {Record<string, IFundingRateMap>} 所有币种的完整费率信息
   */
  get _dataMap(): Record<string, IFundingRateMap> {
    const result: Record<string, IFundingRateMap> = {}
    const fundings = this._fundings

    if (!fundings) return {}

    fundings.forEach(([coin, fundings]) => {
      result[coin] = {}
      fundings.forEach(([platform, data]) => {
        result[coin][platform] = data
      })
    })

    return result
  }

  /**
   * @desc 获取单个币种的完整费率信息
   * @param coin 币种名称
   * @return {IFundingRateMap} 单个币种的完整费率信息
   */
  public getCoinFundingRate(coin: string): IFundingRateMap {
    return this._dataMap[coin]
  }
}
