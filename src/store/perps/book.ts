import {
  hyperHttpClient,
  hyperSocketClient,
  wsTransport,
} from '@/store/perps/api'
import {
  IBbo,
  IBook,
  IPerpBookStore,
  IPerpInitStore,
  IPerpMetaStore,
  IPerpsStore,
  IPriceDiff,
  IWsBbo,
} from '@/store/perps/types'
import { $ten } from '@/utils'
import { L2BookParameters } from '@nktkas/hyperliquid'
import { makeAutoObservable, runInAction } from 'mobx'

/**
 * @desc 订单簿
 * @property {IPerpBookStore} 开放接口
 */
export class PerpBookStore implements IPerpBookStore {
  // 订单簿原始数据
  data: IBook = {} as IBook
  // 买卖盘最优价格
  bbo: IBbo = {} as IBbo
  // 初始化 store
  _initStore: IPerpInitStore
  _metaStore: IPerpMetaStore
  // 存储订阅对象，用于后续取消订阅
  private bookSubscription: any = null
  private bookMessageListener: any = null
  // 存储买卖盘最优价格订阅对象，用于后续取消订阅
  private bookBboSubscription: any = null

  constructor(_rootStore: IPerpsStore) {
    makeAutoObservable(this, {
      convertAmount: false,
    })
    // 初始化 store
    this._initStore = _rootStore.init
    this._metaStore = _rootStore.meta
  }

  /**
   * 转换金额，专供修改计价币种时，ui 层的展示数据
   * @param quoteCurrency 计价单位
   * @param amount 数量
   * @param price 价格
   * @param nSigFigs 精度
   */
  public convertAmount(
    quoteCurrency: string,
    amount: string,
    price: string,
    nSigFigs?: number,
  ): string {
    // 如果是USD计价方式，返回原始数据
    if (quoteCurrency === 'USD') return amount
    // 如果是代币计价方式，进行转换
    const btcPrice = Number(price)
    const usdValue = Number(amount)
    return $ten.toFixed(usdValue / btcPrice, nSigFigs || 1)
  }

  /**
   * @desc 价差
   * @return {string} diff - 价差 = 最低卖价 - 最高买价
   * @return {string} percentage - 价差百分比 = (价差 / 最低卖价) * 100%
   */
  public get priceDiff(): IPriceDiff {
    if (!this.data?.levels || this.data?.levels?.length !== 2) {
      return { diff: '0', percentage: '0' }
    }

    const lowestAsk = Number(this?.data?.levels?.[1]?.[0]?.px || 0)
    const highestBid = Number(this?.data?.levels?.[0]?.[0]?.px || 0)

    if (lowestAsk === 0 || highestBid === 0) {
      return { diff: '0', percentage: '0' }
    }

    // 计算价差
    const diff = lowestAsk - highestBid
    // 计算价差百分比
    const percentage = (diff / lowestAsk) * 100

    return {
      diff: diff.toFixed(0),
      percentage: percentage.toFixed(3),
    }
  }

  /**
   * 获取买卖盘最优价格
   * @return {IBookLevel} bid - 最优买价
   * @return {IBookLevel} offer - 最优卖价
   */
  public get bestPrice(): { bid: string; offer: string } {
    const coin = this._initStore.coin
    const cbbo = this.bbo[coin]
    const meta = this._metaStore.getCoinInfo(coin)
    const impactPxs = meta?.impactPxs || []
    const bid = cbbo?.bbo?.[0]?.px || impactPxs?.[0] || meta?.markPx || '0'
    const offer = cbbo?.bbo?.[1]?.px || impactPxs?.[1] || meta?.markPx || '0'

    return {
      bid,
      offer,
    }
  }

  /**
   * 获取订单簿
   * @param coin 币种 「例：ETH」
   * @param nSigFigs 精度
   * @param mantissa 精度
   */
  async getBook(req: L2BookParameters): Promise<IBook> {
    try {
      const result = await hyperHttpClient.l2Book(req)

      runInAction(() => {
        this.data = result
      })

      return result
    } catch (error) {
      console.error('获取订单簿失败', error)
      return {} as IBook
    }
  }

  /**
   * 订阅订单簿
   * @param coin 币种 「例：ETH」
   * @param nSigFigs 精度
   * @param mantissa 精度
   */
  async subscribeBook(params: L2BookParameters) {
    try {
      // 取消之前的订阅
      await this.unsubscribeBook()
      await new Promise((resolve) => setTimeout(resolve, 500))

      const type = 'l2Book'
      const payload = { type, ...params }
      this.bookSubscription = payload

      this.bookMessageListener = wsTransport.socket.addEventListener(
        'message',
        (listener) => {
          try {
            const data = JSON.parse(listener.data)
            if (data.channel === 'l2Book') {
              if (!data?.data?.coin) return
              runInAction(() => {
                this.data = data.data as IBook
              })
            }
          } catch {
            // 忽略错误
          }
        },
      )

      // 创建新的订阅
      await wsTransport.subscribe(type, payload, () => {})
    } catch {
      // 检查是否已经有订阅数据，如果有则不重试
      if (this.bookSubscription) return
      // 如果没有订阅数据，则进行重试
      setTimeout(() => {
        this.subscribeBook(params)
      }, 1000)
      return {}
    }
  }

  /**
   * 订阅买卖盘最优价格（下单用）
   * @param coin 币种 「例：ETH」
   */
  async subscribeBbo(coin: string) {
    try {
      // 取消之前的订阅
      this.unsubscribeBbo()
      // 创建新的订阅
      const subscription = await hyperSocketClient.bbo({ coin }, (data) => {
        if (!data?.coin) return
        runInAction(() => {
          this.bbo[data?.coin] = data as IWsBbo
        })
      })
      this.bookBboSubscription = subscription
    } catch {
      // 检查是否已经有订阅数据，如果有则不重试
      if (this.bookBboSubscription) return
      // 如果没有订阅数据，则进行重试
      setTimeout(() => {
        this.subscribeBbo(coin)
      }, 1000)
    }
  }

  /**
   * 取消订阅订单簿
   */
  async unsubscribeBook() {
    if (this.bookMessageListener) {
      wsTransport.socket.removeEventListener(
        'message',
        this.bookMessageListener,
      )
    }

    const subscription = this.bookSubscription
    if (subscription) {
      await wsTransport.socket.send(
        JSON.stringify({
          method: 'unsubscribe',
          subscription,
        }),
      )
      runInAction(() => {
        this.bookSubscription = null
        this.data = {} as IBook
      })
    }
  }

  /**
   * 取消订阅买卖盘最优价格
   */
  async unsubscribeBbo() {
    if (this.bookBboSubscription) {
      await this.bookBboSubscription.unsubscribe()
      runInAction(() => {
        this.bookBboSubscription = null
        this.bbo = {} as IBbo
      })
    }
  }
}
