import { action, computed, makeObservable, observable } from 'mobx'
import { setupLocalStorageSync } from '@/store/_setupSync'
import { userStore } from '@/store'

class GuideStore {
  // 是否启用合约交易引导
  perpMark: boolean = false

  // 标记 Canny 弹窗出现的日期
  cannyTimeMark: string = ''

  constructor() {
    makeObservable(this, {
      perpMark: observable,
      setPerpMark: action,
      isPerpGuide: computed,
      showCanny: computed,
      cannyTimeMark: observable,
      setShowCannyTime: action,
    })

    setupLocalStorageSync(this, 'perp.guide', ['perpMark', 'cannyTimeMark'])
  }

  setPerpMark(mark: boolean) {
    this.perpMark = mark
  }

  get isPerpGuide() {
    return userStore.isLogin && !this.perpMark
  }

  // 设置 Canny 弹窗出现的日期
  setShowCannyTime(time: string) {
    this.cannyTimeMark = time
  }
  // 判断是否显示 Canny 更新弹窗
  get showCanny() {
    // 用户登录 && 没有标记过 Canny 弹窗出现的日期
    return userStore.isLogin && !this.cannyTimeMark
  }
}

const guideStore = new GuideStore()

export { guideStore }
