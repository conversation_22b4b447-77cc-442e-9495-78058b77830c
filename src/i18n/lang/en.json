{"pumpkin_web3": "", "login_not_exist": "Login not exist", "error_get_data": "Failed to get data", "error_get_data_empty": "No data", "no_more_data": "No more data", "error_get_data_retry": "Loading failed, please <0>retry</0>", "error_submit": "Submit failed, please try again", "logged_in": "Logged in", "not_logged_in": "Not logged in", "login_page": "<PERSON><PERSON>", "login_status": "Login Status", "login": "<PERSON><PERSON>", "logout": "Logout", "open_mic": "Open mic", "close_mic": "Close mic", "only_host_admin_can_use_this_location": "Only host and admin can join the mic.", "join": "Join", "invite": "Invite", "success": "Success", "mic_closed": "Mic closed", "host": "Host", "exit": "Exit", "retract": "Minimize", "close": "Close", "cancel": "<PERSON><PERSON>", "confirm": "Confirm", "reject": "Reject", "accept": "Accept", "user_declines_your_invitation": "{{nickname}} declined your invitation.", "you_have_been_set_as_an_administrator": "You are set as admin.", "your_administrative_privileges_have_been_removed": "Your admin rights have been removed.", "you_have_been_kicked_out_of_the_room": "You have been kicked out of the Vibe.", "this_room_is_closed": "This Vibe is closed", "you_are_not_authorized_for_the_microphone": "You haven't enabled microphone permission.", "are_you_sure_you_want_to_close_this_vibe": "Are you sure you want to close this Vibe?", "invites_you_to_be_on_stage_do_you_accept": "{{name}} invites you as a guest. Accept?", "10_minutes": "10 minutes", "1_hour": "1 hour", "1_day": "1 day", "1_week": "1 week", "1_month": "1 month", "vibe_announcement": "Announcement", "save": "Save", "edit": "Edit", "got_it": "Got it", "unblock": "Unblock", "error": "Error", "welcome_to_join": "joins the Vibe.", "welcome_to_pumpkin": "Note: <PERSON><PERSON><PERSON> is not responsible for user-shared content. Violence, pornography, or illegal activities are strictly prohibited.", "loading": "Loading", "mute": "Mute", "unmute": "Unmute", "leave_mic": "Leave Mic", "kick_out": "Kick Out", "remove_admin": "Remove <PERSON>", "make_admin": "Set as <PERSON><PERSON>", "follow": "Follow", "chat": "Cha<PERSON>", "room_cover": "Cover", "room_name": "Name", "room_lock": "Lock", "room_bulletin": "Announcement", "done": "Done", "invite_join": "Invite", "invite_join_success": "<PERSON><PERSON><PERSON> sent, awaiting acceptance.", "invite_fail": "In<PERSON><PERSON> failed, please try again.", "guest": "Guest", "online": "Online", "trade": "Trade", "vibe_password": "Vibe Password", "please_enter_the_4th_number_password": "Enter a 4-digit password.", "you_have_been_blocked_and_cannot_join_the_room": "You have been blocked and cannot join the Vibe.", "the_vibe_does_not_exist": "The Vibe is not live yet.", "create_room": "Create Vibe", "collect": "Favorites", "all": "All", "enter": "Enter", "enter_amount": "Enter amount", "failed_please_try_again_later": "Failed, please try again later", "delete": "Delete", "message": "Message", "contacts": "Contacts", "delete_and_clear_chat_history": "Delete chat history?", "view": "View", "msg_not_supported": "This message type is not supported. Please check it in Pumpkin App.", "user_banned": "The user has been banned", "user_deleted": "The user has been deleted", "user_blocked": "Can't chat with blacklisted users.", "blocked_by_other": "Can't chat with blacklisted users.", "ultimate_privacy_experience": "Ultimate privacy: talk freely with friends!", "get_id_failed": "Failed to get ID", "update_success": "Update successful", "update_failed": "Update failed", "update_failed_retry": "Update failed, please try again later", "edit_profile": "Edit Profile", "my_avatar": "My avatar", "update_upload": "Change Avatar", "nickname": "Nickname", "gender": "Gender", "man": "Man", "woman": "Woman", "age": "Age", "bio": "Bio", "submit": "Submit", "followers": "Followers", "follow_btn": "Follow", "following": "Following", "edit_profile_btn": "Edit Profile", "friends": "Friends", "wallet": "Wallet", "copy_success": "Copy successfully", "copy_failed": "Co<PERSON> failed", "copy": "Copy", "referral_commission": "Invite Rebate", "invite_link": "Invite link", "time": "Time", "user": "User", "commission": "Commission", "network": "Network", "source": "Source", "status": "Status", "room_trade": "Owner Rebate", "invite_register": "Invite Rebate", "not_received": "Unclaimed", "receiving": "Claiming", "received": "Claimed", "min_amount": "Minimum claim amount: {{minAmount}} {{symbol}}", "referral": "Referral Commission", "my_inviter": "My inviter", "total_invite_users": "Invited users", "receive": "<PERSON><PERSON><PERSON>", "receive_success": "Claimed, please check your wallet later.", "i_know": "Got it", "invite_commission_rule": "Rebate Rules", "invite_commission_rule_1": "1. Copy Trading Leader: When users trade in your hosted room, you earn commission from their trades.", "invite_commission_rule_2": "2. Referrer: When users successfully register through your referral link, you become their referrer. ", "invite_commission_rule_3": "3. Transaction fees: 1% for spot trades; 0.035% for contract trades. Your commission ratio is calculated based on these fees.", "room_trade_rule": "Trade in Vibe", "room_outside_trade_rule": "Trade outside Vibe", "inviter_rule": "<PERSON><PERSON><PERSON>", "host_rule": "Owner", "bind_twitter": "Verify X", "bind_twitter_desc": "Verify <PERSON>, let others know you.", "bind_twitter_desc_2": "After linking your X account, your social profile will display the X badge, and others can visit your X homepage.", "x_account_authorization": "X Authentication", "unbind_twitter": "Unbind X", "followed": "Followed", "more": "More", "room": "Vibe", "more_vibes": "More", "recent_search": "Recent", "search_crypto": "Search contract address (CA)", "no_match_result": "No matching result", "hot": "Hot", "vol": "Vol", "txns": "Txns", "buy": "Buy", "sell": "<PERSON>ll", "24h_vol": "24h Vol", "liquidity": "Liquidity", "fdv": "FDV", "market_cap": "Market Cap", "warket_cap": "Market Cap", "please_enter_the_transaction_amount": "Enter transaction amount", "token_balance_is_not_enough": "Insufficient {{tokenName}} balance", "buy_order": "Buy", "sell_order": "<PERSON>ll", "balance": "Balance", "connect_wallet": "Connect Wallet", "up": "Buy", "down": "<PERSON>ll", "unknown_room": "Unknown Vibe", "get_user_info_failed": "Failed to get user info", "follow_success": "Follow successful", "follow_failed": "Follow failed", "unfollow_success": "Unfollow successful", "unfollow_failed": "Unfollow failed", "ban_success": "Block successfully", "ban_failed": "Block failed", "unban_success": "Unblock successfully", "unban_failed": "Unblock failed", "ban": "Block", "unban": "Unblock", "profile": "Profile", "deleted_account": "Deleted account", "deleted_account_desc": "This account has been deleted.", "room_location": "Current Vibe", "scan_qr_code_to_deposit": "Only USDC on the Arbitrum network is supported for deposit.", "deposit": "Receive", "withdrawal": "Send", "tokens": "Token", "asset": "<PERSON><PERSON>", "amount": "Amount", "value": "Value", "no_assets": "No assets", "please_login": "Please log in", "not_connected_wallet": "{{currentNetwork}} wallet not connected", "not_found_contract_address": "Contract address for {{selectedAsset}} not found on {{currentNetwork}}.", "withdrawal_failed": "<PERSON><PERSON><PERSON> failed: {{message}}", "receiver": "Recipient", "input_wallet_address": "Enter wallet address", "asset_amount": "Asset amount", "max": "Max", "processing": "Processing", "not_authorized": "Sorry, you don't have permission to access this page.", "back_login": "Back to login", "not_found": "Sorry, the page you visited does not exist.", "back_home": "Home", "not_mobile_only": "This service is available on mobile app only.", "to_app": "To App", "slippage": "Slippage", "block_confirm_message": "After blocking this user, you won't receive any messages from them. Confirm block?", "transaction_success": "Successfully bought", "transaction_success_sell": "Successfully sold", "transaction_failed": "Buy failed", "transaction_failed_sell": "<PERSON><PERSON> failed", "transaction_loading": "In transaction", "prev_page": "Prev", "next_page": "Next", "export_wallet": "Exporting Private Keys", "language": "Language", "profile_settings": "Settings", "customer_service": "Contact Us", "home_search_placeholder": "Enter Vibe ID or User ID", "unfollow": "Unfollow", "back_to_host_view": "Back to Host's view", "chat_cleared": "Public content has been cleared", "chatroom_screen": "Screen Casting", "chatroom_notice": "Announcement", "chatroom_lock": "Lock", "chatroom_unlock": "Unlock", "chatroom_adminlist": "Admin", "chatroom_blacklist": "Blacklist", "chatroom_settings": "Settings", "chatroom_more": "More", "returned_to_host_view": "Host's selected token shown.", "switch_room": "You're already in a Vibe. Switch Vibe?", "chatroom_exit_screen": "Exit Casting", "setting_room": "Vibe Setting", "language_I_know": "Languages I know", "my_interests": "My Interests", "withdrawal_request_submitted": "<PERSON><PERSON>wal request submitted", "valid_address_and_amount": "Please enter a valid address and amount", "user_cancels_transaction": "User cancels transaction", "valid_wallet_address": "Please enter a valid address", "password_incorrect": "Password is incorrect", "host_trade_token": "Host spotting {{tokenName}}", "create_room_btn": "Create", "admin": "Admin", "screen": "Screen", "details": "Details", "remove": "Remove", "unsupported_message": "Unsupported message", "profile_more_app": "More", "download_pumpkin_app": "Download Pumpkin App", "exiting_room_plz_wait": "Requesting, please wait...", "msg_today": "Today", "msg_yesterday": "Yesterday", "auth_confirming": "Authorization in progress", "auth_success": "Authorization successful, transaction in preparation.", "auth_failed": "Authorization failed.", "transaction_executing": "Transaction in progress.", "error_transaction_failed": "Transaction failed.", "error_wallet_not_connected": "Wallet not connected. Please connect your wallet.", "error_auth_timeout": "Authorization timed out. Please try again.", "error_confirm_timeout": "Transaction confirmation timed out.", "error_unknown": "Transaction failed: Unknown error.", "error_approve_failed": "Token authorization failed.", "error_approve_data_error": "Server exception: Failed to retrieve authorization data.", "error_backend_error": "Server exception, transaction reporting failed.", "error_swap_failed": "Server exception, transaction failed.", "error_transaction_failed_trade": "On-chain failure, transaction failed.", "error_gas_setting_failed_gas": "Transaction failed, please check the Gas settings.", "error_user_cancelled": "User canceled the transaction.", "error_insufficient_funds": "Insufficient balance.", "error_wallet_disconnected": "Wallet disconnected, please refresh and try again.", "error_transaction_expired": "On-chain failure, transaction expired.", "error_send_transaction_error": "Error sending transaction.", "msg_image": "Image", "msg_unknow": "unknow", "points_total_points": "Total pumpkin seeds", "points_live": "Stream Reward", "points_order_amount": "Vibe Vol. Reward", "points_trading_volume": "Volume Reward", "points_invitation": "Referral Reward", "points_live_rewards": "Stream Reward", "points_ rewards_title": "<PERSON><PERSON>", "points_how_to_get_points": "How to get <PERSON><PERSON><PERSON> Seeds?", "points_live_reward": "Stream Reward", "points_live_reward_description": "For every {{hours}} hours you stream live, you earn {{points}} Pumpkin Seeds.", "points_trade_reward": "Vibe Vol. Reward", "points_trade_reward_description": "For every {{value}} USDC in spot trading volume in your Live Room, you earn {{points}} Pumpkin Seeds.", "points_volume_reward": "Volume Reward", "points_volume_reward_description": "For every {{value}} USDC in spot trading volume, you earn {{points}} <PERSON><PERSON><PERSON> Seed.", "points_invite_new_user": "Referral Reward", "points_invite_new_user_description": "Your invited user completes their first trade, both get {{value}} Pumpkin Seeds.", "points_exchange_info": "The more Pumpkin Seeds you hold, the more $PUMP you can exchange after TGE, the more $PUMP you'll get in airdrops.", "clear_history_success": "Cleared", "clear": "Clear", "select_a_token": "Select token", "points_copy_invite_link": "Copy invite link", "need_interaction": "Interaction required", "browsers_require_user_interaction": "Browser requires user interaction to play audio. Click \"OK\" to continue.", "user_interaction_confirm": "OK", "user_interaction_join": "Join", "coming_soon_to_join": "Join <PERSON>", "network_status": "Internet connection", "network_lag": "Delay", "network_send": "Send", "network_recv": "Receive", "network_status_bad": "Poor", "network_status_general": "General", "network_status_good": "Good", "network_status_excellent": "Excellent", "privacy": "Privacy", "broadcast_my_trades": "Broadcast my trades", "danmu_toggle_enable": "Enable trade pop-ups", "danmu_toggle_disable": "Disable trade pop-ups", "trade_message_buy": "<role>{{role}}</role><avatar>{{avatar}}</avatar><nickname>{{nickname}}</nickname> bought <amount>{{amount}}</amount><unit>{{unit}}</unit> worth <token>{{token}}</token>", "trade_message_sell": "<role>{{role}}</role><avatar>{{avatar}}</avatar><nickname>{{nickname}}</nickname> sold <token>{{token}}</token> worth <amount>{{amount}}</amount><unit>{{unit}}</unit>", "role_type_audience": "Audience", "hot_chat_rooms": "<icon>{{icon}}</icon><count>{{count}}</count> Live rooms buzzing with <token>{{token}}</token>", "role_type_host": "Host", "clear_msg": "Clear Chat", "error_53004": "Audio and video service connection timed out. Please check your network.", "offline_toast": "Network disconnected. Please check your network.", "network_connecting": "Connecting", "network_connection_failed": "Connection failed", "cancel_all_orders": "Cancel all", "cancel_all_orders_desc": "This will cancel all your pending orders.", "close_all_orders": "Close all", "close_all_orders_desc": "This will close all your positions and cancel their associated take profit and stop loss orders.", "close_all_orders_type": "Position closing method", "close_all_orders_market": "Market Close", "close_all_orders_limit": "Limit Close at Mid Price", "order_failed": "Order failed", "limit_order_success": "Limit order placed successfully", "limit_order_title": "Limit Close", "limit_order_desc": "This will send an order to close your position at the limit price.", "limit_order_price_placeholder": "Price (USD)", "tit_mid": "Mid", "limit_order_amount_placeholder": "Size", "limit_order_estimated_pnl": "Estimated closed PNL (without fees)", "market_order_success": "Market order has been filled", "market_order_success_info": "{{amount}} {{coin}} {{isLong}} at avg. price: ${{avgPrice}}", "market_order_title": "Market Close", "market_order_desc": "This will attempt to immediately close the position.", "tit_price": "Price", "tpsl_profit_price": "TP Price", "tpsl_loss_price": "SL Price", "tpsl_order_success": "TP/SL order has been sent, waiting for execution", "tpsl_order_failed": "TP/SL order failed", "tpsl_order_title": "TP/SL for Position", "tpsl_order_contract": "Perpetual", "tpsl_order_open_price": "Entry Price", "tpsl_order_mark_price": "<PERSON>", "tpsl_order_amount_config": "Configure Amount", "tpsl_order_desc01": "By default take-profit and stop-loss orders apply to the entire position. Take-profit and stop-loss automatically cancel after closing the position. A market order is triggered when the stop loss or take profit price is reached.", "tpsl_order_desc02": "If the order size is configured above, the TP/SL order will be for that size no matter how the position changes in the future.", "tpsl_order_cancel_success": "Order Cancelled", "tpsl_order_cancel_failed": "Order cancellation failed", "tpsl_order_price_above": "Price higher than {{price}}", "tpsl_order_price_below": "Price lower than {{price}}", "tpsl_order_expected_profit": "Expected Profit", "tpsl_order_expected_loss": "Expected Loss", "loss": "Stop Loss", "market": "Market", "limit": "Limit", "reduce": "Reduce Only", "profitLoss": "TP/SL", "tit_tif": "Time in Force", "tif_gtc": "GTC(Good Til Cancel):The order will be held until it is fulfilled or cancelled.", "tif_ioc": "IOC(Immediate Or Cancel):The portion that was not immediately executed will be cancelled.", "tif_alo": "ALO (Add Liquidity Only): The order will only exist as a limit order in the order book, also known as a pending order only.", "buy_long": "Buy/Long", "sell_short": "Sell/Short", "form_loading": "Market order submitted", "form_loading_limit": "Limit order submitted", "top_up": "Go to Deposit", "reduce_too_large": "Reduce only too large", "cannot_switch_leverage_in_position": "Unable to switch leverage types while holding positions", "switch_leverage_success": "Mode switched successfully", "cross": "Cross", "isolated": "Isolated", "margin_mode": "Margin Mode", "cross_desc": "All cross positions share the same cross margin as collateral. In the event of liquidation, your cross margin balance and any remaining open positions under assets in this mode may be forfeited.", "isolated_desc": "Manage your risk on individual positions by restricting the amount of margin allocated to each. If the margin ratio of an isolated position reaches 100%, the position will be liquidated. Margin can be added or removed to individual positions in this mode.", "liquidation_price": "Liq<PERSON>", "order_price": "Order Value", "required_margin": "<PERSON><PERSON> Required", "fee": "Fee", "set_leverage_success": "Leverage adjusted successfully", "adjust_leverage": "Adjust Leverage", "adjust_leverage_warning": "Please note: the higher the leverage, the greater the risk of liquidation.", "adjust_leverage_desc": "Adjust leverage for your {{coin}} position. Max leverage is {{leverage}}.", "available": "Available", "current_position": "Current Position", "lossPct": "Loss", "profitPct": "Profit", "top_up_usdc": "Deposit USDC", "min_transfer_usdc": "Minimum deposit: {{amount}} USDC. Funds will be auto-bridged to Hyperliquid.", "no_cancelable_orders": "No orders can be cancelled", "cancel_all_orders_success": "All orders have been cancelled", "tpsl_order_cancel_failed_retry": "Cancel all orders failed, please try again.", "current": "Current", "long": "<PERSON>", "short": "Short", "type": "Type", "coin": "Token", "direction": "Direction", "original_size": "Original Size", "order_value": "Order Value", "trigger_condition": "Trigger Condition", "reduce_only": "Reduce Only", "tpsl_order": "TP/SL", "cancel_all": "Cancel All", "operation": "Operation", "limit_stop": "Limit Stop Loss", "market_stop": "Market stop loss", "no_current_order_data": "No data", "position_direction": "Position Side", "quantity": "Amount", "payment": "Payment", "funding_rate": "Rate", "no_funding_history_data": "No data", "order_id": "Order ID", "filled_size": "Size", "price": "Price", "order_type": "Order Type", "order_type_filled": "Filled", "order_type_canceled": "Canceled", "order_type_open": "Unfilled", "order_type_rejected": "Rejected", "no_history_order_data": "No historical order data", "realized_profit_loss": "Realized PnL", "no_history_trade_data": "No historical transaction data", "no_position_to_close": "No open positions to close", "close_all_position_failed": "Failed to close all positions", "market_order_submitted": "Market Order Submitted", "limit_order_submitted": "Limit Order Submitted", "position_value": "Position Value", "entry_price": "Entry Price", "mark_price": "<PERSON>", "profit_loss_roe": "PnL (ROE%)", "margin_used": "<PERSON><PERSON>", "close_all_position": "Close All", "no_position_data": "No data", "view_order": "View Order", "oracle": "Oracle", "change_24h": "24h Change", "volume_24h": "24h Volume", "open_interest": "Open Interest", "funding_countdown": "Funding / Countdown", "search_coin": "Search", "recents_search": "Recent Searches", "recommend_coin": "Recommended Tokens", "symbol": "Symbol", "last_price": "Last Price", "position": "Position", "current_order": "Open Orders", "history_trade": "Trade History", "funding_history": "Funding History", "history_order": "Order History", "chart": "Chart", "orderbook": "Order Book", "latest": "Trades", "perp_overview": "Perps Overview", "unrealized_profit_loss": "Unrealized PnL", "margin_ratio": "Cross Margin Ratio", "maintenance_margin": "Maintenance Margin", "account_leverage": "Cross Account <PERSON>ge", "yes": "Yes", "no": "No", "no_data": "No data", "wallet_tab_perp": "Perpetual", "wallet_tab_spot": "Spot", "wallet_crypto_assets": "Crypto Assets", "wallet_tab_spot_wallet": "Spot Wallet", "wallet_tab_perp_wallet": "Perpetual Wallet", "wallet_perp_realized_profit": "Current Realized Profit:", "wallet_perp_withdrawable_balance": "Withdrawable Balance", "wallet_perp_total_balance": "Wallet Balance", "wallet_perp_unrealized_pnl": "Unrealized PnL", "commission_details": "Commission Details", "spot_trading": "Spot", "perp_trading": "Perpetual", "trade_value": "Trade Value", "perp_withdrawal_notice": "Withdrawal initiated by Hyperliquid; a fee of 1 USDC applies.", "points_reward_limit": "Daily Limit: {{limit}} P<PERSON>kin Seeds", "points_no_limit": "No Limit", "funding_fee": "Funding", "book_spread": "Spread", "book_total": "Total", "book_size": "Size", "my_assets": "My Assets", "contract_trade": "Perpetual", "spot_trade": "Spot", "spot": "Spot", "perpetuo": "Perpetual", "unclaimed_commission​​": "Unclaimed Commission", "scan_spot_to_deposit": "Scan QR code to deposit", "perp_referral_notice": "Claiming initiated by Hyperliquid; a fee of 1 USDC applies.", "deposit_required": "Need to deposit", "order_type_triggered": "triggered", "order_type_marginCanceled": "marginCanceled", "order_type_vaultWithdrawalCanceled": "vaultWithdrawalCanceled", "order_type_openInterestCapCanceled": "openInterestCapCanceled", "order_type_selfTradeCanceled": "selfTradeCanceled", "order_type_reduceOnlyCanceled": "reduceOnlyCanceled", "order_type_siblingFilledCanceled": "siblingFilledCanceled", "order_type_delistedCanceled": "delistedCanceled", "order_type_liquidatedCanceled": "liquidatedCanceled", "order_type_scheduledCancel": "scheduledCancel", "points_contract_room_reward": "For every {{value}} USDC in contract trading volume in your Live Room, you earn {{points}} Pumpkin Seeds.", "points_contact_trading_reward": "For every {{value}} USDC in contract trading volume, you earn {{points}} <PERSON><PERSON><PERSON> Seed.", "margin_modal_title": "Adjust margin", "margin_modal_desc": "Reduce the likelihood of liquidation by increasing more margin or removing excess margin for other positions.", "margin_modal_current_margin": "Current {{coin}} margin", "margin_modal_add_margin": "Available to {{type}} margin", "add": "Add", "margin_modal_success": "Successed to adjust the margin of isolated", "margin_modal_failed": "Failed to adjust the margin of isolated", "skip": "<PERSON><PERSON>", "guide_next": "Next Step", "guide_select_contract_pair": "Select/search for perpetual trading pairs", "guide_top_up": "Recharge perpetual account", "guide_view_position": "View/Close Current Position", "guide_update": "Update", "guide_update_content": "Got it", "guide_update_link": "Powerd by <PERSON><PERSON>", "guide_update_link_all": "View all updates", "mic_has_been_muted": "You have been muted by the admin.", "chat_room_inexistence": "Vibe does not exist.", "too_frequent_request": "Too frequent requests.", "you_are_already_on_the_microphone": "You're already on mic.", "invite_commission_tradingScenario": "Trading Scenario", "invite_commission_tradingWithOrders": "Copy Trading", "invite_commission_ordinaryTransactions": "Regular Trading", "invite_commission_singlePerson": "Copy Trading Leader:", "invite_commission_inviter": "Inviter:", "invite_commission_rule_4": "4. <1>Contact Us</1> to learn more about our rebate policy.", "invite_commission_theLowest": "The lowest"}