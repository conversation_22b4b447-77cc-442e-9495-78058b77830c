import { useLogin, usePrivy, getAccessToken } from '@privy-io/react-auth'
import { userStore } from '@/store'
import { TRIPARTITE_LOGIN } from '@/api/interface/TRIPARTITE_LOGIN'
import { StorageUtils } from '@/utils/STORAGE_REG'
import { message } from 'antd'
import { useRef, useEffect } from 'react'
import { useRoute } from '@/hooks/useRoute'

// 全局登录状态标志
const globalLoginState = {
  isProcessingLogin: false,
  lastLoginTime: 0,
}

export const useAuthLogin = (redirectPath?: string) => {
  const { location, path } = useRoute()
  const { authenticated, logout } = usePrivy()
  const isComponentMounted = useRef(true)

  // 组件卸载时清理状态
  useEffect(() => {
    return () => {
      isComponentMounted.current = false
    }
  }, [])

  const loginEventCallback = {
    onComplete: async () => {
      // 检查是否已认证、是否正在登录、组件是否已卸载
      if (!authenticated) return
      // 防止短时间内重复调用
      const now = Date.now()
      if (
        globalLoginState.isProcessingLogin ||
        now - globalLoginState.lastLoginTime < 3000
      ) {
        // 登录流程已在进行中或刚刚完成，跳过
        return
      }
      // 用户已登录，跳过登录流程
      if (userStore.IS_LOGIN()) return

      try {
        globalLoginState.isProcessingLogin = true
        globalLoginState.lastLoginTime = now
        // 开始登录 or 注册流程
        const token = await getAccessToken()
        const res = await TRIPARTITE_LOGIN({
          inviter: StorageUtils.getReferralCid() || '',
          tripartiteToken: token || '',
          clientId: '',
          platform: 5,
        })

        if (res.code === 20004) {
          userStore.reset()
          await logout()
          message.error('login_not_exist')
        } else if (res.code === 200) {
          // 确保先设置token再设置其他信息
          userStore.setToken(res?.data?.token)
          userStore.setIMToken(res?.data?.ryIMToken)
          userStore.setInfo(res?.data)

          // 移除邀请码
          StorageUtils.removeReferralCid()

          const { pathname, search } = location?.state?.from || {}
          if (pathname === '/chatroom') {
            const searchParams = new URLSearchParams(search)
            const searchObj = Object.fromEntries(searchParams.entries())
            if (searchObj?.roomId) {
              path(
                `/chatroom?roomId=${searchObj?.roomId}&roomMode=${searchObj?.roomMode}`,
              )
              return
            }
          }
          // 使用 replace 而不是 href，避免历史记录问题
          window.location.replace('/')
        } else {
          message.error(`${res?.message}(${res.code})`)
        }
      } catch (error) {
        console.error('登录失败:', error)
      } finally {
        if (isComponentMounted.current) {
          globalLoginState.isProcessingLogin = false
        }
      }
    },
    onError: (error: any) => {
      console.error('登录错误:', error)
      globalLoginState.isProcessingLogin = false
    },
  }

  const { login } = useLogin(loginEventCallback)

  const handleLogin = () => {
    // 检查全局状态和本地状态
    if (globalLoginState.isProcessingLogin) {
      console.log('正在登录中，请稍候')
      return
    }

    // 检查是否已登录
    if (userStore.IS_LOGIN()) {
      console.log('用户已登录，无需重复登录')
      return
    }

    login()
  }

  const handleLogoutAndLogin = async () => {
    if (globalLoginState.isProcessingLogin) {
      console.log('正在登录中，请稍候')
      return
    }

    try {
      await logout()
      // 确保登出后状态已重置
      userStore.reset()

      // 添加延迟确保登出完成
      setTimeout(() => {
        if (isComponentMounted.current) {
          login()
        }
      }, 500)
    } catch (error) {
      console.error('登出失败:', error)
      globalLoginState.isProcessingLogin = false
    }
  }

  return {
    login: handleLogin,
    logoutAndLogin: handleLogoutAndLogin,
  }
}
