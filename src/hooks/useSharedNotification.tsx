import { NotificationTrade } from '@/components/Customize/Notification'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
} from '@ant-design/icons'
import { notification } from 'antd'
import clsx from 'clsx'
import { useCallback } from 'react'

export const useSharedNotification = () => {
  const [notificationApi, contextHolder] = notification.useNotification()

  // 使用方法直接操作全局的通知实例
  const openNotification = useCallback(
    (
      key: string,
      type: 'success' | 'error' | 'loading',
      message?: React.ReactNode,
      duration?: number,
    ) => {
      // 使用 setTimeout 避免在渲染期间调用
      setTimeout(() => {
        const isSuccess = type === 'success'
        const isLoading = type === 'loading'
        const isError = type === 'error'

        notificationApi.open({
          key,
          duration: duration || 5,
          className: clsx(
            'rounded border',
            isSuccess && 'border-[#14b855] bg-[#f6fef9]',
            isError && 'border-[#da2525] bg-[#fef6f6]',
            isLoading && 'border-theme-primary bg-theme-light',
          ),
          message: '',
          description: (
            <div className="flex w-full items-center gap-2">
              {isSuccess && (
                <CheckCircleOutlined className="text-sm text-[#14b855]" />
              )}
              {isLoading && <LoadingOutlined className="text-sm" />}
              {isError && (
                <CloseCircleOutlined className="text-sm text-[#da2525]" />
              )}
              <div className="flex flex-1 items-center justify-between gap-2">
                {message}
              </div>
            </div>
          ),
        })
      }, 0)
    },
    [],
  )

  // 返回一个可以直接使用的组件
  const NotificationComponent = useCallback(() => {
    return <NotificationTrade>{contextHolder}</NotificationTrade>
  }, [])

  return {
    openNotification,
    NotificationComponent,
  }
}
