/**
 * @description 默认通用配置
 */
import { language } from '@/utils'

// 网站适配的多语言路由
export const langRouting = [
  /* 英语 */ {
    lang: 'en_US',
    path: 'en',
    label: 'English',
    cannyId: '684290b16d72c208640ea04e',
  },
  /* 泰语 */ {
    lang: 'th_TH',
    path: 'th',
    label: 'ภาษาไทย',
    cannyId: '6842b2e51adf8537d4783d4a',
  },
  /* 日语 */ {
    lang: 'ja_JP',
    path: 'ja',
    label: '日本語',
    cannyId: '6842b2f48ec794b769f0020d',
  },
  /* 韩语 */ {
    lang: 'ko_KR',
    path: 'ko',
    label: '한국어',
    cannyId: '6842aa38c6e77f1d94d20f54',
  },
  /* 越南语 */ {
    lang: 'vi_VN',
    path: 'vi',
    label: 'Tiếng Việt',
    cannyId: '6842b309d073254d0399447b',
  },
  /* 印度尼西亚语 */ {
    lang: 'id_ID',
    path: 'id',
    label: 'Bahasa',
    cannyId: '6842b312c6e77f1d94da804f',
  },
  /* 简体中文 */ {
    lang: 'zh_CN',
    path: 'zh',
    label: '简体中文',
    cannyId: '684275b39657d2b26e74dc8b',
  },
  /* 繁体中文 */ {
    lang: 'zh_TW',
    path: 'tw',
    label: '繁体中文',
    cannyId: '6842b3277e5c111bb0467dc0',
  },
  /* 西班牙语 */ {
    lang: 'es_ES',
    path: 'es',
    label: 'Español',
    cannyId: '6842b3321adf8537d478aab3',
  },
  /* 土耳其语 */ {
    lang: 'tr_TR',
    path: 'tr',
    label: 'Türkçe',
    cannyId: '6842b33c007db83cc1fc425a',
  },
]

// 网站支持语种
export type supportedLanguage =
  | 'en_US'
  | 'th_TH'
  | 'ja_JP'
  | 'ko_KR'
  | 'vi_VN'
  | 'id_ID'
  | 'zh_CN'
  | 'zh_TW'
  | 'es_ES'
  | 'tr_TR'

// 网站默认语种
export const defaultLang: supportedLanguage =
  (language() as supportedLanguage) || 'en_US'

// 语音房是否使用 okx 兑换与授权接口
export const IS_USE_OKX_SWAP = false

// 是否编辑模式
export const IS_ENV_DEVELOPMENT =
  import.meta.env.VITE_USER_NODE_ENV === 'development'
// 是否构建模式
export const IS_ENV_PRODUCTION =
  import.meta.env.VITE_USER_NODE_ENV === 'production'
// 是否开发环境
export const IS_ENV_DEV = import.meta.env.MODE === 'dev'
// 是否测试环境
export const IS_ENV_TEST = import.meta.env.MODE === 'test'
// 是否生产环境
export const IS_ENV_MAIN = import.meta.env.MODE === 'main'

// 是否开启 room 定义的 log「后期需要移除」
export const IS_OPEN_ROOM_LOG = import.meta.env.VITE_OPEN_ROOM_LOG === 'false'
