import store from 'store2'
/**
 * @description localStorage 注册表
 */

// 用户 token
export const STORAGE_TOKEN = 'token'
// 合约交易搜索历史
export const STORAGE_PERPS_SEARCH_HISTORY = 'perps_search_history'

/**
 * @description sessionStorage 注册表
 */

export const STORAGE_NAV_ISDARK = 'is_nav_dark'

export const STORAGE_TRADE_CRYPTO_LIST = 'trade_crypto_list'

/**
 * @class StorageUtils
 * @description 存储工具类，提供统一的存储操作方法
 */
export class StorageUtils {
  /**
   * @constant DANMU_VISIBLE
   * @description 弹幕显示状态
   */
  static DANMU_VISIBLE = 'danmu_visible'

  /**
   * @constant LAST_PATHNAME
   * @description 最后访问的路径
   */
  static LAST_PATHNAME = 'last_pathname'

  /**
   * @constant TOKEN
   * @description 用户token
   */
  static TOKEN = STORAGE_TOKEN

  /**
   * @constant NAV_ISDARK
   * @description 导航栏暗黑模式状态
   */
  static NAV_ISDARK = STORAGE_NAV_ISDARK

  /**
   * @constant TRADE_CRYPTO_LIST
   * @description 交易加密货币列表
   */
  static TRADE_CRYPTO_LIST = STORAGE_TRADE_CRYPTO_LIST

  /**
   * @constant REFERRAL_CID
   * @description 邀请码
   */
  static REFERRAL_CID = 'referral_cid'

  /**
   * @description 设置弹幕显示状态
   * @param {boolean} visible - 显示状态
   */
  static setDanmuVisible(visible: boolean) {
    store.set(this.DANMU_VISIBLE, visible)
  }

  /**
   * @description 获取弹幕显示状态
   * @returns {boolean} 显示状态
   */
  static getDanmuVisible() {
    return store.get(this.DANMU_VISIBLE)
  }

  /**
   * @description 移除弹幕显示状态
   */
  static removeDanmuVisible() {
    store.remove(this.DANMU_VISIBLE)
  }

  /**
   * @description 设置用户token
   * @param {string} token - 用户token
   */
  static setToken(token: string) {
    store.set(this.TOKEN, token)
  }

  /**
   * @description 获取用户token
   * @returns {string} 用户token
   */
  static getToken() {
    return store.get(this.TOKEN)
  }

  /**
   * @description 移除用户token
   */
  static removeToken() {
    store.remove(this.TOKEN)
  }

  /**
   * @description 设置导航栏暗黑模式状态
   * @param {boolean} isDark - 暗黑模式状态
   */
  static setNavIsDark(isDark: boolean) {
    store.set(this.NAV_ISDARK, isDark)
  }

  /**
   * @description 获取导航栏暗黑模式状态
   * @returns {boolean} 暗黑模式状态
   */
  static getNavIsDark() {
    return store.get(this.NAV_ISDARK)
  }

  /**
   * @description 移除导航栏暗黑模式状态
   */
  static removeNavIsDark() {
    store.remove(this.NAV_ISDARK)
  }

  /**
   * @description 设置交易加密货币列表
   * @param {any} list - 加密货币列表
   */
  static setTradeCryptoList(list: any) {
    store.set(this.TRADE_CRYPTO_LIST, list)
  }

  /**
   * @description 获取交易加密货币列表
   * @returns {any} 加密货币列表
   */
  static getTradeCryptoList() {
    return store.get(this.TRADE_CRYPTO_LIST)
  }

  /**
   * @description 移除交易加密货币列表
   */
  static removeTradeCryptoList() {
    store.remove(this.TRADE_CRYPTO_LIST)
  }

  /**
   * @description 设置邀请码
   * @param {string} cid - 邀请码
   */
  static setReferralCid(cid: string) {
    store.set(this.REFERRAL_CID, cid)
  }

  /**
   * @description 获取邀请码
   * @returns {string} 邀请码
   */
  static getReferralCid() {
    return store.get(this.REFERRAL_CID)
  }

  /**
   * @description 移除邀请码
   */
  static removeReferralCid() {
    store.remove(this.REFERRAL_CID)
  }
}
