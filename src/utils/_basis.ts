/* eslint-disable */
// @ts-nocheck
import { timestamp } from 'rxjs'
import { base64decode, base64encode } from './_base64'
export { base64encode, base64decode }
import dayjs from 'dayjs'

/**
 * @description 存储localStorage
 */
export const setStore = (name: string, content: any) => {
  if (!name) return
  if (typeof content !== 'string') content = JSON.stringify(content)
  window.localStorage.setItem(name, content)
}

/**
 * @description 获取localStorage
 */
export const getStore = (name: string) => {
  if (!name) return
  const Store = window.localStorage.getItem(name)
  try {
    return JSON.parse(Store)
  } catch (err) {
    return Store
  }
}

/**
 * @description 删除localStorage
 */
export const removeStore = (name: string) => {
  if (!name) return
  window.localStorage.removeItem(name)
}
/**
 * @description 存储sessionStorage
 * @param name, content
 */
export const setSessionStore = (name: string, content: number | string) => {
  if (!name) return
  if (typeof content !== 'string') content = JSON.stringify(content)
  window.sessionStorage.setItem(name, content)
}

/**
 * @description 存储sessionStorage
 * @param key
 * @param value
 * @param expirationMinutes 过期时间
 */
export const saveDataSessionStore = (key, value, expirationMinutes = 10) => {
  const expirationTime = dayjs().add(expirationMinutes, 'minutes').toDate()
  const data = {
    value: value,
    expiresAt: expirationTime,
  }
  window.sessionStorage.setItem(key, JSON.stringify(data))
}

export const getDataSessionStore = (key) => {
  const storeData = window.sessionStorage.getItem(key)
  if (storeData) {
    const data = JSON.parse(storeData)
    if (dayjs(data.expiresAt).isAfter(dayjs())) {
      // 未过期
      return data.value
    } else {
      // 过期
      window.sessionStorage.removeItem(key)
    }
  }
  return null
}

/**
 * @description 获取sessionStorage
 * @param name
 */
export const getSessionStore = (name: string) => {
  if (!name) return
  return window.sessionStorage.getItem(name)
}

/**
 * @description 删除sessionStorage
 * @param name
 */
export const removeSessionStore = (name: string) => {
  if (!name) return
  window.sessionStorage.removeItem(name)
}

/**
 * @description 节流
 * @param fn, wait
 * @returns {function}
 */
export const throttle = (fn: any, wait: any) => {
  let start = 0
  return (...argument: any) => {
    const now = +new Date()
    if (now - start > wait) {
      fn(...argument)
      start = now
    }
  }
}

/**
 * @description 防抖
 * @param fn, wait
 * @returns {function}
 */
export const debounce = (fn: any, wait: any) => {
  let start: any = null
  return () => {
    clearTimeout(start)
    start = setTimeout(fn, wait)
  }
}

/**
 * @description 缩略token地址
 * @param token
 * @returns {string}
 */
export const cutToken = (
  token: string,
  charts?: string = '****',
  start?: number = 4,
) => {
  return token && token.length > 5
    ? `${token.substr(0, start)}${charts}${token.substr(-4)}`
    : token
}

/**
 * @description 缩略银行卡
 * @param token
 * @returns {string}
 */
export const cutBanks = (token: string, charts?: string = '****') => {
  return token && token.length > 5 ? `${charts} ${token.substr(-4)}` : token
}

/**
 * @description addEventListener
 * @type {function(...[*]=)}
 */
export const _on = (el: any, event: any, fn: any) => {
  el.addEventListener(
    event,
    fn,
    navigator.userAgent.match(/(?:Trident.*rv[ :]?11\.|msie|iemobile)/i)
      ? false
      : { capture: false, passive: false },
  )
}

/**
 * @description removeEventListener
 * @type {function(...[*]=)}
 */
export const _off = (el: any, event: any, fn: any) => {
  el.removeEventListener(
    event,
    fn,
    navigator.userAgent.match(/(?:Trident.*rv[ :]?11\.|msie|iemobile)/i)
      ? false
      : { capture: false, passive: false },
  )
}

/**
 * @description 获取随机id
 * @param length
 * @returns {string}
 */
export const uuid = (length = 32) => {
  const num = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890'
  let str = ''
  for (let i = 0; i < length; i++) {
    str += num.charAt(Math.floor(Math.random() * num.length))
  }
  return str
}

/**
 * @description 金额千分位分隔符
 * @param length
 * @returns {string}
 */
export const milliFormat = (input: number | string) => {
  const DIGIT_PATTERN = /(^|\s)\d+(?=\.?\d*($|\s))/g
  const MILLI_PATTERN = /(?=(?!\b)(\d{3})+$)/g

  if (!input) return input

  return input
    .toString()
    .replace(DIGIT_PATTERN, (m) => m.replace(MILLI_PATTERN, ','))
}

/**
 * @description 获取浏览器语言信息（本地多语言控制版）
 * @returns {string}
 */
export const language = (): string | undefined | null => {
  let language
  const LANGUAGE =
    navigator.language ||
    navigator.browserLanguage ||
    navigator.systemLanguage ||
    navigator.userLanguage

  // 重定义
  if (LANGUAGE.includes('zh')) language = 'zh_CN'
  // 简中
  else if (LANGUAGE.includes('TW')) {
    language = 'zh_TW'
  } // 繁中
  else if (LANGUAGE.includes('en')) {
    language = 'en_US'
  } // 英文
  else if (LANGUAGE.includes('ko')) {
    language = 'ko_KR'
  } // 韩语
  else if (LANGUAGE.includes('ja')) {
    language = 'ja_JP'
  } // 日语
  else if (LANGUAGE.includes('vi')) {
    language = 'vi_VN'
  } // 越南语
  else if (LANGUAGE.includes('th')) {
    language = 'th_TH'
  } // 泰语
  else if (LANGUAGE.includes('id')) {
    language = 'id_ID'
  } // 印度尼西亚语
  else if (LANGUAGE.includes('es')) {
    language = 'es_ES'
  } // 西班牙语
  else if (LANGUAGE.includes('tr')) {
    language = 'tr_TR'
  } // 土耳其语

  return language
}

/**
 * @description 计算百分比
 * @param num, total
 * @returns {number}
 */
export const percentageNum = (num: number, total: number) => {
  if (num === 0 || total === 0) {
    return 0
  }
  return Math.round((num / total) * 10000) / 100
}
