/**
 * Datafeed JS API for TradingView Advanced Charts
 * @packageDocumentation
 * @module Datafeed
 */
// Generated by dts-bundle-generator v9.5.1

/**
 * This is the generic type useful for declaring a nominal type,
 * which does not structurally matches with the base type and
 * the other types declared over the same base type
 *
 * Usage:
 * @example
 * type Index = Nominal<number, 'Index'>;
 * // let i: Index = 42; // this fails to compile
 * let i: Index = 42 as Index; // OK
 * @example
 * type TagName = Nominal<string, 'TagName'>;
 */
export declare type Nominal<T, Name extends string> = T & { /* eslint-disable-next-line jsdoc/require-jsdoc */
	[Symbol.species]: Name;
};
/**
 * Bar data point
 */
export interface Bar {
	/** Bar time.
	 * Amount of **milliseconds** since Unix epoch start in **UTC** timezone.
	 * `time` for daily, weekly, and monthly bars is expected to be a trading day (not session start day) at 00:00 UTC.
	 * The library adjusts time according to `session` from {@link LibrarySymbolInfo}.
	 */
	time: number;
	/** Opening price */
	open: number;
	/** High price */
	high: number;
	/** Low price */
	low: number;
	/** Closing price */
	close: number;
	/** Trading Volume */
	volume?: number;
}
export interface CurrencyItem {
	/** Unique ID */
	id: string;
	/** Currency code. @example `USD` */
	code: string;
	/** URL to an image of the currency. SVG logos are preferable, but please make sure that the provided image fits nicely in the currency select control (for raster images the expected size is 24x24px). */
	logoUrl?: string;
	/** Description for the currency */
	description?: string;
}
/**
 * Depth of Market (Order Book) Data
 */
export interface DOMData {
	/**
	 * Whether the Depth of Market data is a snapshot (has the full set of depth data).
	 * - If `true` then the data contains the full set of depth data.
	 * - If `false` then data only contains updated levels.
	 */
	snapshot: boolean;
	/** Ask order levels (must be sorted by `price` in ascending order) */
	asks: DOMLevel[];
	/** Bid order levels (must be sorted by `price` in ascending order) */
	bids: DOMLevel[];
}
/**
 * Depth of Market Level
 */
export interface DOMLevel {
	/** Price for DOM level */
	price: number;
	/** Volume for DOM level */
	volume: number;
}
/**
 * Datafeed configuration data.
 * Pass the resulting array of properties as a parameter to {@link OnReadyCallback} of the [`onReady`](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Datafeed-API#onready) method.
 */
export interface DatafeedConfiguration {
	/**
	 * List of exchange descriptors.
	 * An empty array leads to an absence of the exchanges filter in the Symbol Search list.
	 * Use `value=''` if you wish to include all exchanges.
	 */
	exchanges?: Exchange[];
	/**
	 * List of [resolutions](https://www.tradingview.com/charting-library-docs/latest/core_concepts/Resolution) that the chart should support.
	 * Each item of the array is expected to be a string that has a specific [format](https://www.tradingview.com/charting-library-docs/latest/core_concepts/Resolution#resolution-format).
	 * If you set this property to `undefined` or an empty array, the _Resolution_ drop-down menu displays the list of resolutions available for
	 * the current symbol ({@link LibrarySymbolInfo.supported_resolutions}).
	 *
	 * @example
	 * `["1", "15", "240", "D", "6M"]` will give you "1 minute, 15 minutes, 4 hours, 1 day, 6 months" in the _Resolution_ drop-down menu.
	 */
	supported_resolutions?: ResolutionString[];
	/**
	 * Supported unit groups. Each group can have several unit objects.
	 *
	 * @example
	 * ```javascript
	 * {
	 *     weight: [
	 *         { id: 'kg', name: 'kg', description: 'Kilograms' },
	 *         { id: 'lb', name: 'lb', description: 'Pounds'},
	 *     ]
	 * }
	 * ```
	 */
	units?: Record<string, Unit[]>;
	/**
	 * Supported currencies for currency conversion.
	 *
	 * When a currency code is supplied as a string then the library will automatically convert it to `{ id: value, code: value }` object.
	 *
	 * @example `["USD", "EUR", "GBP"]`
	 * @example `[{ id: "USD", code: "USD", description: "$" }, { id: "EUR", code: "EUR", description: "€" }]`
	 */
	currency_codes?: (string | CurrencyItem)[];
	/** Does the datafeed supports marks on bars (`true`), or not (`false | undefined`). */
	supports_marks?: boolean;
	/** Set this one to `true` if your datafeed provides server time (unix time). It is used to adjust Countdown on the Price scale. */
	supports_time?: boolean;
	/** Does the datafeed supports marks on the timescale (`true`), or not (`false | undefined`). */
	supports_timescale_marks?: boolean;
	/**
	 * List of filter descriptors.
	 *
	 * Setting this property to `undefined` or an empty array leads to the absence of filter types in Symbol Search list. Use `value = ''` if you wish to include all filter types.
	 * `value` within the descriptor will be passed as `symbolType` argument to {@link IDatafeedChartApi.searchSymbols}
	 */
	symbols_types?: DatafeedSymbolType[];
	/**
	 * Set it if you want to group symbols in the [Symbol Search](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Symbol-Search).
	 * Represents an object where keys are symbol types {@link SymbolType} and values are regular expressions (each regular expression should divide an instrument name into 2 parts: a root and an expiration).
	 *
	 * Sample:
	 * ```
	 * {
	 *   "futures": `/^(.+)([12]!|[FGHJKMNQUVXZ]\d{1,2})$/`,
	 *   "stock": `/^(.+)([12]!|[FGHJKMNQUVXZ]\d{1,2})$/`,
	 * }
	 * ```
	 * It will be applied to the instruments with futures and stock as a type.
	 * Refer to [Symbol grouping](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Symbol-Search#symbol-grouping) for more information.
	 */
	symbols_grouping?: Record<string, string>;
}
/**
 * This object contains symbol quote values, where a quote represents a set of data describing the current price.
 * The library uses quote data for various trading functionalities, including the [Order Ticket](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/order-ticket), [Legend](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Legend),
 * and widgets, such as [Watchlist](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/Watch-List), [Details](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/#details),
 * [News](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/news/), and [Depth of Market](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/#depth-of-market).
 *
 * While all properties in this object are marked as optional, populating most of them is required for supporting trading functionalities.
 * See property descriptions for more information.
 */
export interface DatafeedQuoteValues {
	/**
	 * Price change (usually counts as an open price on a particular day).
	 * Required for mobile apps. Otherwise, `NaN` values will appear in the [Legend](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Legend).
	 */
	ch?: number;
	/**
	 * Price change percentage.
	 * Required for mobile apps. Otherwise, `NaN` values will appear in the [Legend](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Legend).
	 */
	chp?: number;
	/** Short name for a symbol. Short name is used in the title for the [News](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/news/) widget. */
	short_name?: string;
	/** The name of the exchange */
	exchange?: string;
	/** A short description of the symbol. This description is displayed in the [Details](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/#details) widget and the tooltip of the [Watchlist](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/Watch-List) widget. */
	description?: string;
	/**
	 * The price at which the most recent trade of a security occurred, regardless of whether it was a buy or sell.
	 * Required for the [Details](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/#details) and [Watchlist](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/Watch-List) widgets.
	 */
	lp?: number;
	/** Ask price */
	ask?: number;
	/** Bid price */
	bid?: number;
	/** Spread (difference between the ask and bid prices) */
	spread?: number;
	/** Today's opening price */
	open_price?: number;
	/** Today's high price */
	high_price?: number;
	/** Today's low price */
	low_price?: number;
	/**
	 * Yesterday's closing price.
	 * Required for mobile apps. Otherwise, `NaN` values will appear in the [Legend](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Legend).
	 */
	prev_close_price?: number;
	/** Today's trading volume */
	volume?: number;
	/** Original name */
	original_name?: string;
	[valueName: string]: string | number | string[] | number[] | undefined;
}
export interface DatafeedSymbolType {
	/** Name of the symbol type */
	name: string;
	/** Value to be passed as the `symbolType` argument to `searchSymbols` */
	value: string;
}
/** Exchange Description */
export interface Exchange {
	/** Value to be passed as the `exchange` argument to `searchSymbols` */
	value: string;
	/** Name of the exchange */
	name: string;
	/** Description of the exchange */
	desc: string;
}
/**
 * Information passed to `onHistoryCallback` for getBars.
 */
export interface HistoryMetadata {
	/**
	 * Optional value indicating to the library that there is no more data on the server.
	 */
	noData?: boolean;
	/**
	 * The time of the next available bar in history. The time value should be represented with a Unix timestamp in milliseconds.
	 *
	 * You can pass the `nextTime` value to the library if there is no data in the time range that the library requests.
	 * Therefore, you notify the library about available data before the requested range and ensure that the next data request is for the right range.
	 * For more information, refer to the [How nextTime works](https://www.tradingview.com/charting-library-docs/latest/connecting_data/UDF#how-nexttime-works) section.
	 */
	nextTime?: number | null;
}
export interface IDatafeedChartApi {
	/**
	 * The library calls this function to get marks for visible bars range.
	 * The library assumes that you will call `onDataCallback` only once per `getMarks` call.
	 *
	 * A few marks per bar are allowed (for now, the maximum is 10). The time of each mark must match the time of a bar. For example, if the bar times are `2023-01-01`, `2023-01-08`, and `2023-01-15`, then a mark cannot have the time `2023-01-05`.
	 *
	 * **Remark:** This function will be called only if you confirmed that your back-end is supporting marks ({@link DatafeedConfiguration.supports_marks}).
	 *
	 * @param symbolInfo A SymbolInfo object
	 * @param from Unix timestamp (leftmost visible bar)
	 * @param to Unix timestamp (rightmost visible bar)
	 * @param onDataCallback Callback function containing an array of marks
	 * @param resolution Resolution of the symbol
	 */
	getMarks?(symbolInfo: LibrarySymbolInfo, from: number, to: number, onDataCallback: GetMarksCallback<Mark>, resolution: ResolutionString): void;
	/**
	 * The library calls this function to get timescale marks for visible bars range.
	 * The library assumes that you will call `onDataCallback` only once per `getTimescaleMarks` call.
	 *
	 * **Remark:** This function will be called only if you confirmed that your back-end is supporting marks ({@link DatafeedConfiguration.supports_timescale_marks}).
	 *
	 * @param symbolInfo A SymbolInfo object
	 * @param from Unix timestamp (leftmost visible bar)
	 * @param to Unix timestamp (rightmost visible bar)
	 * @param onDataCallback Callback function containing an array of marks
	 * @param resolution Resolution of the symbol
	 */
	getTimescaleMarks?(symbolInfo: LibrarySymbolInfo, from: number, to: number, onDataCallback: GetMarksCallback<TimescaleMark>, resolution: ResolutionString): void;
	/**
	 * This function is called if the `supports_time` configuration flag is `true` when the chart needs to know the server time.
	 * The library expects a callback to be called once.
	 * The time is provided without milliseconds. Example: `1445324591`.
	 *
	 * `getServerTime` is used to display countdown on the price scale.
	 * Note that the countdown can be displayed only for [intraday](https://www.tradingview.com/charting-library-docs/latest/core_concepts/Resolution#resolution-in-minutes-intraday) resolutions.
	 */
	getServerTime?(callback: ServerTimeCallback): void;
	/**
	 * Provides a list of symbols that match the user's search query.
	 *
	 * @param userInput Text entered by user in the symbol search field
	 * @param exchange The requested exchange. Empty value means no filter was specified
	 * @param symbolType Type of symbol. Empty value means no filter was specified
	 * @param onResult Callback function that returns an array of results ({@link SearchSymbolResultItem}) or empty array if no symbols found
	 */
	searchSymbols(userInput: string, exchange: string, symbolType: string, onResult: SearchSymbolsCallback): void;
	/**
	 * The library will call this function when it needs to get SymbolInfo by symbol name.
	 *
	 * @param symbolName Symbol name or `ticker`
	 * @param onResolve Callback function returning a SymbolInfo ({@link LibrarySymbolInfo})
	 * @param onError Callback function whose only argument is a text error message
	 * @param extension An optional object with additional parameters
	 */
	resolveSymbol(symbolName: string, onResolve: ResolveCallback, onError: DatafeedErrorCallback, extension?: SymbolResolveExtension): void;
	/**
	 * This function is called when the chart needs a history fragment defined by dates range.
	 *
	 * @param symbolInfo A SymbolInfo object
	 * @param resolution Resolution of the symbol
	 * @param periodParams An object used to pass specific requirements for getting bars
	 * @param onResult Callback function for historical data
	 * @param onError Callback function whose only argument is a text error message
	 */
	getBars(symbolInfo: LibrarySymbolInfo, resolution: ResolutionString, periodParams: PeriodParams, onResult: HistoryCallback, onError: DatafeedErrorCallback): void;
	/**
	 * The library calls this function when it wants to receive real-time updates for a symbol.
	 * The library assumes that you will call the callback provided by the `onTick` parameter every time you want to update the most recent bar or to add a new one.
	 *
	 * @param symbolInfo A SymbolInfo object
	 * @param resolution Resolution of the symbol
	 * @param onTick Callback function returning a Bar object
	 * @param listenerGuid
	 * @param onResetCacheNeededCallback Function to be executed when bar data has changed
	 */
	subscribeBars(symbolInfo: LibrarySymbolInfo, resolution: ResolutionString, onTick: SubscribeBarsCallback, listenerGuid: string, onResetCacheNeededCallback: () => void): void;
	/**
	 * The library calls this function when it doesn't want to receive updates anymore.
	 *
	 * @param listenerGuid id to unsubscribe from
	 */
	unsubscribeBars(listenerGuid: string): void;
	/**
	 * The library calls this function when it wants to receive real-time symbol data in the [Depth of Market](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/depth-of-market) (DOM) widget.
	 * Note that you should set the {@link BrokerConfigFlags.supportLevel2Data} configuration flag to `true`.
	 *
	 * @param symbol A SymbolInfo object
	 * @param callback A function returning an object to update DOM data
	 * @returns A unique identifier that will be used to unsubscribe from the data
	 */
	subscribeDepth?(symbol: string, callback: DOMCallback): string;
	/**
	 * The library calls this function to stop receiving real-time updates for the [Depth of Market](https://www.tradingview.com/charting-library-docs/latest/trading_terminal/depth-of-market) listener.
	 * Note that you should set the {@link BrokerConfigFlags.supportLevel2Data} configuration flag to `true`.
	 *
	 * @param subscriberUID A string returned by `subscribeDepth`
	 */
	unsubscribeDepth?(subscriberUID: string): void;
	/**
	 * The library calls this function to get the resolution that will be used to calculate the Volume Profile Visible Range indicator.
	 *
	 * Usually you might want to implement this method to calculate the indicator more accurately.
	 * The implementation really depends on how much data you can transfer to the library and the depth of data in your data feed.
	 * **Remark:** If this function is not provided the library uses currentResolution.
	 *
	 * @param currentResolution Resolution of the symbol
	 * @param from Unix timestamp (leftmost visible bar)
	 * @param to Unix timestamp (rightmost visible bar)
	 * @param symbolInfo A Symbol object
	 * @returns A resolution
	 */
	getVolumeProfileResolutionForPeriod?(currentResolution: ResolutionString, from: number, to: number, symbolInfo: LibrarySymbolInfo): ResolutionString;
}
/** Quotes datafeed API */
export interface IDatafeedQuotesApi {
	/**
	 * This function is called when the library needs quote data.
	 * The library assumes that `onDataCallback` is called once when all the requested data is received.
	 * @param  {string[]} symbols - symbol names.
	 * @param  {QuotesCallback} onDataCallback - callback to return the requested data.
	 * @param  {QuotesErrorCallback} onErrorCallback - callback for responding with an error.
	 */
	getQuotes(symbols: string[], onDataCallback: QuotesCallback, onErrorCallback: QuotesErrorCallback): void;
	/**
	 * Trading Platform calls this function when it wants to receive real-time quotes for a symbol.
	 * The library assumes that you will call `onRealtimeCallback` every time you want to update the quotes.
	 * @param  {string[]} symbols - list of symbols that should be updated rarely (once per minute). These symbols are included in the watchlist but they are not visible at the moment.
	 * @param  {string[]} fastSymbols - list of symbols that should be updated frequently (at least once every 10 seconds)
	 * @param  {QuotesCallback} onRealtimeCallback - callback to send realtime quote data updates
	 * @param  {string} listenerGUID - unique identifier of the listener
	 */
	subscribeQuotes(symbols: string[], fastSymbols: string[], onRealtimeCallback: QuotesCallback, listenerGUID: string): void;
	/**
	 * Trading Platform calls this function when it doesn't want to receive updates for this listener anymore.
	 * `listenerGUID` will be the same object that the Library passed to `subscribeQuotes` before.
	 * @param  {string} listenerGUID - unique identifier of the listener
	 */
	unsubscribeQuotes(listenerGUID: string): void;
}
export interface IExternalDatafeed {
	/**
	 * This call is intended to provide the object filled with the configuration data.
	 * The lib assumes that you will call the callback function and pass your datafeed {@link DatafeedConfiguration} as an argument.
	 *
	 * @param  {OnReadyCallback} callback - callback to return your datafeed configuration ({@link DatafeedConfiguration}) to the library.
	 */
	onReady(callback: OnReadyCallback): void;
}
export interface LibrarySubsessionInfo {
	/**
	 * Description of the subsession.
	 *
	 * @example "Regular Trading Hours"
	 */
	description: string;
	/**
	 * Subsession ID.
	 */
	id: LibrarySessionId;
	/**
	 * Session string. See {@link LibrarySymbolInfo.session}.
	 */
	session: string;
	/**
	 * Session corrections string. See {@link LibrarySymbolInfo.corrections}.
	 */
	"session-correction"?: string;
	/**
	 * Session to display. See {@link LibrarySymbolInfo.session_display}.
	 */
	"session-display"?: string;
}
export interface LibrarySymbolInfo {
	/**
	 * It is a symbol name within an exchange, such as `AAPL` or `9988` (Hong Kong).
	 * Note that it should not contain the exchange name.
	 * This symbol name is visible to users and can be repeated.
	 *
	 * By default, `name` is used to resolve symbols in the [Datafeed API](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Datafeed-API).
	 * If you use {@link LibrarySymbolInfo.ticker}, the library will use the ticker for Datafeed API requests.
	 */
	name: string;
	/**
	 * Array of base symbols
	 * Example: for `AAPL*MSFT` it is `['NASDAQ:AAPL', 'NASDAQ:MSFT']`
	 */
	base_name?: [
		string
	];
	/**
	 * It is an unique identifier for a particular symbol in your [symbology](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Symbology).
	 * If you specify this property, its value will be used for all data requests for this symbol.
	 * `ticker` will be treated the same as {@link LibrarySymbolInfo.name} if not specified explicitly.
	 * Note that it should not contain the exchange name.
	 */
	ticker?: string;
	/**
	 * The description of the symbol.
	 * Will be displayed in the chart legend for this symbol.
	 */
	description: string;
	/**
	 * Symbol Long description
	 *
	 * Optional long(er) description for the symbol.
	 */
	long_description?: string;
	/**
	 * Type of the instrument.
	 * Possible values: {@link SymbolType}
	 */
	type: string;
	/**
	 * Trading hours for the symbol. The time should be in the **exchange** time zone specified in the {@link LibrarySymbolInfo.timezone} property. Refer to the [Trading sessions](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Trading-Sessions) article for more information.
	 * @example "1700-0200"
	 */
	session: string;
	/**
	 * The session value to display in the UI. If not specified, then `session` is used.
	 */
	session_display?: string;
	/**
	 * A string that contains a list of non-trading holidays for the symbol.
	 * Holiday dates should be in the `YYYYMMDD` format.
	 * These dates are not displayed on the chart.
	 *
	 * You can specify a correction for a holiday using {@link LibrarySymbolInfo.corrections}.
	 * @example "20181105,20181107,20181112"
	 */
	session_holidays?: string;
	/**
	 * List of corrections for a symbol. The corrections are days when the trading session differs from the default one set in {@link LibrarySymbolInfo.session}.
	 * The `corrections` value is a string in the following format: `SESSION:YYYYMMDD`.
	 * For more information, refer to [corrections](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Symbology#corrections).
	 *
	 * The string below specifies corrections for two trading days:
	 *
	 * - November 13, 2018. This trading day is split into two sessions. The first session starts at 19:00 four days before (November 9, 2018) and ends at 23:50 four days before. The second session starts at 10:00 and ends at 18:45.
	 * - November 14, 2018. The session starts at 10:00 and ends at 14:00.
	 *
	 * @example "1900F4-2350F4,1000-1845:20181113;1000-1400:20181114"
	 */
	corrections?: string;
	/**
	 * Traded exchange (current (proxy) exchange).
	 * The name will be displayed in the chart legend for this symbol.
	 *
	 * @example "NYSE"
	 */
	exchange: string;
	/**
	 * short name of the exchange where this symbol is traded (real listed exchange).
	 * The name will be displayed in the chart legend for this symbol.
	 *
	 * @example "NYSE"
	 */
	listed_exchange: string;
	/**
	 * The time zone of the **exchange** where the symbol is listed. The time zone value should be in the OlsonDB format.
	 * Refer to [Time zones](https://www.tradingview.com/charting-library-docs/latest/ui_elements/timezones) for a full list of supported time zones.
	 */
	timezone: Timezone;
	/**
	 * Format of displaying labels on the price scale:
	 *
	 * `price` - formats decimal or fractional numbers based on `minmov`, `pricescale`, `minmove2`, `fractional` and `variableMinTick` values. See [Price format](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Symbology#price-format) for more details.
	 * `volume` - formats decimal numbers in thousands, millions, billions or trillions
	 */
	format: SeriesFormat;
	/**
	 * A number of decimal places or fractions that the price has.
	 *
	 * The `pricescale` value depends on the [price format](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Symbology#price-format).
	 * If you want to display the price in the decimal format, `pricescale` should be `10^n`, where `n` is the number of decimal places.
	 * **Examples:** `1`, `10`, `10000000`.
	 *
	 * If you want to display the price in the fractional format, `pricescale` should be `2^n`.
	 * This value represents the number of fractions.
	 * **Examples:** `8`, `16`, `256`.
	 */
	pricescale: number;
	/**
	 * The number of units that make up one tick.
	 * For example, U.S. equities are quotes in decimals, and tick in decimals, and can go up +/- `.01`.
	 * Therefore, the tick increment is 1 and `minmov = 1`. But the e-mini S&P futures contract, though quoted in decimals, goes up in `.25` increments, so the tick increment is 25 and `minmov = 25`.
	 * Refer to [Price format](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Symbology#price-format) for more information.
	 */
	minmov: number;
	/**
	 * For common prices this can be skipped.
	 *
	 * Fractional prices are displayed 2 different forms: 1) `xx'yy` (for example, `133'21`) 2) `xx'yy'zz` (for example, `133'21'5`).
	 *
	 * - `xx` is an integer part.
	 * - `minmov/pricescale` is a Fraction.
	 * - `minmove2` is used in form 2.
	 * - `fractional` is `true`.
	 * - `variableMinTick` is skipped.
	 *
	 * Example:
	 *
	 * If `minmov = 1`, `pricescale = 128` and `minmove2 = 4`:
	 *
	 * - `119'16'0` represents `119 + 16/32`
	 * - `119'16'2` represents `119 + 16.25/32`
	 * - `119'16'5` represents `119 + 16.5/32`
	 * - `119'16'7` represents `119 + 16.75/32`
	 *
	 * More examples:
	 *
	 * - `ZBM2014 (T-Bond)` with `1/32`: `minmov = 1`, `pricescale = 32`, `minmove2 = 0`
	 * - `ZCM2014 (Corn)` with `2/8`: `minmov = 2`, `pricescale = 8`, `minmove2 = 0`
	 * - `ZFM2014 (5 year t-note)` with `1/4 of 1/32`: `minmov = 1`, `pricescale = 128`, `minmove2 = 4`
	 */
	fractional?: boolean;
	/**
	 * This property is used to display prices in the [fraction of a fraction](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Symbology#fraction-of-a-fraction-format) format.
	 * For example, the ZBM2023 tick size is 1/4 of a 32nd. To display this security, set `minmov = 1`, `pricescale = 128`, `minmove2 = 4`.
	 *
	 * For common prices, `minmove2` can be skipped or set to `0`.
	 */
	minmove2?: number;
	/**
	 * Dynamic minimum price movement. It is used if the instrument's minimum price movement changes depending on the price range.
	 *
	 * For example, '0.01 10 0.02 25 0.05', where the tick size is 0.01 for a price less than 10, the tick size is 0.02 for a price less than 25, the tick size is 0.05 for a price greater than or equal to 25.
	 */
	variable_tick_size?: string;
	/**
	 * Boolean value showing whether the symbol includes intraday (minutes) historical data.
	 *
	 * If it's `false` then all buttons for intraday resolutions will be disabled for this particular symbol.
	 * If it is set to `true`, all intradays resolutions that are supplied directly by the datafeed must be provided in `intraday_multipliers` array.
	 *
	 * **WARNING** Any daily, weekly or monthly resolutions cannot be inferred from intraday resolutions.
	 *
	 * `false` if DWM only
	 * @default false
	 */
	has_intraday?: boolean;
	/**
	 * An array of [resolutions](https://www.tradingview.com/charting-library-docs/latest/core_concepts/Resolution) which should be enabled in the _Resolution_ drop-down menu for this symbol.
	 * Each item of the array is expected to be a string that has a specific [format](https://www.tradingview.com/charting-library-docs/latest/core_concepts/Resolution#resolution-format).
	 *
	 * If one changes the symbol and the new symbol does not support the selected resolution, the resolution will be switched to the first available one in the list.
	 *
	 * **Resolution availability logic (pseudocode):**
	 * ```
	 * resolutionAvailable  =
	 *     resolution.isIntraday
	 *         ? symbol.has_intraday && symbol.supported_resolutions(resolution)
	 *         : symbol.supported_resolutions(resolution);
	 * ```
	 *
	 * If `supported_resolutions` is `[]` (empty array), all resolutions are disabled in the _Resolution_ drop-down menu.
	 *
	 * If `supported_resolutions` is `undefined`, all resolutions that the chart support ({@link DatafeedConfiguration.supported_resolutions}) and custom resolutions are enabled.
	 *
	 * Note that the list of available time frames depends on supported resolutions.
	 * Time frames that require resolutions that are unavailable for a particular symbol will be hidden.
	 * Refer to [Time frame toolbar](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Time-Scale#time-frame-toolbar) for more information.
	 */
	supported_resolutions?: ResolutionString[];
	/**
	 * Array of resolutions (in minutes) supported directly by the data feed. Each such resolution may be passed to, and should be implemented by, `getBars`. The default of [] means that the data feed supports aggregating by any number of minutes.
	 *
	 * If the data feed only supports certain minute resolutions but not the requested resolution, `getBars` will be called (repeatedly if needed) with a higher resolution as a parameter, in order to build the requested resolution.
	 *
	 * For example, if the data feed only supports minute resolution, set `intraday_multipliers` to `['1']`.
	 *
	 * When the user wants to see 5-minute data, `getBars` will be called with the resolution set to 1 until the library builds all the 5-minute resolution by itself.
	 * @example (for ex.: "1,5,60") - only these resolutions will be requested, all others will be built using them if possible
	 * @default []
	 */
	intraday_multipliers?: string[];
	/**
	 * Boolean value showing whether the symbol includes seconds in the historical data.
	 *
	 * If it's `false` then all buttons for resolutions that include seconds will be disabled for this particular symbol.
	 *
	 * If it is set to `true`, all resolutions that are supplied directly by the data feed must be provided in `seconds_multipliers` array.
	 * @default false
	 */
	has_seconds?: boolean;
	/**
	 * Boolean value showing whether the symbol includes ticks in the historical data.
	 *
	 * If it's `false` then all buttons for resolutions that include ticks will be disabled for this particular symbol.
	 * @default false
	 */
	has_ticks?: boolean;
	/**
	 * It is an array containing resolutions that include seconds (excluding postfix) that the data feed provides.
	 * E.g., if the data feed supports resolutions such as `["1S", "5S", "15S"]`, but has 1-second bars for some symbols then you should set `seconds_multipliers` of this symbol to `[1]`.
	 * This will make the library build 5S and 15S resolutions by itself.
	 */
	seconds_multipliers?: string[];
	/**
	 * The boolean value specifying whether the datafeed can supply historical data at the daily resolution.
	 *
	 * If `has_daily` is set to `false`, all buttons for resolutions that include days are disabled for this particular symbol.
	 * Otherwise, the library requests daily bars from the datafeed.
	 * All daily resolutions that the datafeed supplies must be included in the {@link LibrarySymbolInfo.daily_multipliers} array.
	 *
	 * @default true
	 */
	has_daily?: boolean;
	/**
	 * Array (of strings) containing the [resolutions](https://www.tradingview.com/charting-library-docs/latest/core_concepts/Resolution#resolution-format) (in days - without the suffix) supported by the datafeed. {@link ResolutionString}
	 *
	 * For example it could be something like
	 *
	 * ```javascript
	 * daily_multipliers = ['1', '3', '4', '6', '7'];
	 * ```
	 * @default ['1']
	 */
	daily_multipliers?: string[];
	/**
	 * The boolean value showing whether data feed has its own weekly and monthly resolution bars or not.
	 *
	 * If `has_weekly_and_monthly` = `false` then the library will build the respective resolutions using daily bars by itself.
	 * If not, then it will request those bars from the data feed using either the `weekly_multipliers` or `monthly_multipliers` if specified.
	 * If resolution is not within either list an error will be raised.
	 * @default false
	 */
	has_weekly_and_monthly?: boolean;
	/**
	 * Array (of strings) containing the [resolutions](https://www.tradingview.com/charting-library-docs/latest/core_concepts/Resolution#resolution-in-weeks--months) (in weeks - without the suffix) supported by the data feed. {@link ResolutionString}
	 *
	 * For example it could be something like
	 *
	 * ```javascript
	 * weekly_multipliers = ['1', '5', '10'];
	 * ```
	 * @default ['1']
	 */
	weekly_multipliers?: string[];
	/**
	 * Array (of strings) containing the [resolutions](https://www.tradingview.com/charting-library-docs/latest/core_concepts/Resolution#resolution-in-weeks--months) (in months - without the suffix) supported by the data feed. {@link ResolutionString}
	 *
	 * For example it could be something like
	 *
	 * ```javascript
	 * monthly_multipliers = ['1', '3', '4', '12'];
	 * ```
	 * @default ['1']
	 */
	monthly_multipliers?: string[];
	/**
	 * The boolean value showing whether the library should generate empty bars in the session when there is no data from the data feed for this particular time.
	 *
	 * I.e., if your session is `0900-1600` and your data has gaps between `11:00` and `12:00` and your `has_empty_bars` is `true`, then the Library will fill the gaps with bars for this time.
	 *
	 * Flag `has_empty_bars` = `true` cannot be used if featureset `disable_resolution_rebuild` is enabled.
	 * @default false
	 */
	has_empty_bars?: boolean;
	/**
	 * Represents what values are supported by the symbol. Possible values:
	 *
	 * - `ohlcv` — the symbol supports open, high, low, close prices and has volume.
	 * - `ohlc` — the symbol supports open, high, low, close, prices but doesn't have volume.
	 * - `c` — the symbol supports only close price. This makes the chart show the symbol data using only line-based styles.
	 * @default 'ohlcv'
	 */
	visible_plots_set?: VisiblePlotsSet;
	/**
	 * Integer showing typical volume value decimal places for a particular symbol.
	 * 0 means volume is always an integer.
	 * 1 means that there might be 1 numeric character after the comma.
	 * @default '0'
	 */
	volume_precision?: number;
	/**
	 * The status code of a series with this symbol.
	 * For `delayed_streaming` and `endofday` type of data, the status is displayed as an icon and the *Data is delayed* section in the [_Legend_](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Legend#display-delayed-data-information), next to the market status icon.
	 * Note that you should also enable the [`display_data_mode`](https://www.tradingview.com/charting-library-docs/latest/customization/Featuresets#display_data_mode) featureset.
	 *
	 * When declaring `delayed_streaming` you also have to specify its {@link LibrarySymbolInfo.delay} in seconds.
	 */
	data_status?: "streaming" | "endofday" | "delayed_streaming";
	/**
	 * Type of delay that is associated to the data or real delay for real time data.
	 * - `0` for realtime
	 * - `-1` for endofday
	 * - or delay in seconds (for delayed realtime)
	 */
	delay?: number;
	/**
	 * Boolean showing whether this symbol is expired futures contract or not.
	 * @default false
	 */
	expired?: boolean;
	/**
	 * Unix timestamp of the expiration date. One must set this value when `expired` = `true`.
	 * The library will request data for this symbol starting from that time point.
	 */
	expiration_date?: number;
	/** Sector for stocks to be displayed in the Security Info. */
	sector?: string;
	/** Industry for stocks to be displayed in the Security Info. */
	industry?: string;
	/**
	 * The currency in which the instrument is traded or some other currency if currency conversion is enabled.
	 * It is displayed in the Security Info dialog and on the price axes.
	 */
	currency_code?: string;
	/** The currency in which the instrument is traded. */
	original_currency_code?: string;
	/**
	 * A unique identifier of a unit in which the instrument is traded or some other identifier if unit conversion is enabled.
	 * It is displayed on the price axes.
	 */
	unit_id?: string;
	/**
	 * A unique identifier of a unit in which the instrument is traded.
	 */
	original_unit_id?: string;
	/**
	 * Allowed unit conversion group names.
	 */
	unit_conversion_types?: string[];
	/**
	 * An ID of a subsession specified in {@link subsessions}. The value must match the subsession that is currently displayed on the chart.
	 * For more information, refer to the [Extended sessions](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Symbology#extended-sessions) section.
	 */
	subsession_id?: string;
	/**
	 * An array of objects that contain information about certain subsessions within the extended session.
	 * For more information, refer to the [Extended sessions](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Symbology#extended-sessions) section.
	 */
	subsessions?: LibrarySubsessionInfo[];
	/**
	 * Optional ID of a price source for a symbol. Should match one of the price sources from the {@link price_sources} array.
	 *
	 * Note that you should set the [`symbol_info_price_source`](https://www.tradingview.com/charting-library-docs/latest/customization/Featuresets#symbol_info_price_source) featureset to `true` to display the symbol price source in the main series legend.
	 */
	price_source_id?: string;
	/**
	 * Supported price sources for the symbol.
	 * Price sources appear in the series legend and indicate the origin of values represented by symbol bars.
	 * Example price sources: "Spot Price", "Ask", "Bid", etc.
	 * The price source information is valuable when viewing non-OHLC series types.
	 *
	 * Note that you should set the [`symbol_info_price_source`](https://www.tradingview.com/charting-library-docs/latest/customization/Featuresets#symbol_info_price_source) featureset to `true` to display the symbol price source in the main series legend.
	 * @example [{ id: '1', name: 'Spot Price' }, { id: '321', name: 'Bid' }]
	 */
	price_sources?: SymbolInfoPriceSource[];
	/**
	 * URL of image/s to be displayed as the logo/s for the symbol. The `show_symbol_logos` featureset needs to be enabled for this to be visible in the UI.
	 *
	 * - If a single url is returned then that url will solely be used to display the symbol logo.
	 * - If two urls are provided then the images will be displayed as two partially overlapping
	 * circles with the first url appearing on top. This is typically used for FOREX where you would
	 * like to display two country flags are the symbol logo.
	 *
	 * The image/s should ideally be square in dimension. You can use any image type which
	 * the browser supports natively.
	 *
	 * Examples:
	 * - `https://yourserver.com/apple.svg`
	 * - `/images/myImage.png`
	 * - `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3...`
	 * - `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAASABIAAD/4...`
	 */
	logo_urls?: [
		string
	] | [
		string,
		string
	];
	/**
	 * URL of image to be displayed as the logo for the exchange. The `show_exchange_logos` featureset needs to be enabled for this to be visible in the UI.
	 *
	 * The image should ideally be square in dimension. You can use any image type which
	 * the browser supports natively. Simple SVG images are recommended.
	 *
	 * Examples:
	 * - `https://yourserver.com/exchangeLogo.svg`
	 * - `/images/myImage.png`
	 * - `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3...`
	 * - `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAASABIAAD/4...`
	 */
	exchange_logo?: string;
	/**
	 * Additional metadata to include with the symbol information.
	 * These parameters will not affect existing properties such as 'ticker' or 'name'
	 * and will not be processed by the library. However, they can be accessed later
	 * through the {@link IChartWidgetApi.symbolExt} method, impacting existing
	 * properties such as 'ticker' or 'name'.
	 */
	library_custom_fields?: Record<string, unknown>;
}
export interface Mark {
	/** ID of the mark */
	id: string | number;
	/**
	 * Time for the mark.
	 * Unix timestamp in seconds.
	 */
	time: number;
	/** Color for the mark */
	color: MarkConstColors | MarkCustomColor;
	/** Text content for the mark */
	text: string;
	/** Label for the mark */
	label: string;
	/** Text color for the mark */
	labelFontColor: string;
	/** Minimum size for the mark */
	minSize: number;
	/** Border Width */
	borderWidth?: number;
	/** Border Width when hovering over bar mark */
	hoveredBorderWidth?: number;
	/**
	 * Optional URL for an image to be displayed within the timescale mark.
	 *
	 * The image should ideally be square in dimension. You can use any image type which
	 * the browser supports natively.
	 *
	 * Examples:
	 * - `https://yourserver.com/adobe.svg`
	 * - `/images/myImage.png`
	 * - `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3...`
	 * - `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAASABIAAD/4...`
	 */
	imageUrl?: string;
	/**
	 * Continue to show text label even when an image has
	 * been loaded for the timescale mark.
	 *
	 * Defaults to `false` if undefined.
	 */
	showLabelWhenImageLoaded?: boolean;
}
export interface MarkCustomColor {
	/** Border color */
	border: string;
	/** Background color */
	background: string;
}
/**
 * Parameters passed to getBars
 */
export interface PeriodParams {
	/**
	 * Unix timestamp (leftmost requested bar)
	 */
	from: number;
	/**
	 * Unix timestamp (rightmost requested bar - not inclusive)
	 */
	to: number;
	/**
	 * The exact amount of bars to load, should be considered a higher priority than `from` if your datafeed supports it
	 */
	countBack: number;
	/**
	 * Used to identify if it's the first call of getBars
	 */
	firstDataRequest: boolean;
}
export interface QuoteDataResponse {
	/** Status code for symbol. Expected values: `ok` | `error` */
	s: "ok" | "error";
	/** Symbol name. This value must be **exactly the same** as in the request */
	n: string;
	/** Quote Values */
	v: unknown;
}
/** Quote Data Error Response */
export interface QuoteErrorData extends QuoteDataResponse {
	/** @inheritDoc */
	s: "error";
	/** @inheritDoc */
	v: object;
}
/** Quote Data Ok Response */
export interface QuoteOkData extends QuoteDataResponse {
	/** @inheritDoc */
	s: "ok";
	/** @inheritDoc */
	v: DatafeedQuoteValues;
}
/**
 * [Symbol Search](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Symbol-Search) result item.
 * Pass the resulting array of symbols as a parameter to {@link SearchSymbolsCallback} of the [`searchSymbols`](https://www.tradingview.com/charting-library-docs/latest/connecting_data/Datafeed-API#searchsymbols) method.
 *
 * @example
 * ```
 * {
 * 	description: 'Apple Inc.',
 * 	exchange: 'NasdaqNM',
 * 	symbol: 'AAPL',
 * 	ticker: 'AAPL',
 * 	type: 'stock',
 * }
 * ```
 */
export interface SearchSymbolResultItem {
	/** Short symbol name */
	symbol: string;
	/** Description */
	description: string;
	/** Exchange name */
	exchange: string;
	/** Symbol ticker name. Should be an unique id */
	ticker?: string;
	/**
	 * Type of symbol
	 *
	 * 'stock' | 'futures' | 'forex' | 'index'
	 */
	type: string;
	/**
	 * URL of image/s to be displayed as the logo/s for the symbol. The `show_symbol_logos` featureset needs to be enabled for this to be visible in the UI.
	 *
	 * - If a single url is returned then that url will solely be used to display the symbol logo.
	 * - If two urls are provided then the images will be displayed as two partially overlapping
	 * circles with the first url appearing on top. This is typically used for FOREX where you would
	 * like to display two country flags as the symbol logo.
	 *
	 * The image/s should ideally be square in dimension. You can use any image type which
	 * the browser supports natively. Simple SVG images are recommended.
	 *
	 * Examples:
	 * - `https://yourserver.com/symbolName.svg`
	 * - `/images/myImage.png`
	 * - `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3...`
	 * - `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAASABIAAD/4...`
	 */
	logo_urls?: [
		string
	] | [
		string,
		string
	];
	/**
	 * URL of image to be displayed as the logo for the exchange. The `show_exchange_logos` featureset needs to be enabled for this to be visible in the UI.
	 *
	 * The image should ideally be square in dimension. You can use any image type which
	 * the browser supports natively. Simple SVG images are recommended.
	 *
	 * Examples:
	 * - `https://yourserver.com/exchangeLogo.svg`
	 * - `/images/myImage.png`
	 * - `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3...`
	 * - `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAASABIAAD/4...`
	 */
	exchange_logo?: string;
}
export interface SymbolInfoPriceSource {
	/** Unique ID */
	id: string;
	/** Short name */
	name: string;
}
/** Additional information about the Symbol's currency or unit */
export interface SymbolResolveExtension {
	/**
	 * Indicates the currency for conversions if `currency_codes` configuration field is set,
	 * and `currency_code` is provided in the original symbol information ({@link LibrarySymbolInfo}).
	 * Read more about [currency conversion](https://www.tradingview.com/charting-library-docs/latest/ui_elements/Price-Scale#currency-conversion).
	 */
	currencyCode?: string;
	/**
	 * Indicates the unit for conversion if `units` configuration
	 * field is set and `unit_id` is provided in the original symbol information ({@link LibrarySymbolInfo}).
	 */
	unitId?: string;
	/**
	 * Trading session type, such as `"regular"` or `"extended"`, that the chart should currently display.
	 */
	session?: string;
}
export interface TimescaleMark {
	/** ID of the timescale mark */
	id: string | number;
	/**
	 * Time for the mark.
	 * Unix timestamp in seconds.
	 */
	time: number;
	/** Color for the timescale mark */
	color: MarkConstColors | string;
	/**
	 * Color for the timescale mark text label.
	 * If undefined then the value provided for `color` will be used.
	 */
	labelFontColor?: MarkConstColors | string;
	/** Label for the timescale mark */
	label: string;
	/** Tooltip content */
	tooltip: string[];
	/** Shape of the timescale mark */
	shape?: TimeScaleMarkShape;
	/**
	 * Optional URL for an image to be displayed within the timescale mark.
	 *
	 * The image should ideally be square in dimension. You can use any image type which
	 * the browser supports natively.
	 *
	 * Examples:
	 * - `https://s3-symbol-logo.tradingview.com/crypto/XTVCBTC.svg`
	 * - `/images/myImage.png`
	 * - `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3...`
	 * - `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAASABIAAD/4...`
	 */
	imageUrl?: string;
	/**
	 * Continue to show text label even when an image has
	 * been loaded for the timescale mark.
	 *
	 * Defaults to `false` if undefined.
	 */
	showLabelWhenImageLoaded?: boolean;
}
export interface Unit {
	/** Unique ID */
	id: string;
	/** Short name */
	name: string;
	/** Description */
	description: string;
}
export type CustomTimezones = "Africa/Cairo" | "Africa/Casablanca" | "Africa/Johannesburg" | "Africa/Lagos" | "Africa/Nairobi" | "Africa/Tunis" | "America/Anchorage" | "America/Argentina/Buenos_Aires" | "America/Bogota" | "America/Caracas" | "America/Chicago" | "America/El_Salvador" | "America/Juneau" | "America/Lima" | "America/Los_Angeles" | "America/Mexico_City" | "America/New_York" | "America/Phoenix" | "America/Santiago" | "America/Sao_Paulo" | "America/Toronto" | "America/Vancouver" | "Asia/Almaty" | "Asia/Ashkhabad" | "Asia/Bahrain" | "Asia/Bangkok" | "Asia/Chongqing" | "Asia/Colombo" | "Asia/Dhaka" | "Asia/Dubai" | "Asia/Ho_Chi_Minh" | "Asia/Hong_Kong" | "Asia/Jakarta" | "Asia/Jerusalem" | "Asia/Karachi" | "Asia/Kathmandu" | "Asia/Kolkata" | "Asia/Kuala_Lumpur" | "Asia/Kuwait" | "Asia/Manila" | "Asia/Muscat" | "Asia/Nicosia" | "Asia/Qatar" | "Asia/Riyadh" | "Asia/Seoul" | "Asia/Shanghai" | "Asia/Singapore" | "Asia/Taipei" | "Asia/Tehran" | "Asia/Tokyo" | "Asia/Yangon" | "Atlantic/Reykjavik" | "Australia/Adelaide" | "Australia/Brisbane" | "Australia/Perth" | "Australia/Sydney" | "Europe/Amsterdam" | "Europe/Athens" | "Europe/Belgrade" | "Europe/Berlin" | "Europe/Bratislava" | "Europe/Brussels" | "Europe/Bucharest" | "Europe/Budapest" | "Europe/Copenhagen" | "Europe/Dublin" | "Europe/Helsinki" | "Europe/Istanbul" | "Europe/Lisbon" | "Europe/London" | "Europe/Luxembourg" | "Europe/Madrid" | "Europe/Malta" | "Europe/Moscow" | "Europe/Oslo" | "Europe/Paris" | "Europe/Prague" | "Europe/Riga" | "Europe/Rome" | "Europe/Stockholm" | "Europe/Tallinn" | "Europe/Vienna" | "Europe/Vilnius" | "Europe/Warsaw" | "Europe/Zurich" | "Pacific/Auckland" | "Pacific/Chatham" | "Pacific/Fakaofo" | "Pacific/Honolulu" | "Pacific/Norfolk" | "US/Mountain";
export type DOMCallback = (data: DOMData) => void;
export type DatafeedErrorCallback = (reason: string) => void;
export type GetMarksCallback<T> = (marks: T[]) => void;
export type HistoryCallback = (bars: Bar[], meta?: HistoryMetadata) => void;
export type LibrarySessionId = "regular" | "extended" | "premarket" | "postmarket";
export type MarkConstColors = "red" | "green" | "blue" | "yellow";
export type OnReadyCallback = (configuration: DatafeedConfiguration) => void;
export type QuoteData = QuoteOkData | QuoteErrorData;
/**
 * Callback to provide Quote data.
 * @param  {QuoteData[]} data - Quote Data
 */
export type QuotesCallback = (data: QuoteData[]) => void;
/**
 * Error callback for quote data request.
 * @param  {QuoteData[]} reason - message describing the reason for the error
 */
export type QuotesErrorCallback = (reason: string) => void;
/**
 * Resolution or time interval is a time period of one bar. Advanced Charts supports tick, intraday (seconds, minutes, hours), and DWM (daily, weekly, monthly) resolutions. The table below describes how to specify different types of resolutions:
 *
 * Resolution | Format | Example
 * ---------|----------|---------
 * Ticks | `xT` | `1T` — one tick
 * Seconds | `xS` | `1S` — one second
 * Minutes | `x` | `1` — one minute
 * Hours | `x` minutes | `60` — one hour
 * Days | `xD` | `1D` — one day
 * Weeks | `xW` | `1W` — one week
 * Months | `xM` | `1M` — one month
 * Years | `xM` months | `12M` — one year
 *
 * Refer to [Resolution](https://www.tradingview.com/charting-library-docs/latest/core_concepts/Resolution) for more information.
 */
export type ResolutionString = Nominal<string, "ResolutionString">;
export type ResolveCallback = (symbolInfo: LibrarySymbolInfo) => void;
export type SearchSymbolsCallback = (items: SearchSymbolResultItem[]) => void;
export type SeriesFormat = "price" | "volume";
export type ServerTimeCallback = (serverTime: number) => void;
export type SubscribeBarsCallback = (bar: Bar) => void;
export type TimeScaleMarkShape = "circle" | "earningUp" | "earningDown" | "earning";
export type Timezone = "Etc/UTC" | CustomTimezones;
export type VisiblePlotsSet = "ohlcv" | "ohlc" | "c";

export as namespace TradingView;

export {};
