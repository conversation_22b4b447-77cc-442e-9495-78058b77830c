"use strict";var e,t,i,o,r,n,a,s,l,d,c,h,g,u,C,p,S,m,y,T,_,b,P,L,A,w,f,I,v,O,R,D,V,k,F,E,x,W,M,B,N,U,H,j,z,G,$,J,q,K,Z,Q,Y,X,ee,te,ie,oe,re,ne,ae,se,le,de,ce;Object.defineProperty(exports,"__esModule",{value:!0}),(e=exports.ActionId||(exports.ActionId={})).UnknownAction="UnknownAction",e.Spinner="Spinner",e.Loading="Loading",e.AlertAdd="Alert.Add",e.AlertEdit="Alert.Edit",e.AlertsClone="Alerts.Clone",e.AlertsRemove="Alerts.Remove",e.AlertsRemoveAll="Alerts.RemoveAll",e.AlertsRemoveFiltered="Alerts.RemoveFiltered",e.AlertsRemoveAllInactive="Alerts.RemoveAllInactive",e.AlertsRemoveFires="Alerts.RemoveFires",e.AlertsRestart="Alerts.Restart",e.AlertsRestartAllInactive="Alerts.RestartAllInactive",e.AlertsRestartFilteredInactive="Alerts.RestartFilteredInactive",e.AlertsStop="Alerts.Stop",e.AlertsStopAll="Alerts.StopAll",e.AlertsStopFilteredActive="Alerts.StopFilteredActive",e.AlertsExportFiresToCSV="Alerts.ExportFiresToCSV",e.AlertsLogClear="AlertsLog.Clear",e.ChartAddIndicatorToAllCharts="Chart.AddIndicatorToAllCharts",e.ChartAddSymbolToWatchList="Chart.AddSymbolToWatchList",e.ChartAlertLabelToggleExtendLines="Chart.AlertLabel.ToggleExtendLines",e.ChartApplyIndicatorsToAllCharts="Chart.ApplyIndicatorsToAllCharts",e.ChartIndicatorApplyChildIndicator="Chart.Indicator.ApplyChildIndicator",e.ChartIndicatorApplyFinancials="Chart.Indicator.ApplyFinancials",e.ChartIndicatorAbout="Chart.Indicator.About",e.ChartIndicatorPineLogs="Chart.Indicator.PineLogs",e.ChartIndicatorPineSource="Chart.Indicator.PineSource",e.ChartIndicatorAddFavorites="Chart.Indicator.AddFavorites",e.ChartChangeTimeZone="Chart.ChangeTimeZone",e.ChartClipboardCopyPrice="Chart.Clipboard.CopyPrice",e.ChartClipboardCopyLineTools="Chart.Clipboard.CopyLineTools",e.ChartClipboardCopySource="Chart.Clipboard.CopySource",e.ChartClipboardPasteSource="Chart.Clipboard.PasteSource",e.ChartCrosshairLockVerticalCursor="Chart.Crosshair.LockVerticalCursor",e.ChartCrosshairPlusButtonDrawHorizontalLine="Chart.Crosshair.PlusButton.DrawHorizontalLine",e.ChartCustomActionId="Chart.CustomActionId",e.ChartDialogsShowChangeInterval="Chart.Dialogs.ShowChangeInterval",e.ChartDialogsShowChangeSymbol="Chart.Dialogs.ShowChangeSymbol",e.ChartDialogsShowCompareOrAddSymbol="Chart.Dialogs.ShowCompareOrAddSymbol",e.ChartDialogsShowGeneralSettings="Chart.Dialogs.ShowGeneralSettings",e.ChartDialogsShowGeneralSettingsLegendTab="Chart.Dialogs.ShowGeneralSettings.LegendTab",e.ChartDialogsShowGeneralSettingsSymbolTab="Chart.Dialogs.ShowGeneralSettings.SymbolTab",e.ChartDialogsShowGeneralScalesTab="Chart.Dialogs.ShowGeneralSettings.ScalesTab",e.ChartDialogsShowGeneralSettingsEventsAndAlertsTab="Chart.Dialogs.ShowGeneralSettings.EventsAndAlertsTab",e.ChartDialogsShowGoToDate="Chart.Dialogs.ShowGoToDate",e.ChartDialogsShowInsertIndicators="Chart.Dialogs.ShowInsertIndicators",e.ChartDialogsShowInsertFinancials="Chart.Dialogs.ShowInsertFinancials",e.ChartDialogsShowSymbolInfo="Chart.Dialogs.ShowSymbolInfo",e.ChartDrawingToolbarToggleVisibility="Chart.DrawingToolbar.ToggleVisibility",e.ChartExternalActionId="Chart.ExternalActionId",e.ChartFavoriteDrawingToolsToolbarHide="Chart.FavoriteDrawingToolsToolbar.Hide",e.ChartIndicatorShowSettingsDialog="Chart.Indicator.ShowSettingsDialog",e.ChartLegendToggleLastDayChangeValuesVisibility="Chart.Legend.ToggleLastDayChangeValuesVisibility",e.ChartLinkingGroupSync="Chart.LinkingGroupSync",e.ChartLinkingGroupSyncChangeGroup="Chart.LinkingGroupSync.ChangeGroup",e.ChartLegendToggleBarChangeValuesVisibility="Chart.Legend.ToggleBarChangeValuesVisibility",e.ChartLegendTogglePriceSourceVisibility="Chart.Legend.TogglePriceSourceVisibility",e.ChartLegendToggleIndicatorArgumentsVisibility="Chart.Legend.ToggleIndicatorArgumentsVisibility",e.ChartLegendToggleIndicatorTitlesVisibility="Chart.Legend.ToggleIndicatorTitlesVisibility",e.ChartLegendToggleIndicatorValuesVisibility="Chart.Legend.ToggleIndicatorValuesVisibility",e.ChartLegendToggleOhlcValuesVisibility="Chart.Legend.ToggleOhlcValuesVisibility",e.ChartLegendToggleOpenMarketStatusVisibility="Chart.Legend.ToggleOpenMarketStatusVisibility",e.ChartLegendToggleSymbolVisibility="Chart.Legend.ToggleSymbolVisibility",e.ChartLegendToggleVolumeVisibility="Chart.Legend.ToggleVolumeVisibility",e.ChartLines="Chart.Lines",e.ChartLinesToggleBidAskLinesVisibility="Chart.Lines.ToggleBidAskLinesVisibility",e.ChartLinesToggleHighLowLinesVisibility="Chart.Lines.ToggleHighLowLinesVisibility",e.ChartLinesToggleAverageLineVisibility="Chart.Lines.ToggleAverageLineVisibility",e.ChartLinesTogglePrePostMarketLineVisibility="Chart.Lines.TogglePrePostMarketLineVisibility",e.ChartLinesTogglePrePostMarketPriceLineVisibility="Chart.Lines.TogglePrePostMarketPriceLineVisibility",e.ChartLinesToggleSeriesPrevCloseLineVisibility="Chart.Lines.ToggleSeriesPrevCloseLineVisibility",e.ChartLinesToggleSeriesPriceLineVisibility="Chart.Lines.ToggleSeriesPriceLineVisibility",e.ChartLineToolBarsPatternToggleFlipped="Chart.LineTool.BarsPattern.ToggleFlipped",e.ChartLineToolBarsPatternToggleMirrored="Chart.LineTool.BarsPattern.ToggleMirrored",e.ChartLineToolClone="Chart.LineTool.Clone",e.ChartLineToolCreateLimitOrderFromState="Chart.LineTool.CreateLimitOrderFromState",e.ChartLineToolElliotChangeDegreeProperty="Chart.LineTool.Elliot.ChangeDegreeProperty",e.ChartLineToolNoSync="Chart.LineTool.NoSync",e.ChartLineToolPitchforkChangeTypeToInside="Chart.LineTool.Pitchfork.ChangeTypeToInside",e.ChartLineToolPitchforkChangeTypeToModifiedSchiff="Chart.LineTool.Pitchfork.ChangeTypeToModifiedSchiff",e.ChartLineToolPitchforkChangeTypeToOriginal="Chart.LineTool.Pitchfork.ChangeTypeToOriginal",e.ChartLineToolPitchforkChangeTypeToSchiff="Chart.LineTool.Pitchfork.ChangeTypeToSchiff",e.ChartLineToolSyncInLayout="Chart.LineTool.SyncInLayout",e.ChartLineToolSyncGlobally="Chart.LineTool.SyncGlobally",e.ChartLineToolTemplates="Chart.LineTool.Templates",e.ChartLineToolTemplatesApply="Chart.LineTool.Templates.Apply",e.ChartLineToolTemplatesApplyDefaults="Chart.LineTool.Templates.ApplyDefaults",e.ChartLineToolTemplatesSaveAs="Chart.LineTool.Templates.SaveAs",e.ChartLineToolToolbarChangeFontSizeProperty="Chart.LineTool.Toolbar.ChangeFontSizeProperty",e.ChartLineToolToolbarChangeLineStyleToDashed="Chart.LineTool.Toolbar.ChangeLineStyleToDashed",e.ChartLineToolToolbarChangeLineStyleToDotted="Chart.LineTool.Toolbar.ChangeLineStyleToDotted",e.ChartLineToolToolbarChangeLineStyleToSolid="Chart.LineTool.Toolbar.ChangeLineStyleToSolid",e.ChartMarksToggleVisibility="Chart.Marks.ToggleVisibility",e.ChartMoveChartInLayout="Chart.MoveChartInLayout",e.ChartMoveChartInLayoutBack="Chart.MoveChartInLayout.Back",e.ChartMoveChartInLayoutForward="Chart.MoveChartInLayout.Forward",e.ChartTpoResetAllMergesAndSplits="Chart.TPO.ResetAllMergesAndSplits",e.ChartTpoSplitBlock="Chart.TPO.SplitBlock",e.ChartTpoMergeBlock="Chart.TPO.MergeBlock",e.ChartObjectTreeShow="Chart.ObjectTree.Show",e.ChartDataWindowShow="Chart.DataWindow.Show",e.ChartPaneControlsDeletePane="Chart.PaneControls.DeletePane",e.ChartPaneControlsMaximizePane="Chart.PaneControls.MaximizePane",e.ChartPaneControlsMinimizePane="Chart.PaneControls.MinimizePane",e.ChartPaneControlsMovePaneDown="Chart.PaneControls.MovePaneDown",e.ChartPaneControlsMovePaneUp="Chart.PaneControls.MovePaneUp",e.ChartPaneControlsCollapsePane="Chart.PaneControls.CollapsePane",e.ChartPaneControlsRestorePane="Chart.PaneControls.RestorePane",e.ChartPriceScaleLabels="Chart.PriceScale.Labels",e.ChartPriceScaleLabelsToggleBidAskLabelsVisibility="Chart.PriceScale.Labels.ToggleBidAskLabelsVisibility",e.ChartPriceScaleLabelsToggleHighLowPriceLabelsVisibility="Chart.PriceScale.Labels.ToggleHighLowPriceLabelsVisibility",e.ChartPriceScaleLabelsToggleAveragePriceLabelVisibility="Chart.PriceScale.Labels.ToggleAveragePriceLabelVisibility",e.ChartPriceScaleLabelsToggleIndicatorsNameLabelsVisibility="Chart.PriceScale.Labels.ToggleIndicatorsNameLabelsVisibility",e.ChartPriceScaleLabelsToggleIndicatorsValueLabelsVisibility="Chart.PriceScale.Labels.ToggleIndicatorsValueLabelsVisibility",e.ChartPriceScaleLabelsTogglePrePostMarketLabelsVisibility="Chart.PriceScale.Labels.TogglePrePostMarketLabelsVisibility",e.ChartPriceScaleLabelsToggleNoOverlappingLabelsVisibility="Chart.PriceScale.Labels.ToggleNoOverlappingLabelsVisibility",e.ChartPriceScaleLabelsToggleSeriesLastValueVisibility="Chart.PriceScale.Labels.ToggleSeriesLastValueVisibility",e.ChartPriceScaleLabelsToggleSymbolNameLabelsVisibility="Chart.PriceScale.Labels.ToggleSymbolNameLabelsVisibility",e.ChartPriceScaleLabelsToggleSymbolPrevCloseValueVisibility="Chart.PriceScale.Labels.ToggleSymbolPrevCloseValueVisibility",e.ChartPriceScaleMergeAllScales="Chart.PriceScale.MergeAllScales",e.ChartPriceScaleMergeAllScalesToLeft="Chart.PriceScale.MergeAllScalesToLeft",e.ChartPriceScaleMergeAllScalesToRight="Chart.PriceScale.MergeAllScalesToRight",e.ChartPriceScaleMoveToLeft="Chart.PriceScale.MoveToLeft",e.ChartPriceScaleMoveToRight="Chart.PriceScale.MoveToRight",e.ChartPriceScaleReset="Chart.PriceScale.Reset",e.ChartPriceScaleToggleAddOrderPlusButtonVisibility="Chart.PriceScale.ToggleAddOrderPlusButtonVisibility",e.ChartPriceScaleToggleAutoScale="Chart.PriceScale.ToggleAutoScale",e.ChartPriceScaleToggleAutoScaleSeriesOnly="Chart.PriceScale.ToggleAutoScaleSeriesOnly",e.ChartPriceScaleToggleCountdownToBarCloseVisibility="Chart.PriceScale.ToggleCountdownToBarCloseVisibility",e.ChartPriceScaleToggleIndexedTo100="Chart.PriceScale.ToggleIndexedTo100",e.ChartPriceScaleToggleInvertScale="Chart.PriceScale.ToggleInvertScale",e.ChartPriceScaleToggleLogarithmic="Chart.PriceScale.ToggleLogarithmic",e.ChartPriceScaleTogglePercentage="Chart.PriceScale.TogglePercentage",e.ChartPriceScaleToggleRegular="Chart.PriceScale.ToggleRegular",e.ChartRedo="Chart.Redo",e.ChartRemoveAllIndicators="Chart.RemoveAllIndicators",e.ChartRemoveAllIndicatorsAndLineTools="Chart.RemoveAllIndicatorsAndLineTools",e.ChartRemoveAllLineTools="Chart.RemoveAllLineTools",e.ChartScalesReset="Chart.Scales.Reset",e.ChartScalesToggleLockPriceToBarRatio="Chart.Scales.ToggleLockPriceToBarRatio",e.ChartScrollToLineTool="Chart.ScrollToLineTool",e.ChartSelectedObjectHide="Chart.SelectedObject.Hide",e.ChartSelectedObjectRemove="Chart.SelectedObject.Remove",e.ChartSelectedObjectShow="Chart.SelectedObject.Show",e.ChartSelectedObjectShowSettingsDialog="Chart.SelectedObject.ShowSettingsDialog",e.ChartSelectedObjectToggleLocked="Chart.SelectedObject.ToggleLocked",e.ChartSeriesPriceScaleToggleAutoScale="Chart.Series.PriceScale.ToggleAutoScale",e.ChartSeriesPriceScaleToggleIndexedTo100="Chart.Series.PriceScale.ToggleIndexedTo100",e.ChartSeriesPriceScaleToggleInvertPriceScale="Chart.Series.PriceScale.ToggleInvertPriceScale",e.ChartSeriesPriceScaleToggleLogarithmic="Chart.Series.PriceScale.ToggleLogarithmic",e.ChartSeriesPriceScaleTogglePercentage="Chart.Series.PriceScale.TogglePercentage",e.ChartSeriesPriceScaleToggleRegular="Chart.Series.PriceScale.ToggleRegular",e.ChartSessionBreaksToggleVisibility="Chart.SessionBreaks.ToggleVisibility",e.ChartSetSession="Chart.SetSession",e.ChartSourceChangePriceScale="Chart.Source.ChangePriceScale",e.ChartSourceMergeDown="Chart.Source.MergeDown",e.ChartSourceMergeUp="Chart.Source.MergeUp",e.ChartSourceMoveToNoScale="Chart.Source.MoveToNoScale",e.ChartSourceMoveToOtherScale="Chart.Source.MoveToOtherScale",e.ChartSourceMoveToPane="Chart.Source.MoveToPane",e.ChartSourceUnmergeDown="Chart.Source.UnmergeDown",e.ChartSourceUnmergeUp="Chart.Source.UnmergeUp",e.ChartSourceVisualOrder="Chart.Source.VisualOrder",e.ChartSourceVisualOrderBringForward="Chart.Source.VisualOrder.BringForward",e.ChartSourceVisualOrderBringToFront="Chart.Source.VisualOrder.BringToFront",e.ChartSourceVisualOrderSendBackward="Chart.Source.VisualOrder.SendBackward",e.ChartSourceVisualOrderSendToBack="Chart.Source.VisualOrder.SendToBack",e.ChartSourceResetInputPoints="Chart.Source.ResetInputPoints",e.ChartThemeApply="Chart.Theme.Apply",e.ChartThemeApplyCustom="Chart.Theme.Apply.Custom",e.ChartThemeSaveAs="Chart.Theme.SaveAs",e.ChartTimeScaleReset="Chart.TimeScale.Reset",e.ChartUndo="Chart.Undo",e.ChartShowAllIdeas="Chart.ShowAllIdeas",e.ChartShowIdeasOfFollowedUsers="Chart.ShowIdeasOfFollowedUsers",e.ChartShowMyIdeasOnly="Chart.ShowMyIdeasOnly",e.ChartToggleVisibilityAllLineTools="Chart.ToggleVisibility.AllLineTools",e.ChartToggleVisibilityContinuousContractSwitch="Chart.ToggleVisibility.ContinuousContractSwitch",e.ChartToggleVisibilityContractExpiration="Chart.ToggleVisibility.ContractExpiration",e.ChartToggleVisibilityDividends="Chart.ToggleVisibility.Dividends",e.ChartToggleVisibilityEarnings="Chart.ToggleVisibility.Earnings",e.ChartToggleVisibilityEconomicEvents="Chart.ToggleVisibility.EconomicEvents",e.ChartToggleVisibilitySplits="Chart.ToggleVisibility.Splits",e.ChartToggleVisibilityLatestNewsAndMinds="Chart.ToggleVisibility.LatestNewsAndMinds",e.ChartSourceIntervalsVisibility="Chart.Source.IntervalsVisibility",e.ChartSourceIntervalsVisibilityCurrentAndAbove="Chart.Source.IntervalsVisibility.CurrentAndAbove",e.ChartSourceIntervalsVisibilityCurrentAndBelow="Chart.Source.IntervalsVisibility.CurrentAndBelow",e.ChartSourceIntervalsVisibilityOnlyCurrent="Chart.Source.IntervalsVisibility.Current",e.ChartSourceIntervalsVisibilityAll="Chart.Source.IntervalsVisibility.All",e.NoteCreate="Note.Create",e.NoteEdit="Note.Edit",e.NoteRemove="Note.Remove",e.ObjectsTreeCreateGroup="ObjectsTree.CreateGroup",e.ObjectsTreeRemoveItem="ObjectsTree.RemoveItem",e.ObjectsTreeRenameItem="ObjectsTree.RenameItem",e.ObjectsTreeToggleItemLocked="ObjectsTree.ToggleItemLocked",e.ObjectsTreeToggleItemVisibility="ObjectsTree.ToggleItemVisibility",e.PineEditorConsoleCopyMessage="PineEditor.Console.CopyMessage",e.PineEditorConsoleToggleVisibility="PineEditor.Console.ToggleVisibility",e.PineEditorConsoleClear="PineEditor.Console.Clear",e.ScreenerAddSymbolToCompare="Screener.AddSymbolToCompare",e.ScreenerColumnRemove="Screener.Column.Remove",e.ScreenerFilterChange="Screener.Filter.Change",e.ScreenerFilterReset="Screener.Filter.Reset",e.ScreenerOpenSymbolChart="Screener.OpenSymbolChart",e.ScreenerOpenSymbolOverview="Screener.OpenSymbolOverview",e.ScreenerToggleVisibilityCurrency="Screener.ToggleVisibility.Currency",e.ScreenerToggleVisibilityDescription="Screener.ToggleVisibility.Description",e.ScreenerToggleVisibilityRating="Screener.ToggleVisibility.Rating",e.ScreenerToggleVisibilitySymbolType="Screener.ToggleVisibility.SymbolType",e.TradingCancelOrder="Trading.CancelOrder",e.TradingClosePosition="Trading.ClosePosition",e.TradingCustomActionId="Trading.CustomActionId",e.TradingDOMPlaceLimitOrder="Trading.DOMPlaceLimitOrder",e.TradingDOMPlaceMarketOrder="Trading.DOMPlaceMarketOrder",e.TradingDOMPlaceStopLimitOrder="Trading.DOMPlaceStopLimitOrder",e.TradingDOMPlaceStopOrder="Trading.DOMPlaceStopOrder",e.TradingEditOrder="Trading.EditOrder",e.TradingModifyPosition="Trading.ModifyPosition",e.TradingReversePosition="Trading.ReversePosition",e.TradingSellBuyButtonsToggleVisibility="Trading.SellBuyButtonsToggleVisibility",e.TradingTradeFromChart="Trading.TradeFromChart",e.TradingNoOverlapMode="Trading.NoOverlapMode",e.TradingShowSelectBrokerPanel="Trading.ShowSelectBrokerPanel",e.TradingOrderTitle="Trading.OrderTitle",e.TradingPositionTitle="Trading.PositionTitle",e.WatchlistActions="Watchlist.Actions",e.WatchlistAddSelectedSymbolsToCompare="Watchlist.AddSelectedSymbolsToCompare ",e.WatchlistAddSymbolToCompare="Watchlist.AddSymbolToCompare",e.WatchlistAddSymbolToSection="Watchlist.AddSymbolToSection",e.WatchlistChangeFlaggedGroupColor="Watchlist.ChangeFlaggedGroupColor",e.WatchlistAddSymbol="Watchlist.AddSymbol",e.WatchlistCreate="Watchlist.Create",e.WatchlistAddSelectedSymbols="Watchlist.AddSelectedSymbols",e.WatchlistAddSelectedSymbolsLists="Watchlist.AddSelectedSymbols.Lists",e.WatchlistGetDisplayedTickerDescription="Watchlist.GetDisplayedTickerDescription",e.WatchlistCreateSection="Watchlist.CreateSection",e.WatchlistFlagSelectedSymbols="Watchlist.FlagSelectedSymbols",e.WatchlistFlagSymbol="Watchlist.FlagSymbol",e.WatchlistOpenSymbolChart="Watchlist.OpenSymbolChart",e.WatchlistOpenSymbolOverview="Watchlist.OpenSymbolOverview",e.WatchlistRemoveSection="Watchlist.RemoveSection",e.WatchlistRemoveSymbol="Watchlist.RemoveSymbol",e.WatchlistRenameSection="Watchlist.RenameSection",e.WatchlistUnflagAllSymbols="Watchlist.UnflagAllSymbols",e.WatchlistUnflagSelectedSymbols="Watchlist.UnflagSelectedSymbols",e.WatchlistUnflagSymbol="Watchlist.UnflagSymbol",function(e){e.extractErrorReason=function(e){return e.params[1]}}(t||(t={})),function(e){e.Default="default",e.FullSingleSession="full_single_session"}(i||(i={})),(o=exports.TimeFrameType||(exports.TimeFrameType={})).PeriodBack="period-back",o.TimeRange="time-range",function(e){e.PeriodBack="period-back",e.TimeRange="time-range"}(r||(r={})),(n=exports.MarketStatus||(exports.MarketStatus={})).Open="market",n.Pre="pre_market",n.Post="post_market",n.Close="out_of_session",n.Holiday="holiday",(a=exports.MenuItemType||(exports.MenuItemType={})).Separator="separator",a.Action="action",(s=exports.ClearMarksMode||(exports.ClearMarksMode={}))[s.All=0]="All",s[s.BarMarks=1]="BarMarks",s[s.TimeScaleMarks=2]="TimeScaleMarks",(l=exports.LineStudyPlotStyle||(exports.LineStudyPlotStyle={}))[l.Line=0]="Line",l[l.Histogram=1]="Histogram",l[l.Cross=3]="Cross",l[l.Area=4]="Area",l[l.Columns=5]="Columns",l[l.Circles=6]="Circles",l[l.LineWithBreaks=7]="LineWithBreaks",l[l.AreaWithBreaks=8]="AreaWithBreaks",l[l.StepLine=9]="StepLine",l[l.StepLineWithDiamonds=10]="StepLineWithDiamonds",l[l.StepLineWithBreaks=11]="StepLineWithBreaks",(d=exports.StudyPlotType||(exports.StudyPlotType={})).Line="line",d.Colorer="colorer",d.BarColorer="bar_colorer",d.BgColorer="bg_colorer",d.TextColorer="text_colorer",d.OhlcColorer="ohlc_colorer",d.CandleWickColorer="wick_colorer",d.CandleBorderColorer="border_colorer",d.UpColorer="up_colorer",d.DownColorer="down_colorer",d.Shapes="shapes",d.Chars="chars",d.Arrows="arrows",d.Data="data",d.DataOffset="dataoffset",d.OhlcOpen="ohlc_open",d.OhlcHigh="ohlc_high",d.OhlcLow="ohlc_low",d.OhlcClose="ohlc_close",function(e){e.AlertCondition="alertcondition"}(c||(c={})),(h=exports.StudyPlotDisplayTarget||(exports.StudyPlotDisplayTarget={}))[h.None=0]="None",h[h.Pane=1]="Pane",h[h.DataWindow=2]="DataWindow",h[h.PriceScale=4]="PriceScale",h[h.StatusLine=8]="StatusLine",h[h.All=15]="All",function(e){e[e.None=0]="None",e[e.Pane=1]="Pane",e[e.DataWindow=2]="DataWindow",e[e.PriceScale=4]="PriceScale",e[e.StatusLine=8]="StatusLine",e[e.All=15]="All"}(g||(g={})),(u=exports.OhlcStudyPlotStyle||(exports.OhlcStudyPlotStyle={})).OhlcBars="ohlc_bars",u.OhlcCandles="ohlc_candles",function(e){e.Auto="auto",e.Tiny="tiny",e.Small="small",e.Normal="normal",e.Large="large",e.Huge="huge"}(C||(C={})),(p=exports.StudyInputType||(exports.StudyInputType={})).Integer="integer",p.Float="float",p.Price="price",p.Bool="bool",p.Text="text",p.Symbol="symbol",p.Session="session",p.Source="source",p.Resolution="resolution",p.Time="time",p.BarTime="bar_time",p.Color="color",p.Textarea="text_area",function(e){e[e.None=0]="None",e[e.DataWindow=2]="DataWindow",e[e.StatusLine=8]="StatusLine",e[e.All=15]="All"}(S||(S={})),function(e){e.InitialCapital="initial_capital",e.Currency="currency",e.DefaultQTYValue="default_qty_value",e.DefaultQTYType="default_qty_type",e.Pyramiding="pyramiding",e.ComissionValue="commission_value",e.ComissionType="commission_type",e.BacktestFillLimitsAssumtion="backtest_fill_limits_assumption",e.Slippage="slippage",e.CalcOnOrderFills="calc_on_order_fills",e.CalcOnEveryTick="calc_on_every_tick",e.MarginLong="margin_long",e.MarginShort="margin_short",e.UseBarMagnifier="use_bar_magnifier",e.ProcessOrdersOnClose="process_orders_on_close",e.FillOrdersOnStandardOHLC="fill_orders_on_standard_ohlc"}(m||(m={})),function(e){e.Fixed="fixed",e.CashPerOrder="cash_per_order",e.PercentOfEquity="percent_of_equity"}(y||(y={})),function(e){e.Percent="percent",e.CashPerContract="cash_per_contract",e.CashPerOrder="cash_per_order"}(T||(T={})),function(e){e.FirstBar="first_visible_bar_time",e.LastBar="last_visible_bar_time",e.Realtime="subscribeRealtime"}(_||(_={})),function(e){e.FgColor="__chart_fgcolor",e.BgColor="__chart_bgcolor"}(b||(b={})),(P=exports.StudyTargetPriceScale||(exports.StudyTargetPriceScale={}))[P.Right=0]="Right",P[P.Left=1]="Left",P[P.NoScale=2]="NoScale",function(e){e[e.Right=0]="Right",e[e.Left=1]="Left",e[e.None=2]="None"}(L||(L={})),(A=exports.FilledAreaType||(exports.FilledAreaType={})).TypePlots="plot_plot",A.TypeHlines="hline_hline",function(e){e[e.StopLoss=0]="StopLoss",e[e.TrailingStop=1]="TrailingStop",e[e.GuaranteedStop=2]="GuaranteedStop"}(w||(w={})),function(e){e.Symbol="symbol"}(f||(f={})),function(e){e[e.PopUp=0]="PopUp",e[e.Notification=1]="Notification"}(I||(I={})),function(e){e[e.CONNECTED=1]="CONNECTED",e[e.CONNECTING=2]="CONNECTING",e[e.DISCONNECTED=3]="DISCONNECTED",e[e.ERROR=4]="ERROR"}(v||(v={})),(O=exports.ConnectionStatus||(exports.ConnectionStatus={}))[O.Connected=1]="Connected",O[O.Connecting=2]="Connecting",O[O.Disconnected=3]="Disconnected",O[O.Error=4]="Error",function(e){e[e.LIMIT=1]="LIMIT",e[e.MARKET=2]="MARKET",e[e.STOP=3]="STOP",e[e.STOPLIMIT=4]="STOPLIMIT"}(R||(R={})),(D=exports.OrderType||(exports.OrderType={}))[D.Limit=1]="Limit",D[D.Market=2]="Market",D[D.Stop=3]="Stop",D[D.StopLimit=4]="StopLimit",function(e){e[e.BUY=1]="BUY",e[e.SELL=-1]="SELL"}(V||(V={})),(k=exports.Side||(exports.Side={}))[k.Buy=1]="Buy",k[k.Sell=-1]="Sell",function(e){e[e.CANCELED=1]="CANCELED",e[e.FILLED=2]="FILLED",e[e.INACTIVE=3]="INACTIVE",e[e.PLACING=4]="PLACING",e[e.REJECTED=5]="REJECTED",e[e.WORKING=6]="WORKING"}(F||(F={})),function(e){e[e.ALL=0]="ALL",e[e.CANCELED=1]="CANCELED",e[e.FILLED=2]="FILLED",e[e.INACTIVE=3]="INACTIVE",e[e.REJECTED=5]="REJECTED",e[e.WORKING=6]="WORKING"}(E||(E={})),(x=exports.OrderStatus||(exports.OrderStatus={}))[x.Canceled=1]="Canceled",x[x.Filled=2]="Filled",x[x.Inactive=3]="Inactive",x[x.Placing=4]="Placing",x[x.Rejected=5]="Rejected",x[x.Working=6]="Working",(W=exports.OrderStatusFilter||(exports.OrderStatusFilter={}))[W.All=0]="All",W[W.Canceled=1]="Canceled",W[W.Filled=2]="Filled",W[W.Inactive=3]="Inactive",W[W.Rejected=5]="Rejected",W[W.Working=6]="Working",function(e){e[e.Order=1]="Order",e[e.Position=2]="Position"}(M||(M={})),function(e){e[e.ORDER=1]="ORDER",e[e.POSITION=2]="POSITION"}(B||(B={})),(N=exports.ParentType||(exports.ParentType={}))[N.Order=1]="Order",N[N.Position=2]="Position",N[N.IndividualPosition=3]="IndividualPosition",function(e){e[e.StopLoss=0]="StopLoss",e[e.TakeProfit=1]="TakeProfit",e[e.TrailingStop=2]="TrailingStop",e[e.GuaranteedStop=3]="GuaranteedStop"}(U||(U={})),function(e){e[e.LIMITPRICE=1]="LIMITPRICE",e[e.STOPPRICE=2]="STOPPRICE",e[e.TAKEPROFIT=3]="TAKEPROFIT",e[e.STOPLOSS=4]="STOPLOSS"}(H||(H={})),(j=exports.OrderTicketFocusControl||(exports.OrderTicketFocusControl={}))[j.LimitPrice=1]="LimitPrice",j[j.StopPrice=2]="StopPrice",j[j.TakeProfit=3]="TakeProfit",j[j.StopLoss=4]="StopLoss",j[j.Quantity=5]="Quantity",function(e){e[e.ERROR=0]="ERROR",e[e.SUCCESS=1]="SUCCESS"}(z||(z={})),(G=exports.NotificationType||(exports.NotificationType={}))[G.Error=0]="Error",G[G.Success=1]="Success",function(e){e[e.Demo=1]="Demo",e[e.Real=0]="Real"}($||($={})),(J=exports.OrderOrPositionMessageType||(exports.OrderOrPositionMessageType={})).Information="information",J.Warning="warning",J.Error="error",function(e){e.Demo="demo",e.Live="live"}(q||(q={})),function(e){e[e.LogOut=0]="LogOut",e[e.FailedRestoring=1]="FailedRestoring",e[e.Offline=2]="Offline",e[e.APIError=3]="APIError",e[e.TwoFactorRequired=4]="TwoFactorRequired",e[e.CancelAuthorization=5]="CancelAuthorization",e[e.TimeOutForAuthorization=6]="TimeOutForAuthorization",e[e.OauthError=7]="OauthError",e[e.BrokenConnection=8]="BrokenConnection",e[e.FailedSignIn=9]="FailedSignIn"}(K||(K={})),function(e){e[e.None=0]="None",e[e.Pips=1]="Pips",e[e.Ticks=2]="Ticks"}(Z||(Z={})),function(e){e.Halted="HALTED",e.NotShortable="NOT-SHORTABLE",e.HardToBorrow="HARD-TO-BORROW"}(Q||(Q={})),function(e){e[e.Limit=1]="Limit",e[e.Stop=2]="Stop"}(Y||(Y={})),function(e){e.Disallowed="disallowed",e.Allowed="allowed",e.AllowedWithWarning="allowed_with_warning"}(X||(X={})),function(e){e.PlaceOrder="place_order",e.ModifyOrder="modify_order",e.CancelOrder="cancel_order",e.ModifyPosition="modify_position",e.ClosePosition="close_position",e.ModifyIndividualPosition="modify_individual_position",e.CloseIndividualPosition="close_individual_position",e.CloseNetPosition="close_net_position"}(ee||(ee={})),(te=exports.StandardFormatterName||(exports.StandardFormatterName={})).Date="date",te.DateOrDateTime="dateOrDateTime",te.Default="default",te.Fixed="fixed",te.FixedInCurrency="fixedInCurrency",te.VariablePrecision="variablePrecision",te.FormatQuantity="formatQuantity",te.FormatPrice="formatPrice",te.FormatPriceForexSup="formatPriceForexSup",te.FormatPriceInCurrency="formatPriceInCurrency",te.IntegerSeparated="integerSeparated",te.LocalDate="localDate",te.LocalDateOrDateTime="localDateOrDateTime",te.Percentage="percentage",te.Pips="pips",te.Profit="profit",te.ProfitInInstrumentCurrency="profitInInstrumentCurrency",te.Side="side",te.PositionSide="positionSide",te.Status="status",te.Symbol="symbol",te.Text="text",te.Type="type",te.MarginPercent="marginPercent",te.Empty="empty",(ie=exports.OverridePriceAxisLastValueMode||(exports.OverridePriceAxisLastValueMode={}))[ie.LastPriceAndPercentageValue=0]="LastPriceAndPercentageValue",ie[ie.LastValueAccordingToScale=1]="LastValueAccordingToScale",(oe=exports.OverrideLineStyle||(exports.OverrideLineStyle={}))[oe.Solid=0]="Solid",oe[oe.Dotted=1]="Dotted",oe[oe.Dashed=2]="Dashed",function(e){e[e.Offline=0]="Offline",e[e.Resolving=1]="Resolving",e[e.Loading=2]="Loading",e[e.Ready=3]="Ready",e[e.InvalidSymbol=4]="InvalidSymbol",e[e.Snapshot=5]="Snapshot",e[e.EOD=6]="EOD",e[e.Pulse=7]="Pulse",e[e.Delayed=8]="Delayed",e[e.DelayedSteaming=9]="DelayedSteaming",e[e.NoBars=10]="NoBars",e[e.Replay=11]="Replay",e[e.Error=12]="Error",e[e.CalculationError=13]="CalculationError",e[e.UnsupportedResolution=14]="UnsupportedResolution"}(re||(re={})),function(e){e[e.Markers=0]="Markers",e[e.Stepline=1]="Stepline",e[e.Simple=2]="Simple"}(ne||(ne={})),(ae=exports.ChartStyle||(exports.ChartStyle={}))[ae.Bar=0]="Bar",ae[ae.Candle=1]="Candle",ae[ae.Line=2]="Line",ae[ae.Area=3]="Area",ae[ae.Renko=4]="Renko",ae[ae.Kagi=5]="Kagi",ae[ae.PnF=6]="PnF",ae[ae.LineBreak=7]="LineBreak",ae[ae.HeikinAshi=8]="HeikinAshi",ae[ae.HollowCandle=9]="HollowCandle",ae[ae.Baseline=10]="Baseline",ae[ae.Range=11]="Range",ae[ae.HiLo=12]="HiLo",ae[ae.Column=13]="Column",ae[ae.LineWithMarkers=14]="LineWithMarkers",ae[ae.Stepline=15]="Stepline",ae[ae.HLCArea=16]="HLCArea",ae[ae.VolFootprint=17]="VolFootprint",ae[ae.TPO=18]="TPO",ae[ae.VolCandle=19]="VolCandle",ae[ae.SVP=20]="SVP",(se=exports.TimeHoursFormat||(exports.TimeHoursFormat={})).TwentyFourHours="24-hours",se.TwelveHours="12-hours",function(e){e[e.Initial=2]="Initial",e[e.SeriesZOrderIsAlwaysZero=3]="SeriesZOrderIsAlwaysZero",e[e.Current=3]="Current"}(le||(le={})),(de=exports.PlDisplay||(exports.PlDisplay={}))[de.Money=0]="Money",de[de.Pips=1]="Pips",de[de.Percentage=2]="Percentage",(ce=exports.TradedGroupHorizontalAlignment||(exports.TradedGroupHorizontalAlignment={}))[ce.Left=0]="Left",ce[ce.Center=1]="Center",ce[ce.Right=2]="Right";exports.PlDisplay.Money,exports.PlDisplay.Money,exports.TradedGroupHorizontalAlignment.Right;var he,ge,ue,Ce,pe,Se,me,ye,Te,_e,be,Pe,Le,Ae,we;function fe(e,t){const i={...e};for(const o in t)"object"!=typeof e[o]||null===e[o]||Array.isArray(e[o])?void 0!==t[o]&&(i[o]=t[o]):i[o]=fe(e[o],t[o]);return i}!function(e){e[e.Background=0]="Background",e[e.Foreground=1]="Foreground",e[e.Topmost=2]="Topmost"}(he||(he={})),function(e){e[e.Unavailable=0]="Unavailable",e[e.AvailableReadonlyAlwaysDisabled=1]="AvailableReadonlyAlwaysDisabled",e[e.AvailableReadonlyAlwaysEnabled=2]="AvailableReadonlyAlwaysEnabled",e[e.Available=3]="Available"}(ge||(ge={})),function(e){e[e.ViewportChangeUserAction=0]="ViewportChangeUserAction",e[e.DataUpdate=1]="DataUpdate",e[e.SeriesRestart=2]="SeriesRestart",e[e.SeriesCompleted=3]="SeriesCompleted",e[e.StudyCreation=4]="StudyCreation"}(ue||(ue={})),function(e){e[e.Chart=0]="Chart"}(Ce||(Ce={})),(pe=exports.VisibilityType||(exports.VisibilityType={})).AlwaysOn="alwaysOn",pe.VisibleOnMouseOver="visibleOnMouseOver",pe.AlwaysOff="alwaysOff",(Se=exports.PriceScaleMode||(exports.PriceScaleMode={}))[Se.Normal=0]="Normal",Se[Se.Log=1]="Log",Se[Se.Percentage=2]="Percentage",Se[Se.IndexedTo100=3]="IndexedTo100",(me=exports.SeriesType||(exports.SeriesType={}))[me.Bars=0]="Bars",me[me.Candles=1]="Candles",me[me.Line=2]="Line",me[me.Area=3]="Area",me[me.HeikenAshi=8]="HeikenAshi",me[me.HollowCandles=9]="HollowCandles",me[me.Baseline=10]="Baseline",me[me.HiLo=12]="HiLo",me[me.Column=13]="Column",me[me.LineWithMarkers=14]="LineWithMarkers",me[me.Stepline=15]="Stepline",me[me.HLCArea=16]="HLCArea",me[me.VolCandle=19]="VolCandle",me[me.Renko=4]="Renko",me[me.Kagi=5]="Kagi",me[me.PointAndFigure=6]="PointAndFigure",me[me.LineBreak=7]="LineBreak",function(e){e.Value="_seriesId"}(ye||(ye={})),(Te=exports.HHistDirection||(exports.HHistDirection={})).LeftToRight="left_to_right",Te.RightToLeft="right_to_left",function(e){e.Relative="relative",e.Absolute="absolute"}(_e||(_e={})),function(e){e.UpDown="Up/Down",e.Total="Total",e.Delta="Delta"}(be||(be={})),(Pe=exports.MarkLocation||(exports.MarkLocation={})).AboveBar="AboveBar",Pe.BelowBar="BelowBar",Pe.Top="Top",Pe.Bottom="Bottom",Pe.Right="Right",Pe.Left="Left",Pe.Absolute="Absolute",Pe.AbsoluteUp="AbsoluteUp",Pe.AbsoluteDown="AbsoluteDown",function(e){e.Left="left",e.Center="center",e.Right="right"}(Le||(Le={})),function(e){e.Top="top",e.Middle="middle",e.Bottom="bottom"}(Ae||(Ae={})),(we=exports.LineStyle||(exports.LineStyle={}))[we.Solid=0]="Solid",we[we.Dotted=1]="Dotted",we[we.Dashed=2]="Dashed";const Ie={width:800,height:500,interval:"1D",timezone:"Etc/UTC",container:"",library_path:"",locale:"en",widgetbar:{details:!1,watchlist:!1,news:!1,datawindow:!1,watchlist_settings:{default_symbols:[]}},overrides:{"mainSeriesProperties.showCountdown":!1},studies_overrides:{},trading_customization:{position:{},order:{}},brokerConfig:{configFlags:{}},fullscreen:!1,autosize:!1,disabled_features:[],enabled_features:[],debug:!1,logo:{},time_frames:[{text:"5y",resolution:"1W"},{text:"1y",resolution:"1W"},{text:"6m",resolution:"120"},{text:"3m",resolution:"60"},{text:"1m",resolution:"30"},{text:"5d",resolution:"5"},{text:"1d",resolution:"1"}],client_id:"0",user_id:"0",charts_storage_api_version:"1.0",favorites:{intervals:[],chartTypes:[],indicators:[],drawingTools:[]}},ve=JSON.parse('[{"iso":"ar","dir":"rtl","language":"ar"},{"iso":"pt","dir":"ltr","language":"pt"},{"iso":"ca","dir":"ltr","language":"ca_ES"},{"iso":"cs","dir":"ltr","language":"cs"},{"iso":"de","dir":"ltr","language":"de"},{"iso":"en","dir":"ltr","language":"en"},{"iso":"es","dir":"ltr","language":"es"},{"iso":"fa","dir":"rtl","language":"fa"},{"iso":"fr","dir":"ltr","language":"fr"},{"iso":"he","dir":"rtl","language":"he_IL"},{"iso":"hu","dir":"ltr","language":"hu_HU"},{"iso":"id","dir":"ltr","language":"id_ID"},{"iso":"en","dir":"ltr","language":"en"},{"iso":"it","dir":"ltr","language":"it"},{"iso":"ja","dir":"ltr","language":"ja"},{"iso":"ko","dir":"ltr","language":"ko"},{"iso":"ms","dir":"ltr","language":"ms_MY"},{"iso":"pl","dir":"ltr","language":"pl"},{"iso":"ru","dir":"ltr","language":"ru"},{"iso":"sv","dir":"ltr","language":"sv"},{"iso":"th","dir":"ltr","language":"th"},{"iso":"tr","dir":"ltr","language":"tr"},{"iso":"vi","dir":"ltr","language":"vi"},{"iso":"zh-Hans","dir":"ltr","language":"zh"},{"iso":"zh-Hant","dir":"ltr","language":"zh_TW"},{"iso":"el","dir":"ltr","language":"el"},{"iso":"nl","dir":"ltr","language":"nl_NL"},{"iso":"ro","dir":"ltr","language":"ro"}]');let Oe=!1;function Re(){return"CL v28.0.0 (internal id 1e591f07 @ 2024-08-13T15:03:46.703Z)"}const De=class{constructor(e){var t,i;if(this._id=`tradingview_${(1048576*(1+Math.random())|0).toString(16).substring(1)}`,this._ready=!1,this._readyHandlers=[],this._onWindowResize=this._autoResizeChart.bind(this),!e.datafeed)throw new Error("Datafeed is not defined");(null===(t=e.overrides)||void 0===t?void 0:t["mainSeriesProperties.priceAxisProperties.lockScale"])&&(console.warn("mainSeriesProperties.priceAxisProperties.lockScale can not be set to true within the widget constructor"),delete e.overrides["mainSeriesProperties.priceAxisProperties.lockScale"]),this._options=fe(Ie,e);"dark"===(null!==(i=this._options.theme)&&void 0!==i?i:"light").toLowerCase()&&void 0===this._options.loading_screen&&(this._options.loading_screen={backgroundColor:"#131722"}),(this._options.debug||this._options.debug_broker)&&(Oe||(Oe=!0,console.log("Using CL v28.0.0 (internal id 1e591f07 @ 2024-08-13T15:03:46.703Z)"))),this._create()}setDebugMode(e){this._innerAPI().setDebugMode(e)}onChartReady(e){this._ready?e.call(this):this._readyHandlers.push(e)}headerReady(){return this._innerWindowLoaded.then((()=>this._innerWindow().headerReady()))}onGrayedObjectClicked(e){this._doWhenInnerApiLoaded((t=>{t.onGrayedObjectClicked(e)}))}onShortcut(e,t){this._doWhenInnerWindowLoaded((i=>{i.createShortcutAction(e,t)}))}subscribe(e,t){this._doWhenInnerApiLoaded((i=>{i.subscribe(e,t)}))}unsubscribe(e,t){this._doWhenInnerApiLoaded((i=>{i.unsubscribe(e,t)}))}chart(e){return this._innerAPI().chart(e)}getLanguage(){return this._options.locale}setSymbol(e,t,i){this._innerAPI().changeSymbol(e,t,i)}remove(){window.removeEventListener("resize",this._onWindowResize),this._readyHandlers.splice(0,this._readyHandlers.length),delete window[this._id],this._iFrame.parentNode&&this._iFrame.parentNode.removeChild(this._iFrame)}closePopupsAndDialogs(){this._doWhenInnerApiLoaded((e=>{e.closePopupsAndDialogs()}))}selectLineTool(e,t){this._innerAPI().selectLineTool(e,t)}selectedLineTool(){return this._innerAPI().selectedLineTool()}save(e,t){this._innerAPI().saveChart(e,t)}load(e,t){this._innerAPI().loadChart({json:e,extendedData:t})}getSavedCharts(e){this._innerAPI().getSavedCharts(e)}loadChartFromServer(e){this._innerAPI().loadChartFromServer(e)}saveChartToServer(e,t,i){this._innerAPI().saveChartToServer(e,t,i)}removeChartFromServer(e,t){this._innerAPI().removeChartFromServer(e,t)}onContextMenu(e){this._doWhenInnerApiLoaded((t=>{t.onContextMenu(e)}))}createButton(e){return this._innerWindow().createButton(e)}createDropdown(e){return this._innerWindow().createDropdown(e)}showNoticeDialog(e){this._doWhenInnerApiLoaded((t=>{t.showNoticeDialog(e)}))}showConfirmDialog(e){this._doWhenInnerApiLoaded((t=>{t.showConfirmDialog(e)}))}showLoadChartDialog(){this._innerAPI().showLoadChartDialog()}showSaveAsChartDialog(){this._innerAPI().showSaveAsChartDialog()}symbolInterval(){return this._innerAPI().getSymbolInterval()}mainSeriesPriceFormatter(){return this._innerAPI().mainSeriesPriceFormatter()}getIntervals(){return this._innerAPI().getIntervals()}getStudiesList(){return this._innerAPI().getStudiesList()}getStudyInputs(e){return this._innerAPI().getStudyInputs(e)}getStudyStyles(e){return this._innerAPI().getStudyStyles(e)}addCustomCSSFile(e){this._innerWindow().addCustomCSSFile(e)}applyOverrides(e){this._options=fe(this._options,{overrides:e}),this._doWhenInnerWindowLoaded((t=>{t.applyOverrides(e)}))}applyStudiesOverrides(e){this._doWhenInnerWindowLoaded((t=>{t.applyStudiesOverrides(e)}))}watchList(){return this._innerAPI().watchlist()}news(){return this._innerAPI().news()}widgetbar(){return this._innerAPI().widgetbar()}activeChart(){return this._innerAPI().activeChart()}activeChartIndex(){return this._innerAPI().activeChartIndex()}setActiveChart(e){return this._innerAPI().setActiveChart(e)}chartsCount(){return this._innerAPI().chartsCount()}layout(){return this._innerAPI().layout()}setLayout(e){this._innerAPI().setLayout(e)}layoutName(){return this._innerAPI().layoutName()}resetLayoutSizes(e){this._innerAPI().resetLayoutSizes(e)}changeTheme(e,t){return this._innerWindow().changeTheme(e,t)}getTheme(){return this._innerWindow().getTheme()}takeScreenshot(){this._doWhenInnerApiLoaded((e=>{e.takeScreenshot()}))}lockAllDrawingTools(){return this._innerAPI().lockAllDrawingTools()}hideAllDrawingTools(){return this._innerAPI().hideAllDrawingTools()}drawOnAllChartsEnabled(){return this._innerAPI().drawOnAllChartsEnabled()}drawOnAllCharts(e){this._innerAPI().drawOnAllCharts(e)}magnetEnabled(){return this._innerAPI().magnetEnabled()}magnetMode(){return this._innerAPI().magnetMode()}undoRedoState(){return this._innerAPI().undoRedoState()}setIntervalLinkingEnabled(e){this._innerAPI().setIntervalLinkingEnabled(e)}setDateRangeLinkingEnabled(e){this._innerAPI().setDateRangeLinkingEnabled(e)}setTimeFrame(e){this._innerAPI().setTimeFrame(e)}symbolSync(){return this._innerAPI().symbolSync()}intervalSync(){return this._innerAPI().intervalSync()}crosshairSync(){return this._innerAPI().crosshairSync()}timeSync(){return this._innerAPI().timeSync()}dateRangeSync(){return this._innerAPI().dateRangeSync()}setFeatureEnabled(e,t){this._innerAPI().setFeatureEnabled(e,t)}getAllFeatures(){return this._innerWindow().getAllFeatures()}clearUndoHistory(){return this._innerAPI().clearUndoHistory()}undo(){return this._innerAPI().undo()}redo(){return this._innerAPI().redo()}startFullscreen(){this._innerAPI().startFullscreen()}exitFullscreen(){this._innerAPI().exitFullscreen()}takeClientScreenshot(e){return this._innerAPI().takeClientScreenshot(e)}navigationButtonsVisibility(){return this._innerWindow().getNavigationButtonsVisibility()}paneButtonsVisibility(){return this._innerWindow().getPaneButtonsVisibility()}dateFormat(){return this._innerWindow().getDateFormat()}timeHoursFormat(){return this._innerWindow().getTimeHoursFormat()}currencyAndUnitVisibility(){return this._innerWindow().getCurrencyAndUnitVisibility()}supportedChartTypes(){return this._innerAPI().supportedChartTypes()}watermark(){return this._innerAPI().watermark()}customSymbolStatus(){return this._innerWindow().customSymbolStatus()}setCSSCustomProperty(e,t){if(!1===e.startsWith("--"))throw new Error("customPropertyName should begin with a double hyphen");this._innerWindow().document.body.style.setProperty(e,t)}getCSSCustomPropertyValue(e){if(!1===e.startsWith("--"))throw new Error("customPropertyName should begin with a double hyphen");const t=this._innerWindow().document.body,i=t.style.getPropertyValue(e);if(i)return i;return getComputedStyle(t).getPropertyValue(e)}unloadUnusedCharts(){this._innerAPI().unloadUnusedCharts()}async customThemes(){return this._innerWindow().customThemes()}linking(){return this._innerAPI().linking}_innerAPI(){return this._innerWindow().tradingViewApi}_innerWindow(){return this._iFrame.contentWindow}_doWhenInnerWindowLoaded(e){this._ready?e(this._innerWindow()):this._innerWindowLoaded.then((()=>{e(this._innerWindow())}))}_doWhenInnerApiLoaded(e){this._doWhenInnerWindowLoaded((t=>{t.doWhenApiIsReady((()=>e(this._innerAPI())))}))}_autoResizeChart(){this._options.fullscreen&&(this._iFrame.style.height=window.innerHeight+"px",Ve&&setTimeout((()=>{this._iFrame.style.height=window.innerHeight+"px"}),30))}_create(){var e,t;const i=null!==(t=null===(e=this._options.enabled_features)||void 0===e?void 0:e.includes("iframe_loading_compatibility_mode"))&&void 0!==t&&t,[o,r]=this._render(!i),n=this._options.container,a="string"==typeof n?document.getElementById(n):n;if(null===a)throw new Error(`There is no such element - #${this._options.container}`);a.innerHTML=o,this._iFrame=a.querySelector(`#${this._id}`);const s=this._iFrame;this._innerWindowLoaded=new Promise((e=>{const t=()=>{s.removeEventListener("load",t,!1),e()};s.addEventListener("load",t,!1)})),i&&(s.contentWindow?(s.contentWindow.document.open(),s.contentWindow.document.write(r),s.contentWindow.document.close()):console.warn("Unable to locate contentWindow for the created iframe. Please try disabling the `iframe_loading_compatibility_mode` featureset.")),(this._options.autosize||this._options.fullscreen)&&(s.style.width="100%",this._options.fullscreen||(s.style.height="100%")),window.addEventListener("resize",this._onWindowResize),this._onWindowResize(),this._innerWindowLoaded.then((()=>{try{this._innerWindow().widgetReady((()=>{this._ready=!0;for(const e of this._readyHandlers)try{e.call(this)}catch(e){console.error(e)}this._innerWindow().initializationFinished()}))}catch(e){if(e instanceof Error&&/widgetReady is not a function/.test(e.message))throw new Error(`There was an error when loading the library. Usually this error means the library failed to load its static files. Check that the library files are available at ${window.location.host}/${this._options.library_path||""} or correct the library_path option.`)}}))}_render(e){const t=window;if(t[this._id]={datafeed:this._options.datafeed,customFormatters:this._options.custom_formatters,brokerFactory:this._options.broker_factory,overrides:this._options.overrides,studiesOverrides:this._options.studies_overrides,tradingCustomization:this._options.trading_customization,disabledFeatures:this._options.disabled_features,enabledFeatures:this._options.enabled_features,brokerConfig:this._options.broker_config||this._options.brokerConfig,restConfig:this._options.restConfig,favorites:this._options.favorites,logo:this._options.logo,numeric_formatting:this._options.numeric_formatting,rss_news_feed:this._options.rss_news_feed,rss_news_title:this._options.rss_news_title,newsProvider:this._options.news_provider,loadLastChart:this._options.load_last_chart,saveLoadAdapter:this._options.save_load_adapter,loading_screen:this._options.loading_screen,settingsAdapter:this._options.settings_adapter,getCustomIndicators:this._options.custom_indicators_getter,additionalSymbolInfoFields:this._options.additional_symbol_info_fields,headerWidgetButtonsMode:this._options.header_widget_buttons_mode,customTranslateFunction:this._options.custom_translate_function,symbolSearchComplete:this._options.symbol_search_complete,contextMenu:this._options.context_menu,settingsOverrides:this._options.settings_overrides,timeframe:this._options.timeframe,customTimezones:this._options.custom_timezones,customChartDescriptionFunction:this._options.custom_chart_description_function,customThemes:this._options.custom_themes},this._options.saved_data)t[this._id].chartContent={json:this._options.saved_data},this._options.saved_data_meta_info&&(t[this._id].chartContentExtendedData=this._options.saved_data_meta_info);else if(!this._options.load_last_chart&&!this._options.symbol)throw new Error("Symbol is not defined: either 'symbol' or 'load_last_chart' option must be set");if(this._options.library_path&&!this._options.library_path.endsWith("/")&&console.warn("library_path option should contain a trailing forward slash"),this._options.locale){const e=encodeURIComponent(this._options.locale);ve.findIndex((t=>t.language===e))>=0||(console.warn("locale isn't supported. Using default of `en`."),this._options.locale="en")}const i=function(e,t){var i;const o=new URL(`${e||""}`,location.href).href,r=JSON.parse('["bundles/runtime.4a59971d836709dfc991.js","bundles/__LANG__.4026.7e5fb703c7ef1394cbb4.js","bundles/9662.03109f673cda5962c847.css","bundles/7346.b02c4b5d2d08b5be4162.js","bundles/library.b717a95e17e4d6e9f5ca.js"]'),n=encodeURIComponent(t),a=null!==(i=ve.find((e=>e.language===n)))&&void 0!==i?i:{iso:"en",dir:"ltr"},s=`lang="${a.iso}" dir="${a.dir}"`,l=`\n${function(e,t,i){if(void 0===e)return"";const o=[],r=[];for(const n of e)n.endsWith(".js")?o.push(`<script defer crossorigin="anonymous" src="${n.replace("__LANG__",i)}"><\/script>`):n.endsWith(".css")&&r.push(`<link type="text/css" href="${t?n.replace(/\.css$/i,".rtl.css"):n}" rel="stylesheet"/>`);return[...o,...r].join("\n")}(r,"rtl"===a.dir,n)}\n`;return`<!DOCTYPE html><html ${(d={bundles:l,localeLanguage:n,htmlAttrs:s,libraryPath:o}).htmlAttrs}><head><base href="${d.libraryPath}"><meta charset="utf-8"><script>window===window.parent&&(location.href="about:blank")<\/script> ${d.bundles} </head><body class="chart-page unselectable on-widget"><div class="loading-indicator" id="loading-indicator"></div><script>var JSServer={},__initialEnabledFeaturesets=["charting_library"]<\/script><script>(function() {\n\t\twindow.urlParams = (function () {\n\t\t\tvar match,\n\t\t\t\tpl\t = /\\+/g,  // Regex for replacing addition symbol with a space\n\t\t\t\tsearch = /([^&=]+)=?([^&]*)/g,\n\t\t\t\tdecode = function (s) { return decodeURIComponent(s.replace(pl, ' ')).replace(/<\\/?[^>]+(>|$)/g, ''); },\n\t\t\t\tquery = function() {\n\t\t\t\t\t// We don't use hash on the url because: safari 13 throws an error if you attempt this\n\t\t\t\t\t// on a blob, and safari 14 will strip hash from blob urls.\n\t\t\t\t\tif (frameElement && frameElement.dataset.widgetOptions) {\n\t\t\t\t\t\treturn frameElement.dataset.widgetOptions;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow "Unexpected use of this page";\n\t\t\t\t\t}\n\t\t\t\t}(),\n\t\t\t\tresult = {};\n\n\t\t\twhile (match = search.exec(query)) {\n\t\t\t\tresult[decode(match[1])] = decode(match[2]);\n\t\t\t}\n\n\t\t\tvar additionalSettingsObject = window.parent[result.uid];\n\n\t\t\tvar customObjectNames = ['datafeed', 'customFormatters', 'brokerFactory', 'save_load_adapter', 'customTranslateFunction', 'contextMenu'];\n\n\t\t\tfor (var p in additionalSettingsObject) {\n\t\t\t\tif (customObjectNames.indexOf(p) === -1) {\n\t\t\t\t\tresult[p] = JSON.stringify(additionalSettingsObject[p]);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn result;\n\t\t})();\n\n\t\twindow.locale = urlParams.locale;\n\t\twindow.language = urlParams.locale; // a very big attention needed here\n\t\twindow.customTranslateFunction = window.parent[urlParams.uid].customTranslateFunction;\n\t\twindow.customChartDescriptionFunction = window.parent[urlParams.uid].customChartDescriptionFunction;\n\n\t\twindow.addCustomCSSFile = function(href) {\n\t\t\tvar link = document.createElement('link');\n\t\t\tlink.setAttribute('type', 'text/css');\n\t\t\tlink.setAttribute('rel', 'stylesheet');\n\t\t\tlink.setAttribute('href', href);\n\t\t\tlink.setAttribute('cross-origin', 'anonymous');\n\n\t\t\twindow.loadedCustomCss = new Promise((resolve) => {\n\t\t\t\tlink.onload = resolve;\n\t\t\t\tlink.onerror = resolve;\n\t\t\t});\n\t\t\tdocument.body.appendChild(link);\n\t\t};\n\n\t\twindow.loadedCustomCss = Promise.resolve();\n\t\tif (!!urlParams.customCSS) {\n\t\t\twindow.addCustomCSSFile(urlParams.customCSS);\n\t\t}\n\n\t\tvar loadingScreenParams = {};\n\n\t\tif (typeof urlParams.loading_screen === 'string') {\n\t\t\ttry {\n\t\t\t\tloadingScreenParams = JSON.parse(urlParams.loading_screen);\n\t\t\t} catch(e) {}\n\t\t}\n\n\t\tvar loadingIndicatorElement = document.getElementById('loading-indicator');\n\n\t\tif (loadingScreenParams.backgroundColor) {\n\t\t\tloadingIndicatorElement.style = 'background-color: ' + loadingScreenParams.backgroundColor;\n\t\t}\n\n\t\t!function(){"use strict";var t,e=new WeakMap;!function(t){t[t.Element=1]="Element",t[t.Document=9]="Document"}(t||(t={}));var n={mini:"xsmall",xxsmall:"xxsmall",xsmall:"xsmall",small:"small",medium:"medium",large:"large"};var s,i,o,r,l,c=(void 0===l&&(l=""),s='<div class="tv-spinner '.concat(l,'" role="progressbar"></div>'),o=function(n,s){var i,o;return i=null==s?document.documentElement:s.nodeType===t.Document?s.documentElement:s,e&&(o=e.get(i)),o||((o=i.ownerDocument.createRange()).selectNodeContents(i),e&&e.set(i,o)),o.createContextualFragment(n)}(s,i),null!==(r=o.firstElementChild)&&o.removeChild(r),r),a=function(){function t(t){this._shown=!1,this._el=c.cloneNode(!0),this.setSize(n[t||"large"])}return t.prototype.spin=function(t){return this._el.classList.add("tv-spinner--shown"),void 0===this._container&&(this._container=t,void 0!==t&&t.appendChild(this._el)),this._shown=!0,this},t.prototype.stop=function(t){return t&&void 0!==this._container&&this._container.removeChild(this._el),this._el&&this._el.classList.remove("tv-spinner--shown"),this._shown=!1,this},t.prototype.setStyle=function(t){var e=this;return Object.keys(t).forEach((function(n){var s=t[n];void 0!==s&&e._el.style.setProperty(n,s)})),this},t.prototype.style=function(){return this._el.style},t.prototype.setSize=function(t){var e=void 0!==t?"tv-spinner--size_".concat(t):"";return this._el.className="tv-spinner ".concat(e," ").concat(this._shown?"tv-spinner--shown":""),this},t.prototype.getEl=function(){return this._el},t.prototype.destroy=function(){this.stop(),delete this._el,delete this._container},t}();window.Spinner=a}();\n\n\n\t\tvar spinnerColor = (loadingScreenParams.foregroundColor) ? loadingScreenParams.foregroundColor : undefined;\n\n\t\tvar loadingSpinner = new Spinner('large').setStyle({\n\t\t\t'--tv-spinner-color': spinnerColor,\n\t\t\tzIndex: String(2e9),\n\t\t});\n\t\tloadingSpinner.getEl().classList.add('spinner');\n\t\tloadingSpinner.spin(loadingIndicatorElement);\n\t})();<\/script></body></html>`;var d}(this._options.library_path||"",this._options.locale);let o=new URL("about:blank");if(e){const e=new Blob([i],{type:"text/html"}),t=URL.createObjectURL(e);o=new URL(t)}const r="symbol="+encodeURIComponent(this._options.symbol||"")+"&interval="+encodeURIComponent(this._options.interval)+(this._options.toolbar_bg?"&toolbarbg="+encodeURIComponent(this._options.toolbar_bg.replace("#","")):"")+(this._options.studies_access?"&studiesAccess="+encodeURIComponent(JSON.stringify(this._options.studies_access)):"")+"&widgetbar="+encodeURIComponent(JSON.stringify(this._options.widgetbar))+(this._options.drawings_access?"&drawingsAccess="+encodeURIComponent(JSON.stringify(this._options.drawings_access)):"")+"&timeFrames="+encodeURIComponent(JSON.stringify(this._options.time_frames))+"&locale="+encodeURIComponent(this._options.locale)+"&uid="+encodeURIComponent(this._id)+"&clientId="+encodeURIComponent(String(this._options.client_id))+"&userId="+encodeURIComponent(String(this._options.user_id))+(this._options.charts_storage_url?"&chartsStorageUrl="+encodeURIComponent(this._options.charts_storage_url):"")+(this._options.charts_storage_api_version?"&chartsStorageVer="+encodeURIComponent(this._options.charts_storage_api_version):"")+(this._options.custom_css_url?"&customCSS="+encodeURIComponent(this._options.custom_css_url):"")+(this._options.custom_font_family?"&customFontFamily="+encodeURIComponent(this._options.custom_font_family):"")+(this._options.auto_save_delay?"&autoSaveDelay="+encodeURIComponent(String(this._options.auto_save_delay)):"")+"&debug="+encodeURIComponent(String(this._options.debug))+(this._options.debug_broker?"&debugBroker="+encodeURIComponent(String(this._options.debug_broker)):"")+(this._options.snapshot_url?"&snapshotUrl="+encodeURIComponent(this._options.snapshot_url):"")+(this._options.timezone?"&timezone="+encodeURIComponent(this._options.timezone):"")+(this._options.study_count_limit?"&studyCountLimit="+encodeURIComponent(String(this._options.study_count_limit)):"")+(this._options.symbol_search_request_delay?"&ssreqdelay="+encodeURIComponent(String(this._options.symbol_search_request_delay)):"")+(this._options.compare_symbols?"&compareSymbols="+encodeURIComponent(JSON.stringify(this._options.compare_symbols)):"")+(this._options.theme?"&theme="+encodeURIComponent(String(this._options.theme)):"")+(this._options.header_widget_buttons_mode?"&header_widget_buttons_mode="+encodeURIComponent(String(this._options.header_widget_buttons_mode)):"")+(this._options.time_scale?"&time_scale="+encodeURIComponent(JSON.stringify(this._options.time_scale)):"");return[`<iframe\n\t\tid="${this._id}" name="${this._id}" src="${o.href}" data-widget-options="${r}"\n\t\t${this._options.autosize||this._options.fullscreen?"":`width="${this._options.width}" height="${this._options.height}"`} title="Financial Chart" frameborder="0" allowTransparency="true" scrolling="no" allowfullscreen style="display:block;">\n\t</iframe>`,i]}};"undefined"!=typeof window&&(window.TradingView=window.TradingView||{},window.TradingView.version=Re);const Ve=!("undefined"==typeof window||!window.navigator||!window.navigator.userAgent)&&window.navigator.userAgent.includes("CriOS");exports.version=Re,exports.widget=De;
