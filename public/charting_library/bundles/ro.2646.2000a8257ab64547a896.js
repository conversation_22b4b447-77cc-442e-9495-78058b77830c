(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2646,4109,9093],{4543:o=>{o.exports={en:["Cancel"],ro:["Cancel"]}},47742:o=>{o.exports={en:["Close menu"],ro:["Close menu"]}},69207:o=>{o.exports={en:["Add to favorites"],ro:["Add to favorites"]}},30448:o=>{o.exports={en:["Do you really want to delete Chart Layout '{name}' ?"],ro:["Do you really want to delete Chart Layout '{name}' ?"]}},79573:o=>{o.exports={en:["Do you really want to delete Chart Layout '{name}' that contains {n_drawings_on_n_symbols}?"],ro:["Do you really want to delete Chart Layout '{name}' that contains {n_drawings_on_n_symbols}?"]}},55108:o=>{o.exports={en:["Date modified (oldest first)"],ro:["Date modified (oldest first)"]}},75272:o=>{o.exports={en:["Date modified (newest first)"],ro:["Date modified (newest first)"]}},79825:o=>{o.exports={en:["Load layout"],ro:["Load layout"]}},11478:o=>{o.exports={en:["Layout name"],ro:["Layout name"]}},21329:o=>{o.exports={en:["Layout name (A to Z)"],ro:["Layout name (A to Z)"]}},11324:o=>{o.exports={en:["Layout name (Z to A)"],ro:["Layout name (Z to A)"]}},5191:o=>{o.exports={en:["Sort by layout name, date changed"],ro:["Sort by layout name, date changed"]}},8573:o=>{o.exports={en:["Search"],ro:["Search"]}},85106:o=>{o.exports={en:["Remove from favorites"],ro:["Remove from favorites"]}},30502:o=>{o.exports={en:["on {amount} symbol","on {amount} symbols"],ro:["on {amount} symbol","on {amount} symbols"]}},22299:o=>{o.exports={en:["{amount} drawing","{amount} drawings"],ro:["{amount} drawing","{amount} drawings"]}}}]);