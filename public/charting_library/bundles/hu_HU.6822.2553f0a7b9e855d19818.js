(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6822],{94920:s=>{s.exports={en:["ADJ"],hu_HU:["adj"]}},16755:s=>{s.exports={en:["B-ADJ"],hu_HU:["B-ADJ"]}},82631:s=>{s.exports={en:["SET"],hu_HU:["SET"]}},22233:s=>{s.exports={en:["auto"],hu_HU:["auto"]}},4161:s=>{s.exports={en:["log"],hu_HU:["log"]}},58221:s=>{s.exports={en:["All data"],hu_HU:["All data"]}},19273:s=>{s.exports={en:["Year to day"],hu_HU:["Year to day"]}},58426:s=>{s.exports={en:["{timePeriod} in {timeInterval}"],hu_HU:["{timePeriod} in {timeInterval}"]}},93020:s=>{s.exports={en:["Adjust data for dividends"],hu_HU:["Adjust data for dividends"]}},68921:s=>{s.exports={en:["Adjust for contract changes"],hu_HU:["Adjust for contract changes"]}},42432:s=>{s.exports={en:["Go to"],hu_HU:["Ugrás ide:"]}},92966:s=>{s.exports={en:["Extended Hours is available only for intraday charts"],hu_HU:["Extended Hours is available only for intraday charts"]}},61206:s=>{s.exports={en:["Maximize chart"],hu_HU:["Maximize chart"]}},2031:s=>{s.exports={en:["Main symbol data is adjusted for dividends only"],hu_HU:["Main symbol data is adjusted for dividends only"]}},95739:s=>{s.exports={en:["Main symbol data is adjusted for splits only"],hu_HU:["Main symbol data is adjusted for splits only"]}},27665:s=>{s.exports={en:["Sessions"],hu_HU:["Sessions"]}},31142:s=>{s.exports={en:["Restore chart"],hu_HU:["Restore chart"]}},41888:s=>{s.exports={en:["Toggle Auto Scale"],hu_HU:["Váltás Automata Méretezés"]}},1e4:s=>{s.exports={en:["Toggle Log Scale"],hu_HU:["Váltás Log Skála"]}},81649:s=>{s.exports={en:["Toggle Percentage"],hu_HU:["Váltás Százalék"]}},77073:s=>{s.exports={en:["Timezone"],hu_HU:["Időzóna"]}},49545:s=>{s.exports={en:["Use settlement as close on daily interval"],hu_HU:["Use settlement as close on daily interval"]}},8586:s=>{s.exports={en:["ext"],hu_HU:["ext"]}},63808:s=>{s.exports={en:["{str} day","{str} days"],hu_HU:["{str} days"]}},62368:s=>{s.exports={en:["{str} day","{str} days"],hu_HU:["{str} days"]}},561:s=>{s.exports={en:["{str} day intervals","{str} days intervals"],hu_HU:["{str} days intervals"]}},72495:s=>{s.exports={en:["{str} hour","{str} hours"],hu_HU:["{str} hours"]}},64963:s=>{s.exports={en:["{str} hour","{str} hours"],hu_HU:["{str} hours"]}},14887:s=>{s.exports={en:["{str} hour intervals","{str} hours intervals"],hu_HU:["{str} hours intervals"]}},12752:s=>{s.exports={en:["{str} month","{str} months"],hu_HU:["{str} months"]}},20062:s=>{s.exports={en:["{str} month","{str} months"],hu_HU:["{str} months"]}},48514:s=>{s.exports={en:["{str} month intervals","{str} months intervals"],hu_HU:["{str} months intervals"]}},95484:s=>{s.exports={en:["{str} minute","{str} minutes"],hu_HU:["{str} minutes"]}},5926:s=>{s.exports={en:["{str} minute","{str} minutes"],hu_HU:["{str} minutes"]}},15489:s=>{s.exports={en:["{str} minute intervals","{str} minutes intervals"],hu_HU:["{str} minutes intervals"]}},6088:s=>{s.exports={en:["{str} week","{str} weeks"],hu_HU:["{str} weeks"]}},49306:s=>{s.exports={en:["{str} week","{str} weeks"],hu_HU:["{str} weeks"]}},
60316:s=>{s.exports={en:["{str} week intervals","{str} weeks intervals"],hu_HU:["{str} weeks intervals"]}},96325:s=>{s.exports={en:["{str} year","{str} years"],hu_HU:["{str} years"]}},91549:s=>{s.exports={en:["{str} year","{str} years"],hu_HU:["{str} years"]}},78971:s=>{s.exports={en:["{str} year intervals","{str} years intervals"],hu_HU:["{str} years intervals"]}}}]);