(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4026],{53310:e=>{e.exports={en:["Re"],ro:["Re"]}},94073:e=>{e.exports={en:["A"],ro:["A"]}},66384:e=>{e.exports={en:["L"],ro:["L"]}},85119:e=>{e.exports={en:["Dark"],ro:["Dark"]}},96870:e=>{e.exports={en:["Light"],ro:["Light"]}},85886:e=>{e.exports={en:["d"],ro:["d"]}},44634:e=>{e.exports={en:["h"],ro:["h"]}},5977:e=>{e.exports={en:["m"],ro:["m"]}},21492:e=>{e.exports={en:["s"],ro:["s"]}},97559:e=>{e.exports={en:["{title} copy"],ro:["{title} copy"]}},38691:e=>{e.exports={en:["D"],ro:["D"]}},77995:e=>{e.exports={en:["M"],ro:["M"]}},93934:e=>{e.exports={en:["R"],ro:["R"]}},82901:e=>{e.exports={en:["T"],ro:["T"]}},7408:e=>{e.exports={en:["W"],ro:["W"]}},38048:e=>{e.exports={en:["h"],ro:["h"]}},68430:e=>{e.exports={en:["m"],ro:["m"]}},68823:e=>{e.exports={en:["s"],ro:["s"]}},2696:e=>{e.exports={en:["C"],ro:["C"]}},43253:e=>{e.exports={en:["H"],ro:["H"]}},61372:e=>{e.exports={en:["HL2"],ro:["HL2"]}},55096:e=>{e.exports={en:["HLC3"],ro:["HLC3"]}},94174:e=>{e.exports={en:["OHLC4"],ro:["OHLC4"]}},89923:e=>{e.exports={en:["L"],ro:["L"]}},46728:e=>{e.exports={en:["O"],ro:["O"]}},32856:e=>{e.exports=Object.create(null),e.exports.Close_input={en:["Close"],ro:["Close"]},e.exports.Back_input={en:["Back"],ro:["Back"]},e.exports.Minimize_input={en:["Minimize"],ro:["Minimize"]},e.exports["Hull MA_input"]={en:["Hull MA"],ro:["Hull MA"]},e.exports.from_input={en:["from"],ro:["from"]},e.exports.to_input={en:["to"],ro:["to"]},e.exports["{number} item_combobox_input"]={en:["{number} item","{number} items"],ro:["{number} item","{number} items"]},e.exports.Length_input={en:["Length"],ro:["Length"]},e.exports.Plot_input={en:["Plot"],ro:["Plot"]},e.exports.Zero_input={en:["Zero"],ro:["Zero"]},e.exports.Signal_input={en:["Signal"],ro:["Signal"]},e.exports.Long_input={en:["Long"],ro:["Long"]},e.exports.Short_input={en:["Short"],ro:["Short"]},e.exports.UpperLimit_input={en:["UpperLimit"],ro:["UpperLimit"]},e.exports.LowerLimit_input={en:["LowerLimit"],ro:["LowerLimit"]},e.exports.Offset_input={en:["Offset"],ro:["Offset"]},e.exports.length_input={en:["length"],ro:["length"]},e.exports.mult_input={en:["mult"],ro:["mult"]},e.exports.short_input={en:["short"],ro:["short"]},e.exports.long_input={en:["long"],ro:["long"]},e.exports.Limit_input={en:["Limit"],ro:["Limit"]},e.exports.Move_input={en:["Move"],ro:["Move"]},e.exports.Value_input={en:["Value"],ro:["Value"]},e.exports.Method_input={en:["Method"],ro:["Method"]},e.exports["Values in status line_input"]={en:["Values in status line"],ro:["Values in status line"]},e.exports["Labels on price scale_input"]={en:["Labels on price scale"],ro:["Labels on price scale"]},e.exports["Accumulation/Distribution_input"]={en:["Accumulation/Distribution"],ro:["Accumulation/Distribution"]},e.exports.ADR_B_input={en:["ADR_B"],ro:["ADR_B"]},e.exports["Equality Line_input"]={en:["Equality Line"],ro:["Equality Line"]},e.exports["Window Size_input"]={en:["Window Size"],ro:["Window Size"]},e.exports.Sigma_input={en:["Sigma"],ro:["Sigma"]},
e.exports["Aroon Up_input"]={en:["Aroon Up"],ro:["Aroon Up"]},e.exports["Aroon Down_input"]={en:["Aroon Down"],ro:["Aroon Down"]},e.exports.Upper_input={en:["Upper"],ro:["Upper"]},e.exports.Lower_input={en:["Lower"],ro:["Lower"]},e.exports.Deviation_input={en:["Deviation"],ro:["Deviation"]},e.exports["Levels Format_input"]={en:["Levels Format"],ro:["Levels Format"]},e.exports["Labels Position_input"]={en:["Labels Position"],ro:["Labels Position"]},e.exports["0 Level Color_input"]={en:["0 Level Color"],ro:["0 Level Color"]},e.exports["0.236 Level Color_input"]={en:["0.236 Level Color"],ro:["0.236 Level Color"]},e.exports["0.382 Level Color_input"]={en:["0.382 Level Color"],ro:["0.382 Level Color"]},e.exports["0.5 Level Color_input"]={en:["0.5 Level Color"],ro:["0.5 Level Color"]},e.exports["0.618 Level Color_input"]={en:["0.618 Level Color"],ro:["0.618 Level Color"]},e.exports["0.65 Level Color_input"]={en:["0.65 Level Color"],ro:["0.65 Level Color"]},e.exports["0.786 Level Color_input"]={en:["0.786 Level Color"],ro:["0.786 Level Color"]},e.exports["1 Level Color_input"]={en:["1 Level Color"],ro:["1 Level Color"]},e.exports["1.272 Level Color_input"]={en:["1.272 Level Color"],ro:["1.272 Level Color"]},e.exports["1.414 Level Color_input"]={en:["1.414 Level Color"],ro:["1.414 Level Color"]},e.exports["1.618 Level Color_input"]={en:["1.618 Level Color"],ro:["1.618 Level Color"]},e.exports["1.65 Level Color_input"]={en:["1.65 Level Color"],ro:["1.65 Level Color"]},e.exports["2.618 Level Color_input"]={en:["2.618 Level Color"],ro:["2.618 Level Color"]},e.exports["2.65 Level Color_input"]={en:["2.65 Level Color"],ro:["2.65 Level Color"]},e.exports["3.618 Level Color_input"]={en:["3.618 Level Color"],ro:["3.618 Level Color"]},e.exports["3.65 Level Color_input"]={en:["3.65 Level Color"],ro:["3.65 Level Color"]},e.exports["4.236 Level Color_input"]={en:["4.236 Level Color"],ro:["4.236 Level Color"]},e.exports["-0.236 Level Color_input"]={en:["-0.236 Level Color"],ro:["-0.236 Level Color"]},e.exports["-0.382 Level Color_input"]={en:["-0.382 Level Color"],ro:["-0.382 Level Color"]},e.exports["-0.618 Level Color_input"]={en:["-0.618 Level Color"],ro:["-0.618 Level Color"]},e.exports["-0.65 Level Color_input"]={en:["-0.65 Level Color"],ro:["-0.65 Level Color"]},e.exports.ADX_input={en:["ADX"],ro:["ADX"]},e.exports["ADX Smoothing_input"]={en:["ADX Smoothing"],ro:["ADX Smoothing"]},e.exports["DI Length_input"]={en:["DI Length"],ro:["DI Length"]},e.exports.Smoothing_input={en:["Smoothing"],ro:["Smoothing"]},e.exports.ATR_input={en:["ATR"],ro:["ATR"]},e.exports.Growing_input={en:["Growing"],ro:["Growing"]},e.exports.Falling_input={en:["Falling"],ro:["Falling"]},e.exports["Color 0_input"]={en:["Color 0"],ro:["Color 0"]},e.exports["Color 1_input"]={en:["Color 1"],ro:["Color 1"]},e.exports.Source_input={en:["Source"],ro:["Source"]},e.exports.StdDev_input={en:["StdDev"],ro:["StdDev"]},e.exports.Basis_input={en:["Basis"],ro:["Basis"]},e.exports.Median_input={en:["Median"],ro:["Median"]},e.exports["Bollinger Bands %B_input"]={
en:["Bollinger Bands %B"],ro:["Bollinger Bands %B"]},e.exports.Overbought_input={en:["Overbought"],ro:["Overbought"]},e.exports.Oversold_input={en:["Oversold"],ro:["Oversold"]},e.exports["Bollinger Bands Width_input"]={en:["Bollinger Bands Width"],ro:["Bollinger Bands Width"]},e.exports["RSI Length_input"]={en:["RSI Length"],ro:["RSI Length"]},e.exports["UpDown Length_input"]={en:["UpDown Length"],ro:["UpDown Length"]},e.exports["ROC Length_input"]={en:["ROC Length"],ro:["ROC Length"]},e.exports.MF_input={en:["MF"],ro:["MF"]},e.exports.resolution_input={en:["resolution"],ro:["resolution"]},e.exports["Fast Length_input"]={en:["Fast Length"],ro:["Fast Length"]},e.exports["Slow Length_input"]={en:["Slow Length"],ro:["Slow Length"]},e.exports["Chaikin Oscillator_input"]={en:["Chaikin Oscillator"],ro:["Chaikin Oscillator"]},e.exports.P_input={en:["P"],ro:["P"]},e.exports.X_input={en:["X"],ro:["X"]},e.exports.Q_input={en:["Q"],ro:["Q"]},e.exports.p_input={en:["p"],ro:["p"]},e.exports.x_input={en:["x"],ro:["x"]},e.exports.q_input={en:["q"],ro:["q"]},e.exports.Price_input={en:["Price"],ro:["Price"]},e.exports["Chande MO_input"]={en:["Chande MO"],ro:["Chande MO"]},e.exports["Zero Line_input"]={en:["Zero Line"],ro:["Zero Line"]},e.exports["Color 2_input"]={en:["Color 2"],ro:["Color 2"]},e.exports["Color 3_input"]={en:["Color 3"],ro:["Color 3"]},e.exports["Color 4_input"]={en:["Color 4"],ro:["Color 4"]},e.exports["Color 5_input"]={en:["Color 5"],ro:["Color 5"]},e.exports["Color 6_input"]={en:["Color 6"],ro:["Color 6"]},e.exports["Color 7_input"]={en:["Color 7"],ro:["Color 7"]},e.exports["Color 8_input"]={en:["Color 8"],ro:["Color 8"]},e.exports.CHOP_input={en:["CHOP"],ro:["CHOP"]},e.exports["Upper Band_input"]={en:["Upper Band"],ro:["Upper Band"]},e.exports["Lower Band_input"]={en:["Lower Band"],ro:["Lower Band"]},e.exports.CCI_input={en:["CCI"],ro:["CCI"]},e.exports["Smoothing Line_input"]={en:["Smoothing Line"],ro:["Smoothing Line"]},e.exports["Smoothing Length_input"]={en:["Smoothing Length"],ro:["Smoothing Length"]},e.exports["WMA Length_input"]={en:["WMA Length"],ro:["WMA Length"]},e.exports["Long RoC Length_input"]={en:["Long RoC Length"],ro:["Long RoC Length"]},e.exports["Short RoC Length_input"]={en:["Short RoC Length"],ro:["Short RoC Length"]},e.exports.sym_input={en:["sym"],ro:["sym"]},e.exports.Symbol_input={en:["Symbol"],ro:["Symbol"]},e.exports.Correlation_input={en:["Correlation"],ro:["Correlation"]},e.exports.Period_input={en:["Period"],ro:["Period"]},e.exports.Centered_input={en:["Centered"],ro:["Centered"]},e.exports["Detrended Price Oscillator_input"]={en:["Detrended Price Oscillator"],ro:["Detrended Price Oscillator"]},e.exports.isCentered_input={en:["isCentered"],ro:["isCentered"]},e.exports.DPO_input={en:["DPO"],ro:["DPO"]},e.exports["ADX smoothing_input"]={en:["ADX smoothing"],ro:["ADX smoothing"]},e.exports["+DI_input"]={en:["+DI"],ro:["+DI"]},e.exports["-DI_input"]={en:["-DI"],ro:["-DI"]},e.exports.DEMA_input={en:["DEMA"],ro:["DEMA"]},e.exports["Multi timeframe_input"]={en:["Multi timeframe"],
ro:["Multi timeframe"]},e.exports.Timeframe_input={en:["Timeframe"],ro:["Timeframe"]},e.exports["Wait for timeframe closes_input"]={en:["Wait for timeframe closes"],ro:["Wait for timeframe closes"]},e.exports.Divisor_input={en:["Divisor"],ro:["Divisor"]},e.exports.EOM_input={en:["EOM"],ro:["EOM"]},e.exports["Elder's Force Index_input"]={en:["Elder's Force Index"],ro:["Elder's Force Index"]},e.exports.Percent_input={en:["Percent"],ro:["Percent"]},e.exports.Exponential_input={en:["Exponential"],ro:["Exponential"]},e.exports.Average_input={en:["Average"],ro:["Average"]},e.exports["Upper Percentage_input"]={en:["Upper Percentage"],ro:["Upper Percentage"]},e.exports["Lower Percentage_input"]={en:["Lower Percentage"],ro:["Lower Percentage"]},e.exports.Fisher_input={en:["Fisher"],ro:["Fisher"]},e.exports.Trigger_input={en:["Trigger"],ro:["Trigger"]},e.exports.Level_input={en:["Level"],ro:["Level"]},e.exports["Trader EMA 1 length_input"]={en:["Trader EMA 1 length"],ro:["Trader EMA 1 length"]},e.exports["Trader EMA 2 length_input"]={en:["Trader EMA 2 length"],ro:["Trader EMA 2 length"]},e.exports["Trader EMA 3 length_input"]={en:["Trader EMA 3 length"],ro:["Trader EMA 3 length"]},e.exports["Trader EMA 4 length_input"]={en:["Trader EMA 4 length"],ro:["Trader EMA 4 length"]},e.exports["Trader EMA 5 length_input"]={en:["Trader EMA 5 length"],ro:["Trader EMA 5 length"]},e.exports["Trader EMA 6 length_input"]={en:["Trader EMA 6 length"],ro:["Trader EMA 6 length"]},e.exports["Investor EMA 1 length_input"]={en:["Investor EMA 1 length"],ro:["Investor EMA 1 length"]},e.exports["Investor EMA 2 length_input"]={en:["Investor EMA 2 length"],ro:["Investor EMA 2 length"]},e.exports["Investor EMA 3 length_input"]={en:["Investor EMA 3 length"],ro:["Investor EMA 3 length"]},e.exports["Investor EMA 4 length_input"]={en:["Investor EMA 4 length"],ro:["Investor EMA 4 length"]},e.exports["Investor EMA 5 length_input"]={en:["Investor EMA 5 length"],ro:["Investor EMA 5 length"]},e.exports["Investor EMA 6 length_input"]={en:["Investor EMA 6 length"],ro:["Investor EMA 6 length"]},e.exports.HV_input={en:["HV"],ro:["HV"]},e.exports["Conversion Line Periods_input"]={en:["Conversion Line Periods"],ro:["Conversion Line Periods"]},e.exports["Base Line Periods_input"]={en:["Base Line Periods"],ro:["Base Line Periods"]},e.exports["Lagging Span_input"]={en:["Lagging Span"],ro:["Lagging Span"]},e.exports["Conversion Line_input"]={en:["Conversion Line"],ro:["Conversion Line"]},e.exports["Base Line_input"]={en:["Base Line"],ro:["Base Line"]},e.exports["Leading Span A_input"]={en:["Leading Span A"],ro:["Lead 1"]},e.exports["Leading Span B_input"]={},e.exports["Plots Background_input"]={en:["Plots Background"],ro:["Plots Background"]},e.exports["yay Color 0_input"]={en:["yay Color 0"],ro:["yay Color 0"]},e.exports["yay Color 1_input"]={en:["yay Color 1"],ro:["yay Color 1"]},e.exports.Multiplier_input={en:["Multiplier"],ro:["Multiplier"]},e.exports["Bands style_input"]={en:["Bands style"],ro:["Bands style"]},e.exports.Middle_input={en:["Middle"],ro:["Middle"]},
e.exports.useTrueRange_input={en:["useTrueRange"],ro:["useTrueRange"]},e.exports.ROCLen1_input={en:["ROCLen1"],ro:["ROCLen1"]},e.exports.ROCLen2_input={en:["ROCLen2"],ro:["ROCLen2"]},e.exports.ROCLen3_input={en:["ROCLen3"],ro:["ROCLen3"]},e.exports.ROCLen4_input={en:["ROCLen4"],ro:["ROCLen4"]},e.exports.SMALen1_input={en:["SMALen1"],ro:["SMALen1"]},e.exports.SMALen2_input={en:["SMALen2"],ro:["SMALen2"]},e.exports.SMALen3_input={en:["SMALen3"],ro:["SMALen3"]},e.exports.SMALen4_input={en:["SMALen4"],ro:["SMALen4"]},e.exports.SigLen_input={en:["SigLen"],ro:["SigLen"]},e.exports.KST_input={en:["KST"],ro:["KST"]},e.exports.Sig_input={en:["Sig"],ro:["Sig"]},e.exports.roclen1_input={en:["roclen1"],ro:["roclen1"]},e.exports.roclen2_input={en:["roclen2"],ro:["roclen2"]},e.exports.roclen3_input={en:["roclen3"],ro:["roclen3"]},e.exports.roclen4_input={en:["roclen4"],ro:["roclen4"]},e.exports.smalen1_input={en:["smalen1"],ro:["smalen1"]},e.exports.smalen2_input={en:["smalen2"],ro:["smalen2"]},e.exports.smalen3_input={en:["smalen3"],ro:["smalen3"]},e.exports.smalen4_input={en:["smalen4"],ro:["smalen4"]},e.exports.siglen_input={en:["siglen"],ro:["siglen"]},e.exports["Upper Deviation_input"]={en:["Upper Deviation"],ro:["Upper Deviation"]},e.exports["Lower Deviation_input"]={en:["Lower Deviation"],ro:["Lower Deviation"]},e.exports["Use Upper Deviation_input"]={en:["Use Upper Deviation"],ro:["Use Upper Deviation"]},e.exports["Use Lower Deviation_input"]={en:["Use Lower Deviation"],ro:["Use Lower Deviation"]},e.exports.Count_input={en:["Count"],ro:["Count"]},e.exports.Crosses_input={en:["Crosses"],ro:["Crosses"]},e.exports.MOM_input={en:["MOM"],ro:["MOM"]},e.exports.MA_input={en:["MA"],ro:["MA"]},e.exports["Length EMA_input"]={en:["Length EMA"],ro:["Length EMA"]},e.exports["Length MA_input"]={en:["Length MA"],ro:["Length MA"]},e.exports["Fast length_input"]={en:["Fast length"],ro:["Fast length"]},e.exports["Slow length_input"]={en:["Slow length"],ro:["Slow length"]},e.exports["Signal smoothing_input"]={en:["Signal smoothing"],ro:["Signal smoothing"]},e.exports["Simple ma(oscillator)_input"]={en:["Simple ma(oscillator)"],ro:["Simple ma(oscillator)"]},e.exports["Simple ma(signal line)_input"]={en:["Simple ma(signal line)"],ro:["Simple ma(signal line)"]},e.exports.Histogram_input={en:["Histogram"],ro:["Histogram"]},e.exports.MACD_input={en:["MACD"],ro:["MACD"]},e.exports.fastLength_input={en:["fastLength"],ro:["fastLength"]},e.exports.slowLength_input={en:["slowLength"],ro:["slowLength"]},e.exports.signalLength_input={en:["signalLength"],ro:["signalLength"]},e.exports.NV_input={en:["NV"],ro:["NV"]},e.exports.OnBalanceVolume_input={en:["OnBalanceVolume"],ro:["OnBalanceVolume"]},e.exports.Start_input={en:["Start"],ro:["Start"]},e.exports.Increment_input={en:["Increment"],ro:["Increment"]},e.exports["Max value_input"]={en:["Max value"],ro:["Max value"]},e.exports.ParabolicSAR_input={en:["ParabolicSAR"],ro:["ParabolicSAR"]},e.exports.start_input={en:["start"],ro:["start"]},e.exports.increment_input={en:["increment"],ro:["increment"]},
e.exports.maximum_input={en:["maximum"],ro:["maximum"]},e.exports["Short length_input"]={en:["Short length"],ro:["Short length"]},e.exports["Long length_input"]={en:["Long length"],ro:["Long length"]},e.exports.OSC_input={en:["OSC"],ro:["OSC"]},e.exports.shortlen_input={en:["shortlen"],ro:["shortlen"]},e.exports.longlen_input={en:["longlen"],ro:["longlen"]},e.exports.PVT_input={en:["PVT"],ro:["PVT"]},e.exports.ROC_input={en:["ROC"],ro:["ROC"]},e.exports.RSI_input={en:["RSI"],ro:["RSI"]},e.exports.RVGI_input={en:["RVGI"],ro:["RVGI"]},e.exports.RVI_input={en:["RVI"],ro:["RVI"]},e.exports["Long period_input"]={en:["Long period"],ro:["Long period"]},e.exports["Short period_input"]={en:["Short period"],ro:["Short period"]},e.exports["Signal line period_input"]={en:["Signal line period"],ro:["Signal line period"]},e.exports.SMI_input={en:["SMI"],ro:["SMI"]},e.exports["SMI Ergodic Oscillator_input"]={en:["SMI Ergodic Oscillator"],ro:["SMI Ergodic Oscillator"]},e.exports.Indicator_input={en:["Indicator"],ro:["Indicator"]},e.exports.Oscillator_input={en:["Oscillator"],ro:["Oscillator"]},e.exports.K_input={en:["K"],ro:["K"]},e.exports.D_input={en:["D"],ro:["D"]},e.exports.smoothK_input={en:["smoothK"],ro:["smoothK"]},e.exports.smoothD_input={en:["smoothD"],ro:["smoothD"]},e.exports["%K_input"]={en:["%K"],ro:["%K"]},e.exports["%D_input"]={en:["%D"],ro:["%D"]},e.exports["Stochastic Length_input"]={en:["Stochastic Length"],ro:["Stochastic Length"]},e.exports["RSI Source_input"]={en:["RSI Source"],ro:["RSI Source"]},e.exports.lengthRSI_input={en:["lengthRSI"],ro:["lengthRSI"]},e.exports.lengthStoch_input={en:["lengthStoch"],ro:["lengthStoch"]},e.exports.TRIX_input={en:["TRIX"],ro:["TRIX"]},e.exports.TEMA_input={en:["TEMA"],ro:["TEMA"]},e.exports["Long Length_input"]={en:["Long Length"],ro:["Long Length"]},e.exports["Short Length_input"]={en:["Short Length"],ro:["Short Length"]},e.exports["Signal Length_input"]={en:["Signal Length"],ro:["Signal Length"]},e.exports.Length1_input={en:["Length1"],ro:["Length1"]},e.exports.Length2_input={en:["Length2"],ro:["Length2"]},e.exports.Length3_input={en:["Length3"],ro:["Length3"]},e.exports.length7_input={en:["length7"],ro:["length7"]},e.exports.length14_input={en:["length14"],ro:["length14"]},e.exports.length28_input={en:["length28"],ro:["length28"]},e.exports.UO_input={en:["UO"],ro:["UO"]},e.exports.VWMA_input={en:["VWMA"],ro:["VWMA"]},e.exports.len_input={en:["len"],ro:["len"]},e.exports["VI +_input"]={en:["VI +"],ro:["VI +"]},e.exports["VI -_input"]={en:["VI -"],ro:["VI -"]},e.exports["%R_input"]={en:["%R"],ro:["%R"]},e.exports["Jaw Length_input"]={en:["Jaw Length"],ro:["Jaw Length"]},e.exports["Teeth Length_input"]={en:["Teeth Length"],ro:["Teeth Length"]},e.exports["Lips Length_input"]={en:["Lips Length"],ro:["Lips Length"]},e.exports.Jaw_input={en:["Jaw"],ro:["Jaw"]},e.exports.Teeth_input={en:["Teeth"],ro:["Teeth"]},e.exports.Lips_input={en:["Lips"],ro:["Lips"]},e.exports["Jaw Offset_input"]={en:["Jaw Offset"],ro:["Jaw Offset"]},e.exports["Teeth Offset_input"]={en:["Teeth Offset"],
ro:["Teeth Offset"]},e.exports["Lips Offset_input"]={en:["Lips Offset"],ro:["Lips Offset"]},e.exports["Down fractals_input"]={en:["Down fractals"],ro:["Down fractals"]},e.exports["Up fractals_input"]={en:["Up fractals"],ro:["Up fractals"]},e.exports.Periods_input={en:["Periods"],ro:["Periods"]},e.exports.Shapes_input={en:["Shapes"],ro:["Shapes"]},e.exports["show MA_input"]={en:["show MA"],ro:["show MA"]},e.exports["MA Length_input"]={en:["MA Length"],ro:["MA Length"]},e.exports["Color based on previous close_input"]={en:["Color based on previous close"],ro:["Color based on previous close"]},e.exports["Rows Layout_input"]={en:["Rows Layout"],ro:["Rows Layout"]},e.exports["Row Size_input"]={en:["Row Size"],ro:["Row Size"]},e.exports.Volume_input={en:["Volume"],ro:["Volume"]},e.exports["Value Area volume_input"]={en:["Value Area volume"],ro:["Value Area volume"]},e.exports["Extend Right_input"]={en:["Extend Right"],ro:["Extend Right"]},e.exports["Extend POC Right_input"]={en:["Extend POC Right"],ro:["Extend POC Right"]},e.exports["Extend VAH Right_input"]={en:["Extend VAH Right"],ro:["Extend VAH Right"]},e.exports["Extend VAL Right_input"]={en:["Extend VAL Right"],ro:["Extend VAL Right"]},e.exports["Value Area Volume_input"]={en:["Value Area Volume"],ro:["Value Area Volume"]},e.exports.Placement_input={en:["Placement"],ro:["Placement"]},e.exports.POC_input={en:["POC"],ro:["POC"]},e.exports["Developing Poc_input"]={en:["Developing Poc"],ro:["Developing Poc"]},e.exports["Up Volume_input"]={en:["Up Volume"],ro:["Up Volume"]},e.exports["Down Volume_input"]={en:["Down Volume"],ro:["Down Volume"]},e.exports["Value Area_input"]={en:["Value Area"],ro:["Value Area"]},e.exports["Histogram Box_input"]={en:["Histogram Box"],ro:["Histogram Box"]},e.exports["Value Area Up_input"]={en:["Value Area Up"],ro:["Value Area Up"]},e.exports["Value Area Down_input"]={en:["Value Area Down"],ro:["Value Area Down"]},e.exports["Number Of Rows_input"]={en:["Number Of Rows"],ro:["Number Of Rows"]},e.exports["Ticks Per Row_input"]={en:["Ticks Per Row"],ro:["Ticks Per Row"]},e.exports["Up/Down_input"]={en:["Up/Down"],ro:["Up/Down"]},e.exports.Total_input={en:["Total"],ro:["Total"]},e.exports.Delta_input={en:["Delta"],ro:["Delta"]},e.exports.Bar_input={en:["Bar"],ro:["Bar"]},e.exports.Day_input={en:["Day"],ro:["Day"]},e.exports["Deviation (%)_input"]={en:["Deviation (%)"],ro:["Deviation (%)"]},e.exports.Depth_input={en:["Depth"],ro:["Depth"]},e.exports["Extend to last bar_input"]={en:["Extend to last bar"],ro:["Extend to last bar"]},e.exports.Simple_input={en:["Simple"],ro:["Simple"]},e.exports.Weighted_input={en:["Weighted"],ro:["Weighted"]},e.exports["Wilder's Smoothing_input"]={en:["Wilder's Smoothing"],ro:["Wilder's Smoothing"]},e.exports["1st Period_input"]={en:["1st Period"],ro:["1st Period"]},e.exports["2nd Period_input"]={en:["2nd Period"],ro:["2nd Period"]},e.exports["3rd Period_input"]={en:["3rd Period"],ro:["3rd Period"]},e.exports["4th Period_input"]={en:["4th Period"],ro:["4th Period"]},e.exports["5th Period_input"]={en:["5th Period"],
ro:["5th Period"]},e.exports["6th Period_input"]={en:["6th Period"],ro:["6th Period"]},e.exports["Rate of Change Lookback_input"]={en:["Rate of Change Lookback"],ro:["Rate of Change Lookback"]},e.exports["Instrument 1_input"]={en:["Instrument 1"],ro:["Instrument 1"]},e.exports["Instrument 2_input"]={en:["Instrument 2"],ro:["Instrument 2"]},e.exports["Rolling Period_input"]={en:["Rolling Period"],ro:["Rolling Period"]},e.exports["Standard Errors_input"]={en:["Standard Errors"],ro:["Standard Errors"]},e.exports["Averaging Periods_input"]={en:["Averaging Periods"],ro:["Averaging Periods"]},e.exports["Days Per Year_input"]={en:["Days Per Year"],ro:["Days Per Year"]},e.exports["Market Closed Percentage_input"]={en:["Market Closed Percentage"],ro:["Market Closed Percentage"]},e.exports["ATR Mult_input"]={en:["ATR Mult"],ro:["ATR Mult"]},e.exports.VWAP_input={en:["VWAP"],ro:["VWAP"]},e.exports["Anchor Period_input"]={en:["Anchor Period"],ro:["Anchor Period"]},e.exports.Session_input={en:["Session"],ro:["Session"]},e.exports.Week_input={en:["Week"],ro:["Week"]},e.exports.Month_input={en:["Month"],ro:["Month"]},e.exports.Year_input={en:["Year"],ro:["Year"]},e.exports.Decade_input={en:["Decade"],ro:["Decade"]},e.exports.Century_input={en:["Century"],ro:["Century"]},e.exports.Sessions_input={en:["Sessions"],ro:["Sessions"]},e.exports["Each (pre-market, market, post-market)_input"]={en:["Each (pre-market, market, post-market)"],ro:["Each (pre-market, market, post-market)"]},e.exports["Pre-market only_input"]={en:["Pre-market only"],ro:["Pre-market only"]},e.exports["Market only_input"]={en:["Market only"],ro:["Market only"]},e.exports["Post-market only_input"]={en:["Post-market only"],ro:["Post-market only"]},e.exports["Main chart symbol_input"]={en:["Main chart symbol"],ro:["Main chart symbol"]},e.exports["Another symbol_input"]={en:["Another symbol"],ro:["Another symbol"]},e.exports.Line_input={en:["Line"],ro:["Line"]},e.exports["Nothing selected_combobox_input"]={en:["Nothing selected"],ro:["Nothing selected"]},e.exports["All items_combobox_input"]={en:["All items"],ro:["All items"]},e.exports.Cancel_input={en:["Cancel"],ro:["Cancel"]},e.exports.Open_input={en:["Open"],ro:["Open"]},e.exports.MM_month_input={en:["MM"],ro:["MM"]},e.exports.YY_year_input={en:["YY"],ro:["YY"]},e.exports.Style_input={en:["Style"],ro:["Style"]},e.exports["Box size assignment method_input"]={en:["Box size assignment method"],ro:["Box size assignment method"]},e.exports["Color bars based on previous close_input"]={en:["Color bars based on previous close"],ro:["Color bars based on previous close"]},e.exports.Candles_input={en:["Candles"],ro:["Candles"]},e.exports.Borders_input={en:["Borders"],ro:["Borders"]},e.exports.Wick_input={en:["Wick"],ro:["Wick"]},e.exports["HLC bars_input"]={en:["HLC bars"],ro:["HLC bars"]},e.exports["Price source_input"]={en:["Price source"],ro:["Price source"]},e.exports.Type_input={en:["Type"],ro:["Type"]},e.exports["Show real prices on price scale (instead of Heikin-Ashi price)_input"]={
en:["Show real prices on price scale (instead of Heikin-Ashi price)"],ro:["Show real prices on price scale (instead of Heikin-Ashi price)"]},e.exports["Up bars_input"]={en:["Up bars"],ro:["Up bars"]},e.exports["Down bars_input"]={en:["Down bars"],ro:["Down bars"]},e.exports["Projection up bars_input"]={en:["Projection up bars"],ro:["Projection up bars"]},e.exports["Projection down bars_input"]={en:["Projection down bars"],ro:["Projection down bars"]},e.exports["Projection up color_input"]={en:["Projection up color"],ro:["Projection up color"]},e.exports["Projection down color_input"]={en:["Projection down color"],ro:["Projection down color"]},e.exports.Fill_input={en:["Fill"],ro:["Fill"]},e.exports["Up color_input"]={en:["Up color"],ro:["Up color"]},e.exports["Down color_input"]={en:["Down color"],ro:["Down color"]},e.exports.Traditional_input={en:["Traditional"],ro:["Traditional"]},e.exports.PercentageLTP_input={en:["PercentageLTP"],ro:["PercentageLTP"]},e.exports["Box size_input"]={en:["Box size"],ro:["Box size"]},e.exports["Number of line_input"]={en:["Number of line"],ro:["Number of line"]},e.exports["ATR length_input"]={en:["ATR length"],ro:["ATR length"]},e.exports.Percentage_input={en:["Percentage"],ro:["Percentage"]},e.exports["Reversal amount_input"]={en:["Reversal amount"],ro:["Reversal amount"]},e.exports["Phantom bars_input"]={en:["Phantom bars"],ro:["Phantom bars"]},e.exports["One step back building_input"]={en:["One step back building"],ro:["One step back building"]},e.exports.Wicks_input={en:["Wicks"],ro:["Wicks"]},e.exports.Range_input={en:["Range"],ro:["Range"]},e.exports.All_input={en:["All"],ro:["All"]},e.exports.Custom_input={en:["Custom"],ro:["Custom"]},e.exports["Leading Span Periods_input"]={en:["Leading Span Periods"],ro:["Lagging Span 2 Periods"]},e.exports["Leading Shift Periods_input"]={en:["Leading Shift Periods"],ro:["Leading Shift Periods"]},e.exports["ATR({atrValue})_input"]={en:["ATR({atrValue})"],ro:["ATR({atrValue})"]},e.exports["PercentageLTP({percentageLTPValue}%)_input"]={en:["PercentageLTP({percentageLTPValue}%)"],ro:["PercentageLTP({percentageLTPValue}%)"]}},50873:e=>{e.exports={en:["ATR({atrValue})"],ro:["ATR({atrValue})"]}},28037:e=>{e.exports={en:["PercentageLTP({percentageLTPValue}%)"],ro:["PercentageLTP({percentageLTPValue}%)"]}},40566:e=>{e.exports={en:["Traditional"],ro:["Traditional"]}},75163:e=>{e.exports={en:["Invert scale"],ro:["Invert Scale"]}},35210:e=>{e.exports={en:["Indexed to 100"],ro:["Indexed to 100"]}},31340:e=>{e.exports={en:["Logarithmic"],ro:["Logarithmic"]}},19405:e=>{e.exports={en:["No overlapping labels"],ro:["No Overlapping Labels"]}},34954:e=>{e.exports={en:["Percent"],ro:["Percent"]}},55300:e=>{e.exports={en:["Regular"],ro:["Regular"]}},8029:e=>{e.exports={en:["ETH"],ro:["ETH"]}},34647:e=>{e.exports={en:["Electronic trading hours"],ro:["Electronic Trading Hours"]}},36862:e=>{e.exports={en:["Extended trading hours"],ro:["Extended trading hours"]}},7807:e=>{e.exports={en:["POST"],ro:["post"]}},46273:e=>{e.exports={en:["PRE"],ro:["pre"]}},50434:e=>{
e.exports={en:["Postmarket"],ro:["Postmarket"]}},59330:e=>{e.exports={en:["Premarket"],ro:["Premarket"]}},35342:e=>{e.exports={en:["RTH"],ro:["RTH"]}},84246:e=>{e.exports={en:["Regular trading hours"],ro:["Regular Trading Hours"]}},13132:e=>{e.exports={en:["May"],ro:["May"]}},83477:e=>{e.exports=Object.create(null),e.exports.Technicals_study={en:["Technicals"],ro:["Technicals"]},e.exports["Average Day Range_study"]={en:["Average Day Range"],ro:["Average Day Range"]},e.exports["Bull Bear Power_study"]={en:["Bull Bear Power"],ro:["Bull Bear Power"]},e.exports["Capital expenditures_study"]={en:["Capital expenditures"],ro:["Capital expenditures"]},e.exports["Cash to debt ratio_study"]={en:["Cash to debt ratio"],ro:["Cash to debt ratio"]},e.exports["Debt to EBITDA ratio_study"]={en:["Debt to EBITDA ratio"],ro:["Debt to EBITDA ratio"]},e.exports["Directional Movement Index_study"]={en:["Directional Movement Index"],ro:["Directional Movement Index"]},e.exports.DMI_study={en:["DMI"],ro:["DMI"]},e.exports["Dividend payout ratio %_study"]={en:["Dividend payout ratio %"],ro:["Dividend payout ratio %"]},e.exports["Equity to assets ratio_study"]={en:["Equity to assets ratio"],ro:["Equity to assets ratio"]},e.exports["Enterprise value to EBIT ratio_study"]={en:["Enterprise value to EBIT ratio"],ro:["Enterprise value to EBIT ratio"]},e.exports["Enterprise value to EBITDA ratio_study"]={en:["Enterprise value to EBITDA ratio"],ro:["Enterprise value to EBITDA ratio"]},e.exports["Enterprise value to revenue ratio_study"]={en:["Enterprise value to revenue ratio"],ro:["Enterprise value to revenue ratio"]},e.exports["Goodwill, net_study"]={en:["Goodwill, net"],ro:["Goodwill, net"]},e.exports["Ichimoku Cloud_study"]={en:["Ichimoku Cloud"],ro:["Ichimoku Cloud"]},e.exports.Ichimoku_study={en:["Ichimoku"],ro:["Ichimoku"]},e.exports["Moving Average Convergence Divergence_study"]={en:["Moving Average Convergence Divergence"],ro:["Moving Average Convergence Divergence"]},e.exports["Operating income_study"]={en:["Operating income"],ro:["Operating income"]},e.exports["Price to book ratio_study"]={en:["Price to book ratio"],ro:["Price to book ratio"]},e.exports["Price to cash flow ratio_study"]={en:["Price to cash flow ratio"],ro:["Price to cash flow ratio"]},e.exports["Price to earnings ratio_study"]={en:["Price to earnings ratio"],ro:["Price to earnings ratio"]},e.exports["Price to free cash flow ratio_study"]={en:["Price to free cash flow ratio"],ro:["Price to free cash flow ratio"]},e.exports["Price to sales ratio_study"]={en:["Price to sales ratio"],ro:["Price to sales ratio"]},e.exports["Float shares outstanding_study"]={en:["Float shares outstanding"],ro:["Float shares outstanding"]},e.exports["Total common shares outstanding_study"]={en:["Total common shares outstanding"],ro:["Total common shares outstanding"]},e.exports["Volume Weighted Average Price_study"]={en:["Volume Weighted Average Price"],ro:["Volume Weighted Average Price"]},e.exports["Volume Weighted Moving Average_study"]={en:["Volume Weighted Moving Average"],
ro:["Volume Weighted Moving Average"]},e.exports["Williams Percent Range_study"]={en:["Williams Percent Range"],ro:["Williams Percent Range"]},e.exports.Doji_study={en:["Doji"],ro:["Doji"]},e.exports["Spinning Top Black_study"]={en:["Spinning Top Black"],ro:["Spinning Top Black"]},e.exports["Spinning Top White_study"]={en:["Spinning Top White"],ro:["Spinning Top White"]},e.exports["Accounts payable_study"]={en:["Accounts payable"],ro:["Accounts payable"]},e.exports["Accounts receivables, gross_study"]={en:["Accounts receivables, gross"],ro:["Accounts receivables, gross"]},e.exports["Accounts receivable - trade, net_study"]={en:["Accounts receivable - trade, net"],ro:["Accounts receivable - trade, net"]},e.exports.Accruals_study={en:["Accruals"],ro:["Accruals"]},e.exports["Accrued payroll_study"]={en:["Accrued payroll"],ro:["Accrued payroll"]},e.exports["Accumulated depreciation, total_study"]={en:["Accumulated depreciation, total"],ro:["Accumulated depreciation, total"]},e.exports["Additional paid-in capital/Capital surplus_study"]={en:["Additional paid-in capital/Capital surplus"],ro:["Additional paid-in capital/Capital surplus"]},e.exports["After tax other income/expense_study"]={en:["After tax other income/expense"],ro:["After tax other income/expense"]},e.exports["Altman Z-score_study"]={en:["Altman Z-score"],ro:["Altman Z-score"]},e.exports.Amortization_study={en:["Amortization"],ro:["Amortization"]},e.exports["Amortization of intangibles_study"]={en:["Amortization of intangibles"],ro:["Amortization of intangibles"]},e.exports["Amortization of deferred charges_study"]={en:["Amortization of deferred charges"],ro:["Amortization of deferred charges"]},e.exports["Asset turnover_study"]={en:["Asset turnover"],ro:["Asset turnover"]},e.exports["Average basic shares outstanding_study"]={en:["Average basic shares outstanding"],ro:["Average basic shares outstanding"]},e.exports["Bad debt / Doubtful accounts_study"]={en:["Bad debt / Doubtful accounts"],ro:["Bad debt / Doubtful accounts"]},e.exports["Basic EPS_study"]={en:["Basic EPS"],ro:["Basic EPS"]},e.exports["Basic earnings per share (Basic EPS)_study"]={en:["Basic earnings per share (Basic EPS)"],ro:["Basic earnings per share (Basic EPS)"]},e.exports["Beneish M-score_study"]={en:["Beneish M-score"],ro:["Beneish M-score"]},e.exports["Book value per share_study"]={en:["Book value per share"],ro:["Book value per share"]},e.exports["Buyback yield %_study"]={en:["Buyback yield %"],ro:["Buyback yield %"]},e.exports["Capital and operating lease obligations_study"]={en:["Capital and operating lease obligations"],ro:["Capital and operating lease obligations"]},e.exports["Capital expenditures - fixed assets_study"]={en:["Capital expenditures - fixed assets"],ro:["Capital expenditures - fixed assets"]},e.exports["Capital expenditures - other assets_study"]={en:["Capital expenditures - other assets"],ro:["Capital expenditures - other assets"]},e.exports["Capitalized lease obligations_study"]={en:["Capitalized lease obligations"],ro:["Capitalized lease obligations"]},
e.exports["Cash and short term investments_study"]={en:["Cash and short term investments"],ro:["Cash and short term investments"]},e.exports["Cash conversion cycle_study"]={en:["Cash conversion cycle"],ro:["Cash conversion cycle"]},e.exports["Cash & equivalents_study"]={en:["Cash & equivalents"],ro:["Cash & equivalents"]},e.exports["Cash from financing activities_study"]={en:["Cash from financing activities"],ro:["Cash from financing activities"]},e.exports["Cash from investing activities_study"]={en:["Cash from investing activities"],ro:["Cash from investing activities"]},e.exports["Cash from operating activities_study"]={en:["Cash from operating activities"],ro:["Cash from operating activities"]},e.exports["Change in accounts payable_study"]={en:["Change in accounts payable"],ro:["Change in accounts payable"]},e.exports["Change in accounts receivable_study"]={en:["Change in accounts receivable"],ro:["Change in accounts receivable"]},e.exports["Change in accrued expenses_study"]={en:["Change in accrued expenses"],ro:["Change in accrued expenses"]},e.exports["Change in inventories_study"]={en:["Change in inventories"],ro:["Change in inventories"]},e.exports["Change in other assets/liabilities_study"]={en:["Change in other assets/liabilities"],ro:["Change in other assets/liabilities"]},e.exports["Change in taxes payable_study"]={en:["Change in taxes payable"],ro:["Change in taxes payable"]},e.exports["Changes in working capital_study"]={en:["Changes in working capital"],ro:["Changes in working capital"]},e.exports["COGS to revenue ratio_study"]={en:["COGS to revenue ratio"],ro:["COGS to revenue ratio"]},e.exports["Common dividends paid_study"]={en:["Common dividends paid"],ro:["Common dividends paid"]},e.exports["Common equity, total_study"]={en:["Common equity, total"],ro:["Common equity, total"]},e.exports["Common stock par/Carrying value_study"]={en:["Common stock par/Carrying value"],ro:["Common stock par/Carrying value"]},e.exports["Cost of goods_study"]={en:["Cost of goods"],ro:["Cost of goods"]},e.exports["Cost of goods sold_study"]={en:["Cost of goods sold"],ro:["Cost of goods sold"]},e.exports["Current portion of LT debt and capital leases_study"]={en:["Current portion of LT debt and capital leases"],ro:["Current portion of LT debt and capital leases"]},e.exports["Current ratio_study"]={en:["Current ratio"],ro:["Current ratio"]},e.exports["Days inventory_study"]={en:["Days inventory"],ro:["Days inventory"]},e.exports["Days payable_study"]={en:["Days payable"],ro:["Days payable"]},e.exports["Days sales outstanding_study"]={en:["Days sales outstanding"],ro:["Days sales outstanding"]},e.exports["Debt to assets ratio_study"]={en:["Debt to assets ratio"],ro:["Debt to assets ratio"]},e.exports["Debt to equity ratio_study"]={en:["Debt to equity ratio"],ro:["Debt to equity ratio"]},e.exports["Debt to revenue ratio_study"]={en:["Debt to revenue ratio"],ro:["Debt to revenue ratio"]},e.exports["Deferred income, current_study"]={en:["Deferred income, current"],ro:["Deferred income, current"]},
e.exports["Deferred income, non-current_study"]={en:["Deferred income, non-current"],ro:["Deferred income, non-current"]},e.exports["Deferred tax assets_study"]={en:["Deferred tax assets"],ro:["Deferred tax assets"]},e.exports["Deferred taxes (cash flow)_study"]={en:["Deferred taxes (cash flow)"],ro:["Deferred taxes (cash flow)"]},e.exports["Deferred tax liabilities_study"]={en:["Deferred tax liabilities"],ro:["Deferred tax liabilities"]},e.exports.Depreciation_study={en:["Depreciation"],ro:["Depreciation"]},e.exports["Deprecation and amortization_study"]={en:["Deprecation and amortization"],ro:["Deprecation and amortization"]},e.exports["Depreciation & amortization (cash flow)_study"]={en:["Depreciation & amortization (cash flow)"],ro:["Depreciation & amortization (cash flow)"]},e.exports["Depreciation/depletion_study"]={en:["Depreciation/depletion"],ro:["Depreciation/depletion"]},e.exports["Diluted EPS_study"]={en:["Diluted EPS"],ro:["Diluted EPS"]},e.exports["Diluted earnings per share (Diluted EPS)_study"]={en:["Diluted earnings per share (Diluted EPS)"],ro:["Diluted earnings per share (Diluted EPS)"]},e.exports["Diluted net income available to common stockholders_study"]={en:["Diluted net income available to common stockholders"],ro:["Diluted net income available to common stockholders"]},e.exports["Diluted shares outstanding_study"]={en:["Diluted shares outstanding"],ro:["Diluted shares outstanding"]},e.exports["Dilution adjustment_study"]={en:["Dilution adjustment"],ro:["Dilution adjustment"]},e.exports["Discontinued operations_study"]={en:["Discontinued operations"],ro:["Discontinued operations"]},e.exports["Dividends payable_study"]={en:["Dividends payable"],ro:["Dividends payable"]},e.exports["Dividends per share - common stock primary issue_study"]={en:["Dividends per share - common stock primary issue"],ro:["Dividends per share - common stock primary issue"]},e.exports["Dividend yield %_study"]={en:["Dividend yield %"],ro:["Dividend yield %"]},e.exports["Earnings yield_study"]={en:["Earnings yield"],ro:["Earnings yield"]},e.exports.EBIT_study={en:["EBIT"],ro:["EBIT"]},e.exports.EBITDA_study={en:["EBITDA"],ro:["EBITDA"]},e.exports["EBITDA margin %_study"]={en:["EBITDA margin %"],ro:["EBITDA margin %"]},e.exports["Effective interest rate on debt %_study"]={en:["Effective interest rate on debt %"],ro:["Effective interest rate on debt %"]},e.exports["Enterprise value_study"]={en:["Enterprise value"],ro:["Enterprise value"]},e.exports["EPS basic one year growth_study"]={en:["EPS basic one year growth"],ro:["EPS basic one year growth"]},e.exports["EPS diluted one year growth_study"]={en:["EPS diluted one year growth"],ro:["EPS diluted one year growth"]},e.exports["EPS estimates_study"]={en:["EPS estimates"],ro:["EPS estimates"]},e.exports["Equity in earnings_study"]={en:["Equity in earnings"],ro:["Equity in earnings"]},e.exports["Financing activities – other sources_study"]={en:["Financing activities – other sources"],ro:["Financing activities – other sources"]},
e.exports["Financing activities – other uses_study"]={en:["Financing activities – other uses"],ro:["Financing activities – other uses"]},e.exports["Free cash flow_study"]={en:["Free cash flow"],ro:["Free cash flow"]},e.exports["Free cash flow margin %_study"]={en:["Free cash flow margin %"],ro:["Free cash flow margin %"]},e.exports["Fulmer H factor_study"]={en:["Fulmer H factor"],ro:["Fulmer H factor"]},e.exports["Funds from operations_study"]={en:["Funds from operations"],ro:["Funds from operations"]},e.exports["Goodwill to assets ratio_study"]={en:["Goodwill to assets ratio"],ro:["Goodwill to assets ratio"]},e.exports["Graham's number_study"]={en:["Graham's number"],ro:["Graham's number"]},e.exports["Gross margin %_study"]={en:["Gross margin %"],ro:["Gross margin %"]},e.exports["Gross profit_study"]={en:["Gross profit"],ro:["Gross profit"]},e.exports["Gross profit to assets ratio_study"]={en:["Gross profit to assets ratio"],ro:["Gross profit to assets ratio"]},e.exports["Gross property/plant/equipment_study"]={en:["Gross property/plant/equipment"],ro:["Gross property/plant/equipment"]},e.exports.Impairments_study={en:["Impairments"],ro:["Impairments"]},e.exports["Income Tax Credits_study"]={en:["Income Tax Credits"],ro:["Income Tax Credits"]},e.exports["Income tax, current_study"]={en:["Income tax, current"],ro:["Income tax, current"]},e.exports["Income tax, current - domestic_study"]={en:["Income tax, current - domestic"],ro:["Income tax, current - domestic"]},e.exports["Income Tax, current - foreign_study"]={en:["Income Tax, current - foreign"],ro:["Income Tax, current - foreign"]},e.exports["Income tax, deferred_study"]={en:["Income tax, deferred"],ro:["Income tax, deferred"]},e.exports["Income tax, deferred - domestic_study"]={en:["Income tax, deferred - domestic"],ro:["Income tax, deferred - domestic"]},e.exports["Income tax, deferred - foreign_study"]={en:["Income tax, deferred - foreign"],ro:["Income tax, deferred - foreign"]},e.exports["Income tax payable_study"]={en:["Income tax payable"],ro:["Income tax payable"]},e.exports["Interest capitalized_study"]={en:["Interest capitalized"],ro:["Interest capitalized"]},e.exports["Interest coverage_study"]={en:["Interest coverage"],ro:["Interest coverage"]},e.exports["Interest expense, net of interest capitalized_study"]={en:["Interest expense, net of interest capitalized"],ro:["Interest expense, net of interest capitalized"]},e.exports["Interest expense on debt_study"]={en:["Interest expense on debt"],ro:["Interest expense on debt"]},e.exports["Inventories - finished goods_study"]={en:["Inventories - finished goods"],ro:["Inventories - finished goods"]},e.exports["Inventories - progress payments & other_study"]={en:["Inventories - progress payments & other"],ro:["Inventories - progress payments & other"]},e.exports["Inventories - raw materials_study"]={en:["Inventories - raw materials"],ro:["Inventories - raw materials"]},e.exports["Inventories - work in progress_study"]={en:["Inventories - work in progress"],ro:["Inventories - work in progress"]},
e.exports["Inventory to revenue ratio_study"]={en:["Inventory to revenue ratio"],ro:["Inventory to revenue ratio"]},e.exports["Inventory turnover_study"]={en:["Inventory turnover"],ro:["Inventory turnover"]},e.exports["Investing activities – other sources_study"]={en:["Investing activities – other sources"],ro:["Investing activities – other sources"]},e.exports["Investing activities – other uses_study"]={en:["Investing activities – other uses"],ro:["Investing activities – other uses"]},e.exports["Investments in unconsolidated subsidiaries_study"]={en:["Investments in unconsolidated subsidiaries"],ro:["Investments in unconsolidated subsidiaries"]},e.exports["Issuance of long term debt_study"]={en:["Issuance of long term debt"],ro:["Issuance of long term debt"]},e.exports["Issuance/retirement of debt, net_study"]={en:["Issuance/retirement of debt, net"],ro:["Issuance/retirement of debt, net"]},e.exports["Issuance/retirement of long term debt_study"]={en:["Issuance/retirement of long term debt"],ro:["Issuance/retirement of long term debt"]},e.exports["Issuance/retirement of other debt_study"]={en:["Issuance/retirement of other debt"],ro:["Issuance/retirement of other debt"]},e.exports["Issuance/retirement of short term debt_study"]={en:["Issuance/retirement of short term debt"],ro:["Issuance/retirement of short term debt"]},e.exports["Issuance/retirement of stock, net_study"]={en:["Issuance/retirement of stock, net"],ro:["Issuance/retirement of stock, net"]},e.exports["KZ index_study"]={en:["KZ index"],ro:["KZ index"]},e.exports["Legal claim expense_study"]={en:["Legal claim expense"],ro:["Legal claim expense"]},e.exports["Long term debt_study"]={en:["Long term debt"],ro:["Long term debt"]},e.exports["Long term debt excl. lease liabilities_study"]={en:["Long term debt excl. lease liabilities"],ro:["Long term debt excl. lease liabilities"]},e.exports["Long term debt to total assets ratio_study"]={en:["Long term debt to total assets ratio"],ro:["Long term debt to total assets ratio"]},e.exports["Long term debt to total equity ratio_study"]={en:["Long term debt to total equity ratio"],ro:["Long term debt to total equity ratio"]},e.exports["Long term investments_study"]={en:["Long term investments"],ro:["Long term investments"]},e.exports["Market capitalization_study"]={en:["Market capitalization"],ro:["Market capitalization"]},e.exports["Minority interest_study"]={en:["Minority interest"],ro:["Minority interest"]},e.exports["Miscellaneous non-operating expense_study"]={en:["Miscellaneous non-operating expense"],ro:["Miscellaneous non-operating expense"]},e.exports["Net current asset value per share_study"]={en:["Net current asset value per share"],ro:["Net current asset value per share"]},e.exports["Net debt_study"]={en:["Net debt"],ro:["Net debt"]},e.exports["Net income_study"]={en:["Net income"],ro:["Net income"]},e.exports["Net income before discontinued operations_study"]={en:["Net income before discontinued operations"],ro:["Net income before discontinued operations"]},e.exports["Net income (cash flow)_study"]={
en:["Net income (cash flow)"],ro:["Net income (cash flow)"]},e.exports["Net income per employee_study"]={en:["Net income per employee"],ro:["Net income per employee"]},e.exports["Net intangible assets_study"]={en:["Net intangible assets"],ro:["Net intangible assets"]},e.exports["Net margin %_study"]={en:["Net margin %"],ro:["Net margin %"]},e.exports["Net property/plant/equipment_study"]={en:["Net property/plant/equipment"],ro:["Net property/plant/equipment"]},e.exports["Non-cash items_study"]={en:["Non-cash items"],ro:["Non-cash items"]},e.exports["Non-controlling/minority interest_study"]={en:["Non-controlling/minority interest"],ro:["Non-controlling/minority interest"]},e.exports["Non-operating income, excl. interest expenses_study"]={en:["Non-operating income, excl. interest expenses"],ro:["Non-operating income, excl. interest expenses"]},e.exports["Non-operating income, total_study"]={en:["Non-operating income, total"],ro:["Non-operating income, total"]},e.exports["Non-operating interest income_study"]={en:["Non-operating interest income"],ro:["Non-operating interest income"]},e.exports["Note receivable - long term_study"]={en:["Note receivable - long term"],ro:["Note receivable - long term"]},e.exports["Notes payable_study"]={en:["Notes payable"],ro:["Notes payable"]},e.exports["Number of employees_study"]={en:["Number of employees"],ro:["Number of employees"]},e.exports["Number of shareholders_study"]={en:["Number of shareholders"],ro:["Number of shareholders"]},e.exports["Operating earnings yield %_study"]={en:["Operating earnings yield %"],ro:["Operating earnings yield %"]},e.exports["Operating expenses (excl. COGS)_study"]={en:["Operating expenses (excl. COGS)"],ro:["Operating expenses (excl. COGS)"]},e.exports["Operating lease liabilities_study"]={en:["Operating lease liabilities"],ro:["Operating lease liabilities"]},e.exports["Operating margin %_study"]={en:["Operating margin %"],ro:["Operating margin %"]},e.exports["Other COGS_study"]={en:["Other COGS"],ro:["Other COGS"]},e.exports["Other common equity_study"]={en:["Other common equity"],ro:["Other common equity"]},e.exports["Other current assets, total_study"]={en:["Other current assets, total"],ro:["Other current assets, total"]},e.exports["Other current liabilities_study"]={en:["Other current liabilities"],ro:["Other current liabilities"]},e.exports["Other cost of goods sold_study"]={en:["Other cost of goods sold"],ro:["Other cost of goods sold"]},e.exports["Other exceptional charges_study"]={en:["Other exceptional charges"],ro:["Other exceptional charges"]},e.exports["Other financing cash flow items, total_study"]={en:["Other financing cash flow items, total"],ro:["Other financing cash flow items, total"]},e.exports["Other intangibles, net_study"]={en:["Other intangibles, net"],ro:["Other intangibles, net"]},e.exports["Other investing cash flow items, total_study"]={en:["Other investing cash flow items, total"],ro:["Other investing cash flow items, total"]},e.exports["Other investments_study"]={en:["Other investments"],ro:["Other investments"]},
e.exports["Other liabilities, total_study"]={en:["Other liabilities, total"],ro:["Other liabilities, total"]},e.exports["Other long term assets, total_study"]={en:["Other long term assets, total"],ro:["Other long term assets, total"]},e.exports["Other non-current liabilities, total_study"]={en:["Other non-current liabilities, total"],ro:["Other non-current liabilities, total"]},e.exports["Other operating expenses, total_study"]={en:["Other operating expenses, total"],ro:["Other operating expenses, total"]},e.exports["Other receivables_study"]={en:["Other receivables"],ro:["Other receivables"]},e.exports["Other short term debt_study"]={en:["Other short term debt"],ro:["Other short term debt"]},e.exports["Paid in capital_study"]={en:["Paid in capital"],ro:["Paid in capital"]},e.exports["PEG ratio_study"]={en:["PEG ratio"],ro:["PEG ratio"]},e.exports["Piotroski F-score_study"]={en:["Piotroski F-score"],ro:["Piotroski F-score"]},e.exports["Preferred dividends_study"]={en:["Preferred dividends"],ro:["Preferred dividends"]},e.exports["Preferred dividends paid_study"]={en:["Preferred dividends paid"],ro:["Preferred dividends paid"]},e.exports["Preferred stock, carrying value_study"]={en:["Preferred stock, carrying value"],ro:["Preferred stock, carrying value"]},e.exports["Prepaid expenses_study"]={en:["Prepaid expenses"],ro:["Prepaid expenses"]},e.exports["Pretax equity in earnings_study"]={en:["Pretax equity in earnings"],ro:["Pretax equity in earnings"]},e.exports["Pretax income_study"]={en:["Pretax income"],ro:["Pretax income"]},e.exports["Price earnings ratio forward_study"]={en:["Price earnings ratio forward"],ro:["Price earnings ratio forward"]},e.exports["Price sales ratio forward_study"]={en:["Price sales ratio forward"],ro:["Price sales ratio forward"]},e.exports["Price to tangible book ratio_study"]={en:["Price to tangible book ratio"],ro:["Price to tangible book ratio"]},e.exports["Provision for risks & charge_study"]={en:["Provision for risks & charge"],ro:["Provision for risks & charge"]},e.exports["Purchase/acquisition of business_study"]={en:["Purchase/acquisition of business"],ro:["Purchase/acquisition of business"]},e.exports["Purchase of investments_study"]={en:["Purchase of investments"],ro:["Purchase of investments"]},e.exports["Purchase/sale of business, net_study"]={en:["Purchase/sale of business, net"],ro:["Purchase/sale of business, net"]},e.exports["Purchase/sale of investments, net_study"]={en:["Purchase/sale of investments, net"],ro:["Purchase/sale of investments, net"]},e.exports["Quality ratio_study"]={en:["Quality ratio"],ro:["Quality ratio"]},e.exports["Quick ratio_study"]={en:["Quick ratio"],ro:["Quick ratio"]},e.exports["Reduction of long term debt_study"]={en:["Reduction of long term debt"],ro:["Reduction of long term debt"]},e.exports["Repurchase of common & preferred stock_study"]={en:["Repurchase of common & preferred stock"],ro:["Repurchase of common & preferred stock"]},e.exports["Research & development_study"]={en:["Research & development"],ro:["Research & development"]},
e.exports["Research & development to revenue ratio_study"]={en:["Research & development to revenue ratio"],ro:["Research & development to revenue ratio"]},e.exports["Restructuring charge_study"]={en:["Restructuring charge"],ro:["Restructuring charge"]},e.exports["Retained earnings_study"]={en:["Retained earnings"],ro:["Retained earnings"]},e.exports["Return on assets %_study"]={en:["Return on assets %"],ro:["Return on assets %"]},e.exports["Return on equity %_study"]={en:["Return on equity %"],ro:["Return on equity %"]},e.exports["Return on equity adjusted to book value %_study"]={en:["Return on equity adjusted to book value %"],ro:["Return on equity adjusted to book value %"]},e.exports["Return on invested capital %_study"]={en:["Return on invested capital %"],ro:["Return on invested capital %"]},e.exports["Return on tangible assets %_study"]={en:["Return on tangible assets %"],ro:["Return on tangible assets %"]},e.exports["Return on tangible equity %_study"]={en:["Return on tangible equity %"],ro:["Return on tangible equity %"]},e.exports["Revenue estimates_study"]={en:["Revenue estimates"],ro:["Revenue estimates"]},e.exports["Revenue one year growth_study"]={en:["Revenue one year growth"],ro:["Revenue one year growth"]},e.exports["Revenue per employee_study"]={en:["Revenue per employee"],ro:["Revenue per employee"]},e.exports["Sale/maturity of investments_study"]={en:["Sale/maturity of investments"],ro:["Sale/maturity of investments"]},e.exports["Sale of common & preferred stock_study"]={en:["Sale of common & preferred stock"],ro:["Sale of common & preferred stock"]},e.exports["Sale of fixed assets & businesses_study"]={en:["Sale of fixed assets & businesses"],ro:["Sale of fixed assets & businesses"]},e.exports["Selling/general/admin expenses, other_study"]={en:["Selling/general/admin expenses, other"],ro:["Selling/general/admin expenses, other"]},e.exports["Selling/general/admin expenses, total_study"]={en:["Selling/general/admin expenses, total"],ro:["Selling/general/admin expenses, total"]},e.exports["Shareholders' equity_study"]={en:["Shareholders' equity"],ro:["Shareholders' equity"]},e.exports["Shares buyback ratio %_study"]={en:["Shares buyback ratio %"],ro:["Shares buyback ratio %"]},e.exports["Short term debt_study"]={en:["Short term debt"],ro:["Short term debt"]},e.exports["Short term debt excl. current portion of LT debt_study"]={en:["Short term debt excl. current portion of LT debt"],ro:["Short term debt excl. current portion of LT debt"]},e.exports["Short term investments_study"]={en:["Short term investments"],ro:["Short term investments"]},e.exports["Sloan ratio %_study"]={en:["Sloan ratio %"],ro:["Sloan ratio %"]},e.exports["Springate score_study"]={en:["Springate score"],ro:["Springate score"]},e.exports["Sustainable growth rate_study"]={en:["Sustainable growth rate"],ro:["Sustainable growth rate"]},e.exports["Tangible book value per share_study"]={en:["Tangible book value per share"],ro:["Tangible book value per share"]},e.exports["Tangible common equity ratio_study"]={
en:["Tangible common equity ratio"],ro:["Tangible common equity ratio"]},e.exports.Taxes_study={en:["Taxes"],ro:["Taxes"]},e.exports["Tobin's Q (approximate)_study"]={en:["Tobin's Q (approximate)"],ro:["Tobin's Q (approximate)"]},e.exports["Total assets_study"]={en:["Total assets"],ro:["Total assets"]},e.exports["Total cash dividends paid_study"]={en:["Total cash dividends paid"],ro:["Total cash dividends paid"]},e.exports["Total current assets_study"]={en:["Total current assets"],ro:["Total current assets"]},e.exports["Total current liabilities_study"]={en:["Total current liabilities"],ro:["Total current liabilities"]},e.exports["Total debt_study"]={en:["Total debt"],ro:["Total debt"]},e.exports["Total equity_study"]={en:["Total equity"],ro:["Total equity"]},e.exports["Total inventory_study"]={en:["Total inventory"],ro:["Total inventory"]},e.exports["Total liabilities_study"]={en:["Total liabilities"],ro:["Total liabilities"]},e.exports["Total liabilities & shareholders' equities_study"]={en:["Total liabilities & shareholders' equities"],ro:["Total liabilities & shareholders' equities"]},e.exports["Total non-current assets_study"]={en:["Total non-current assets"],ro:["Total non-current assets"]},e.exports["Total non-current liabilities_study"]={en:["Total non-current liabilities"],ro:["Total non-current liabilities"]},e.exports["Total operating expenses_study"]={en:["Total operating expenses"],ro:["Total operating expenses"]},e.exports["Total receivables, net_study"]={en:["Total receivables, net"],ro:["Total receivables, net"]},e.exports["Total revenue_study"]={en:["Total revenue"],ro:["Total revenue"]},e.exports["Treasury stock - common_study"]={en:["Treasury stock - common"],ro:["Treasury stock - common"]},e.exports["Unrealized gain/loss_study"]={en:["Unrealized gain/loss"],ro:["Unrealized gain/loss"]},e.exports["Unusual income/expense_study"]={en:["Unusual income/expense"],ro:["Unusual income/expense"]},e.exports["Zmijewski score_study"]={en:["Zmijewski score"],ro:["Zmijewski score"]},e.exports["Valuation ratios_study"]={en:["Valuation ratios"],ro:["Valuation ratios"]},e.exports["Profitability ratios_study"]={en:["Profitability ratios"],ro:["Profitability ratios"]},e.exports["Liquidity ratios_study"]={en:["Liquidity ratios"],ro:["Liquidity ratios"]},e.exports["Solvency ratios_study"]={en:["Solvency ratios"],ro:["Solvency ratios"]},e.exports["Key stats_study"]={en:["Key stats"],ro:["Key stats"]},e.exports["Accumulation/Distribution_study"]={en:["Accumulation/Distribution"],ro:["Accumulation/Distribution"]},e.exports["Accumulative Swing Index_study"]={en:["Accumulative Swing Index"],ro:["Accumulative Swing Index"]},e.exports["Advance/Decline_study"]={en:["Advance/Decline"],ro:["Advance/Decline"]},e.exports["All Chart Patterns_study"]={en:["All Chart Patterns"],ro:["All Chart Patterns"]},e.exports["Arnaud Legoux Moving Average_study"]={en:["Arnaud Legoux Moving Average"],ro:["Arnaud Legoux Moving Average"]},e.exports.Aroon_study={en:["Aroon"],ro:["Aroon"]},e.exports.ASI_study={en:["ASI"],ro:["ASI"]},
e.exports["Average Directional Index_study"]={en:["Average Directional Index"],ro:["Average Directional Index"]},e.exports["Average True Range_study"]={en:["Average True Range"],ro:["Average True Range"]},e.exports["Awesome Oscillator_study"]={en:["Awesome Oscillator"],ro:["Awesome Oscillator"]},e.exports["Balance of Power_study"]={en:["Balance of Power"],ro:["Balance of Power"]},e.exports["Bollinger Bands %B_study"]={en:["Bollinger Bands %B"],ro:["Bollinger Bands %B"]},e.exports["Bollinger Bands Width_study"]={en:["Bollinger Bands Width"],ro:["Bollinger Bands Width"]},e.exports["Bollinger Bands_study"]={en:["Bollinger Bands"],ro:["Bollinger Bands"]},e.exports["Chaikin Money Flow_study"]={en:["Chaikin Money Flow"],ro:["Chaikin Money Flow"]},e.exports["Chaikin Oscillator_study"]={en:["Chaikin Oscillator"],ro:["Chaikin Oscillator"]},e.exports["Chande Kroll Stop_study"]={en:["Chande Kroll Stop"],ro:["Chande Kroll Stop"]},e.exports["Chande Momentum Oscillator_study"]={en:["Chande Momentum Oscillator"],ro:["Chande Momentum Oscillator"]},e.exports["Chop Zone_study"]={en:["Chop Zone"],ro:["Chop Zone"]},e.exports["Choppiness Index_study"]={en:["Choppiness Index"],ro:["Choppiness Index"]},e.exports["Commodity Channel Index_study"]={en:["Commodity Channel Index"],ro:["Commodity Channel Index"]},e.exports["Connors RSI_study"]={en:["Connors RSI"],ro:["Connors RSI"]},e.exports["Coppock Curve_study"]={en:["Coppock Curve"],ro:["Coppock Curve"]},e.exports["Correlation Coefficient_study"]={en:["Correlation Coefficient"],ro:["Correlation Coefficient"]},e.exports.CRSI_study={en:["CRSI"],ro:["CRSI"]},e.exports["Detrended Price Oscillator_study"]={en:["Detrended Price Oscillator"],ro:["Detrended Price Oscillator"]},e.exports["Directional Movement_study"]={en:["Directional Movement"],ro:["Directional Movement"]},e.exports["Donchian Channels_study"]={en:["Donchian Channels"],ro:["Donchian Channels"]},e.exports["Double EMA_study"]={en:["Double EMA"],ro:["Double EMA"]},e.exports["Ease Of Movement_study"]={en:["Ease Of Movement"],ro:["Ease Of Movement"]},e.exports["Elder Force Index_study"]={en:["Elder Force Index"],ro:["Elder's Force Index"]},e.exports["EMA Cross_study"]={en:["EMA Cross"],ro:["EMA Cross"]},e.exports.Envelopes_study={en:["Envelopes"],ro:["Envelopes"]},e.exports["Fisher Transform_study"]={en:["Fisher Transform"],ro:["Fisher Transform"]},e.exports["Fixed Range_study"]={en:["Fixed Range"],ro:["Fixed Range"]},e.exports["Fixed Range Volume Profile_study"]={en:["Fixed Range Volume Profile"],ro:["Fixed Range Volume Profile"]},e.exports["Guppy Multiple Moving Average_study"]={en:["Guppy Multiple Moving Average"],ro:["Guppy Multiple Moving Average"]},e.exports["Historical Volatility_study"]={en:["Historical Volatility"],ro:["Historical Volatility"]},e.exports["Hull Moving Average_study"]={en:["Hull Moving Average"],ro:["Hull Moving Average"]},e.exports["Keltner Channels_study"]={en:["Keltner Channels"],ro:["Keltner Channels"]},e.exports["Klinger Oscillator_study"]={en:["Klinger Oscillator"],ro:["Klinger Oscillator"]},
e.exports["Know Sure Thing_study"]={en:["Know Sure Thing"],ro:["Know Sure Thing"]},e.exports["Least Squares Moving Average_study"]={en:["Least Squares Moving Average"],ro:["Least Squares Moving Average"]},e.exports["Linear Regression Curve_study"]={en:["Linear Regression Curve"],ro:["Linear Regression Curve"]},e.exports["MA Cross_study"]={en:["MA Cross"],ro:["MA Cross"]},e.exports["MA with EMA Cross_study"]={en:["MA with EMA Cross"],ro:["MA with EMA Cross"]},e.exports["MA/EMA Cross_study"]={en:["MA/EMA Cross"],ro:["MA/EMA Cross"]},e.exports.MACD_study={en:["MACD"],ro:["MACD"]},e.exports["Mass Index_study"]={en:["Mass Index"],ro:["Mass Index"]},e.exports["McGinley Dynamic_study"]={en:["McGinley Dynamic"],ro:["McGinley Dynamic"]},e.exports.Median_study={en:["Median"],ro:["Median"]},e.exports.Momentum_study={en:["Momentum"],ro:["Momentum"]},e.exports["Money Flow_study"]={en:["Money Flow"],ro:["Money Flow"]},e.exports["Moving Average Channel_study"]={en:["Moving Average Channel"],ro:["Moving Average Channel"]},e.exports["Moving Average Exponential_study"]={en:["Moving Average Exponential"],ro:["Moving Average Exponential"]},e.exports["Moving Average Weighted_study"]={en:["Moving Average Weighted"],ro:["Moving Average Weighted"]},e.exports["Moving Average Simple_study"]={en:["Moving Average Simple"],ro:["Moving Average Simple"]},e.exports["Net Volume_study"]={en:["Net Volume"],ro:["Net Volume"]},e.exports["On Balance Volume_study"]={en:["On Balance Volume"],ro:["On Balance Volume"]},e.exports["Parabolic SAR_study"]={en:["Parabolic SAR"],ro:["Parabolic SAR"]},e.exports["Pivot Points Standard_study"]={en:["Pivot Points Standard"],ro:["Pivot Points Standard"]},e.exports["Periodic Volume Profile_study"]={en:["Periodic Volume Profile"],ro:["Periodic Volume Profile"]},e.exports["Price Channel_study"]={en:["Price Channel"],ro:["Price Channel"]},e.exports["Price Oscillator_study"]={en:["Price Oscillator"],ro:["Price Oscillator"]},e.exports["Price Volume Trend_study"]={en:["Price Volume Trend"],ro:["Price Volume Trend"]},e.exports["Rate Of Change_study"]={en:["Rate Of Change"],ro:["Rate Of Change"]},e.exports["Relative Strength Index_study"]={en:["Relative Strength Index"],ro:["Relative Strength Index"]},e.exports["Relative Vigor Index_study"]={en:["Relative Vigor Index"],ro:["Relative Vigor Index"]},e.exports["Relative Volatility Index_study"]={en:["Relative Volatility Index"],ro:["Relative Volatility Index"]},e.exports["Relative Volume at Time_study"]={en:["Relative Volume at Time"],ro:["Relative Volume at Time"]},e.exports["Session Volume_study"]={en:["Session Volume"],ro:["Session Volume"]},e.exports["Session Volume HD_study"]={en:["Session Volume HD"],ro:["Session Volume"]},e.exports["Session Volume Profile_study"]={en:["Session Volume Profile"],ro:["Session Volume Profile"]},e.exports["Session Volume Profile HD_study"]={en:["Session Volume Profile HD"],ro:["Session Volume Profile HD"]},e.exports["SMI Ergodic Indicator/Oscillator_study"]={en:["SMI Ergodic Indicator/Oscillator"],ro:["SMI Ergodic Indicator/Oscillator"]},
e.exports["Smoothed Moving Average_study"]={en:["Smoothed Moving Average"],ro:["Smoothed Moving Average"]},e.exports["Stochastic Momentum Index_study"]={en:["Stochastic Momentum Index"],ro:["Stochastic Momentum Index"]},e.exports.Stoch_study={en:["Stoch"],ro:["Stoch"]},e.exports["Stochastic RSI_study"]={en:["Stochastic RSI"],ro:["Stochastic RSI"]},e.exports.Stochastic_study={en:["Stochastic"],ro:["Stochastic"]},e.exports["Time Weighted Average Price_study"]={en:["Time Weighted Average Price"],ro:["Time Weighted Average Price"]},e.exports["Triple EMA_study"]={en:["Triple EMA"],ro:["Triple EMA"]},e.exports.TRIX_study={en:["TRIX"],ro:["TRIX"]},e.exports["True Strength Indicator_study"]={en:["True Strength Indicator"],ro:["True Strength Indicator"]},e.exports["Ultimate Oscillator_study"]={en:["Ultimate Oscillator"],ro:["Ultimate Oscillator"]},e.exports["Visible Range_study"]={en:["Visible Range"],ro:["Visible Range"]},e.exports["Visible Range Volume Profile_study"]={en:["Visible Range Volume Profile"],ro:["Visible Range Volume Profile"]},e.exports["Volume Oscillator_study"]={en:["Volume Oscillator"],ro:["Volume Oscillator"]},e.exports.Volume_study={en:["Volume"],ro:["Volume"]},e.exports.Vol_study={en:["Vol"],ro:["Vol"]},e.exports["Vortex Indicator_study"]={en:["Vortex Indicator"],ro:["Vortex Indicator"]},e.exports.VWAP_study={en:["VWAP"],ro:["VWAP"]},e.exports.VWMA_study={en:["VWMA"],ro:["VWMA"]},e.exports["Williams %R_study"]={en:["Williams %R"],ro:["Williams %R"]},e.exports["Williams Alligator_study"]={en:["Williams Alligator"],ro:["Williams Alligator"]},e.exports["Williams Fractal_study"]={en:["Williams Fractal"],ro:["Williams Fractal"]},e.exports["Zig Zag_study"]={en:["Zig Zag"],ro:["Zig Zag"]},e.exports["24-hour Volume_study"]={en:["24-hour Volume"],ro:["24-hour Volume"]},e.exports["Ease of Movement_study"]={en:["Ease of Movement"],ro:["Ease Of Movement"]},e.exports["Elders Force Index_study"]={en:["Elders Force Index"],ro:["Elders Force Index"]},e.exports.Envelope_study={en:["Envelope"],ro:["Envelope"]},e.exports.Gaps_study={en:["Gaps"],ro:["Gaps"]},e.exports["Linear Regression Channel_study"]={en:["Linear Regression Channel"],ro:["Linear Regression Channel"]},e.exports["Moving Average Ribbon_study"]={en:["Moving Average Ribbon"],ro:["Moving Average Ribbon"]},e.exports["Multi-Time Period Charts_study"]={en:["Multi-Time Period Charts"],ro:["Multi-Time Period Charts"]},e.exports["Open Interest_study"]={en:["Open Interest"],ro:["Open Interest"]},e.exports["Rob Booker - Intraday Pivot Points_study"]={en:["Rob Booker - Intraday Pivot Points"],ro:["Rob Booker - Intraday Pivot Points"]},e.exports["Rob Booker - Knoxville Divergence_study"]={en:["Rob Booker - Knoxville Divergence"],ro:["Rob Booker - Knoxville Divergence"]},e.exports["Rob Booker - Missed Pivot Points_study"]={en:["Rob Booker - Missed Pivot Points"],ro:["Rob Booker - Missed Pivot Points"]},e.exports["Rob Booker - Reversal_study"]={en:["Rob Booker - Reversal"],ro:["Rob Booker - Reversal"]},e.exports["Rob Booker - Ziv Ghost Pivots_study"]={
en:["Rob Booker - Ziv Ghost Pivots"],ro:["Rob Booker - Ziv Ghost Pivots"]},e.exports.Supertrend_study={en:["Supertrend"],ro:["Supertrend"]},e.exports["Technical Ratings_study"]={en:["Technical Ratings"],ro:["Technical Ratings"]},e.exports["True Strength Index_study"]={en:["True Strength Index"],ro:["True Strength Index"]},e.exports["Up/Down Volume_study"]={en:["Up/Down Volume"],ro:["Up/Down Volume"]},e.exports["Visible Average Price_study"]={en:["Visible Average Price"],ro:["Visible Average Price"]},e.exports["Williams Fractals_study"]={en:["Williams Fractals"],ro:["Williams Fractals"]},e.exports["Keltner Channels Strategy_study"]={en:["Keltner Channels Strategy"],ro:["Keltner Channels Strategy"]},e.exports["Rob Booker - ADX Breakout_study"]={en:["Rob Booker - ADX Breakout"],ro:["Rob Booker - ADX Breakout"]},e.exports["Supertrend Strategy_study"]={en:["Supertrend Strategy"],ro:["Supertrend Strategy"]},e.exports["Technical Ratings Strategy_study"]={en:["Technical Ratings Strategy"],ro:["Technical Ratings Strategy"]},e.exports["Auto Anchored Volume Profile_study"]={en:["Auto Anchored Volume Profile"],ro:["Auto Anchored Volume Profile"]},e.exports["Auto Fib Extension_study"]={en:["Auto Fib Extension"],ro:["Auto Fib Extension"]},e.exports["Auto Fib Retracement_study"]={en:["Auto Fib Retracement"],ro:["Auto Fib Retracement"]},e.exports["Auto Pitchfork_study"]={en:["Auto Pitchfork"],ro:["Auto Pitchfork"]},e.exports["Bearish Flag Chart Pattern_study"]={en:["Bearish Flag Chart Pattern"],ro:["Bearish Flag Chart Pattern"]},e.exports["Bullish Flag Chart Pattern_study"]={en:["Bullish Flag Chart Pattern"],ro:["Bullish Flag Chart Pattern"]},e.exports["Bearish Pennant Chart Pattern_study"]={en:["Bearish Pennant Chart Pattern"],ro:["Bearish Pennant Chart Pattern"]},e.exports["Bullish Pennant Chart Pattern_study"]={en:["Bullish Pennant Chart Pattern"],ro:["Bullish Pennant Chart Pattern"]},e.exports["Double Bottom Chart Pattern_study"]={en:["Double Bottom Chart Pattern"],ro:["Double Bottom Chart Pattern"]},e.exports["Double Top Chart Pattern_study"]={en:["Double Top Chart Pattern"],ro:["Double Top Chart Pattern"]},e.exports["Elliott Wave Chart Pattern_study"]={en:["Elliott Wave Chart Pattern"],ro:["Elliott Wave Chart Pattern"]},e.exports["Falling Wedge Chart Pattern_study"]={en:["Falling Wedge Chart Pattern"],ro:["Falling Wedge Chart Pattern"]},e.exports["Head And Shoulders Chart Pattern_study"]={},e.exports["Inverse Head And Shoulders Chart Pattern_study"]={},e.exports["Rectangle Chart Pattern_study"]={en:["Rectangle Chart Pattern"],ro:["Rectangle Chart Pattern"]},e.exports["Rising Wedge Chart Pattern_study"]={en:["Rising Wedge Chart Pattern"],ro:["Rising Wedge Chart Pattern"]},e.exports["Triangle Chart Pattern_study"]={en:["Triangle Chart Pattern"],ro:["Triangle Chart Pattern"]},e.exports["Triple Bottom Chart Pattern_study"]={en:["Triple Bottom Chart Pattern"],ro:["Triple Bottom Chart Pattern"]},e.exports["Triple Top Chart Pattern_study"]={en:["Triple Top Chart Pattern"],ro:["Triple Top Chart Pattern"]},
e.exports["VWAP Auto Anchored_study"]={en:["VWAP Auto Anchored"],ro:["VWAP Auto Anchored"]},e.exports["*All Candlestick Patterns*_study"]={en:["*All Candlestick Patterns*"],ro:["*All Candlestick Patterns*"]},e.exports["Abandoned Baby - Bearish_study"]={en:["Abandoned Baby - Bearish"],ro:["Abandoned Baby - Bearish"]},e.exports["Abandoned Baby - Bullish_study"]={en:["Abandoned Baby - Bullish"],ro:["Abandoned Baby - Bullish"]},e.exports["Dark Cloud Cover - Bearish_study"]={en:["Dark Cloud Cover - Bearish"],ro:["Dark Cloud Cover - Bearish"]},e.exports["Doji Star - Bearish_study"]={en:["Doji Star - Bearish"],ro:["Doji Star - Bearish"]},e.exports["Doji Star - Bullish_study"]={en:["Doji Star - Bullish"],ro:["Doji Star - Bullish"]},e.exports["Downside Tasuki Gap - Bearish_study"]={en:["Downside Tasuki Gap - Bearish"],ro:["Downside Tasuki Gap - Bearish"]},e.exports["Dragonfly Doji - Bullish_study"]={en:["Dragonfly Doji - Bullish"],ro:["Dragonfly Doji - Bullish"]},e.exports["Engulfing - Bearish_study"]={en:["Engulfing - Bearish"],ro:["Engulfing - Bearish"]},e.exports["Engulfing - Bullish_study"]={en:["Engulfing - Bullish"],ro:["Engulfing - Bullish"]},e.exports["Evening Doji Star - Bearish_study"]={en:["Evening Doji Star - Bearish"],ro:["Evening Doji Star - Bearish"]},e.exports["Evening Star - Bearish_study"]={en:["Evening Star - Bearish"],ro:["Evening Star - Bearish"]},e.exports["Falling Three Methods - Bearish_study"]={en:["Falling Three Methods - Bearish"],ro:["Falling Three Methods - Bearish"]},e.exports["Falling Window - Bearish_study"]={en:["Falling Window - Bearish"],ro:["Falling Window - Bearish"]},e.exports["Gravestone Doji - Bearish_study"]={en:["Gravestone Doji - Bearish"],ro:["Gravestone Doji - Bearish"]},e.exports["Hammer - Bullish_study"]={en:["Hammer - Bullish"],ro:["Hammer - Bullish"]},e.exports["Hanging Man - Bearish_study"]={en:["Hanging Man - Bearish"],ro:["Hanging Man - Bearish"]},e.exports["Harami - Bearish_study"]={en:["Harami - Bearish"],ro:["Harami - Bearish"]},e.exports["Harami - Bullish_study"]={en:["Harami - Bullish"],ro:["Harami - Bullish"]},e.exports["Harami Cross - Bearish_study"]={en:["Harami Cross - Bearish"],ro:["Harami Cross - Bearish"]},e.exports["Harami Cross - Bullish_study"]={en:["Harami Cross - Bullish"],ro:["Harami Cross - Bullish"]},e.exports["Inverted Hammer - Bullish_study"]={en:["Inverted Hammer - Bullish"],ro:["Inverted Hammer - Bullish"]},e.exports["Kicking - Bearish_study"]={en:["Kicking - Bearish"],ro:["Kicking - Bearish"]},e.exports["Kicking - Bullish_study"]={en:["Kicking - Bullish"],ro:["Kicking - Bullish"]},e.exports["Long Lower Shadow - Bullish_study"]={en:["Long Lower Shadow - Bullish"],ro:["Long Lower Shadow - Bullish"]},e.exports["Long Upper Shadow - Bearish_study"]={en:["Long Upper Shadow - Bearish"],ro:["Long Upper Shadow - Bearish"]},e.exports["Marubozu Black - Bearish_study"]={en:["Marubozu Black - Bearish"],ro:["Marubozu Black - Bearish"]},e.exports["Marubozu White - Bullish_study"]={en:["Marubozu White - Bullish"],ro:["Marubozu White - Bullish"]},
e.exports["Morning Doji Star - Bullish_study"]={en:["Morning Doji Star - Bullish"],ro:["Morning Doji Star - Bullish"]},e.exports["Morning Star - Bullish_study"]={en:["Morning Star - Bullish"],ro:["Morning Star - Bullish"]},e.exports["On Neck - Bearish_study"]={en:["On Neck - Bearish"],ro:["On Neck - Bearish"]},e.exports["Piercing - Bullish_study"]={en:["Piercing - Bullish"],ro:["Piercing - Bullish"]},e.exports["Rising Three Methods - Bullish_study"]={en:["Rising Three Methods - Bullish"],ro:["Rising Three Methods - Bullish"]},e.exports["Rising Window - Bullish_study"]={en:["Rising Window - Bullish"],ro:["Rising Window - Bullish"]},e.exports["Shooting Star - Bearish_study"]={en:["Shooting Star - Bearish"],ro:["Shooting Star - Bearish"]},e.exports["Three Black Crows - Bearish_study"]={en:["Three Black Crows - Bearish"],ro:["Three Black Crows - Bearish"]},e.exports["Three White Soldiers - Bullish_study"]={en:["Three White Soldiers - Bullish"],ro:["Three White Soldiers - Bullish"]},e.exports["Tri-Star - Bearish_study"]={en:["Tri-Star - Bearish"],ro:["Tri-Star - Bearish"]},e.exports["Tri-Star - Bullish_study"]={en:["Tri-Star - Bullish"],ro:["Tri-Star - Bullish"]},e.exports["Tweezer Top - Bearish_study"]={en:["Tweezer Top - Bearish"],ro:["Tweezer Top - Bearish"]},e.exports["Upside Tasuki Gap - Bullish_study"]={en:["Upside Tasuki Gap - Bullish"],ro:["Upside Tasuki Gap - Bullish"]},e.exports.SuperTrend_study={en:["SuperTrend"],ro:["SuperTrend"]},e.exports["Average Price_study"]={en:["Average Price"],ro:["Average Price"]},e.exports["Typical Price_study"]={en:["Typical Price"],ro:["Typical Price"]},e.exports["Median Price_study"]={en:["Median Price"],ro:["Median Price"]},e.exports["Money Flow Index_study"]={en:["Money Flow Index"],ro:["Money Flow Index"]},e.exports["Moving Average Double_study"]={en:["Moving Average Double"],ro:["Moving Average Double"]},e.exports["Moving Average Triple_study"]={en:["Moving Average Triple"],ro:["Moving Average Triple"]},e.exports["Moving Average Adaptive_study"]={en:["Moving Average Adaptive"],ro:["Moving Average Adaptive"]},e.exports["Moving Average Hamming_study"]={en:["Moving Average Hamming"],ro:["Moving Average Hamming"]},e.exports["Moving Average Modified_study"]={en:["Moving Average Modified"],ro:["Moving Average Modified"]},e.exports["Moving Average Multiple_study"]={en:["Moving Average Multiple"],ro:["Moving Average Multiple"]},e.exports["Linear Regression Slope_study"]={en:["Linear Regression Slope"],ro:["Linear Regression Slope"]},e.exports["Standard Error_study"]={en:["Standard Error"],ro:["Standard Error"]},e.exports["Standard Error Bands_study"]={en:["Standard Error Bands"],ro:["Standard Error Bands"]},e.exports["Correlation - Log_study"]={en:["Correlation - Log"],ro:["Correlation - Log"]},e.exports["Standard Deviation_study"]={en:["Standard Deviation"],ro:["Standard Deviation"]},e.exports["Chaikin Volatility_study"]={en:["Chaikin Volatility"],ro:["Chaikin Volatility"]},e.exports["Volatility Close-to-Close_study"]={en:["Volatility Close-to-Close"],ro:["Volatility Close-to-Close"]
},e.exports["Volatility Zero Trend Close-to-Close_study"]={en:["Volatility Zero Trend Close-to-Close"],ro:["Volatility Zero Trend Close-to-Close"]},e.exports["Volatility O-H-L-C_study"]={en:["Volatility O-H-L-C"],ro:["Volatility O-H-L-C"]},e.exports["Volatility Index_study"]={en:["Volatility Index"],ro:["Volatility Index"]},e.exports["Trend Strength Index_study"]={en:["Trend Strength Index"],ro:["Trend Strength Index"]},e.exports["Majority Rule_study"]={en:["Majority Rule"],ro:["Majority Rule"]},e.exports["Advance Decline Line_study"]={en:["Advance Decline Line"],ro:["Advance Decline Line"]},e.exports["Advance Decline Ratio_study"]={en:["Advance Decline Ratio"],ro:["Advance Decline Ratio"]},e.exports["Advance/Decline Ratio (Bars)_study"]={en:["Advance/Decline Ratio (Bars)"],ro:["Advance/Decline Ratio (Bars)"]},e.exports["BarUpDn Strategy_study"]={en:["BarUpDn Strategy"],ro:["BarUpDn Strategy"]},e.exports["Bollinger Bands Strategy directed_study"]={en:["Bollinger Bands Strategy directed"],ro:["Bollinger Bands Strategy directed"]},e.exports["Bollinger Bands Strategy_study"]={en:["Bollinger Bands Strategy"],ro:["Bollinger Bands Strategy"]},e.exports.ChannelBreakOutStrategy_study={en:["ChannelBreakOutStrategy"],ro:["ChannelBreakOutStrategy"]},e.exports.Compare_study={en:["Compare"],ro:["Compare"]},e.exports["Conditional Expressions_study"]={en:["Conditional Expressions"],ro:["Conditional Expressions"]},e.exports.ConnorsRSI_study={en:["ConnorsRSI"],ro:["ConnorsRSI"]},e.exports["Consecutive Up/Down Strategy_study"]={en:["Consecutive Up/Down Strategy"],ro:["Consecutive Up/Down Strategy"]},e.exports["Cumulative Volume Index_study"]={en:["Cumulative Volume Index"],ro:["Cumulative Volume Index"]},e.exports["Divergence Indicator_study"]={en:["Divergence Indicator"],ro:["Divergence Indicator"]},e.exports["Greedy Strategy_study"]={en:["Greedy Strategy"],ro:["Greedy Strategy"]},e.exports["InSide Bar Strategy_study"]={en:["InSide Bar Strategy"],ro:["InSide Bar Strategy"]},e.exports["Keltner Channel Strategy_study"]={en:["Keltner Channel Strategy"],ro:["Keltner Channel Strategy"]},e.exports["Linear Regression_study"]={en:["Linear Regression"],ro:["Linear Regression"]},e.exports["MACD Strategy_study"]={en:["MACD Strategy"],ro:["MACD Strategy"]},e.exports["Momentum Strategy_study"]={en:["Momentum Strategy"],ro:["Momentum Strategy"]},e.exports["Moon Phases_study"]={en:["Moon Phases"],ro:["Moon Phases"]},e.exports["Moving Average Convergence/Divergence_study"]={en:["Moving Average Convergence/Divergence"],ro:["Moving Average Convergence/Divergence"]},e.exports["MovingAvg Cross_study"]={en:["MovingAvg Cross"],ro:["MovingAvg Cross"]},e.exports["MovingAvg2Line Cross_study"]={en:["MovingAvg2Line Cross"],ro:["MovingAvg2Line Cross"]},e.exports["OutSide Bar Strategy_study"]={en:["OutSide Bar Strategy"],ro:["OutSide Bar Strategy"]},e.exports.Overlay_study={en:["Overlay"],ro:["Overlay"]},e.exports["Parabolic SAR Strategy_study"]={en:["Parabolic SAR Strategy"],ro:["Parabolic SAR Strategy"]},e.exports["Pivot Extension Strategy_study"]={
en:["Pivot Extension Strategy"],ro:["Pivot Extension Strategy"]},e.exports["Pivot Points High Low_study"]={en:["Pivot Points High Low"],ro:["Pivot Points High Low"]},e.exports["Pivot Reversal Strategy_study"]={en:["Pivot Reversal Strategy"],ro:["Pivot Reversal Strategy"]},e.exports["Price Channel Strategy_study"]={en:["Price Channel Strategy"],ro:["Price Channel Strategy"]},e.exports["RSI Strategy_study"]={en:["RSI Strategy"],ro:["RSI Strategy"]},e.exports["SMI Ergodic Indicator_study"]={en:["SMI Ergodic Indicator"],ro:["SMI Ergodic Indicator"]},e.exports["SMI Ergodic Oscillator_study"]={en:["SMI Ergodic Oscillator"],ro:["SMI Ergodic Oscillator"]},e.exports["Stochastic Slow Strategy_study"]={en:["Stochastic Slow Strategy"],ro:["Stochastic Slow Strategy"]},e.exports["Volatility Stop_study"]={en:["Volatility Stop"],ro:["Volatility Stop"]},e.exports["Volty Expan Close Strategy_study"]={en:["Volty Expan Close Strategy"],ro:["Volty Expan Close Strategy"]},e.exports["Woodies CCI_study"]={en:["Woodies CCI"],ro:["Woodies CCI"]},e.exports["Anchored Volume Profile_study"]={en:["Anchored Volume Profile"],ro:["Anchored Volume Profile"]},e.exports["Trading Sessions_study"]={en:["Trading Sessions"],ro:["Trading Sessions"]},e.exports["Cup and Handle Chart Pattern_study"]={en:["Cup and Handle Chart Pattern"],ro:["Cup and Handle Chart Pattern"]},e.exports["Inverted Cup and Handle Chart Pattern_study"]={en:["Inverted Cup and Handle Chart Pattern"],ro:["Inverted Cup and Handle Chart Pattern"]},e.exports["Head and Shoulders Chart Pattern_study"]={en:["Head and Shoulders Chart Pattern"],ro:["Head and Shoulders Chart Pattern"]},e.exports["Inverted Head and Shoulders Chart Pattern_study"]={en:["Inverted Head and Shoulders Chart Pattern"],ro:["Inverted Head and Shoulders Chart Pattern"]}},89633:e=>{e.exports={en:["Anchored Volume Profile"],ro:["Anchored Volume Profile"]}},25705:e=>{e.exports={en:["Fixed Range Volume Profile"],ro:["Fixed Range Volume Profile"]}},24261:e=>{e.exports={en:["Vol"],ro:["Vol"]}},51077:e=>{e.exports={en:["Minor"],ro:["Minor"]}},922:e=>{e.exports={en:["Minute"],ro:["Minute"]}},91405:e=>{e.exports={en:["Text"],ro:["Text"]}},78972:e=>{e.exports={en:["Couldn't copy"],ro:["Couldn't copy"]}},10615:e=>{e.exports={en:["Couldn't cut"],ro:["Couldn't cut"]}},81518:e=>{e.exports={en:["Couldn't paste"],ro:["Couldn't paste"]}},83140:e=>{e.exports={en:["Countdown to bar close"],ro:["Countdown To Bar Close"]}},10871:e=>{e.exports={en:["Colombo"],ro:["Colombo"]}},55761:e=>{e.exports={en:["Columns"],ro:["Columns"]}},9818:e=>{e.exports={en:["Comment"],ro:["Comment"]}},53942:e=>{e.exports={en:["Compare or Add Symbol"],ro:["Compare or Add Symbol"]}},12086:e=>{e.exports={en:["Compilation error"],ro:["Compilation error"]}},48141:e=>{e.exports={en:["Confirm Inputs"],ro:["Confirm Inputs"]}},38917:e=>{e.exports={en:["Copenhagen"],ro:["Copenhagen"]}},49680:e=>{e.exports={en:["Copy"],ro:["Copy"]}},66134:e=>{e.exports={en:["Copy Chart Layout"],ro:["Copy Chart Layout"]}},63553:e=>{e.exports={en:["Copy price"],ro:["Copy price"]}},65736:e=>{
e.exports={en:["Cairo"],ro:["Cairo"]}},25381:e=>{e.exports={en:["Callout"],ro:["Callout"]}},45054:e=>{e.exports={en:["Candles"],ro:["Candles"]}},30948:e=>{e.exports={en:["Caracas"],ro:["Caracas"]}},70409:e=>{e.exports={en:["Casablanca"],ro:["Casablanca"]}},37276:e=>{e.exports={en:["Change"],ro:["Change"]}},85124:e=>{e.exports={en:["Change Symbol"],ro:["Change Symbol"]}},2569:e=>{e.exports={en:["Change interval"],ro:["Change interval"]}},9687:e=>{e.exports={en:["Change interval. Press number or comma"],ro:["Change interval. Press number or comma"]}},36332:e=>{e.exports={en:["Change symbol. Start typing symbol name"],ro:["Change symbol. Start typing symbol name"]}},48566:e=>{e.exports={en:["Change scale currency"],ro:["Change scale currency"]}},85110:e=>{e.exports={en:["Change scale unit"],ro:["Change scale unit"]}},56275:e=>{e.exports={en:["Chart #{index}"],ro:["Chart #{index}"]}},39950:e=>{e.exports={en:["Chart Properties"],ro:["Chart Properties"]}},98856:e=>{e.exports={en:["Chart by TradingView"],ro:["Chart by TradingView"]}},1136:e=>{e.exports={en:["Chart for {symbol}, {interval}"],ro:["Chart for {symbol}, {interval}"]}},69804:e=>{e.exports={en:["Chart image copied to clipboard {emoji}"],ro:["Chart image copied to clipboard {emoji}"]}},6655:e=>{e.exports={en:["Chart image embed code copied to clipboard {emoji}"],ro:["Chart image embed code copied to clipboard {emoji}"]}},36549:e=>{e.exports={en:["Chatham Islands"],ro:["Chatham Islands"]}},72452:e=>{e.exports={en:["Chicago"],ro:["Chicago"]}},50349:e=>{e.exports={en:["Chongqing"],ro:["Chongqing"]}},91944:e=>{e.exports={en:["Circle"],ro:["Circle"]}},14985:e=>{e.exports={en:["Click to set a point"],ro:["Click to set a point"]}},12537:e=>{e.exports={en:["Clone"],ro:["Clone"]}},62578:e=>{e.exports={en:["Close"],ro:["Close"]}},264:e=>{e.exports={en:["Create limit order"],ro:["Create Limit Order"]}},6969:e=>{e.exports={en:["Cross"],ro:["Cross"]}},74334:e=>{e.exports={en:["Cross Line"],ro:["Cross Line"]}},59396:e=>{e.exports={en:["Currencies"],ro:["Currencies"]}},20177:e=>{e.exports={en:["Current interval and above"],ro:["Current interval and above"]}},494:e=>{e.exports={en:["Current interval and below"],ro:["Current interval and below"]}},60668:e=>{e.exports={en:["Current interval only"],ro:["Current interval only"]}},78609:e=>{e.exports={en:["Curve"],ro:["Curve"]}},87380:e=>{e.exports={en:["Cycle"],ro:["Cycle"]}},84031:e=>{e.exports={en:["Cyclic Lines"],ro:["Cyclic Lines"]}},93191:e=>{e.exports={en:["Cypher Pattern"],ro:["Cypher Pattern"]}},7219:e=>{e.exports={en:["A layout with that name already exists"],ro:["A layout with that name already exists"]}},67635:e=>{e.exports={en:["A layout with that name already exists. Do you want to overwrite it?"],ro:["A layout with that name already exists. Do you want to overwrite it?"]}},46712:e=>{e.exports={en:["ABCD Pattern"],ro:["ABCD Pattern"]}},36485:e=>{e.exports={en:["Amsterdam"],ro:["Amsterdam"]}},42630:e=>{e.exports={en:["Anchorage"],ro:["Anchorage"]}},63209:e=>{e.exports={en:["Anchored Note"],ro:["Anchored Note"]}},42669:e=>{
e.exports={en:["Anchored Text"],ro:["Anchored Text"]}},84541:e=>{e.exports={en:["Anchored VWAP"],ro:["Anchored VWAP"]}},77401:e=>{e.exports={en:["Access error"],ro:["Access error"]}},46501:e=>{e.exports={en:["Add Symbol"],ro:["Add Symbol"]}},69709:e=>{e.exports={en:["Add alert on {title}"],ro:["Add Alert For {title}"]}},89295:e=>{e.exports={en:["Add alert on {title} at {price}"],ro:["Add alert on {title} at {price}"]}},2439:e=>{e.exports={en:["Add financial metric for {instrumentName}"],ro:["Add Financial metric for {instrumentName}"]}},35088:e=>{e.exports={en:["Add indicator/strategy on {studyTitle}"],ro:["Add Indicator/Strategy on {studyTitle}"]}},39873:e=>{e.exports={en:["Add text note for {symbol}"],ro:["Add Text Note for {symbol}"]}},35679:e=>{e.exports={en:["Add this financial metric to entire layout"],ro:["Add this Financial Metric to Entire Layout"]}},10996:e=>{e.exports={en:["Add this financial metric to favorites"],ro:["Add this financial metric to favorites"]}},26090:e=>{e.exports={en:["Add this indicator to entire layout"],ro:["Add this Indicator to Entire Layout"]}},92957:e=>{e.exports={en:["Add this indicator to favorites"],ro:["Add this indicator to favorites"]}},95754:e=>{e.exports={en:["Add this strategy to entire layout"],ro:["Add this Strategy to Entire Layout"]}},39010:e=>{e.exports={en:["Add this symbol to entire layout"],ro:["Add this Symbol to Entire Layout"]}},426:e=>{e.exports={en:["Adelaide"],ro:["Adelaide"]}},40452:e=>{e.exports={en:["Always invisible"],ro:["Always Invisible"]}},36299:e=>{e.exports={en:["Always visible"],ro:["Always Visible"]}},81442:e=>{e.exports={en:["All indicators and drawing tools"],ro:["All Indicators And Drawing Tools"]}},58026:e=>{e.exports={en:["All intervals"],ro:["All intervals"]}},78358:e=>{e.exports={en:["Apply default"],ro:["Apply Default"]}},22437:e=>{e.exports={en:["Apply these indicators to entire layout"],ro:["Apply these Indicators to Entire Layout"]}},27072:e=>{e.exports={en:["Apr"],ro:["Apr"]}},59324:e=>{e.exports={en:["Arc"],ro:["Arc"]}},34456:e=>{e.exports={en:["Area"],ro:["Area"]}},11858:e=>{e.exports={en:["Arrow"],ro:["Arrow"]}},34247:e=>{e.exports={en:["Arrow Down"],ro:["Arrow Down"]}},36352:e=>{e.exports={en:["Arrow Marker"],ro:["Arrow Marker"]}},73193:e=>{e.exports={en:["Arrow Mark Down"],ro:["Arrow Mark Down"]}},1949:e=>{e.exports={en:["Arrow Mark Left"],ro:["Arrow Mark Left"]}},86275:e=>{e.exports={en:["Arrow Mark Right"],ro:["Arrow Mark Right"]}},62453:e=>{e.exports={en:["Arrow Mark Up"],ro:["Arrow Mark Up"]}},77231:e=>{e.exports={en:["Arrow Up"],ro:["Arrow Up"]}},98128:e=>{e.exports={en:["Astana"],ro:["Astana"]}},63627:e=>{e.exports={en:["Ashgabat"],ro:["Ashkhabad"]}},72445:e=>{e.exports={en:["At close"],ro:["At close"]}},73702:e=>{e.exports={en:["Athens"],ro:["Athens"]}},21469:e=>{e.exports={en:["Auto"],ro:["Auto"]}},24157:e=>{e.exports={en:["Auto (fits data to screen)"],ro:["Auto (Fits Data To Screen)"]}},46450:e=>{e.exports={en:["Aug"],ro:["Aug"]}},21841:e=>{e.exports={en:["Average close price label"],ro:["Average close price label"]}},
16138:e=>{e.exports={en:["Average close price line"],ro:["Average close price line"]}},73025:e=>{e.exports={en:["Avg"],ro:["Avg"]}},73905:e=>{e.exports={en:["Bogota"],ro:["Bogota"]}},90594:e=>{e.exports={en:["Bahrain"],ro:["Bahrain"]}},70540:e=>{e.exports={en:["Balloon"],ro:["Balloon"]}},47045:e=>{e.exports={en:["Bangkok"],ro:["Bangkok"]}},76651:e=>{e.exports={en:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"],ro:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"]}},68054:e=>{e.exports={en:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"],ro:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"]}},27377:e=>{e.exports={en:["Bars"],ro:["Bars"]}},81994:e=>{e.exports={en:["Bars Pattern"],ro:["Bars Pattern"]}},59213:e=>{e.exports={en:["Baseline"],ro:["Baseline"]}},71797:e=>{e.exports={en:["Belgrade"],ro:["Belgrade"]}},64313:e=>{e.exports={en:["Berlin"],ro:["Berlin"]}},43539:e=>{e.exports={en:["Brush"],ro:["Brush"]}},91499:e=>{e.exports={en:["Brussels"],ro:["Brussels"]}},70876:e=>{e.exports={en:["Bratislava"],ro:["Bratislava"]}},55481:e=>{e.exports={en:["Bring forward"],ro:["Bring Forward"]}},17293:e=>{e.exports={en:["Bring to front"],ro:["Bring to Front"]}},79336:e=>{e.exports={en:["Brisbane"],ro:["Brisbane"]}},33672:e=>{e.exports={en:["Bucharest"],ro:["Bucharest"]}},20313:e=>{e.exports={en:["Budapest"],ro:["Budapest"]}},25282:e=>{e.exports={en:["Buenos Aires"],ro:["Buenos Aires"]}},46768:e=>{e.exports={en:["By TradingView"],ro:["By TradingView"]}},54280:e=>{e.exports={en:["Go to date"],ro:["Go to date"]}},74975:e=>{e.exports={en:["Go to {lineToolName}"],ro:["Go to {lineToolName}"]}},15462:e=>{e.exports={en:["Got it"],ro:["Got it"]}},47460:e=>{e.exports={en:["Gann Box"],ro:["Gann Box"]}},48683:e=>{e.exports={en:["Gann Fan"],ro:["Gann Fan"]}},44763:e=>{e.exports={en:["Gann Square"],ro:["Gann Square"]}},60707:e=>{e.exports={en:["Gann Square Fixed"],ro:["Gann Square Fixed"]}},46808:e=>{e.exports={en:["Ghost Feed"],ro:["Ghost Feed"]}},57726:e=>{e.exports={en:["Grand supercycle"],ro:["Grand Supercycle"]}},34914:e=>{e.exports={en:["Do you really want to delete Study Template '{name}' ?"],ro:["Do you really want to delete Study Template '{name}' ?"]}},77125:e=>{e.exports={en:["Double Curve"],ro:["Double Curve"]}},9430:e=>{e.exports={en:["Double-click any edge to reset layout grid"],ro:["Double-click any edge to reset layout grid"]}},75296:e=>{e.exports={en:["Double-click to finish Path"],ro:["Double-click to finish Path"]}},17409:e=>{e.exports={en:["Double-click to finish Polyline"],ro:["Double-click to finish Polyline"]}},57131:e=>{e.exports={en:["Data Provided by"],ro:["Data Provided by"]}},62154:e=>{e.exports={en:["Date"],ro:["Date"]}},85444:e=>{e.exports={en:["Date Range"],ro:["Date Range"]}},47017:e=>{e.exports={en:["Date and Price Range"],ro:["Date and Price Range"]}},32084:e=>{e.exports={en:["Dec"],ro:["Dec"]}},23403:e=>{e.exports={en:["Degree"],ro:["Degree"]}},27358:e=>{e.exports={
en:["Denver"],ro:["Denver"]}},24959:e=>{e.exports={en:["Dhaka"],ro:["Dhaka"]}},15179:e=>{e.exports={en:["Diamond"],ro:["Diamond"]}},91544:e=>{e.exports={en:["Disjoint Channel"],ro:["Disjoint Channel"]}},70132:e=>{e.exports={en:["Displacement"],ro:["Displacement"]}},93864:e=>{e.exports={en:["Drawings toolbar"],ro:["Drawings Toolbar"]}},96890:e=>{e.exports={en:["Draw Horizontal Line at"],ro:["Draw Horizontal Line on"]}},23650:e=>{e.exports={en:["Dubai"],ro:["Dubai"]}},79716:e=>{e.exports={en:["Dublin"],ro:["Dublin"]}},73456:e=>{e.exports={en:["Emoji"],ro:["Emoji"]}},9541:e=>{e.exports={en:["Enter a new chart layout name"],ro:["Enter a new chart layout name"]}},80943:e=>{e.exports={en:["Elliott Correction Wave (ABC)"],ro:["Elliott Correction Wave (ABC)"]}},75112:e=>{e.exports={en:["Elliott Double Combo Wave (WXY)"],ro:["Elliott Double Combo Wave (WXY)"]}},61114:e=>{e.exports={en:["Elliott Impulse Wave (12345)"],ro:["Elliott Impulse Wave (12345)"]}},72359:e=>{e.exports={en:["Elliott Triangle Wave (ABCDE)"],ro:["Elliott Triangle Wave (ABCDE)"]}},76129:e=>{e.exports={en:["Elliott Triple Combo Wave (WXYXZ)"],ro:["Elliott Triple Combo Wave (WXYXZ)"]}},78996:e=>{e.exports={en:["Ellipse"],ro:["Ellipse"]}},52788:e=>{e.exports={en:["Extended Line"],ro:["Extended Line"]}},86905:e=>{e.exports={en:["Exchange"],ro:["Exchange"]}},19271:e=>{e.exports={en:["Existing pane above"],ro:["Existing Pane Above"]}},46545:e=>{e.exports={en:["Existing pane below"],ro:["Existing Pane Below"]}},20138:e=>{e.exports={en:["Forecast"],ro:["Forecast"]}},2507:e=>{e.exports={en:["Feb"],ro:["Feb"]}},59005:e=>{e.exports={en:["Fib Channel"],ro:["Fib Channel"]}},82330:e=>{e.exports={en:["Fib Circles"],ro:["Fib Circles"]}},55986:e=>{e.exports={en:["Fib Retracement"],ro:["Fib Retracement"]}},33880:e=>{e.exports={en:["Fib Speed Resistance Arcs"],ro:["Fib Speed Resistance Arcs"]}},2395:e=>{e.exports={en:["Fib Speed Resistance Fan"],ro:["Fib Speed Resistance Fan"]}},39014:e=>{e.exports={en:["Fib Spiral"],ro:["Fib Spiral"]}},30622:e=>{e.exports={en:["Fib Time Zone"],ro:["Fib Time Zone"]}},85042:e=>{e.exports={en:["Fib Wedge"],ro:["Fib Wedge"]}},33885:e=>{e.exports={en:["Flag"],ro:["Flag"]}},14600:e=>{e.exports={en:["Flag Mark"],ro:["Flag Mark"]}},45051:e=>{e.exports={en:["Flat Top/Bottom"],ro:["Flat Top/Bottom"]}},63271:e=>{e.exports={en:["Flipped"],ro:["Flipped"]}},39643:e=>{e.exports={en:["Fraction part is invalid."],ro:["Fraction part is invalid."]}},24077:e=>{e.exports={en:["Fundamental studies are no longer available on charts"],ro:["Fundamental studies are no longer available on charts"]}},31561:e=>{e.exports={en:["Kolkata"],ro:["Kolkata"]}},54533:e=>{e.exports={en:["Kathmandu"],ro:["Kathmandu"]}},83490:e=>{e.exports={en:["Kagi"],ro:["Kagi"]}},70913:e=>{e.exports={en:["Karachi"],ro:["Karachi"]}},76614:e=>{e.exports={en:["Kuwait"],ro:["Kuwait"]}},38561:e=>{e.exports={en:["Kuala Lumpur"],ro:["Kuala Lumpur"]}},99906:e=>{e.exports={en:["HLC area"],ro:["HLC area"]}},34491:e=>{e.exports={en:["Ho Chi Minh"],ro:["Ho Chi Minh"]}},13459:e=>{e.exports={
en:["Hollow candles"],ro:["Hollow Candles"]}},48861:e=>{e.exports={en:["Hong Kong"],ro:["Hong Kong"]}},79668:e=>{e.exports={en:["Honolulu"],ro:["Honolulu"]}},21795:e=>{e.exports={en:["Horizontal Line"],ro:["Horizontal Line"]}},25487:e=>{e.exports={en:["Horizontal Ray"],ro:["Horizontal Ray"]}},21928:e=>{e.exports={en:["Head and Shoulders"],ro:["Head and Shoulders"]}},63876:e=>{e.exports={en:["Heikin Ashi"],ro:["Heikin Ashi"]}},48203:e=>{e.exports={en:["Helsinki"],ro:["Helsinki"]}},27298:e=>{e.exports={en:["Hide"],ro:["Hide"]}},47074:e=>{e.exports={en:["Hide all"],ro:["Hide all"]}},52563:e=>{e.exports={en:["Hide all drawings"],ro:["Hide all drawings"]}},90763:e=>{e.exports={en:["Hide all drawings and indicators"],ro:["Hide all drawings and indicators"]}},18216:e=>{e.exports={en:["Hide all drawings, indicators, positions & orders"],ro:["Hide all drawings, indicators, positions & orders"]}},78525:e=>{e.exports={en:["Hide all indicators"],ro:["Hide all indicators"]}},42164:e=>{e.exports={en:["Hide all positions & orders"],ro:["Hide all positions & orders"]}},3217:e=>{e.exports={en:["Hide drawings"],ro:["Hide drawings"]}},97878:e=>{e.exports={en:["Hide events on chart"],ro:["Hide Events on Chart"]}},72351:e=>{e.exports={en:["Hide indicators"],ro:["Hide indicators"]}},28345:e=>{e.exports={en:["Hide marks on bars"],ro:["Hide Marks On Bars"]}},92226:e=>{e.exports={en:["Hide positions & orders"],ro:["Hide positions & orders"]}},78254:e=>{e.exports={en:["High"],ro:["High"]}},98236:e=>{e.exports={en:["High-low"],ro:["High-Low"]}},99479:e=>{e.exports={en:["High and low price labels"],ro:["High and low price labels"]}},33766:e=>{e.exports={en:["High and low price lines"],ro:["High and low price lines"]}},69476:e=>{e.exports={en:["Highlighter"],ro:["Highlighter"]}},72819:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],ro:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},94966:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."],ro:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."]}},66751:e=>{e.exports={en:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],ro:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},68065:e=>{e.exports={en:["Image"],ro:["Image"]}},80185:e=>{e.exports={en:["Intervals less than {resolution} are not supported for {ticker}."],ro:["Intervals less than {resolution} are not supported for {ticker}."]}},10268:e=>{e.exports={en:["Intermediate"],ro:["Intermediate"]}},14285:e=>{e.exports={en:["Invalid Symbol"],ro:["Invalid Symbol"]}},52969:e=>{e.exports={en:["Invalid symbol"],ro:["Invalid symbol"]}},37189:e=>{e.exports={en:["Invert scale"],ro:["Invert Scale"]}},89999:e=>{e.exports={
en:["Indexed to 100"],ro:["Indexed to 100"]}},46850:e=>{e.exports={en:["Indicators value labels"],ro:["Indicators value labels"]}},54418:e=>{e.exports={en:["Indicators name labels"],ro:["Indicators name labels"]}},40490:e=>{e.exports={en:["Indicators, Metrics and Strategies. Press slash"],ro:["Indicators, Metrics and Strategies. Press slash"]}},15992:e=>{e.exports={en:["Info Line"],ro:["Info Line"]}},87829:e=>{e.exports={en:["Insert indicator"],ro:["Insert Indicator"]}},91612:e=>{e.exports={en:["Inside"],ro:["Inside"]}},41686:e=>{e.exports={en:["Inside Pitchfork"],ro:["Inside Pitchfork"]}},37913:e=>{e.exports={en:["Icon"],ro:["Icon"]}},78326:e=>{e.exports={en:["Istanbul"],ro:["Istanbul"]}},39585:e=>{e.exports={en:["Johannesburg"],ro:["Johannesburg"]}},14995:e=>{e.exports={en:["Jakarta"],ro:["Jakarta"]}},62310:e=>{e.exports={en:["Jan"],ro:["Jan"]}},36057:e=>{e.exports={en:["Jerusalem"],ro:["Jerusalem"]}},53786:e=>{e.exports={en:["Jul"],ro:["Jul"]}},429:e=>{e.exports={en:["Jun"],ro:["Jun"]}},67560:e=>{e.exports={en:["Juneau"],ro:["Juneau"]}},62329:e=>{e.exports={en:["On the left"],ro:["On The Left"]}},55813:e=>{e.exports={en:["On the right"],ro:["On The Right"]}},64818:e=>{e.exports={en:["Only {availableResolutions} intervals are supported for {ticker}."],ro:["Only {availableResolutions} intervals are supported for {ticker}."]}},21064:e=>{e.exports={en:["Oops!"],ro:["Oops!"]}},51221:e=>{e.exports={en:["Object Tree"],ro:["Object Tree"]}},12179:e=>{e.exports={en:["Oct"],ro:["Oct"]}},16610:e=>{e.exports={en:["Open"],ro:["Open"]}},46005:e=>{e.exports={en:["Original"],ro:["Original"]}},75722:e=>{e.exports={en:["Oslo"],ro:["Oslo"]}},65318:e=>{e.exports={en:["Low"],ro:["Low"]}},55382:e=>{e.exports={en:["Load layout. Press period"],ro:["Load layout. Press period"]}},5837:e=>{e.exports={en:["Lock"],ro:["Lock"]}},79777:e=>{e.exports={en:["Lock/unlock"],ro:["Lock/Unlock"]}},66005:e=>{e.exports={en:["Lock vertical cursor line by time"],ro:["Lock vertical cursor line by time"]}},14017:e=>{e.exports={en:["Lock price to bar ratio"],ro:["Lock Price To Bar Ratio"]}},16170:e=>{e.exports={en:["Logarithmic"],ro:["Logarithmic"]}},19439:e=>{e.exports={en:["London"],ro:["London"]}},74832:e=>{e.exports={en:["Long Position"],ro:["Long Position"]}},28733:e=>{e.exports={en:["Los Angeles"],ro:["Los Angeles"]}},85924:e=>{e.exports={en:["Label Down"],ro:["Label Down"]}},52402:e=>{e.exports={en:["Label Up"],ro:["Label Up"]}},5119:e=>{e.exports={en:["Labels"],ro:["Labels"]}},19931:e=>{e.exports={en:["Lagos"],ro:["Lagos"]}},63815:e=>{e.exports={en:["Last day change"],ro:["Last day change"]}},59444:e=>{e.exports={en:["Lima"],ro:["Lima"]}},3554:e=>{e.exports={en:["Line"],ro:["Line"]}},9394:e=>{e.exports={en:["Line with markers"],ro:["With Markers"]}},43588:e=>{e.exports={en:["Line break"],ro:["Line Break"]}},56982:e=>{e.exports={en:["Lines"],ro:["Lines"]}},37367:e=>{e.exports={en:["Link to the chart image copied to clipboard {emoji}"],ro:["Link to the chart image copied to clipboard {emoji}"]}},53375:e=>{e.exports={en:["Lisbon"],ro:["Lisbon"]}},
81038:e=>{e.exports={en:["Luxembourg"],ro:["Luxembourg"]}},60663:e=>{e.exports={en:["Move the point to position the anchor then tap to place"],ro:["Move the point to position the anchor then tap to place"]}},35049:e=>{e.exports={en:["Move to"],ro:["Move to"]}},26493:e=>{e.exports={en:["Move scale to left"],ro:["Move Scale To Left"]}},40789:e=>{e.exports={en:["Move scale to right"],ro:["Move Scale To Right"]}},70382:e=>{e.exports={en:["Modified Schiff"],ro:["Modified Schiff"]}},57681:e=>{e.exports={en:["Modified Schiff Pitchfork"],ro:["Modified Schiff Pitchfork"]}},93907:e=>{e.exports={en:["More settings"]}},64039:e=>{e.exports={en:["Moscow"],ro:["Moscow"]}},52066:e=>{e.exports={en:["Madrid"],ro:["Madrid"]}},38365:e=>{e.exports={en:["Malta"],ro:["Malta"]}},48991:e=>{e.exports={en:["Manila"],ro:["Manila"]}},92767:e=>{e.exports={en:["Mar"],ro:["Mar"]}},73332:e=>{e.exports={en:["Mexico City"],ro:["Mexico City"]}},88314:e=>{e.exports={en:["Merge all scales into one"],ro:["Merge All Scales Into One"]}},54215:e=>{e.exports={en:["Mixed"],ro:["Mixed"]}},24866:e=>{e.exports={en:["Micro"],ro:["Micro"]}},87957:e=>{e.exports={en:["Millennium"],ro:["Millennium"]}},14724:e=>{e.exports={en:["Minuette"],ro:["Minuette"]}},78273:e=>{e.exports={en:["Minuscule"],ro:["Minuscule"]}},28941:e=>{e.exports={en:["Mirrored"],ro:["Mirrored"]}},9865:e=>{e.exports={en:["Muscat"],ro:["Muscat"]}},96935:e=>{e.exports={en:["N/A"],ro:["N/A"]}},36252:e=>{e.exports={en:["No data here"],ro:["No data here"]}},11254:e=>{e.exports={en:["No scale (fullscreen)"],ro:["No Scale (Fullscreen)"]}},9140:e=>{e.exports={en:["No sync"],ro:["No sync"]}},50910:e=>{e.exports={en:["No volume data"],ro:["No volume data"]}},94389:e=>{e.exports={en:["Note"],ro:["Note"]}},26899:e=>{e.exports={en:["Nov"],ro:["Nov"]}},67891:e=>{e.exports={en:["Norfolk Island"],ro:["Norfolk Island"]}},40977:e=>{e.exports={en:["Nairobi"],ro:["Nairobi"]}},40544:e=>{e.exports={en:["New York"],ro:["New York"]}},66103:e=>{e.exports={en:["New Zealand"],ro:["New Zealand"]}},15512:e=>{e.exports={en:["New pane above"],ro:["New pane above"]}},52160:e=>{e.exports={en:["New pane below"],ro:["New pane below"]}},94600:e=>{e.exports={en:["Nicosia"],ro:["Nicosia"]}},73013:e=>{e.exports={en:["Something went wrong"],ro:["Something went wrong"]}},4509:e=>{e.exports={en:["Something went wrong. Please try again later."],ro:["Something went wrong. Please try again later."]}},43047:e=>{e.exports={en:["Save New Chart Layout"],ro:["Save New Chart Layout"]}},76266:e=>{e.exports={en:["Save as"],ro:["Save As"]}},55502:e=>{e.exports={en:["San Salvador"],ro:["San Salvador"]}},30231:e=>{e.exports={en:["Santiago"],ro:["Santiago"]}},91912:e=>{e.exports={en:["Sao Paulo"],ro:["Sao Paulo"]}},43931:e=>{e.exports={en:["Scale currency"],ro:["Scale currency"]}},43758:e=>{e.exports={en:["Scale price chart only"],ro:["Scale Price Chart Only"]}},40012:e=>{e.exports={en:["Scale unit"],ro:["Scale unit"]}},69904:e=>{e.exports={en:["Schiff"],ro:["Schiff"]}},42608:e=>{e.exports={en:["Schiff Pitchfork"],ro:["Schiff Pitchfork"]}},76078:e=>{
e.exports={en:["Script may be not updated if you leave the page."],ro:["Script may be not updated if you leave the page."]}},32514:e=>{e.exports={en:["Settings"],ro:["Settings"]}},70784:e=>{e.exports={en:["Second fraction part is invalid."],ro:["Second fraction part is invalid."]}},75594:e=>{e.exports={en:["Security info"],ro:["Security info"]}},21973:e=>{e.exports={en:["Send to back"],ro:["Send to Back"]}},71179:e=>{e.exports={en:["Send backward"],ro:["Send Backward"]}},26820:e=>{e.exports={en:["Seoul"],ro:["Seoul"]}},6816:e=>{e.exports={en:["Sep"],ro:["Sep"]}},94031:e=>{e.exports={en:["Session"],ro:["Session"]}},83298:e=>{e.exports={en:["Session volume profile"],ro:["Session volume profile"]}},66707:e=>{e.exports={en:["Session breaks"],ro:["Session Breaks"]}},1852:e=>{e.exports={en:["Shanghai"],ro:["Shanghai"]}},8075:e=>{e.exports={en:["Short Position"],ro:["Short Position"]}},98334:e=>{e.exports={en:["Show"],ro:["Show"]}},85891:e=>{e.exports={en:["Show all drawings"],ro:["Show all drawings"]}},25881:e=>{e.exports={en:["Show all drawings and indicators"],ro:["Show all drawings and indicators"]}},86738:e=>{e.exports={en:["Show all drawings, indicators, positions & orders"],ro:["Show all drawings, indicators, positions & orders"]}},98753:e=>{e.exports={en:["Show all indicators"],ro:["Show all indicators"]}},55418:e=>{e.exports={en:["Show all ideas"],ro:["Show All Ideas"]}},20506:e=>{e.exports={en:["Show all positions & orders"],ro:["Show all positions & orders"]}},33158:e=>{e.exports={en:["Show continuous contract switch"],ro:["Show continuous contract switch"]}},81465:e=>{e.exports={en:["Show contract expiration"],ro:["Show contract expiration"]}},29449:e=>{e.exports={en:["Show dividends"],ro:["Show Dividends"]}},37113:e=>{e.exports={en:["Show earnings"],ro:["Show Earnings"]}},10261:e=>{e.exports={en:["Show ideas of followed users"],ro:["Show Ideas of Followed Users"]}},68112:e=>{e.exports={en:["Show latest news and Minds"],ro:["Show latest news and Minds"]}},44020:e=>{e.exports={en:["Show my ideas only"],ro:["Show My Ideas Only"]}},50849:e=>{e.exports={en:["Show splits"],ro:["Show Splits"]}},67751:e=>{e.exports={en:["Signpost"],ro:["Signpost"]}},77377:e=>{e.exports={en:["Singapore"],ro:["Singapore"]}},39090:e=>{e.exports={en:["Sine Line"],ro:["Sine Line"]}},66205:e=>{e.exports={en:["Square"],ro:["Square"]}},86146:e=>{e.exports={en:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."],ro:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."]}},92516:e=>{e.exports={en:["Style"],ro:["Style"]}},61507:e=>{e.exports={en:["Stack on the left"],ro:["Stack On The Left"]}},97800:e=>{e.exports={en:["Stack on the right"],ro:["Stack On The Right"]}},85166:e=>{e.exports={en:["Start using keyboard navigation mode. Press {shortcut}"],ro:["Start using keyboard navigation mode. Press {shortcut}"]}},4035:e=>{e.exports={en:["Stay in drawing mode"],ro:["Stay in Drawing Mode"]}},69217:e=>{e.exports={en:["Step line"],ro:["Step Line"]}},43114:e=>{e.exports={en:["Sticker"],
ro:["Sticker"]}},86716:e=>{e.exports={en:["Stockholm"],ro:["Stockholm"]}},1145:e=>{e.exports={en:["Submicro"],ro:["Submicro"]}},63375:e=>{e.exports={en:["Submillennium"],ro:["Submillennium"]}},30585:e=>{e.exports={en:["Subminuette"],ro:["Subminuette"]}},67948:e=>{e.exports={en:["Supercycle"],ro:["Supercycle"]}},3348:e=>{e.exports={en:["Supermillennium"],ro:["Supermillennium"]}},18905:e=>{e.exports={en:["Switch to {resolution}"],ro:["Switch to {resolution}"]}},31622:e=>{e.exports={en:["Sydney"],ro:["Sydney"]}},70963:e=>{e.exports={en:["Symbol Error"],ro:["Symbol Error"]}},32390:e=>{e.exports={en:["Symbol name label"],ro:["Symbol Name Label"]}},10127:e=>{e.exports={en:["Symbol last price label"],ro:["Symbol Last Value Label"]}},39079:e=>{e.exports={en:["Sync globally"],ro:["Sync globally"]}},46607:e=>{e.exports={en:["Sync in layout"],ro:["Sync To All Charts"]}},76519:e=>{e.exports={en:["Point & figure"],ro:["Point & Figure"]}},39949:e=>{e.exports={en:["Polyline"],ro:["Polyline"]}},371:e=>{e.exports={en:["Path"],ro:["Path"]}},59256:e=>{e.exports={en:["Parallel Channel"],ro:["Parallel Channel"]}},61879:e=>{e.exports={en:["Paris"],ro:["Paris"]}},35140:e=>{e.exports={en:["Paste"],ro:["Paste"]}},6919:e=>{e.exports={en:["Percent"],ro:["Percent"]}},24436:e=>{e.exports={en:["Perth"],ro:["Perth"]}},14055:e=>{e.exports={en:["Phoenix"],ro:["Phoenix"]}},34156:e=>{e.exports={en:["Pitchfan"],ro:["Pitchfan"]}},19634:e=>{e.exports={en:["Pitchfork"],ro:["Pitchfork"]}},33110:e=>{e.exports={en:["Pin to new left scale"],ro:["Pin To New Left Scale"]}},28280:e=>{e.exports={en:["Pin to new right scale"],ro:["Pin To New Right Scale"]}},14115:e=>{e.exports={en:["Pin to left scale"],ro:["Pin To Left Scale"]}},72046:e=>{e.exports={en:["Pin to left scale (hidden)"],ro:["Pin To Left Scale (Hidden)"]}},81054:e=>{e.exports={en:["Pin to right scale"],ro:["Pin to right scale"]}},16986:e=>{e.exports={en:["Pin to right scale (hidden)"],ro:["Pin To Right Scale (Hidden)"]}},60035:e=>{e.exports={en:["Pin to scale (now left)"],ro:["Pin To Scale (Now Left)"]}},94210:e=>{e.exports={en:["Pin to scale (now no scale)"],ro:["Pin To Scale (Now No Scale)"]}},10761:e=>{e.exports={en:["Pin to scale (now right)"],ro:["Pin To Scale (Now Right)"]}},76150:e=>{e.exports={en:["Pin to scale (now {label})"],ro:["Pin To Scale (Now {label})"]}},29436:e=>{e.exports={en:["Pin to scale {label}"],ro:["Pin To Scale {label}"]}},2165:e=>{e.exports={en:["Pin to scale {label} (hidden)"],ro:["Pin To Scale {label} (Hidden)"]}},90095:e=>{e.exports={en:["Pinned to left scale"],ro:["Pinned To Left Scale"]}},32538:e=>{e.exports={en:["Pinned to left scale (hidden)"],ro:["Pinned To Left Scale (Hidden)"]}},44579:e=>{e.exports={en:["Pinned to right scale"],ro:["Pinned To Right Scale"]}},94559:e=>{e.exports={en:["Pinned to right scale (hidden)"],ro:["Pin To Right Scale (Hidden)"]}},12645:e=>{e.exports={en:["Pinned to scale {label}"],ro:["Pinned To Scale {label}"]}},3564:e=>{e.exports={en:["Pinned to scale {label} (hidden)"],ro:["Pinned To Scale {label} (Hidden)"]}},71566:e=>{e.exports={
en:["Plus button"],ro:["Plus Button"]}},28298:e=>{e.exports={en:["Please give us a clipboard writing permission in your browser or press {keystroke}"],ro:["Please give us a clipboard writing permission in your browser or press {keystroke}"]}},81248:e=>{e.exports={en:["Prague"],ro:["Prague"]}},81712:e=>{e.exports={en:["Press and hold {key} while zooming to maintain the chart position"],ro:["Press and hold {key} while zooming to maintain the chart position"]}},91282:e=>{e.exports={en:["Price Label"],ro:["Price Label"]}},97512:e=>{e.exports={en:["Price Note"],ro:["Price Note"]}},68941:e=>{e.exports={en:["Price Range"],ro:["Price Range"]}},66123:e=>{e.exports={en:["Price format is invalid."],ro:["Price format is invalid."]}},72926:e=>{e.exports={en:["Price line"],ro:["Price Line"]}},59189:e=>{e.exports={en:["Primary"],ro:["Primary"]}},75747:e=>{e.exports={en:["Projection"],ro:["Projection"]}},55801:e=>{e.exports={en:["Published on {customer}, {date}"],ro:["Published on {customer}, {date}"]}},28756:e=>{e.exports={en:["Qatar"],ro:["Qatar"]}},57959:e=>{e.exports={en:["Quick search. Press {shortcut}"],ro:["Quick search. Press {shortcut}"]}},56820:e=>{e.exports={en:["Rotated Rectangle"],ro:["Rotated Rectangle"]}},52961:e=>{e.exports={en:["Rome"],ro:["Rome"]}},50318:e=>{e.exports={en:["Ray"],ro:["Ray"]}},55169:e=>{e.exports={en:["Range"],ro:["Range"]}},13386:e=>{e.exports={en:["Reykjavik"],ro:["Reykjavik"]}},26001:e=>{e.exports={en:["Rectangle"],ro:["Rectangle"]}},48236:e=>{e.exports={en:["Redo"],ro:["Redo"]}},2460:e=>{e.exports={en:["Regression Trend"],ro:["Regression Trend"]}},67410:e=>{e.exports={en:["Remove"],ro:["Remove"]}},96374:e=>{e.exports={en:["Remove drawings"],ro:["Remove Drawings"]}},99984:e=>{e.exports={en:["Remove indicators"],ro:["Remove Indicators"]}},3061:e=>{e.exports={en:["Remove this financial metric from favorites"],ro:["Remove this financial metric from favorites"]}},58764:e=>{e.exports={en:["Remove this indicator from favorites"],ro:["Remove this indicator from favorites"]}},22584:e=>{e.exports={en:["Rename Chart Layout"],ro:["Rename Chart Layout"]}},88130:e=>{e.exports={en:["Renko"],ro:["Renko"]}},75246:e=>{e.exports={en:["Reset chart view"],ro:["Reset chart view"]}},88853:e=>{e.exports={en:["Reset points"],ro:["Reset points"]}},15332:e=>{e.exports={en:["Reset price scale"],ro:["Reset Price Scale"]}},54170:e=>{e.exports={en:["Reset time scale"],ro:["Reset Time Scale"]}},37974:e=>{e.exports={en:["Riyadh"],ro:["Riyadh"]}},94022:e=>{e.exports={en:["Riga"],ro:["Riga"]}},60630:e=>{e.exports={en:["Runtime error"],ro:["Runtime error"]}},66719:e=>{e.exports={en:["Warning"],ro:["Warning"]}},5959:e=>{e.exports={en:["Warsaw"],ro:["Warsaw"]}},94465:e=>{e.exports={en:["Toggle auto scale"],ro:["Toggle auto scale"]}},46992:e=>{e.exports={en:["Toggle log scale"],ro:["Toggle log scale"]}},98549:e=>{e.exports={en:["Tokelau"],ro:["Tokelau"]}},69122:e=>{e.exports={en:["Tokyo"],ro:["Tokyo"]}},10095:e=>{e.exports={en:["Toronto"],ro:["Toronto"]}},11034:e=>{e.exports={en:["Taipei"],ro:["Taipei"]}},79995:e=>{e.exports={
en:["Tallinn"],ro:["Tallinn"]}},6686:e=>{e.exports={en:["Tehran"],ro:["Tehran"]}},93553:e=>{e.exports={en:["Template"],ro:["Template"]}},81657:e=>{e.exports={en:["The data vendor doesn't provide volume data for this symbol."],ro:["The data vendor doesn't provide volume data for this symbol."]}},49947:e=>{e.exports={en:["The publication preview could not be loaded. Please disable your browser extensions and try again."],ro:["The publication preview could not be loaded. Please disable your browser extensions and try again."]}},59519:e=>{e.exports={en:["This indicator cannot be applied to another indicator."],ro:["This indicator cannot be applied to another indicator."]}},18260:e=>{e.exports={en:["This script contains an error. Please contact its author."],ro:["This script contains an error. Please contact its author."]}},76989:e=>{e.exports={en:["This script is invite-only. To request access, please contact its author."],ro:["This script is invite-only. To request access, please contact its author."]}},47773:e=>{e.exports={en:["This symbol is only available on {linkStart}TradingView{linkEnd}."],ro:["The symbol available only on {linkStart}TradingView{linkEnd}."]}},46982:e=>{e.exports={en:["Three Drives Pattern"],ro:["Three Drives Pattern"]}},24821:e=>{e.exports={en:["Ticks"],ro:["Ticks"]}},80254:e=>{e.exports={en:["Tick-based intervals are not available for {ticker}."],ro:["Tick-based intervals are not available for {ticker}."]}},12806:e=>{e.exports={en:["Time"],ro:["Time"]}},20909:e=>{e.exports={en:["Time zone"],ro:["Time Zone"]}},46852:e=>{e.exports={en:["Time Cycles"],ro:["Time Cycles"]}},17809:e=>{e.exports={en:["Time Price Opportunity"],ro:["Time Price Opportunity"]}},66823:e=>{e.exports={en:["Trade"],ro:["Trade"]}},7697:e=>{e.exports={en:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"],ro:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"]}},35757:e=>{e.exports={en:["Trend Angle"],ro:["Trend Angle"]}},97339:e=>{e.exports={en:["Trend Line"],ro:["Trend Line"]}},80583:e=>{e.exports={en:["Trend-Based Fib Extension"],ro:["Trend-Based Fib Extension"]}},72159:e=>{e.exports={en:["Trend-Based Fib Time"],ro:["Trend-Based Fib Time"]}},1671:e=>{e.exports={en:["Triangle"],ro:["Triangle"]}},76152:e=>{e.exports={en:["Triangle Down"],ro:["Triangle Down"]}},90148:e=>{e.exports={en:["Triangle Pattern"],ro:["Triangle Pattern"]}},21236:e=>{e.exports={en:["Triangle Up"],ro:["Triangle Up"]}},21007:e=>{e.exports={en:["Tunis"],ro:["Tunis"]}},1833:e=>{e.exports={en:["UTC"],ro:["UTC"]}},14804:e=>{e.exports={en:["Undo"],ro:["Undo"]}},15432:e=>{e.exports={en:["Units"],ro:["Units"]}},11768:e=>{e.exports={en:["Unknown error"],ro:["Unknown error"]}},99894:e=>{e.exports={en:["Unlock"],ro:["Unlock"]}},75546:e=>{e.exports={en:["Unsupported interval"],ro:["Unsupported interval"]}},8580:e=>{e.exports={en:["User-defined error"],
ro:["User-defined error"]}},40693:e=>{e.exports={en:["Volume Profile Fixed Range"],ro:["Volume Profile Fixed Range"]}},39903:e=>{e.exports={en:["Volume Profile indicator available only on our upgraded plans."],ro:["Volume Profile indicator available only on our upgraded plans."]}},93722:e=>{e.exports={en:["Volume candles"],ro:["Volume candles"]}},69156:e=>{e.exports={en:["Volume data is not provided in BIST MIXED data plan."],ro:["Volume data is not provided in BIST MIXED data plan."]}},92763:e=>{e.exports={en:["Volume footprint"],ro:["Volume footprint"]}},32838:e=>{e.exports={en:["Vancouver"],ro:["Vancouver"]}},29535:e=>{e.exports={en:["Vertical Line"],ro:["Vertical Line"]}},23160:e=>{e.exports={en:["Vienna"],ro:["Vienna"]}},60534:e=>{e.exports={en:["Vilnius"],ro:["Vilnius"]}},40091:e=>{e.exports={en:["Visibility"],ro:["Visibility"]}},54853:e=>{e.exports={en:["Visibility on intervals"],ro:["Visibility on intervals"]}},10309:e=>{e.exports={en:["Visible on mouse over"],ro:["Visible on Mouse Over"]}},4077:e=>{e.exports={en:["Visual order"],ro:["Visual Order"]}},11316:e=>{e.exports={en:["X Cross"],ro:["X Cross"]}},42231:e=>{e.exports={en:["XABCD Pattern"],ro:["XABCD Pattern"]}},25059:e=>{e.exports={en:["You cannot see this pivot timeframe on this resolution"],ro:["You cannot see this pivot timeframe on this resolution"]}},53168:e=>{e.exports={en:["Yangon"],ro:["Yangon"]}},62859:e=>{e.exports={en:["Zurich"],ro:["Zurich"]}},47977:e=>{e.exports={en:["change Elliott degree"],ro:["change Elliott degree"]}},61557:e=>{e.exports={en:["change no overlapping labels"],ro:["change no overlapping labels"]}},76852:e=>{e.exports={en:["change average close price label visibility"],ro:["change average close price label visibility"]}},1022:e=>{e.exports={en:["change average close price line visibility"],ro:["change average close price line visibility"]}},69362:e=>{e.exports={en:["change bid and ask labels visibility"],ro:["change bid and ask labels visibility"]}},52919:e=>{e.exports={en:["change bid and ask lines visibility"],ro:["change bid and ask lines visibility"]}},32302:e=>{e.exports={en:["change currency"],ro:["change currency"]}},68846:e=>{e.exports={en:["change chart layout to {title}"],ro:["change chart layout to {title}"]}},18867:e=>{e.exports={en:["change continuous contract switch visibility"],ro:["change continuous contract switch visibility"]}},39383:e=>{e.exports={en:["change countdown to bar close visibility"],ro:["change countdown to bar close visibility"]}},16979:e=>{e.exports={en:["change date range"],ro:["change date range"]}},53929:e=>{e.exports={en:["change dividends visibility"],ro:["change dividends visibility"]}},6119:e=>{e.exports={en:["change events visibility on chart"],ro:["change events visibility on chart"]}},6819:e=>{e.exports={en:["change earnings visibility"],ro:["change earnings visibility"]}},85532:e=>{e.exports={en:["change futures contract expiration visibility"],ro:["change futures contract expiration visibility"]}},24226:e=>{e.exports={en:["change high and low price labels visibility"],
ro:["change high and low price labels visibility"]}},80692:e=>{e.exports={en:["change high and low price lines visibility"],ro:["change high and low price lines visibility"]}},24893:e=>{e.exports={en:["change indicators name labels visibility"],ro:["change indicators name labels visibility"]}},64729:e=>{e.exports={en:["change indicators value labels visibility"],ro:["change indicators value labels visibility"]}},50243:e=>{e.exports={en:["change latest news and Minds visibility"],ro:["change latest news and Minds visibility"]}},88849:e=>{e.exports={en:["change linking group"],ro:["change linking group"]}},14691:e=>{e.exports={en:["change pane height"],ro:["change pane height"]}},96379:e=>{e.exports={en:["change plus button visibility"],ro:["change plus button visibility"]}},30870:e=>{e.exports={en:["change pre/post market price label visibility"],ro:["change pre/post market price label visibility"]}},11718:e=>{e.exports={en:["change pre/post market price line visibility"],ro:["change pre/post market price line visibility"]}},58419:e=>{e.exports={en:["change previous close price line visibility"],ro:["change previous close price line visibility"]}},8662:e=>{e.exports={en:["change price line visibility"],ro:["Change Price Line"]}},2509:e=>{e.exports={en:["change price to bar ratio"],ro:["change price to bar ratio"]}},32829:e=>{e.exports={en:["change resolution"],ro:["Change Resolution"]}},35400:e=>{e.exports={en:["change symbol"],ro:["Change symbol"]}},73357:e=>{e.exports={en:["change symbol labels visibility"],ro:["change symbol labels visibility"]}},67453:e=>{e.exports={en:["change symbol last value visibility"],ro:["change symbol last value visibility"]}},4729:e=>{e.exports={en:["change symbol previous close value visibility"],ro:["change symbol previous close value visibility"]}},87041:e=>{e.exports={en:["change session"],ro:["change session"]}},38413:e=>{e.exports={en:["change session breaks visibility"],ro:["change session breaks visibility"]}},49965:e=>{e.exports={en:["change series style"],ro:["change series style"]}},47474:e=>{e.exports={en:["change splits visibility"],ro:["change splits visibility"]}},20137:e=>{e.exports={en:["change timezone"],ro:["change timezone"]}},85975:e=>{e.exports={en:["change unit"],ro:["change unit"]}},1924:e=>{e.exports={en:["change visibility"],ro:["Change Visibility"]}},84331:e=>{e.exports={en:["change visibility at current interval"],ro:["change visibility at current interval"]}},45800:e=>{e.exports={en:["change visibility at current interval and above"],ro:["change visibility at current interval and above"]}},75645:e=>{e.exports={en:["change visibility at current interval and below"],ro:["change visibility at current interval and below"]}},57916:e=>{e.exports={en:["change visibility at all intervals"],ro:["change visibility at all intervals"]}},98463:e=>{e.exports={en:["change {title} style"],ro:["change {title} style"]}},57122:e=>{e.exports={en:["change {title} text"],ro:["change {title} text"]}},63058:e=>{e.exports={en:["change {pointIndex} point"],
ro:["change {pointIndex} point"]}},94566:e=>{e.exports={en:["charts by TradingView"],ro:["charts by TradingView"]}},32943:e=>{e.exports={en:["clone line tools"],ro:["Clone line tools"]}},46219:e=>{e.exports={en:["create line tools group"],ro:["Create line tools group"]}},95394:e=>{e.exports={en:["create line tools group from selection"],ro:["Create line tools group from selection"]}},12898:e=>{e.exports={en:["create {tool}"],ro:["create {tool}"]}},94227:e=>{e.exports={en:["cut sources"],ro:["cut sources"]}},11500:e=>{e.exports={en:["cut {title}"],ro:["cut {title}"]}},12570:e=>{e.exports={en:["add line tool {lineTool} to group {name}"],ro:["Add line tool {lineTool} to group {name}"]}},21162:e=>{e.exports={en:["add line tool(s) to group {group}"],ro:["add line tool(s) to group {group}"]}},67608:e=>{e.exports={en:["add this financial metric to entire layout"],ro:["Add this Financial Metric to Entire Layout"]}},96677:e=>{e.exports={en:["add this indicator to entire layout"],ro:["Add this Indicator to Entire Layout"]}},58156:e=>{e.exports={en:["add this strategy to entire layout"],ro:["Add this Strategy to Entire Layout"]}},79290:e=>{e.exports={en:["add this symbol to entire layout"],ro:["Add this Symbol to Entire Layout"]}},68231:e=>{e.exports={en:["apply chart theme"],ro:["apply chart theme"]}},99551:e=>{e.exports={en:["apply all chart properties"],ro:["apply all chart properties"]}},89720:e=>{e.exports={en:["apply drawing template"],ro:["Apply Drawing Template"]}},27851:e=>{e.exports={en:["apply factory defaults to selected sources"],ro:["apply factory defaults to selected sources"]}},70507:e=>{e.exports={en:["apply indicators to entire layout"],ro:["apply indicators to entire layout"]}},69604:e=>{e.exports={en:["apply study template {template}"],ro:["Apply study template {template}"]}},86708:e=>{e.exports={en:["apply toolbars theme"],ro:["apply toolbars theme"]}},1979:e=>{e.exports={en:["bring group {title} forward"],ro:["bring group {title} forward"]}},53159:e=>{e.exports={en:["bring {title} to front"],ro:["bring {title} to front"]}},41966:e=>{e.exports={en:["bring {title} forward"],ro:["Bring {title} forward"]}},44676:e=>{e.exports={en:["by TradingView"],ro:["by TradingView"]}},58850:e=>{e.exports={en:["date range lock"],ro:["date range lock"]}},35111:e=>{e.exports={en:["erase level line"],ro:["erase level line"]}},99395:e=>{e.exports={en:["exclude line tools from group {group}"],ro:["Exclude line tools from group {group}"]}},82349:e=>{e.exports={en:["flip bars pattern"],ro:["flip bars pattern"]}},13017:e=>{e.exports={en:["hide {title}"],ro:["Hide {title}"]}},62249:e=>{e.exports={en:["hide marks on bars"],ro:["Hide Marks On Bars"]}},56558:e=>{e.exports={en:["interval lock"],ro:["interval lock"]}},6830:e=>{e.exports={en:["invert scale"],ro:["Invert Scale"]}},48818:e=>{e.exports={en:["insert {title}"],ro:["insert {title}"]}},56307:e=>{e.exports={en:["insert {title} after {targetTitle}"],ro:["insert {title} after {targetTitle}"]}},32960:e=>{e.exports={en:["insert {title} after {target}"],
ro:["Insert {title} after {target}"]}},57106:e=>{e.exports={en:["insert {title} before {target}"],ro:["Insert {title} before {target}"]}},46229:e=>{e.exports={en:["insert {title} before {targetTitle}"],ro:["Insert {title} before {targetTitle}"]}},43364:e=>{e.exports={en:["load default drawing template"],ro:["load default drawing template"]}},62011:e=>{e.exports={en:["loading..."],ro:["loading..."]}},76104:e=>{e.exports={en:["lock {title}"],ro:["Lock {title}"]}},20453:e=>{e.exports={en:["lock group {group}"],ro:["lock group {group}"]}},18942:e=>{e.exports={en:["lock objects"],ro:["lock objects"]}},98277:e=>{e.exports={en:["move"],ro:["move"]}},58228:e=>{e.exports={en:["move {title} to new left scale"],ro:["Move {title} To New Left Scale"]}},77482:e=>{e.exports={en:["move {title} to new right scale"],ro:["move {title} to new right scale"]}},64077:e=>{e.exports={en:["move all scales to left"],ro:["Move All Scales To Left"]}},19013:e=>{e.exports={en:["move all scales to right"],ro:["Move All Scales To Right"]}},52510:e=>{e.exports={en:["move drawing(s)"],ro:["Move Drawing(s)"]}},79209:e=>{e.exports={en:["move left"],ro:["move left"]}},60114:e=>{e.exports={en:["move right"],ro:["move right"]}},44854:e=>{e.exports={en:["move scale"],ro:["Move scale"]}},10625:e=>{e.exports={en:["make {title} no scale (Full screen)"],ro:["Make {title} No Scale (Full Screen)"]}},76709:e=>{e.exports={en:["make group {group} invisible"],ro:["Make group {group} invisible"]}},45987:e=>{e.exports={en:["make group {group} visible"],ro:["Make group {group} visible"]}},78055:e=>{e.exports={en:["merge down"],ro:["merge down"]}},41866:e=>{e.exports={en:["merge to pane"],ro:["merge to pane"]}},52458:e=>{e.exports={en:["merge up"],ro:["merge up"]}},20965:e=>{e.exports={en:["mirror bars pattern"],ro:["mirror bars pattern"]}},90091:e=>{e.exports={en:["n/a"],ro:["n/a"]}},94981:e=>{e.exports={en:["scale price"],ro:["scale price"]}},63796:e=>{e.exports={en:["scale price chart only"],ro:["Scale Price Chart Only"]}},70771:e=>{e.exports={en:["scale time"],ro:["scale time"]}},42070:e=>{e.exports={en:["scroll"],ro:["scroll"]}},87840:e=>{e.exports={en:["scroll time"],ro:["scroll time"]}},82241:e=>{e.exports={en:["set price scale selection strategy to {title}"],ro:["set price scale selection strategy to {title}"]}},40962:e=>{e.exports={en:["send {title} backward"],ro:["Send {title} backward"]}},5005:e=>{e.exports={en:["send {title} to back"],ro:["send {title} to back"]}},69546:e=>{e.exports={en:["send group {title} backward"],ro:["send group {title} backward"]}},63934:e=>{e.exports={en:["share line tools globally"],ro:["share line tools globally"]}},90221:e=>{e.exports={en:["share line tools in layout"],ro:["share line tools in layout"]}},13336:e=>{e.exports={en:["show all ideas"],ro:["show all ideas"]}},91395:e=>{e.exports={en:["show ideas of followed users"],ro:["show ideas of followed users"]}},57460:e=>{e.exports={en:["show my ideas only"],ro:["show my ideas only"]}},4114:e=>{e.exports={en:["stay in drawing mode"],ro:["stay in drawing mode"]}},3350:e=>{
e.exports={en:["stop syncing drawing"],ro:["stop syncing drawing"]}},49183:e=>{e.exports={en:["stop syncing line tool(s)"],ro:["stop syncing line tool(s)"]}},53278:e=>{e.exports={en:["symbol lock"],ro:["symbol lock"]}},91677:e=>{e.exports={en:["sync time"],ro:["sync time"]}},3140:e=>{e.exports={en:["powered by"],ro:["powered by"]}},92800:e=>{e.exports={en:["powered by TradingView"],ro:["powered by TradingView"]}},62192:e=>{e.exports={en:["paste drawing"],ro:["paste drawing"]}},1064:e=>{e.exports={en:["paste indicator"],ro:["paste indicator"]}},57010:e=>{e.exports={en:["paste {title}"],ro:["paste {title}"]}},78690:e=>{e.exports={en:["pin to left scale"],ro:["pin to left scale"]}},7495:e=>{e.exports={en:["pin to right scale"],ro:["Pin To Right Scale"]}},81566:e=>{e.exports={en:["pin to scale {label}"],ro:["pin to scale {label}"]}},2618:e=>{e.exports={en:["rearrange panes"],ro:["rearrange panes"]}},43172:e=>{e.exports={en:["remove all studies"],ro:["Remove all studies"]}},56253:e=>{e.exports={en:["remove all studies and drawing tools"],ro:["Remove all studies and drawing tools"]}},27171:e=>{e.exports={en:["remove deselected empty line tools"],ro:["remove deselected empty line tools"]}},30538:e=>{e.exports={en:["remove drawings"],ro:["Remove Drawings"]}},1193:e=>{e.exports={en:["remove drawings group"],ro:["remove drawings group"]}},38199:e=>{e.exports={en:["remove line data sources"],ro:["remove line data sources"]}},93333:e=>{e.exports={en:["remove pane"],ro:["remove pane"]}},94543:e=>{e.exports={en:["remove {title}"],ro:["remove {title}"]}},41430:e=>{e.exports={en:["removing line tools group {name}"],ro:["removing line tools group {name}"]}},80491:e=>{e.exports={en:["rename group {group} to {newName}"],ro:["Rename group {group} to {newName}"]}},85366:e=>{e.exports={en:["reset layout sizes"],ro:["reset layout sizes"]}},3323:e=>{e.exports={en:["reset scales"],ro:["reset scales"]}},17336:e=>{e.exports={en:["reset time scale"],ro:["Reset Time Scale"]}},47418:e=>{e.exports={en:["resize layout"],ro:["resize layout"]}},85815:e=>{e.exports={en:["restore defaults"],ro:["restore defaults"]}},96881:e=>{e.exports={en:["restore study defaults"],ro:["restore study defaults"]}},42240:e=>{e.exports={en:["toggle auto scale"],ro:["toggle auto scale"]}},46054:e=>{e.exports={en:["toggle collapsed pane state"],ro:["toggle collapsed pane state"]}},24736:e=>{e.exports={en:["toggle indexed to 100 scale"],ro:["toggle indexed to 100 scale"]}},49695:e=>{e.exports={en:["toggle lock scale"],ro:["toggle lock scale"]}},49403:e=>{e.exports={en:["toggle log scale"],ro:["toggle log scale"]}},98994:e=>{e.exports={en:["toggle percentage scale"],ro:["Toggle Percentage Scale"]}},80688:e=>{e.exports={en:["toggle regular scale"],ro:["toggle regular scale"]}},46807:e=>{e.exports={en:["track time"],ro:["track time"]}},8040:e=>{e.exports={en:["turn line tools sharing off"],ro:["turn line tools sharing off"]}},23230:e=>{e.exports={en:["unlock objects"],ro:["unlock objects"]}},74590:e=>{e.exports={en:["unlock group {group}"],ro:["unlock group {group}"]}},
12525:e=>{e.exports={en:["unlock {title}"],ro:["unlock {title}"]}},81576:e=>{e.exports={en:["unmerge to new bottom pane"],ro:["unmerge to new bottom pane"]}},79443:e=>{e.exports={en:["unmerge up"],ro:["unmerge up"]}},46453:e=>{e.exports={en:["unmerge down"],ro:["unmerge down"]}},94656:e=>{e.exports={en:["{chartStyle} chart type isn't currently available for tick-based intervals."],ro:["{chartStyle} chart type isn't currently available for tick-based intervals."]}},41643:e=>{e.exports={en:["{count} bars"],ro:["{count} bars"]}},83470:e=>{e.exports={en:["{symbol} financials by TradingView"],ro:["{symbol} financials by TradingView"]}},40947:e=>{e.exports={en:["{userName} published on {customer}, {date}"],ro:["{userName} published on {customer}, {date}"]}},91084:e=>{e.exports={en:["zoom"],ro:["zoom"]}},49856:e=>{e.exports={en:["zoom in"],ro:["zoom in"]}},73638:e=>{e.exports={en:["zoom out"],ro:["zoom out"]}},41807:e=>{e.exports={en:["day","days"],ro:["day","days"]}},42328:e=>{e.exports={en:["hour","hours"],ro:["hour","hours"]}},98393:e=>{e.exports={en:["month","months"],ro:["month","months"]}},78318:e=>{e.exports={en:["minute","minutes"],ro:["minute","minutes"]}},33232:e=>{e.exports={en:["second","seconds"],ro:["second","seconds"]}},89937:e=>{e.exports={en:["range","ranges"],ro:["range","ranges"]}},48898:e=>{e.exports={en:["week","weeks"],ro:["week","weeks"]}},11913:e=>{e.exports={en:["tick","ticks"],ro:["tick","ticks"]}},58590:e=>{e.exports={en:["{count}m","{count}m"],ro:["{count}m","{count}m"]}},47801:e=>{e.exports={en:["{count}d","{count}d"],ro:["{count}d","{count}d"]}},46766:e=>{e.exports={en:["{count}y","{count}y"],ro:["{count}y","{count}y"]}},56316:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]={en:["Apple Inc"],ro:["APPLE INC"]},e.exports["#AUDCAD-symbol-description"]={en:["Australian Dollar/Canadian Dollar"],ro:["Australian Dollar/Canadian Dollar"]},e.exports["#AUDCHF-symbol-description"]={en:["Australian Dollar / Swiss Franc"],ro:["Australian Dollar/Swiss Franc"]},e.exports["#AUDJPY-symbol-description"]={en:["Australian Dollar / Japanese Yen"],ro:["Australian Dollar/Japanese Yen"]},e.exports["#AUDNZD-symbol-description"]={en:["Australian Dollar / New Zealand Dollar"],ro:["Australian Dollar/New Zealand Dollar"]},e.exports["#AUDRUB-symbol-description"]={en:["Australian Dollar / Russian Ruble"],ro:["AUSTRALIAN DOLLAR / RUSSIAN RUBLE"]},e.exports["#AUDUSD-symbol-description"]={en:["Australian Dollar / U.S. Dollar"],ro:["Australian Dollar/U.S. Dollar"]},e.exports["#BRLJPY-symbol-description"]={en:["Brazilian Real / Japanese Yen"],ro:["Brazilian Real / Japanese Yen"]},e.exports["#BTCCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],ro:["Bitcoin / Canadian Dollar"]},e.exports["#BTCCNY-symbol-description"]={en:["Bitcoin / Chinese Yuan"],ro:["Bitcoin / Chinese Yuan"]},e.exports["#BTCEUR-symbol-description"]={en:["Bitcoin / Euro"],ro:["Bitcoin / Euro"]},e.exports["#BTCKRW-symbol-description"]={en:["Bitcoin / South Korean Won"],ro:["Bitcoin / South Korean Won"]},
e.exports["#BTCRUR-symbol-description"]={en:["Bitcoin / Ruble"],ro:["Bitcoin / Ruble"]},e.exports["#BTCUSD-symbol-description"]={en:["Bitcoin / U.S. Dollar"],ro:["Bitcoin / Dollar"]},e.exports["#BVSP-symbol-description"]={en:["Brazil Bovespa Index"],ro:["Brazil Bovespa Index"]},e.exports["#CADJPY-symbol-description"]={en:["Canadian Dollar / Japanese Yen"],ro:["Canadian Dollar/Japanese Yen"]},e.exports["#CHFJPY-symbol-description"]={en:["Swiss Franc / Japanese Yen"],ro:["Swiss Franc/Japanese Yen"]},e.exports["#COPPER-symbol-description"]={en:["CFDs on Copper"],ro:["Copper"]},e.exports["#ES1-symbol-description"]={en:["S&P 500 E-Mini Futures"],ro:["S&P 500 E-Mini Futures"]},e.exports["#ESP35-symbol-description"]={en:["IBEX 35 Index"],ro:["IBEX 35 Index"]},e.exports["#EUBUND-symbol-description"]={en:["Euro Bund"],ro:["Euro Bund"]},e.exports["#EURAUD-symbol-description"]={en:["Euro / Australian Dollar"],ro:["Euro Fx/Australian Dollar"]},e.exports["#EURBRL-symbol-description"]={en:["Euro / Brazilian Real"],ro:["Euro / Brazilian Real"]},e.exports["#EURCAD-symbol-description"]={en:["Euro / Canadian Dollar"],ro:["Euro Fx/Canadian Dollar"]},e.exports["#EURCHF-symbol-description"]={en:["Euro / Swiss Franc"],ro:["Euro Fx/Swiss Franc"]},e.exports["#EURGBP-symbol-description"]={en:["Euro / British Pound"],ro:["Euro Fx/British Pound"]},e.exports["#EURJPY-symbol-description"]={en:["Euro / Japanese Yen"],ro:["Euro Fx/Japanese Yen"]},e.exports["#EURNZD-symbol-description"]={en:["Euro / New Zealand Dollar"],ro:["Euro Fx/New Zealand Dollar"]},e.exports["#EURRUB-symbol-description"]={en:["Euro / Russian Ruble"],ro:["EURO / RUSSIAN RUBLE"]},e.exports["#EURRUB_TOM-symbol-description"]={en:["Euro / Russian Ruble TOM"],ro:["EUR/RUB TOM"]},e.exports["#EURSEK-symbol-description"]={en:["Euro / Swedish Krona"],ro:["Euro / Swedish Krona"]},e.exports["#EURTRY-symbol-description"]={en:["Euro / Turkish Lira"],ro:["Euro Fx/Turkish New Lira"]},e.exports["#EURUSD-symbol-description"]={en:["Euro / U.S. Dollar"],ro:["Euro Fx/U.S. Dollar"]},e.exports["#EUSTX50-symbol-description"]={en:["Euro Stoxx 50 Index"],ro:["Euro Stoxx 50 index of European listed shares"]},e.exports["#FRA40-symbol-description"]={en:["CAC 40 Index"],ro:["CAC 40 Index"]},e.exports["#GB10-symbol-description"]={en:["UK Government Bonds 10 yr"],ro:["UK Government Bonds 10 yr"]},e.exports["#GBPAUD-symbol-description"]={en:["British Pound / Australian Dollar"],ro:["British Pound/Australian Dollar"]},e.exports["#GBPCAD-symbol-description"]={en:["British Pound / Canadian Dollar"],ro:["British Pound/Canadian Dollar"]},e.exports["#GBPCHF-symbol-description"]={en:["British Pound / Swiss Franc"],ro:["British Pound/Swiss Franc"]},e.exports["#GBPEUR-symbol-description"]={en:["British Pound / Euro"],ro:["POUND STERLING / EURO"]},e.exports["#GBPJPY-symbol-description"]={en:["British Pound / Japanese Yen"],ro:["British Pound/Japanese Yen"]},e.exports["#GBPNZD-symbol-description"]={en:["British Pound / New Zealand Dollar"],ro:["British Pound/New Zealand Dollar"]},
e.exports["#GBPRUB-symbol-description"]={en:["British Pound / Russian Ruble"],ro:["Pound Sterling / Russian Ruble"]},e.exports["#GBPUSD-symbol-description"]={en:["British Pound / U.S. Dollar"],ro:["British Pound/U.S. Dollar"]},e.exports["#GER30-symbol-description"]={en:["DAX Index"],ro:["DAX index of German listed shares"]},e.exports["#GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"],ro:["GOOGLE INC"]},e.exports["#ITA40-symbol-description"]={en:["FTSE MIB Index"],ro:["FTSE MIB index of Italian listed shares"]},e.exports["#JPN225-symbol-description"]={en:["Nikkei 225 Index"],ro:["Nikkei 225 Index"]},e.exports["#JPYKRW-symbol-description"]={en:["Japanese Yen / South Korean Won"],ro:["YEN / WON"]},e.exports["#JPYRUB-symbol-description"]={en:["Japanese Yen / Russian Ruble"],ro:["YEN / RUSSIAN RUBLE"]},e.exports["#KA1-symbol-description"]={en:["Sugar #11 Futures"],ro:["Sugar #11 Futures"]},e.exports["#KG1-symbol-description"]={en:["Cotton Futures"],ro:["Cotton Futures"]},e.exports["#KT1-symbol-description"]={en:["Key Tronic Corр."],ro:["Key Tronic Corр."]},e.exports["#LKOH-symbol-description"]={en:["LUKOIL"],ro:["LUKOIL"]},e.exports["#LTCBTC-symbol-description"]={en:["Litecoin / Bitcoin"],ro:["Litecoin / Bitcoin"]},e.exports["#MGNT-symbol-description"]={en:["Magnit"],ro:["MAGNIT"]},e.exports["#MICEX-symbol-description"]={en:["MICEX Index"],ro:["MICEX INDEX"]},e.exports["#MNOD_ME.EQRP-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],ro:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#MSFT-symbol-description"]={en:["Microsoft Corp."],ro:["MICROSOFT CORP"]},e.exports["#NAS100-symbol-description"]={en:["US 100 Cash CFD"],ro:["NASDAQ 100 index of US listed shares"]},e.exports["#NGAS-symbol-description"]={en:["Natural Gas (Henry Hub)"],ro:["Natural Gas (Henry Hub)"]},e.exports["#NKY-symbol-description"]={en:["Nikkei 225 Index"],ro:["Nikkei 225 Index"]},e.exports["#NZDJPY-symbol-description"]={en:["New Zealand Dollar / Japanese Yen"],ro:["New Zealand Dollar/Japanese Yen"]},e.exports["#NZDUSD-symbol-description"]={en:["New Zealand Dollar / U.S. Dollar"],ro:["New Zealand Dollar/U.S. Dollar"]},e.exports["#RB1-symbol-description"]={en:["RBOB Gasoline Futures"],ro:["RBOB Gasoline Futures"]},e.exports["#RTS-symbol-description"]={en:["Russian RTS Index"],ro:["Russian RTS Index"]},e.exports["#SBER-symbol-description"]={en:["SBERBANK"],ro:["SBERBANK"]},e.exports["#SPX500-symbol-description"]={en:["S&P 500 Index"],ro:["S&P 500 index of US listed shares"]},e.exports["#TWTR-symbol-description"]={en:["Twitter Inc"],ro:["TWITTER INC"]},e.exports["#UK100-symbol-description"]={en:["FTSE 100 Index"],ro:["FTSE 100 Index"]},e.exports["#USDBRL-symbol-description"]={en:["U.S. Dollar / Brazilian Real"],ro:["U.S. Dollar / Brazilian Real"]},e.exports["#USDCAD-symbol-description"]={en:["U.S. Dollar / Canadian Dollar"],ro:["U.S. Dollar/Canadian Dollar"]},e.exports["#USDCHF-symbol-description"]={en:["U.S. Dollar / Swiss Franc"],ro:["U.S. Dollar/Swiss Franc"]},e.exports["#USDCNY-symbol-description"]={
en:["U.S. Dollar / Chinese Yuan"],ro:["U.S. Dollar / Yuan Renminbi"]},e.exports["#USDDKK-symbol-description"]={en:["U.S. Dollar / Danish Krone"],ro:["US DOLLAR / DANISH KRONE"]},e.exports["#USDHKD-symbol-description"]={en:["U.S. Dollar / Hong Kong Dollar"],ro:["U.S. Dollar/Hong Kong Dollar"]},e.exports["#USDIDR-symbol-description"]={en:["U.S. Dollar / Rupiah"],ro:["U.S. Dollar / Rupiah"]},e.exports["#USDINR-symbol-description"]={en:["U.S. Dollar / Indian Rupee"],ro:["U.S. Dollar / Indian Rupee"]},e.exports["#USDJPY-symbol-description"]={en:["U.S. Dollar / Japanese Yen"],ro:["U.S. Dollar/Japanese Yen"]},e.exports["#USDKRW-symbol-description"]={en:["U.S. Dollar / South Korean"],ro:["US DOLLAR / WON"]},e.exports["#USDMXN-symbol-description"]={en:["U.S. Dollar / Mexican Peso"],ro:["U.S. Dollar / Mexican Peso"]},e.exports["#USDPHP-symbol-description"]={en:["U.S. Dollar / Philippine peso"],ro:["U.S. Dollar / Philippine peso"]},e.exports["#USDRUB-symbol-description"]={en:["U.S. Dollar / Russian Ruble"],ro:["US DOLLAR / RUSSIAN RUBLE"]},e.exports["#USDRUB_TOM-symbol-description"]={en:["U.S. Dollar / Russian Ruble TOM"],ro:["USD/RUB TOM"]},e.exports["#USDSEK-symbol-description"]={en:["U.S. Dollar / Swedish Krona"],ro:["U.S. Dollar/Swedish Krona"]},e.exports["#USDSGD-symbol-description"]={en:["U.S. Dollar / Singapore Dollar"],ro:["US DOLLAR / SINGAPORE DOLLAR"]},e.exports["#USDTRY-symbol-description"]={en:["U.S. Dollar / Turkish Lira"],ro:["U.S. Dollar/Turkish New Lira"]},e.exports["#VTBR-symbol-description"]={en:["VTB"],ro:["VTB"]},e.exports["#XAGUSD-symbol-description"]={en:["Silver / U.S. Dollar"],ro:["Silver/U.S. Dollar"]},e.exports["#XAUUSD-symbol-description"]={en:["Gold Spot / U.S. Dollar"],ro:["Gold / U.S. Dollar"]},e.exports["#XPDUSD-symbol-description"]={en:["CFDs on Palladium"],ro:["CFDs on Palladium"]},e.exports["#XPTUSD-symbol-description"]={en:["Platinum / U.S. Dollar"],ro:["Platinum/U.S. Dollar"]},e.exports["#ZS1-symbol-description"]={en:["Soybean Futures - ECBT"],ro:["Soybean Futures - ECBT"]},e.exports["#ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],ro:["Wheat Futures - ECBT"]},e.exports["#BTCGBP-symbol-description"]={en:["Bitcoin / British Pound"],ro:["Bitcoin / British Pound"]},e.exports["#MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],ro:["MICEX Index"]},e.exports["#BTCAUD-symbol-description"]={en:["Bitcoin / Australian Dollar"],ro:["Bitcoin / Australian Dollar"]},e.exports["#BTCJPY-symbol-description"]={en:["Bitcoin / Japanese Yen"],ro:["Bitcoin / Japanese Yen"]},e.exports["#BTCBRL-symbol-description"]={en:["Bitcoin / Brazilian Real"],ro:["Bitcoin / Brazilian Real"]},e.exports["#PT10-symbol-description"]={en:["Portugal Government Bonds 10 yr"],ro:["Portugal Government Bonds 10 yr"]},e.exports["#TXSX-symbol-description"]={en:["TSX 60 Index"],ro:["TSX 60 Index"]},e.exports["#VIXC-symbol-description"]={en:["TSX 60 VIX Index"],ro:["TSX 60 VIX"]},e.exports["#USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],ro:["USD/PLN"]},e.exports["#EURPLN-symbol-description"]={
en:["Euro / Polish Zloty"],ro:["EUR/PLN"]},e.exports["#BTCPLN-symbol-description"]={en:["Bitcoin / Polish Zloty"],ro:["Bitcoin / Polish Zloty"]},e.exports["#CAC40-symbol-description"]={en:["CAC 40 Index"],ro:["CAC 40"]},e.exports["#XBTCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],ro:["Bitcoin / Canadian Dollar"]},e.exports["#ITI2!-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIF2018-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIF2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIF2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIG2018-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIG2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIG2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIH2018-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIH2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIH2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIJ2018-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIJ2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIJ2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIK2018-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIK2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIK2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIM2017-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIM2018-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIM2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIM2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIN2017-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIN2018-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIN2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIN2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIQ2017-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIQ2018-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIQ2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIQ2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIU2017-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIU2018-symbol-description"]={en:["Iron Ore Futures"],
ro:["Iron Ore Futures"]},e.exports["#ITIU2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIU2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIV2017-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIV2018-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIV2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIV2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIX2017-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIX2018-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIX2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIX2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIZ2017-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIZ2018-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIZ2019-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#ITIZ2020-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#AMEX:GXF-symbol-description"]={en:["Global x FTSE Nordic Region ETF"],ro:["Global x FTSE Nordic Region ETF"]},e.exports["#ASX:XAF-symbol-description"]={en:["S&P/ASX All Australian 50 Index"],ro:["S&P/ASX All Australian 50 Index"]},e.exports["#ASX:XAT-symbol-description"]={en:["S&P/ASX All Australian 200 Index"],ro:["S&P/ASX All Australian 200 Index"]},e.exports["#BIST:XU100-symbol-description"]={en:["BIST 100 Index"],ro:["BIST 100 Index"]},e.exports["#GPW:WIG20-symbol-description"]={en:["WIG20 Index"],ro:["WIG20 Index"]},e.exports["#INDEX:JKSE-symbol-description"]={en:["Jakarta Composite Index"],ro:["Jakarta Composite Index"]},e.exports["#INDEX:KLSE-symbol-description"]={en:["Bursa Malaysia KLCI Index"],ro:["Bursa Malaysia KLCI Index"]},e.exports["#INDEX:NZD-symbol-description"]={en:["NZX 50 Index"],ro:["NZX 50 Index"]},e.exports["#INDEX:STI-symbol-description"]={en:["STI Index"],ro:["STI Index"]},e.exports["#INDEX:XLY0-symbol-description"]={en:["Shanghai Composite Index"],ro:["Shanghai Composite Index"]},e.exports["#MOEX:MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],ro:["MOEX Russia Index"]},e.exports["#NYMEX:KT1!-symbol-description"]={en:["Coffee Futures"],ro:["Coffee Futures"]},e.exports["#OANDA:NATGASUSD-symbol-description"]={en:["CFDs on Natural Gas"],ro:["CFDs on Natural Gas"]},e.exports["#OANDA:USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],ro:["U.S. Dollar / Polish Zloty"]},e.exports["#TSX:TX60-symbol-description"]={en:["S&P/TSX 60 Index"],ro:["S&P/TSX 60 Index"]},e.exports["#TSX:VBU-symbol-description"]={en:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"],ro:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"]},e.exports["#TSX:VIXC-symbol-description"]={
en:["S&P/TSX 60 VIX Index"],ro:["S&P/TSX 60 VIX Index"]},e.exports["#TVC:CAC40-symbol-description"]={en:["CAC 40 Index"],ro:["CAC 40 Index"]},e.exports["#TVC:ES10-symbol-description"]={en:["Spain Government Bonds 10 YR"],ro:["Spain Government Bonds 10 YR"]},e.exports["#TVC:EUBUND-symbol-description"]={en:["Euro Bund"],ro:["Euro Bund"]},e.exports["#TVC:GB02-symbol-description"]={en:["UK Government Bonds 2 YR"],ro:["UK Government Bonds 2 YR"]},e.exports["#TVC:GB10-symbol-description"]={en:["UK Government Bonds 10 YR"],ro:["UK Government Bonds 10 YR"]},e.exports["#TVC:GOLD-symbol-description"]={en:["CFDs on Gold (US$ / OZ)"],ro:["CFDs on Gold (US$ / OZ)"]},e.exports["#TVC:ID03-symbol-description"]={en:["Indonesia Government Bonds 3 YR"],ro:["Indonesia Government Bonds 3 YR"]},e.exports["#TVC:ID10-symbol-description"]={en:["Indonesia Government Bonds 10 YR"],ro:["Indonesia Government Bonds 10 YR"]},e.exports["#TVC:PALLADIUM-symbol-description"]={en:["CFDs on Palladium (US$ / OZ)"],ro:["CFDs on Palladium (US$ / OZ)"]},e.exports["#TVC:PT10-symbol-description"]={en:["Portugal Government Bonds 10 YR"],ro:["Portugal Government Bonds 10 YR"]},e.exports["#TVC:SILVER-symbol-description"]={en:["CFDs on Silver (US$ / OZ)"],ro:["CFDs on Silver (US$ / OZ)"]},e.exports["#TSX:TSX-symbol-description"]={en:["S&P/TSX Composite Index"],ro:["S&P/TSX Composite Index"]},e.exports["#OANDA:CH20CHF-symbol-description"]={en:["Swiss 20 Index"],ro:["Swiss 20 Index"]},e.exports["#TVC:SHCOMP-symbol-description"]={en:["Shanghai Composite Index"],ro:["Shanghai Composite Index"]},e.exports["#NZX:ALLC-symbol-description"]={en:["S&P/NZX All Index (Capital Index)"],ro:["S&P/NZX All Index (Capital Index)"]},e.exports["#AMEX:SHYG-symbol-description"]={en:["Shares 0-5 YEAR High Yield Corporate Bond ETF"],ro:["Shares 0-5 YEAR High Yield Corporate Bond ETF"]},e.exports["#TVC:AU10-symbol-description"]={en:["Australia Government Bonds 10 YR"],ro:["Australia Government Bonds 10 YR"]},e.exports["#TVC:CN10-symbol-description"]={en:["China Government Bonds 10 YR"],ro:["China Government Bonds 10 YR"]},e.exports["#TVC:KR10-symbol-description"]={en:["Korea Government Bonds 10 YR"],ro:["Korea Government Bonds 10 YR"]},e.exports["#NYMEX:RB1!-symbol-description"]={en:["RBOB Gasoline Futures"],ro:["RBOB Gasoline Futures"]},e.exports["#NYMEX:HO1!-symbol-description"]={en:["NY Harbor ULSD Futures"],ro:["NY Harbor ULSD Futures"]},e.exports["#NYMEX:AEZ1!-symbol-description"]={en:["NY Ethanol Futures"],ro:["NY Ethanol Futures"]},e.exports["#OANDA:XCUUSD-symbol-description"]={en:["CFDs on Copper (US$ / lb)"],ro:["CFDs on Copper (US$ / lb)"]},e.exports["#COMEX:ZA1!-symbol-description"]={en:["Zinc Futures"],ro:["Zinc Futures"]},e.exports["#CBOT:ZW1!-symbol-description"]={en:["Wheat Futures"],ro:["Wheat Futures"]},e.exports["#NYMEX:KA1!-symbol-description"]={en:["Sugar #11 Futures"],ro:["Sugar #11 Futures"]},e.exports["#CBOT:QBC1!-symbol-description"]={en:["Corn Futures"],ro:["Corn Futures"]},e.exports["#CME:E61!-symbol-description"]={en:["Euro Futures"],ro:["Euro Futures"]},
e.exports["#CME:B61!-symbol-description"]={en:["British Pound Futures"],ro:["British Pound Futures"]},e.exports["#CME:QJY1!-symbol-description"]={en:["Japanese Yen Futures"],ro:["Japanese Yen Futures"]},e.exports["#CME:A61!-symbol-description"]={en:["Australian Dollar Futures"],ro:["Australian Dollar Futures"]},e.exports["#CME:D61!-symbol-description"]={en:["Canadian Dollar Futures"],ro:["Canadian Dollar Futures"]},e.exports["#CME:SP1!-symbol-description"]={en:["S&P 500 Futures"],ro:["S&P 500 Futures"]},e.exports["#CME_MINI:NQ1!-symbol-description"]={en:["NASDAQ 100 E-mini Futures"],ro:["NASDAQ 100 E-mini Futures"]},e.exports["#CBOT_MINI:YM1!-symbol-description"]={en:["E-mini Dow Jones ($5) Futures"],ro:["E-mini Dow Jones ($5) Futures"]},e.exports["#CME:NY1!-symbol-description"]={en:["NIKKEI 225 Futures"],ro:["NIKKEI 225 Futures"]},e.exports["#EUREX:DY1!-symbol-description"]={en:["DAX Index"],ro:["DAX Index"]},e.exports["#CME:IF1!-symbol-description"]={en:["IBOVESPA Index Futures-US$"],ro:["IBOVESPA Index Futures-US$"]},e.exports["#CBOT:TY1!-symbol-description"]={en:["10 Year T-Note Futures"],ro:["10 Year T-Note Futures"]},e.exports["#CBOT:FV1!-symbol-description"]={en:["5 Year T-Note Futures"],ro:["5 Year T-Note Futures"]},e.exports["#CBOT:ZE1!-symbol-description"]={en:["Treasury Notes - 3 Year Futures"],ro:["Treasury Notes - 3 Year Futures"]},e.exports["#CBOT:TU1!-symbol-description"]={en:["2 Year T-Note Futures"],ro:["2 Year T-Note Futures"]},e.exports["#CBOT:FF1!-symbol-description"]={en:["30-Day FED Funds Interest Rate Futures"],ro:["30-Day FED Funds Interest Rate Futures"]},e.exports["#CBOT:US1!-symbol-description"]={en:["T-Bond Futures"],ro:["T-Bond Futures"]},e.exports["#TVC:EXY-symbol-description"]={en:["Euro Currency Index"],ro:["Euro Currency Index"]},e.exports["#TVC:JXY-symbol-description"]={en:["Japanese Yen Currency Index"],ro:["Japanese Yen Currency Index"]},e.exports["#TVC:BXY-symbol-description"]={en:["British Pound Currency Index"],ro:["British Pound Currency Index"]},e.exports["#TVC:AXY-symbol-description"]={en:["Australian Dollar Currency Index"],ro:["Australian Dollar Currency Index"]},e.exports["#TVC:CXY-symbol-description"]={en:["Canadian Dollar Currency Index"],ro:["Canadian Dollar Currency Index"]},e.exports["#FRED:GDP-symbol-description"]={en:["Gross Domestic Product, 1 Decimal"],ro:["Gross Domestic Product, 1 Decimal"]},e.exports["#FRED:UNRATE-symbol-description"]={en:["Civilian Unemployment Rate"],ro:["Civilian Unemployment Rate"]},e.exports["#FRED:POP-symbol-description"]={en:["Total Population: All Ages Including Armed Forces Overseas"],ro:["Total Population: All Ages Including Armed Forces Overseas"]},e.exports["#ETHUSD-symbol-description"]={en:["Ethereum / U.S. Dollar"],ro:["Ethereum / U.S. Dollar"]},e.exports["#BMFBOVESPA:IBOV-symbol-description"]={en:["Bovespa Index"],ro:["IBovespa Index"]},e.exports["#BMFBOVESPA:IBRA-symbol-description"]={en:["IBrasil Index"],ro:["IBrasil Index"]},e.exports["#BMFBOVESPA:IBXL-symbol-description"]={en:["Brazil 50 Index"],ro:["IBRX 50 Index"]},
e.exports["#COMEX:HG1!-symbol-description"]={en:["Copper Futures"],ro:["Copper Futures"]},e.exports["#INDEX:HSCE-symbol-description"]={en:["Hang Seng China Enterprises Index"],ro:["Hang Seng China Enterprises Index"]},e.exports["#NYMEX:CL1!-symbol-description"]={en:["Light Crude Oil Futures"],ro:["Light Crude Oil Futures"]},e.exports["#OTC:IHRMF-symbol-description"]={en:["Ishares MSCI Japan SHS"],ro:["Ishares MSCI Japan SHS"]},e.exports["#TVC:DAX-symbol-description"]={en:["DAX Index"],ro:["DAX Index"]},e.exports["#TVC:DE10-symbol-description"]={en:["German Government Bonds 10 YR"],ro:["German Government Bonds 10 YR"]},e.exports["#TVC:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],ro:["Dow Jones Industrial Average Index"]},e.exports["#TVC:DXY-symbol-description"]={en:["U.S. Dollar Index"],ro:["U.S. Dollar Currency Index"]},e.exports["#TVC:FR10-symbol-description"]={en:["France Government Bonds 10 YR"],ro:["France Government Bonds 10 YR"]},e.exports["#TVC:HSI-symbol-description"]={en:["Hang Seng Index"],ro:["Hang Seng Index"]},e.exports["#TVC:IBEX35-symbol-description"]={en:["IBEX 35 Index"],ro:["IBEX 35 Index"]},e.exports["#FX:AUS200-symbol-description"]={en:["S&P/ASX Index"],ro:["S&P/ASX Index"]},e.exports["#AMEX:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],ro:["Ishares 1-3 Year Treasury Bond ETF"]},e.exports["#ASX:XJO-symbol-description"]={en:["S&P/ASX 200 Index"],ro:["S&P/ASX 200 Index"]},e.exports["#BSE:SENSEX-symbol-description"]={en:["S&P BSE Sensex Index"],ro:["S&P BSE Sensex Index"]},e.exports["#INDEX:MIB-symbol-description"]={en:["MIB Index"],ro:["MIB Index"]},e.exports["#INDEX:MOY0-symbol-description"]={en:["Euro Stoxx 50 Index"],ro:["Euro Stoxx 50 Index"]},e.exports["#MOEX:RTSI-symbol-description"]={en:["RTS Index"],ro:["RTS Index"]},e.exports["#NSE:NIFTY-symbol-description"]={en:["Nifty 50 Index"],ro:["Nifty 50 Index"]},e.exports["#NYMEX:NG1!-symbol-description"]={en:["Natural Gas Futures"],ro:["Natural Gas Futures"]},e.exports["#NYMEX:ZC1!-symbol-description"]={en:["Corn Futures"],ro:["Corn Futures"]},e.exports["#TVC:IN10-symbol-description"]={en:["India Government Bonds 10 YR"],ro:["India Government Bonds 10 YR"]},e.exports["#TVC:IT10-symbol-description"]={en:["Italy Government Bonds 10 YR"],ro:["Italy Government Bonds 10 YR"]},e.exports["#TVC:JP10-symbol-description"]={en:["Japan Government Bonds 10 YR"],ro:["Japan Government Bonds 10 YR"]},e.exports["#TVC:NDX-symbol-description"]={en:["US 100 Index"],ro:["NASDAQ 100 Index"]},e.exports["#TVC:NI225-symbol-description"]={en:["Nikkei 225 Index"],ro:["Nikkei 225 Index"]},e.exports["#TVC:SPX-symbol-description"]={en:["S&P 500 Index"],ro:["S&P 500 Index"]},e.exports["#TVC:SX5E-symbol-description"]={en:["Euro Stoxx 50 Index"],ro:["Euro Stoxx 50 Index"]},e.exports["#TVC:TR10-symbol-description"]={en:["Turkey Government Bonds 10 YR"],ro:["Turkey Government Bonds 10 YR"]},e.exports["#TVC:UKOIL-symbol-description"]={en:["CFDs on Brent Crude Oil"],ro:["CFDs on Brent Crude Oil"]},e.exports["#TVC:UKX-symbol-description"]={
en:["UK 100 Index"],ro:["UK 100 Index"]},e.exports["#TVC:US02-symbol-description"]={en:["US Government Bonds 2 YR"],ro:["US Government Bonds 2 YR"]},e.exports["#TVC:US05-symbol-description"]={en:["US Government Bonds 5 YR"],ro:["US Government Bonds 5 YR"]},e.exports["#TVC:US10-symbol-description"]={en:["US Government Bonds 10 YR"],ro:["US Government Bonds 10 YR"]},e.exports["#TVC:USOIL-symbol-description"]={en:["CFDs on WTI Crude Oil"],ro:["CFDs on WTI Crude Oil"]},e.exports["#NYMEX:ITI1!-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#NASDAQ:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],ro:["Ishares 1-3 Year Treasury Bond ETF"]},e.exports["#AMEX:ALD-symbol-description"]={en:["WisdomTree Asia Local Debt ETF"],ro:["WisdomTree Asia Local Debt ETF"]},e.exports["#NASDAQ:AMD-symbol-description"]={en:["Advanced Micro Devices Inc"],ro:["Advanced Micro Devices Inc"]},e.exports["#NYSE:BABA-symbol-description"]={en:["Alibaba Group Holdings Ltd."],ro:["ALIBABA GROUP HLDG LTD"]},e.exports["#ICEEUR:CB-symbol-description"]={en:["Crude Oil Brent"],ro:["Crude Oil Brent"]},e.exports["#ICEEUR:CB1!-symbol-description"]={en:["Brent Crude Oil"],ro:["Brent Crude Oil"]},e.exports["#ICEUSA:CC-symbol-description"]={en:["Cocoa"],ro:["Cocoa"]},e.exports["#NYMEX:CL-symbol-description"]={en:["Crude Oil WTI"],ro:["Crude Oil WTI"]},e.exports["#ICEUSA:CT-symbol-description"]={en:["Cotton #2"],ro:["Cotton #2"]},e.exports["#NASDAQ:CTRV-symbol-description"]={en:["ContraVir Pharmaceuticals Inc"],ro:["CONTRAVIR PHARMACEUTICALS INC"]},e.exports["#CME:DL-symbol-description"]={en:["Class III Milk"],ro:["Class III Milk"]},e.exports["#NYSE:F-symbol-description"]={en:["FORD MTR CO DEL"],ro:["FORD MTR CO DEL"]},e.exports["#MOEX:GAZP-symbol-description"]={en:["GAZPROM"],ro:["GAZPROM"]},e.exports["#COMEX:GC-symbol-description"]={en:["Gold"],ro:["Gold"]},e.exports["#CME:GF-symbol-description"]={en:["Feeder Cattle"],ro:["Feeder Cattle"]},e.exports["#CME:HE-symbol-description"]={en:["Lean Hogs"],ro:["Lean Hogs"]},e.exports["#NASDAQ:IEF-symbol-description"]={en:["Ishares 7-10 Year Treasury Bond ETF"],ro:["Ishares 7-10 Year Treasury Bond ETF"]},e.exports["#NASDAQ:IEI-symbol-description"]={en:["Ishares 3-7 Year Treasury Bond ETF"],ro:["Ishares 3-7 Year Treasury Bond ETF"]},e.exports["#NYMEX:KA1-symbol-description"]={en:["Sugar #11 Futures"],ro:["Sugar #11 Futures"]},e.exports["#ICEUSA:KC-symbol-description"]={en:["Coffee"],ro:["Coffee"]},e.exports["#NYMEX:KG1-symbol-description"]={en:["Cotton Futures"],ro:["Cotton Futures"]},e.exports["#FWB:KT1-symbol-description"]={en:["Key Tronic Corр."],ro:["Key Tronic Corр."]},e.exports["#CME:LE-symbol-description"]={en:["Live Cattle"],ro:["Live Cattle"]},e.exports["#ICEEUR:LO-symbol-description"]={en:["ICE Heating Oil"],ro:["ICE Heating Oil"]},e.exports["#CME:LS-symbol-description"]={en:["Lumber"],ro:["Lumber"]},e.exports["#MOEX:MGNT-symbol-description"]={en:["MAGNIT"],ro:["MAGNIT"]},e.exports["#LSIN:MNOD-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],
ro:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#NYMEX:NG-symbol-description"]={en:["Natural Gas"],ro:["Natural Gas"]},e.exports["#ICEUSA:OJ-symbol-description"]={en:["Orange Juice"],ro:["Orange Juice"]},e.exports["#NYMEX:PA-symbol-description"]={en:["Palladium"],ro:["Palladium"]},e.exports["#NYSE:PBR-symbol-description"]={en:["PETROLEO BRASILEIRO SA PETROBR"],ro:["PETROLEO BRASILEIRO SA PETROBR"]},e.exports["#NYMEX:PL-symbol-description"]={en:["Platinum"],ro:["Platinum"]},e.exports["#COMEX_MINI:QC-symbol-description"]={en:["E-Mini Copper"],ro:["E-Mini Copper"]},e.exports["#NYMEX:RB-symbol-description"]={en:["Gasoline RBOB"],ro:["Gasoline RBOB"]},e.exports["#NYMEX:RB1-symbol-description"]={en:["RBOB Gasoline Futures"],ro:["RBOB Gasoline Futures"]},e.exports["#MOEX:SBER-symbol-description"]={en:["SBERBANK"],ro:["SBERBANK"]},e.exports["#AMEX:SCHO-symbol-description"]={en:["Schwab Short-Term U.S. Treasury ETF"],ro:["Schwab Short-Term U.S. Treasury ETF"]},e.exports["#COMEX:SI-symbol-description"]={en:["Silver"],ro:["Silver"]},e.exports["#NASDAQ:TLT-symbol-description"]={en:["Ishares 20+ Year Treasury Bond ETF"],ro:["Ishares 20+ Year Treasury Bond ETF"]},e.exports["#TVC:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],ro:["Volatility S&P 500 Index"]},e.exports["#MOEX:VTBR-symbol-description"]={en:["VTB"],ro:["VTB"]},e.exports["#COMEX:ZA-symbol-description"]={en:["Zinc"],ro:["Zinc"]},e.exports["#CBOT:ZC-symbol-description"]={en:["Corn"],ro:["Corn"]},e.exports["#CBOT:ZK-symbol-description"]={en:["Ethanol Futures"],ro:["Ethanol Futures"]},e.exports["#CBOT:ZL-symbol-description"]={en:["Soybean Oil"],ro:["Soybean Oil"]},e.exports["#CBOT:ZO-symbol-description"]={en:["Oats"],ro:["Oats"]},e.exports["#CBOT:ZR-symbol-description"]={en:["Rough Rice"],ro:["Rough Rice"]},e.exports["#CBOT:ZS-symbol-description"]={en:["Soybeans"],ro:["Soybeans"]},e.exports["#CBOT:ZS1-symbol-description"]={en:["Soybean Futures"],ro:["Soybean Futures"]},e.exports["#CBOT:ZW-symbol-description"]={en:["Wheat"],ro:["Wheat"]},e.exports["#CBOT:ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],ro:["Wheat Futures - ECBT"]},e.exports["#NASDAQ:ITI-symbol-description"]={en:["Iteris Inc"],ro:["Iteris Inc."]},e.exports["#NYMEX:ITI2!-symbol-description"]={en:["Iron Ore Futures"],ro:["Iron Ore Futures"]},e.exports["#CADUSD-symbol-description"]={en:["Canadian Dollar / U.S. Dollar"],ro:["Canadian Dollar / U.S. Dollar"]},e.exports["#CHFUSD-symbol-description"]={en:["Swiss Franc / U.S. Dollar"],ro:["Swiss Franc / U.S. Dollar"]},e.exports["#GPW:ACG-symbol-description"]={en:["Acautogaz"],ro:["Acautogaz"]},e.exports["#JPYUSD-symbol-description"]={en:["Japanese Yen / U.S. Dollar"],ro:["Japanese Yen / U.S. Dollar"]},e.exports["#USDAUD-symbol-description"]={en:["U.S. Dollar / Australian Dollar"],ro:["U.S. Dollar / Australian Dollar"]},e.exports["#USDEUR-symbol-description"]={en:["U.S. Dollar / Euro"],ro:["U.S. Dollar / Euro"]},e.exports["#USDGBP-symbol-description"]={en:["U.S. Dollar / Pound Sterling"],ro:["U.S. Dollar / Pound Sterling"]},
e.exports["#USDNZD-symbol-description"]={en:["U.S. Dollar / New Zealand Dollar"],ro:["U.S. Dollar / New Zealand Dollar"]},e.exports["#UKOIL-symbol-description"]={en:["CFDs on Crude Oil (Brent)"],ro:["CFDs on Crude Oil (Brent)"]},e.exports["#USOIL-symbol-description"]={en:["CFDs on Crude Oil (WTI)"],ro:["CFDs on Crude Oil (WTI)"]},e.exports["#US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],ro:["Dow Jones Industrial Average Index"]},e.exports["#BCHUSD-symbol-description"]={en:["Bitcoin Cash / U.S. Dollar"],ro:["Bitcoin Cash / U.S. Dollar"]},e.exports["#ETCUSD-symbol-description"]={en:["Ethereum Classic / U.S. Dollar"],ro:["Ethereum Classic / U.S. Dollar"]},e.exports["#GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"],ro:["Alphabet Inc (Google) Class C"]},e.exports["#LTCUSD-symbol-description"]={en:["Litecoin / U.S. Dollar"],ro:["Litecoin / U.S. Dollar"]},e.exports["#XRPUSD-symbol-description"]={en:["XRP / U.S. Dollar"],ro:["Ripple / U.S. Dollar"]},e.exports["#SP:SPX-symbol-description"]={en:["S&P 500 Index"],ro:["S&P 500 Index"]},e.exports["#ETCBTC-symbol-description"]={en:["Ethereum Classic / Bitcoin"],ro:["Ethereum Classic / Bitcoin"]},e.exports["#ETHBTC-symbol-description"]={en:["Ethereum / Bitcoin"],ro:["Ethereum / Bitcoin"]},e.exports["#XRPBTC-symbol-description"]={en:["XRP / Bitcoin"],ro:["Ripple / Bitcoin"]},e.exports["#TVC:US30-symbol-description"]={en:["US Government Bonds 30 YR"],ro:["US Government Bonds 30 YR"]},e.exports["#COMEX:SI1!-symbol-description"]={en:["Silver Futures"],ro:["Silver Futures"]},e.exports["#BTGUSD-symbol-description"]={en:["Bitcoin Gold / U.S. Dollar"],ro:["Bitcoin Gold / U.S. Dollar"]},e.exports["#IOTUSD-symbol-description"]={en:["IOTA / U.S. Dollar"],ro:["IOTA / U.S. Dollar"]},e.exports["#CME:BTC1!-symbol-description"]={en:["Bitcoin CME Futures"],ro:["Bitcoin CME Futures"]},e.exports["#COMEX:GC1!-symbol-description"]={en:["Gold Futures"],ro:["Gold Futures"]},e.exports["#CORNUSD-symbol-description"]={en:["CFDs on Corn"],ro:["CFDs on Corn"]},e.exports["#COTUSD-symbol-description"]={en:["CFDs on Cotton"],ro:["CFDs on Cotton"]},e.exports["#DJ:DJA-symbol-description"]={en:["Dow Jones Composite Average Index"],ro:["Dow Jones Composite Average Index"]},e.exports["#DJ:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],ro:["Dow Jones Industrial Average Index"]},e.exports["#ETHEUR-symbol-description"]={en:["Ethereum / Euro"],ro:["Ethereum / Euro"]},e.exports["#ETHGBP-symbol-description"]={en:["Ethereum / British Pound"],ro:["Ethereum / British Pound"]},e.exports["#ETHJPY-symbol-description"]={en:["Ethereum / Japanese Yen"],ro:["Ethereum / Japanese Yen"]},e.exports["#EURNOK-symbol-description"]={en:["Euro / Norwegian Krone"],ro:["Euro / Norwegian Krone"]},e.exports["#GBPPLN-symbol-description"]={en:["British Pound / Polish Zloty"],ro:["British Pound / Polish Zloty"]},e.exports["#MOEX:BR1!-symbol-description"]={en:["Brent Oil Futures"],ro:["Brent Oil Futures"]},e.exports["#NYMEX:KG1!-symbol-description"]={en:["Cotton Futures"],
ro:["Cotton Futures"]},e.exports["#NYMEX:PL1!-symbol-description"]={en:["Platinum Futures"],ro:["Platinum Futures"]},e.exports["#SOYBNUSD-symbol-description"]={en:["CFDs on Soybeans"],ro:["CFDs on Soybeans"]},e.exports["#SUGARUSD-symbol-description"]={en:["CFDs on Sugar"],ro:["CFDs on Sugar"]},e.exports["#TVC:IXIC-symbol-description"]={en:["US Composite Index"],ro:["NASDAQ Composite Index"]},e.exports["#TVC:RU-symbol-description"]={en:["Russell 1000 Index"],ro:["Russell 1000 Index"]},e.exports["#USDZAR-symbol-description"]={en:["U.S. Dollar / South African Rand"],ro:["U.S. Dollar / South African Rand"]},e.exports["#WHEATUSD-symbol-description"]={en:["CFDs on Wheat"],ro:["CFDs on Wheat"]},e.exports["#XRPEUR-symbol-description"]={en:["XRP / Euro"],ro:["Ripple / Euro"]},e.exports["#CBOT:S1!-symbol-description"]={en:["Soybean Futures"],ro:["Soybean Futures"]},e.exports["#SP:MID-symbol-description"]={en:["S&P 400 Index"],ro:["S&P 400 Index"]},e.exports["#TSX:XCUUSD-symbol-description"]={en:["CFDs on Copper"],ro:["CFDs on Copper"]},e.exports["#TVC:NYA-symbol-description"]={en:["NYSE Composite Index"],ro:["NYSE Composite Index"]},e.exports["#TVC:PLATINUM-symbol-description"]={en:["CFDs on Platinum (US$ / OZ)"],ro:["CFDs on Platinum (US$ / OZ)"]},e.exports["#TVC:SSMI-symbol-description"]={en:["Swiss Market Index"],ro:["Swiss Market Index"]},e.exports["#TVC:SXY-symbol-description"]={en:["Swiss Franc Currency Index"],ro:["Swiss Franc Currency Index"]},e.exports["#MOEX:RI1!-symbol-description"]={en:["RTS Index Futures"],ro:["RTS Index Futures"]},e.exports["#MOEX:MX1!-symbol-description"]={en:["MICEX Index Futures"],ro:["MICEX Index Futures"]},e.exports["#CBOE:BG1!-symbol-description"]={en:["Bitcoin CBOE Futures"],ro:["Bitcoin CBOE Futures"]},e.exports["#TVC:MY10-symbol-description"]={en:["Malaysia Government Bonds 10 YR"],ro:["Malaysia Government Bonds 10 YR"]},e.exports["#CME:S61!-symbol-description"]={en:["Swiss Franc Futures"],ro:["Swiss Franc Futures"]},e.exports["#TVC:DEU30-symbol-description"]={en:["DAX Index"],ro:["DAX Index"]},e.exports["#BCHEUR-symbol-description"]={en:["Bitcoin Cash / Euro"],ro:["Bitcoin Cash / Euro"]},e.exports["#TVC:ZXY-symbol-description"]={en:["New Zealand Dollar Currency Index"],ro:["New Zealand Dollar Currency Index"]},e.exports["#MIL:FTSEMIB-symbol-description"]={en:["FTSE MIB Index"],ro:["FTSE MIB Index"]},e.exports["#XETR:DAX-symbol-description"]={en:["DAX Index"],ro:["DAX Index"]},e.exports["#MOEX:IMOEX-symbol-description"]={en:["MOEX Russia Index"],ro:["MOEX Russia Index"]},e.exports["#FX:US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],ro:["Dow Jones Industrial Average Index"]},e.exports["#MOEX:RUAL-symbol-description"]={en:["United Company RUSAL PLC"],ro:["United Company RUSAL PLC"]},e.exports["#MOEX:MX2!-symbol-description"]={en:["MICEX Index Futures"],ro:["MICEX Index Futures"]},e.exports["#NEOUSD-symbol-description"]={en:["NEO / U.S. Dollar"],ro:["NEO / U.S. Dollar"]},e.exports["#XMRUSD-symbol-description"]={en:["Monero / U.S. Dollar"],ro:["Monero / U.S. Dollar"]},
e.exports["#ZECUSD-symbol-description"]={en:["Zcash / U.S. Dollar"],ro:["Zcash / U.S. Dollar"]},e.exports["#TVC:CAC-symbol-description"]={en:["CAC 40 Index"],ro:["CAC 40 Index"]},e.exports["#NASDAQ:ZS-symbol-description"]={en:["Zscaler Inc"],ro:["Zscaler Inc"]},e.exports["#TVC:GB10Y-symbol-description"]={en:["UK Government Bonds 10 YR Yield"],ro:["UK Government Bonds 10 YR Yield"]},e.exports["#TVC:AU10Y-symbol-description"]={en:["Australia Government Bonds 10 YR Yield"],ro:["Australia Government Bonds 10 YR Yield"]},e.exports["#TVC:CN10Y-symbol-description"]={en:["China Government Bonds 10 YR Yield"],ro:["China Government Bonds 10 YR Yield"]},e.exports["#TVC:DE10Y-symbol-description"]={en:["German Government Bonds 10 YR Yield"],ro:["German Government Bonds 10 YR Yield"]},e.exports["#TVC:ES10Y-symbol-description"]={en:["Spain Government Bonds 10 YR Yield"],ro:["Spain Government Bonds 10 YR Yield"]},e.exports["#TVC:FR10Y-symbol-description"]={en:["France Government Bonds 10 YR Yield"],ro:["France Government Bonds 10 YR Yield"]},e.exports["#TVC:IN10Y-symbol-description"]={en:["India Government Bonds 10 YR Yield"],ro:["India Government Bonds 10 yr"]},e.exports["#TVC:IT10Y-symbol-description"]={en:["Italy Government Bonds 10 YR Yield"],ro:["Italy Government Bonds 10 yr"]},e.exports["#TVC:JP10Y-symbol-description"]={en:["Japan Government Bonds 10 YR Yield"],ro:["Japan Government Bonds 10 yr"]},e.exports["#TVC:KR10Y-symbol-description"]={en:["Korea Government Bonds 10 YR Yield"],ro:["Korea Government Bonds 10 YR Yield"]},e.exports["#TVC:MY10Y-symbol-description"]={en:["Malaysia Government Bonds 10 YR Yield"],ro:["Malaysia Government Bonds 10 YR Yield"]},e.exports["#TVC:PT10Y-symbol-description"]={en:["Portugal Government Bonds 10 YR Yield"],ro:["Portugal Government Bonds 10 YR Yield"]},e.exports["#TVC:TR10Y-symbol-description"]={en:["Turkey Government Bonds 10 YR Yield"],ro:["Turkey Government Bonds 10 YR"]},e.exports["#TVC:US02Y-symbol-description"]={en:["US Government Bonds 2 YR Yield"],ro:["US Government Bonds 2 yr"]},e.exports["#TVC:US05Y-symbol-description"]={en:["US Government Bonds 5 YR Yield"],ro:["US Government Bonds 5 yr"]},e.exports["#TVC:US10Y-symbol-description"]={en:["US Government Bonds 10 YR Yield"],ro:["US Government Bonds 10 yr"]},e.exports["#INDEX:TWII-symbol-description"]={en:["Taiwan Weighted Index"],ro:["Taiwan Weighted Index"]},e.exports["#CME:J61!-symbol-description"]={en:["Japanese Yen Futures"],ro:["Japanese Yen Futures"]},e.exports["#CME_MINI:J71!-symbol-description"]={en:["Japanese Yen E-mini Futures"],ro:["Japanese Yen E-mini Futures"]},e.exports["#CME_MINI:WM1!-symbol-description"]={en:["E-micro Japanese Yen / U.S. Dollar Futures"],ro:["E-micro Japanese Yen / U.S. Dollar Futures"]},e.exports["#CME:M61!-symbol-description"]={en:["Mexican Peso Futures"],ro:["Mexican Peso Futures"]},e.exports["#CME:T61!-symbol-description"]={en:["South African Rand Futures"],ro:["South African Rand Futures"]},e.exports["#CME:SK1!-symbol-description"]={en:["Swedish Krona Futures"],ro:["Swedish Krona Futures"]},
e.exports["#CME:QT1!-symbol-description"]={en:["Chinese Renminbi / U.S. Dollar Futures"],ro:["Chinese Renminbi / U.S. Dollar Futures"]},e.exports["#COMEX:AUP1!-symbol-description"]={en:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"],ro:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"]},e.exports["#CME:L61!-symbol-description"]={en:["Brazilian Real Futures"],ro:["Brazilian Real Futures"]},e.exports["#CME:WP1!-symbol-description"]={en:["Polish Zloty Futures"],ro:["Polish Zloty Futures"]},e.exports["#CME:N61!-symbol-description"]={en:["New Zealand Dollar Futures"],ro:["New Zealand Dollar Futures"]},e.exports["#CME_MINI:MG1!-symbol-description"]={en:["E-micro Australian Dollar / U.S. Dollar Futures"],ro:["E-micro Australian Dollar / U.S. Dollar Futures"]},e.exports["#CME_MINI:WN1!-symbol-description"]={en:["E-micro Swiss Franc / U.S. Dollar Futures"],ro:["E-micro Swiss Franc / U.S. Dollar Futures"]},e.exports["#CME_MINI:MF1!-symbol-description"]={en:["E-micro Euro / U.S. Dollar Futures"],ro:["E-micro Euro / U.S. Dollar Futures"]},e.exports["#CME_MINI:E71!-symbol-description"]={en:["Euro E-mini Futures"],ro:["Euro E-mini Futures"]},e.exports["#CBOT:ZK1!-symbol-description"]={en:["Denatured Fuel Ethanol Futures"],ro:["Denatured Fuel Ethanol Futures"]},e.exports["#CME_MINI:MB1!-symbol-description"]={en:["E-micro British Pound / U.S. Dollar Futures"],ro:["E-micro British Pound / U.S. Dollar Futures"]},e.exports["#NYMEX_MINI:QU1!-symbol-description"]={en:["E-mini Gasoline Futures"],ro:["E-mini Gasoline Futures"]},e.exports["#NYMEX_MINI:QX1!-symbol-description"]={en:["E-mini Heating Oil Futures"],ro:["E-mini Heating Oil Futures"]},e.exports["#COMEX_MINI:QC1!-symbol-description"]={en:["E-mini Copper Futures"],ro:["E-mini Copper Futures"]},e.exports["#NYMEX_MINI:QG1!-symbol-description"]={en:["E-mini Natural Gas Futures"],ro:["E-mini Natural Gas Futures"]},e.exports["#CME:E41!-symbol-description"]={en:["U.S. Dollar / Turkish Lira Futures"],ro:["U.S. Dollar / Turkish Lira Futures"]},e.exports["#COMEX_MINI:QI1!-symbol-description"]={en:["Silver (Mini) Futures"],ro:["Silver (Mini) Futures"]},e.exports["#CME:DL1!-symbol-description"]={en:["Milk, Class III Futures"],ro:["Milk, Class III Futures"]},e.exports["#NYMEX:UX1!-symbol-description"]={en:["Uranium Futures"],ro:["Uranium Futures"]},e.exports["#CBOT:BO1!-symbol-description"]={en:["Soybean Oil Futures"],ro:["Soybean Oil Futures"]},e.exports["#CME:HE1!-symbol-description"]={en:["Lean Hogs Futures"],ro:["Lean Hogs Futures"]},e.exports["#NYMEX:IAC1!-symbol-description"]={en:["Newcastle Coal Futures"],ro:["Newcastle Coal Futures"]},e.exports["#NYMEX_MINI:QM1!-symbol-description"]={en:["E-mini Light Crude Oil Futures"],ro:["E-mini Light Crude Oil Futures"]},e.exports["#NYMEX:JMJ1!-symbol-description"]={en:["Mini Brent Financial Futures"],ro:["Mini Brent Financial Futures"]},e.exports["#COMEX:AEP1!-symbol-description"]={en:["Aluminium European Premium Futures"],ro:["Aluminium European Premium Futures"]},e.exports["#CBOT:ZQ1!-symbol-description"]={
en:["30 Day Federal Funds Interest Rate Futures"],ro:["30 Day Federal Funds Interest Rate Futures"]},e.exports["#CME:LE1!-symbol-description"]={en:["Live Cattle Futures"],ro:["Live Cattle Futures"]},e.exports["#CME:UP1!-symbol-description"]={en:["Swiss Franc / Japanese Yen Futures"],ro:["Swiss Franc / Japanese Yen Futures"]},e.exports["#CBOT:ZN1!-symbol-description"]={en:["10 Year T-Note Futures"],ro:["10 Year T-Note Futures"]},e.exports["#CBOT:ZB1!-symbol-description"]={en:["T-Bond Futures"],ro:["T-Bond Futures"]},e.exports["#CME:GF1!-symbol-description"]={en:["Feeder Cattle Futures"],ro:["Feeder Cattle Futures"]},e.exports["#CBOT:UD1!-symbol-description"]={en:["Ultra T-Bond Futures"],ro:["Ultra T-Bond Futures"]},e.exports["#CME:I91!-symbol-description"]={en:["CME Housing Futures — Washington DC"],ro:["CME Housing Futures — Washington DC"]},e.exports["#CBOT:ZO1!-symbol-description"]={en:["Oat Futures"],ro:["Oat Futures"]},e.exports["#CBOT:ZM1!-symbol-description"]={en:["Soybean Meal Futures"],ro:["Soybean Meal Futures"]},e.exports["#CBOT_MINI:XN1!-symbol-description"]={en:["Corn Mini Futures"],ro:["Corn Mini Futures"]},e.exports["#CBOT:ZC1!-symbol-description"]={en:["Corn Futures"],ro:["Corn Futures"]},e.exports["#CME:LS1!-symbol-description"]={en:["Lumber Futures"],ro:["Lumber Futures"]},e.exports["#CBOT_MINI:XW1!-symbol-description"]={en:["Wheat Mini Futures"],ro:["Wheat Mini Futures"]},e.exports["#CBOT_MINI:XK1!-symbol-description"]={en:["Soybean Mini Futures"],ro:["Soybean Mini Futures"]},e.exports["#CBOT:ZS1!-symbol-description"]={en:["Soybean Futures"],ro:["Soybean Futures"]},e.exports["#NYMEX:PA1!-symbol-description"]={en:["Palladium Futures"],ro:["Palladium Futures"]},e.exports["#CME:FTU1!-symbol-description"]={en:["E-mini FTSE 100 Index USD Futures"],ro:["E-mini FTSE 100 Index USD Futures"]},e.exports["#CBOT:ZR1!-symbol-description"]={en:["Rice Futures"],ro:["Rice Futures"]},e.exports["#COMEX_MINI:GR1!-symbol-description"]={en:["Gold (E-micro) Futures"],ro:["Gold (E-micro) Futures"]},e.exports["#COMEX_MINI:QO1!-symbol-description"]={en:["Gold (Mini) Futures"],ro:["Gold (Mini) Futures"]},e.exports["#CME_MINI:RL1!-symbol-description"]={en:["E-mini Russell 1000 Futures"],ro:["E-mini Russell 1000 Futures"]},e.exports["#CME_MINI:EW1!-symbol-description"]={en:["S&P 400 Midcap E-mini Futures"],ro:["S&P 400 Midcap E-mini Futures"]},e.exports["#COMEX:LD1!-symbol-description"]={en:["Lead Futures"],ro:["Lead Futures"]},e.exports["#CME_MINI:ES1!-symbol-description"]={en:["S&P 500 E-mini Futures"],ro:["S&P 500 E-mini Futures"]},e.exports["#TVC:SA40-symbol-description"]={en:["South Africa Top 40 Index"],ro:["South Africa Top 40 Index"]},e.exports["#BMV:ME-symbol-description"]={en:["S&P/BMV IPC Index"],ro:["IPC Mexico Index"]},e.exports["#BCBA:IMV-symbol-description"]={en:["S&P MERVAL Index"],ro:["MERVAL Index"]},e.exports["#HSI:HSI-symbol-description"]={en:["Hang Seng Index"],ro:["Hang Seng Index"]},e.exports["#BVL:SPBLPGPT-symbol-description"]={en:["S&P / BVL Peru General Index (PEN)"],
ro:["S&P / BVL Peru General Index (PEN)"]},e.exports["#EGX:EGX30-symbol-description"]={en:["EGX 30 Index"],ro:["EGX 30 Price Return Index"]},e.exports["#BVC:IGBC-symbol-description"]={en:["Indice General de la Bolsa de Valores de Colombia"],ro:["Indice General de la Bolsa de Valores de Colombia"]},e.exports["#TWSE:TAIEX-symbol-description"]={en:["Taiwan Capitalization Weighted Stock Index"],ro:["Taiwan Capitalization Weighted Stock Index"]},e.exports["#QSE:GNRI-symbol-description"]={en:["QE Index"],ro:["QE Index"]},e.exports["#BME:IBC-symbol-description"]={en:["IBEX 35 Index"],ro:["IBEX 35 Index"]},e.exports["#NZX:NZ50G-symbol-description"]={en:["S&P / NZX 50 Index Gross"],ro:["S&P / NZX 50 Index Gross"]},e.exports["#SIX:SMI-symbol-description"]={en:["Swiss Market Index"],ro:["Swiss Market Index"]},e.exports["#SZSE:399001-symbol-description"]={en:["Shenzhen Component Index"],ro:["SZSE Component Index"]},e.exports["#TADAWUL:TASI-symbol-description"]={en:["Tadawul All Shares Index"],ro:["Tadawul All Shares Index"]},e.exports["#IDX:COMPOSITE-symbol-description"]={en:["IDX Composite Index"],ro:["IDX Composite Index"]},e.exports["#EURONEXT:PX1-symbol-description"]={en:["CAC 40 Index"],ro:["CAC 40 Index"]},e.exports["#OMXHEX:OMXH25-symbol-description"]={en:["OMX Helsinki 25 Index"],ro:["OMX Helsinki 25 Index"]},e.exports["#EURONEXT:BEL20-symbol-description"]={en:["BEL 20 Index"],ro:["BEL 20 Index"]},e.exports["#TVC:STI-symbol-description"]={en:["Straits Times Index"],ro:["Straits Times Index"]},e.exports["#DFM:DFMGI-symbol-description"]={en:["DFM Index"],ro:["DFM Index"]},e.exports["#TVC:KOSPI-symbol-description"]={en:["Korea Composite Stock Price Index"],ro:["Korea Composite Stock Price Index"]},e.exports["#FTSEMYX:FBMKLCI-symbol-description"]={en:["FTSE Bursa Malaysia KLCI Index"],ro:["FTSE Bursa Malaysia KLCI Index"]},e.exports["#TASE:TA35-symbol-description"]={en:["TA-35 Index"],ro:["TA-35 Index"]},e.exports["#OMXSTO:OMXS30-symbol-description"]={en:["OMX Stockholm 30 Index"],ro:["OMX Stockholm 30 Index"]},e.exports["#OMXICE:OMXI8-symbol-description"]={en:["OMX Iceland 8 Index"],ro:["OMX Iceland 8 Index"]},e.exports["#NSENG:NSE30-symbol-description"]={en:["NSE 30 Index"],ro:["NSE 30 Index"]},e.exports["#BAHRAIN:BSEX-symbol-description"]={en:["Bahrain All Share Index"],ro:["Bahrain All Share Index"]},e.exports["#OMXTSE:OMXTGI-symbol-description"]={en:["OMX Tallinn Gross Index"],ro:["OMX Tallinn GI"]},e.exports["#OMXCOP:OMXC25-symbol-description"]={en:["OMX Copenhagen 25 Index"],ro:["OMX Copenhagen 25 Index"]},e.exports["#OMXRSE:OMXRGI-symbol-description"]={en:["OMX Riga Gross Index"],ro:["OMX Riga GI"]},e.exports["#BELEX:BELEX15-symbol-description"]={en:["BELEX 15 Index"],ro:["BELEX 15 Index"]},e.exports["#OMXVSE:OMXVGI-symbol-description"]={en:["OMX Vilnius Gross Index"],ro:["OMX Vilnius GI"]},e.exports["#EURONEXT:AEX-symbol-description"]={en:["AEX Index"],ro:["AEX Index"]},e.exports["#CBOE:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],ro:["Volatility S&P 500 Index"]},
e.exports["#NASDAQ:XAU-symbol-description"]={en:["PHLX Gold and Silver Sector Index"],ro:["PHLX Gold and Silver Sector Index"]},e.exports["#DJ:DJUSCL-symbol-description"]={en:["Dow Jones U.S. Coal Index"],ro:["Dow Jones U.S. Coal Index"]},e.exports["#DJ:DJCIKC-symbol-description"]={en:["Dow Jones Commodity Index Coffee"],ro:["Dow Jones Commodity Index Coffee"]},e.exports["#DJ:DJCIEN-symbol-description"]={en:["Dow Jones Commodity Index Energy"],ro:["Dow Jones Commodity Index Energy"]},e.exports["#NASDAQ:OSX-symbol-description"]={en:["PHLX Oil Service Sector Index"],ro:["PHLX Oil Service Sector Index"]},e.exports["#DJ:DJCISB-symbol-description"]={en:["Dow Jones Commodity Index Sugar"],ro:["Dow Jones Commodity Index Sugar"]},e.exports["#DJ:DJCICC-symbol-description"]={en:["Dow Jones Commodity Index Cocoa"],ro:["Dow Jones Commodity Index Cocoa"]},e.exports["#DJ:DJCIGR-symbol-description"]={en:["Dow Jones Commodity Index Grains"],ro:["Dow Jones Commodity Index Grains"]},e.exports["#DJ:DJCIAGC-symbol-description"]={en:["Dow Jones Commodity Index Agriculture Capped Component"],ro:["Dow Jones Commodity Index Agriculture Capped Component"]},e.exports["#DJ:DJCISI-symbol-description"]={en:["Dow Jones Commodity Index Silver"],ro:["Dow Jones Commodity Index Silver"]},e.exports["#DJ:DJCIIK-symbol-description"]={en:["Dow Jones Commodity Index Nickel"],ro:["Dow Jones Commodity Index Nickel"]},e.exports["#NASDAQ:HGX-symbol-description"]={en:["PHLX Housing Sector Index"],ro:["PHLX Housing Sector Index"]},e.exports["#DJ:DJCIGC-symbol-description"]={en:["Dow Jones Commodity Index Gold"],ro:["Dow Jones Commodity Index Gold"]},e.exports["#SP:SPGSCI-symbol-description"]={en:["S&P Goldman Sachs Commodity Index"],ro:["S&P Goldman Sachs Commodity Index"]},e.exports["#NASDAQ:UTY-symbol-description"]={en:["PHLX Utility Sector Index"],ro:["PHLX Utility Sector Index"]},e.exports["#DJ:DJU-symbol-description"]={en:["Dow Jones Utility Average Index"],ro:["Dow Jones Utility Average Index"]},e.exports["#SP:SVX-symbol-description"]={en:["S&P 500 Value Index"],ro:["S&P 500 Value Index"]},e.exports["#SP:OEX-symbol-description"]={en:["S&P 100 Index"],ro:["S&P 100 Index"]},e.exports["#CBOE:OEX-symbol-description"]={en:["S&P 100 Index"],ro:["S&P 100 Index"]},e.exports["#NASDAQ:SOX-symbol-description"]={en:["Philadelphia Semiconductor Index"],ro:["Philadelphia Semiconductor Index"]},e.exports["#RUSSELL:RUI-symbol-description"]={en:["Russell 1000 Index"],ro:["Russell 1000 Index"]},e.exports["#RUSSELL:RUA-symbol-description"]={en:["Russell 3000 Index"],ro:["Russell 3000 Index"]},e.exports["#RUSSELL:RUT-symbol-description"]={en:["Russell 2000 Index"],ro:["Russell 2000 Index"]},e.exports["#NYSE:XMI-symbol-description"]={en:["NYSE ARCA Major Market Index"],ro:["NYSE ARCA Major Market Index"]},e.exports["#NYSE:XAX-symbol-description"]={en:["AMEX Composite Index"],ro:["AMEX Composite Index"]},e.exports["#NASDAQ:NDX-symbol-description"]={en:["Nasdaq 100 Index"],ro:["Nasdaq 100 Index"]},e.exports["#NASDAQ:IXIC-symbol-description"]={en:["Nasdaq Composite Index"],
ro:["Nasdaq Composite Index"]},e.exports["#DJ:DJT-symbol-description"]={en:["Dow Jones Transportation Average Index"],ro:["Dow Jones Transportation Average Index"]},e.exports["#NYSE:NYA-symbol-description"]={en:["NYSE Composite Index"],ro:["NYSE Composite Index"]},e.exports["#NYMEX:CJ1!-symbol-description"]={en:["Cocoa Futures"],ro:["Cocoa Futures"]},e.exports["#USDILS-symbol-description"]={en:["U.S. Dollar / Israeli Shekel"],ro:["U.S. Dollar / Israeli Shekel"]},e.exports["#TSXV:F-symbol-description"]={en:["Fiore Gold Inc"],ro:["Fiore Gold Inc"]},e.exports["#SIX:F-symbol-description"]={en:["Ford Motor Company"],ro:["Ford Motor Company"]},e.exports["#BMV:F-symbol-description"]={en:["Ford Motor Company"],ro:["Ford Motor Company"]},e.exports["#TWII-symbol-description"]={en:["Taiwan Weighted Index"],ro:["Taiwan Weighted Index"]},e.exports["#TVC:PL10Y-symbol-description"]={en:["Poland Government Bonds 10 YR Yield"],ro:["Poland Government Bonds 10 YR Yield"]},e.exports["#TVC:PL05Y-symbol-description"]={en:["Poland Government Bonds 5 YR Yield"],ro:["Poland Government Bonds 5 YR Yield"]},e.exports["#SET:GC-symbol-description"]={en:["Global Connections Public Company"],ro:["Global Connections Public Company"]},e.exports["#TSX:GC-symbol-description"]={en:["Great Canadian Gaming Corporation"],ro:["Great Canadian Gaming Corporation"]},e.exports["#TVC:FTMIB-symbol-description"]={en:["Milano Italia Borsa Index"],ro:["Milano Italia Borsa Index"]},e.exports["#OANDA:SPX500USD-symbol-description"]={en:["S&P 500 Index"],ro:["S&P 500 Index"]},e.exports["#BMV:CT-symbol-description"]={en:["China SX20 RT"],ro:["China SX20 RT"]},e.exports["#TSXV:CT-symbol-description"]={en:["Centenera Mining Corporation"],ro:["Centenera Mining Corporation"]},e.exports["#BYBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Contract"],ro:["ETHUSD Perpetual Contract"]},e.exports["#BYBIT:XRPUSD-symbol-description"]={en:["XRPUSD Perpetual Contract"],ro:["XRPUSD Perpetual Contract"]},e.exports["#BYBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Contract"],ro:["BTCUSD Perpetual Contract"]},e.exports["#BITMEX:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],ro:["ETHUSD Perpetual Futures Contract"]},e.exports["#DERIBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Futures Contract"],ro:["BTCUSD Perpetual Futures Contract"]},e.exports["#DERIBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],ro:["ETHUSD Perpetual Futures Contract"]},e.exports["#USDHUF-symbol-description"]={en:["U.S. Dollar / Hungarian Forint"],ro:["U.S. Dollar / Hungarian Forint"]},e.exports["#USDTHB-symbol-description"]={en:["U.S. Dollar / Thai Baht"],ro:["U.S. Dollar / Thai Baht"]},e.exports["#FOREXCOM:US2000-symbol-description"]={en:["US Small Cap 2000"],ro:["US Small Cap 2000"]},e.exports["#TSXV:PBR-symbol-description"]={en:["Para Resources Inc"],ro:["Para Resources Inc"]},e.exports["#NYSE:SI-symbol-description"]={en:["Silvergate Capital Corporation"],ro:["Silvergate Capital Corporation"]},e.exports["#NASDAQ:LE-symbol-description"]={
en:["Lands' End Inc"],ro:["Lands' End Inc"]},e.exports["#CME:CB1!-symbol-description"]={en:["Butter Futures-Cash (Continuous: Current contract in front)"],ro:["Butter Futures-Cash (Continuous: Current contract in front)"]},e.exports["#LSE:SCHO-symbol-description"]={en:["Scholium Group Plc Ord 1P"],ro:["Scholium Group Plc Ord 1P"]},e.exports["#NEO:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],ro:["Hanwei Energy Services Corp."]},e.exports["#NYSE:HE-symbol-description"]={en:["Hawaiian Electric Industries"],ro:["Hawaiian Electric Industries"]},e.exports["#OMXCOP:SCHO-symbol-description"]={en:["Schouw & Co A/S"],ro:["Schouw & Co A/S"]},e.exports["#TSX:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],ro:["Hanwei Energy Services Corp."]},e.exports["#BSE:ITI-symbol-description"]={en:["ITI Ltd"],ro:["ITI Ltd"]},e.exports["#NSE:ITI-symbol-description"]={en:["Indian Telephone Industries Limited"],ro:["Indian Telephone Industries Limited"]},e.exports["#TSX:LS-symbol-description"]={en:["Middlefield Healthcare & Life Sciences Dividend Fund"],ro:["Middlefield Healthcare & Life Sciences Dividend Fund"]},e.exports["#BITMEX:XBT-symbol-description"]={en:["Bitcoin / U.S. Dollar Index"],ro:["Bitcoin / U.S. Dollar Index"]},e.exports["#CME_MINI:RTY1!-symbol-description"]={en:["E-Mini Russell 2000 Index Futures"],ro:["E-Mini Russell 2000 Index Futures"]},e.exports["#CRYPTOCAP:TOTAL-symbol-description"]={en:["Crypto Total Market Cap, $"],ro:["Crypto Total Market Cap, $"]},e.exports["#ICEUS:DX1!-symbol-description"]={en:["U.S. Dollar Index Futures"],ro:["U.S. Dollar Index Futures"]},e.exports["#NYMEX:TT1!-symbol-description"]={en:["Cotton Futures"],ro:["Cotton Futures"]},e.exports["#PHEMEX:BTCUSD-symbol-description"]={en:["BTC Perpetual Futures Contract"],ro:["BTC Perpetual Futures Contract"]},e.exports["#PHEMEX:ETHUSD-symbol-description"]={en:["ETH Perpetual Futures Contract"],ro:["ETH Perpetual Futures Contract"]},e.exports["#PHEMEX:XRPUSD-symbol-description"]={en:["XRP Perpetual Futures Contract"],ro:["XRP Perpetual Futures Contract"]},e.exports["#PHEMEX:LTCUSD-symbol-description"]={en:["LTC Perpetual Futures Contract"],ro:["LTC Perpetual Futures Contract"]},e.exports["#BITCOKE:BCHUSD-symbol-description"]={en:["BCH Quanto Swap"],ro:["BCH Quanto Swap"]},e.exports["#BITCOKE:BTCUSD-symbol-description"]={en:["BTC Quanto Swap"],ro:["BTC Quanto Swap"]},e.exports["#BITCOKE:ETHUSD-symbol-description"]={en:["ETH Quanto Swap"],ro:["ETH Quanto Swap"]},e.exports["#BITCOKE:LTCUSD-symbol-description"]={en:["LTC Quanto Swap"],ro:["LTC Quanto Swap"]},e.exports["#TVC:CA10-symbol-description"]={en:["Canadian Government Bonds, 10 YR"],ro:["Canadian Government Bonds, 10 YR"]},e.exports["#TVC:CA10Y-symbol-description"]={en:["Canadian Government Bonds 10 YR Yield"],ro:["Canadian Government Bonds 10 YR Yield"]},e.exports["#TVC:ID10Y-symbol-description"]={en:["Indonesia Government Bonds 10 YR Yield"],ro:["Indonesia Government Bonds 10 YR Yield"]},e.exports["#TVC:NL10-symbol-description"]={en:["Netherlands Government Bonds, 10 YR"],
ro:["Netherlands Government Bonds, 10 YR"]},e.exports["#TVC:NL10Y-symbol-description"]={en:["Netherlands Government Bonds 10 YR Yield"],ro:["Netherlands Government Bonds 10 YR Yield"]},e.exports["#TVC:NZ10-symbol-description"]={en:["New Zealand Government Bonds, 10 YR"],ro:["New Zealand Government Bonds, 10 YR"]},e.exports["#TVC:NZ10Y-symbol-description"]={en:["New Zealand Government Bonds 10 YR Yield"],ro:["New Zealand Government Bonds 10 YR Yield"]},e.exports["#SOLUSD-symbol-description"]={en:["Solana / U.S. Dollar"],ro:["Solana / U.S. Dollar"]},e.exports["#LUNAUSD-symbol-description"]={en:["Luna / U.S. Dollar"],ro:["Luna / U.S. Dollar"]},e.exports["#UNIUSD-symbol-description"]={en:["Uniswap / U.S. Dollar"],ro:["Uniswap / U.S. Dollar"]},e.exports["#LTCBRL-symbol-description"]={en:["Litecoin / Brazilian Real"],ro:["Litecoin / Brazilian Real"]},e.exports["#ETCEUR-symbol-description"]={en:["Ethereum Classic / Euro"],ro:["Ethereum Classic / Euro"]},e.exports["#ETHKRW-symbol-description"]={en:["Ethereum / South Korean Won"],ro:["Ethereum / South Korean Won"]},e.exports["#BTCRUB-symbol-description"]={en:["Bitcoin / Russian Ruble"],ro:["Bitcoin / Russian Ruble"]},e.exports["#BTCTHB-symbol-description"]={en:["Bitcoin / Thai Baht"],ro:["Bitcoin / Thai Baht"]},e.exports["#ETHTHB-symbol-description"]={en:["Ethereum / Thai Baht"],ro:["Ethereum / Thai Baht"]},e.exports["#TVC:EU10YY-symbol-description"]={en:["Euro Government Bonds 10 YR Yield"],ro:["Euro Government Bonds 10 YR Yield"]},e.exports["#NASDAQ:LCID-symbol-description"]={en:["Lucid Group, Inc."],ro:["Lucid Group, Inc."]},e.exports["#TADAWUL:2370-symbol-description"]={en:["Middle East Specialized Cables Co."],ro:["Middle East Specialized Cables Co."]},e.exports["#NASDAQ:GOOG-symbol-description"]={en:["#NASDAQ:GOOG-symbol-description"],ro:["#NASDAQ:GOOG-symbol-description"]},e.exports["#NASDAQ:GOOGL-symbol-description"]={en:["#NASDAQ:GOOGL-symbol-description"],ro:["#NASDAQ:GOOGL-symbol-description"]}}}]);