(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6265],{83072:e=>{e.exports={small:"small-CtnpmPzP",medium:"medium-CtnpmPzP",large:"large-CtnpmPzP",switchView:"switchView-CtnpmPzP",checked:"checked-CtnpmPzP",track:"track-CtnpmPzP",disabled:"disabled-CtnpmPzP",thumb:"thumb-CtnpmPzP"}},12006:e=>{e.exports={switcher:"switcher-fwE97QDf",input:"input-fwE97QDf",thumbWrapper:"thumbWrapper-fwE97QDf",activeStylesEnabled:"activeStylesEnabled-fwE97QDf",select:"select-fwE97QDf"}},57695:e=>{e.exports={scrollable:"scrollable-vwgPOHG8",tabs:"tabs-vwgPOHG8"}},87075:e=>{e.exports={defaultsButtonText:"defaultsButtonText-aJgjxj2V",defaultsButtonItem:"defaultsButtonItem-aJgjxj2V",defaultsButtonIcon:"defaultsButtonIcon-aJgjxj2V"}},87509:e=>{e.exports={themesButtonText:"themesButtonText-AeBgp7zz",themesButtonIcon:"themesButtonIcon-AeBgp7zz",defaultsButtonText:"defaultsButtonText-AeBgp7zz",defaultsButtonItem:"defaultsButtonItem-AeBgp7zz"}},71150:e=>{e.exports={separator:"separator-QjUlCDId",small:"small-QjUlCDId",normal:"normal-QjUlCDId",large:"large-QjUlCDId"}},456:e=>{e.exports={"link-item":"link-item-eIA09f0e"}},10399:e=>{e.exports={"arrow-icon":"arrow-icon-NIrWNOPk",dropped:"dropped-NIrWNOPk","size-xsmall":"size-xsmall-NIrWNOPk","size-small":"size-small-NIrWNOPk","size-medium":"size-medium-NIrWNOPk","size-large":"size-large-NIrWNOPk","size-xlarge":"size-xlarge-NIrWNOPk"}},24554:e=>{e.exports={"underline-tab":"underline-tab-cfYYXvwA","disable-focus-outline":"disable-focus-outline-cfYYXvwA","enable-cursor-pointer":"enable-cursor-pointer-cfYYXvwA",selected:"selected-cfYYXvwA","disable-active-state-styles":"disable-active-state-styles-cfYYXvwA","size-xsmall":"size-xsmall-cfYYXvwA","size-small":"size-small-cfYYXvwA","size-medium":"size-medium-cfYYXvwA","size-large":"size-large-cfYYXvwA","size-xlarge":"size-xlarge-cfYYXvwA",fake:"fake-cfYYXvwA","margin-xsmall":"margin-xsmall-cfYYXvwA","margin-small":"margin-small-cfYYXvwA","margin-medium":"margin-medium-cfYYXvwA","margin-large":"margin-large-cfYYXvwA","margin-xlarge":"margin-xlarge-cfYYXvwA",collapse:"collapse-cfYYXvwA","ellipsis-children":"ellipsis-children-cfYYXvwA"}},7633:e=>{e.exports={"scroll-wrap":"scroll-wrap-SmxgjhBJ","size-xlarge":"size-xlarge-SmxgjhBJ","enable-scroll":"enable-scroll-SmxgjhBJ","underline-tabs":"underline-tabs-SmxgjhBJ","size-large":"size-large-SmxgjhBJ","size-medium":"size-medium-SmxgjhBJ","size-small":"size-small-SmxgjhBJ","size-xsmall":"size-xsmall-SmxgjhBJ","make-grid-column":"make-grid-column-SmxgjhBJ","stretch-tabs":"stretch-tabs-SmxgjhBJ","equal-tab-size":"equal-tab-size-SmxgjhBJ"}},29662:e=>{e.exports={underline:"underline-Pun8HxCz",center:"center-Pun8HxCz",corner:"corner-Pun8HxCz",disabled:"disabled-Pun8HxCz"}},38546:(e,t,l)=>{"use strict";l.d(t,{DialogTabs:()=>i});var n=l(50959),r=l(93081);const i=n.forwardRef((function(e,t){const{id:l,tabs:i,activeTab:o,onChange:s,className:a}=e;return n.createElement("div",{className:a,ref:t},n.createElement(r.UnderlineButtonTabs,{id:l,items:i,isActive:function(e){return e.id===o},
onActivate:function(e){s(e.id)},overflowBehaviour:"scroll"}))}))},81346:(e,t,l)=>{"use strict";l.r(t),l.d(t,{EditObjectDialogRenderer:()=>El});var n=l(50959),r=l(50151),i=l(11542),o=l(45126),s=l(86129),a=l(56840),c=l(56570),d=l(76422),p=l(51768),u=l(50182),h=l(59064),m=l(86656),v=l(72708),y=l(37289),g=l(630),b=l(48531),f=l(38546),w=l(78890),C=l(57695);class P extends n.PureComponent{constructor(e){var t;super(e),this._handleClose=e=>{(null==e?void 0:e.target)&&(e.target.closest('[data-dialog-name="gopro"]')||e.target.closest("[data-name=support-dialog]"))||this.props.onClose()},this._handleResetToDefaults=()=>{const{source:e,model:t}=this.props;(0,v.isStudy)(e)&&t.restorePropertiesForSource(e)},this._handleSaveAsDefaults=()=>{const{source:e}=this.props;(0,v.isStudy)(e)&&e.properties().saveDefaults()},this._renderFooterLeft=e=>{const{source:t,model:l}=this.props;if((0,g.isLineTool)(t))return n.createElement(b.FooterMenu,{sources:[t],chartUndoModel:l});if((0,v.isStudy)(t))return n.createElement(w.PropertyActions,{saveAsDefaults:this._handleSaveAsDefaults,resetToDefaults:this._handleResetToDefaults,mode:e?"compact":"normal"});throw new TypeError("Unsupported source type.")},this._handleSelect=e=>{this.setState({activeTabId:e},(()=>{this._requestResize&&this._requestResize()})),this.props.onActiveTabChanged&&this.props.onActiveTabChanged(e)},this._handleScroll=()=>{h.globalCloseDelegate.fire()},this._handleSubmit=()=>{this.props.onSubmit(),this.props.onClose()};const{pages:l,initialActiveTab:r}=this.props,i=null!==(t=l.find((e=>e.id===r)))&&void 0!==t?t:l[0];this.state={activeTabId:i.id}}render(){const{title:e,onCancel:t,onClose:l,shouldReturnFocus:r}=this.props;return n.createElement(u.AdaptiveConfirmDialog,{dataName:"indicator-properties-dialog",title:e,isOpened:!0,onSubmit:this._handleSubmit,onCancel:t,onClickOutside:this._handleClose,onClose:l,footerLeftRenderer:this._renderFooterLeft,render:this._renderChildren(),submitOnEnterKey:!1,shouldReturnFocus:r})}_renderChildren(){return({requestResize:e})=>{var t;this._requestResize=e;const{pages:l,source:r,model:i}=this.props,{activeTabId:o}=this.state,s=null!==(t=l.find((e=>e.id===o)))&&void 0!==t?t:l[0],a="Component"in s?void 0:s.page,c=l.map((({label:e,id:t})=>({label:e,id:t,dataId:`indicator-properties-dialog-tabs-${t}`})));return n.createElement(n.Fragment,null,n.createElement(f.DialogTabs,{className:C.tabs,id:"indicator-properties-dialog-tabs",activeTab:s.id,onChange:this._handleSelect,tabs:c}),n.createElement(m.TouchScrollContainer,{className:C.scrollable,onScroll:this._handleScroll},"Component"in s?n.createElement(s.Component,{source:r,model:i}):n.createElement(y.PropertiesEditorTab,{page:a,tableKey:s.id})))}}}var S=l(5236),E=l(28388);class T extends n.PureComponent{constructor(e){super(e),this._properties=this.props.source.properties(),this._inputs=new E.MetaInfoHelper(this.props.source.metaInfo()).getUserEditableInputs()}render(){return n.createElement(S.InputsTabContent,{property:this._properties,model:this.props.model,study:this.props.source,
studyMetaInfo:this.props.source.metaInfo(),inputs:this._inputs})}}var x=l(22064),k=l(94113),_=l(68159),I=l(353),R=l(30582),D=l(49794);const L=new o.TranslatedString("change visibility",i.t(null,void 0,l(1924)));class V extends n.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{setValue:t}=this.context,{visible:l}=this.props;l&&t(l,e,L)}}render(){const{id:e,title:t,visible:r,disabled:o}=this.props,a=(0,s.clean)(i.t(t,{context:"input"},l(32856)),!0);return n.createElement(R.BoolInputComponent,{label:a,disabled:o,input:{id:e,type:"bool",defval:!0,name:"visible"},value:!r||(0,D.getPropertyValue)(r),onChange:this._onChange})}}V.contextType=I.StylePropertyContext;var B=l(24980),N=l(11684),z=l(10428),A=l(94697),M=l(94152),W=l(18819),F=l(14643),H=l(46464),O=l(96298),U=l(18621),G=l(98450),Y=l(91512),j=l(93976),X=l(72914),q=l(21579);const J={[k.LineStudyPlotStyle.Line]:{type:k.LineStudyPlotStyle.Line,order:0,icon:M,label:i.t(null,void 0,l(3554))},[k.LineStudyPlotStyle.LineWithBreaks]:{type:k.LineStudyPlotStyle.LineWithBreaks,order:1,icon:W,label:i.t(null,void 0,l(34862))},[k.LineStudyPlotStyle.StepLine]:{type:k.LineStudyPlotStyle.StepLine,order:2,icon:F,label:i.t(null,void 0,l(69217))},[k.LineStudyPlotStyle.StepLineWithBreaks]:{type:k.LineStudyPlotStyle.StepLineWithBreaks,order:3,icon:H,label:i.t(null,void 0,l(14788))},[k.LineStudyPlotStyle.StepLineWithDiamonds]:{type:k.LineStudyPlotStyle.StepLineWithDiamonds,order:4,icon:O,label:i.t(null,void 0,l(11877))},[k.LineStudyPlotStyle.Histogram]:{type:k.LineStudyPlotStyle.Histogram,order:5,icon:U,label:i.t(null,void 0,l(78057))},[k.LineStudyPlotStyle.Cross]:{type:k.LineStudyPlotStyle.Cross,order:6,icon:G,label:i.t(null,{context:"chart_type"},l(33857))},[k.LineStudyPlotStyle.Area]:{type:k.LineStudyPlotStyle.Area,order:7,icon:Y,label:i.t(null,void 0,l(34456))},[k.LineStudyPlotStyle.AreaWithBreaks]:{type:k.LineStudyPlotStyle.AreaWithBreaks,order:8,icon:j,label:i.t(null,void 0,l(7349))},[k.LineStudyPlotStyle.Columns]:{type:k.LineStudyPlotStyle.Columns,order:9,icon:X,label:i.t(null,void 0,l(55761))},[k.LineStudyPlotStyle.Circles]:{type:k.LineStudyPlotStyle.Circles,order:10,icon:q,label:i.t(null,void 0,l(5669))}},$=Object.values(J).sort(((e,t)=>e.order-t.order)).map((e=>({value:e.type,selectedContent:n.createElement(A.DisplayItem,{icon:e.icon}),content:n.createElement(A.DropItem,{icon:e.icon,label:e.label})}))),Z=i.t(null,void 0,l(72926));class K extends n.PureComponent{render(){const{id:e,plotType:t,className:l,priceLine:r,plotTypeChange:i,priceLineChange:o,disabled:s}=this.props;if(!(t in J))return null;const a={readonly:!0,content:n.createElement(n.Fragment,null,n.createElement(z.MenuItemSwitcher,{id:"PlotTypePriceLineSwitch",checked:r,label:Z,preventLabelHighlight:!0,value:"priceLineSwitcher",onChange:o}),n.createElement(N.PopupMenuSeparator,null))};return n.createElement(A.IconDropdown,{id:e,disabled:s,className:l,hideArrowButton:!0,items:[a,...$],value:t,onChange:i})}}var Q=l(14118),ee=l(47159)
;const te=new o.TranslatedString("change plot type",i.t(null,void 0,l(43439))),le=new o.TranslatedString("change price line visibility",i.t(null,void 0,l(8662)));class ne extends n.PureComponent{constructor(){super(...arguments),this._onPlotTypeChange=e=>{const{setValue:t}=this.context,{styleProp:{plottype:l}}=this.props;l&&t(l,e,te)},this._onPriceLineChange=e=>{const{setValue:t}=this.context,{styleProp:{trackPrice:l}}=this.props;l&&t(l,e,le)}}render(){const{id:e,paletteColor:t,paletteColorProps:r,styleProp:o,isLine:s,hasPlotTypeSelect:a,grouped:c,offset:d}=this.props,p=r.childs();return n.createElement(B.InputRow,{grouped:c,label:n.createElement("div",{className:ee.childRowContainer},i.t(t.name,{context:"input"},l(32856))),offset:d},n.createElement(Q.ColorWithThicknessSelect,{disabled:!o.visible.value(),color:p.color,transparency:o.transparency,thickness:s?p.width:void 0,isPaletteColor:!0}),s&&a&&o.plottype&&o.trackPrice?n.createElement(K,{id:(0,x.createDomId)(e,"plot-type-select"),disabled:!o.visible.value(),className:ee.smallStyleControl,plotType:o.plottype.value(),priceLine:o.trackPrice.value(),plotTypeChange:this._onPlotTypeChange,priceLineChange:this._onPriceLineChange}):null)}}ne.contextType=I.StylePropertyContext;var re=l(71891);function ie(e,t,l,i,o,s,a){const c=t.colors,d=l.colors;return Object.keys(c).map(((t,l)=>n.createElement(ne,{key:a?`${t}-secondary`:t,id:e,grouped:!0,paletteColor:(0,r.ensureDefined)(c[t]),paletteColorProps:(0,r.ensureDefined)(d[t]),styleProp:i,isLine:o,hasPlotTypeSelect:0===l,offset:s})))}class oe extends n.PureComponent{render(){const{plot:e,area:t,palette:l,paletteProps:i,hideVisibilitySwitch:o,styleProp:s,showOnlyTitle:a,showSeparator:c=!0,offset:d,secondaryPalette:p,secondaryPaletteProps:u,title:h}=this.props,m=e?e.id:(0,r.ensureDefined)(t).id,v=!m.startsWith("fill")&&e&&(0,k.isLinePlot)(e);return n.createElement(n.Fragment,null,!o&&n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2,offset:d},a?n.createElement("div",null,h):n.createElement(V,{id:m,title:h,visible:s.visible}))),ie(m,l,i,s,v,d),p&&u&&ie(m,p,u,s,v,d,!0),c&&n.createElement(re.PropertyTable.GroupSeparator,null))}}oe.contextType=I.StylePropertyContext;var se=l(1183);class ae extends n.PureComponent{constructor(e){super(e),this._visible=new se.StudyPlotVisibleProperty(e.styleProp.display)}render(){const{title:e,plot:t,area:l,palette:r,paletteProps:i,hideVisibilitySwitch:o,styleProp:s,showOnlyTitle:a,showSeparator:c=!0,offset:d}=this.props;return n.createElement(oe,{plot:t,area:l,title:e,palette:r,paletteProps:i,styleProp:{...s,visible:this._visible},showSeparator:c,hideVisibilitySwitch:o,showOnlyTitle:a,offset:d})}componentWillUnmount(){this._visible.destroy()}}ae.contextType=I.StylePropertyContext;class ce extends n.PureComponent{constructor(e){super(e),this._visible=new se.StudyPlotVisibleProperty(e.display)}render(){const{id:e,title:t,disabled:l}=this.props;return n.createElement(V,{id:e,title:t,disabled:l,visible:this._visible})}componentWillUnmount(){
this._visible.destroy()}}ce.contextType=I.StylePropertyContext;var de=l(50890);const pe=new o.TranslatedString("change plot type",i.t(null,void 0,l(43439))),ue=new o.TranslatedString("change price line visibility",i.t(null,void 0,l(8662)));class he extends n.PureComponent{constructor(){super(...arguments),this._onPlotTypeChange=e=>{const{setValue:t}=this.context,{property:{plottype:l}}=this.props;l&&t(l,e,pe)},this._onPriceLineChange=e=>{const{setValue:t}=this.context,{property:{trackPrice:l}}=this.props;l&&t(l,e,ue)}}render(){const{id:e,title:t,isRGB:l,isFundamental:r,property:{color:i,plottype:o,linewidth:s,transparency:a,trackPrice:c,display:d}}=this.props;return n.createElement(B.InputRow,{label:n.createElement(ce,{id:e,title:t,display:d})},l&&!r?this._getInputForRgb():n.createElement(Q.ColorWithThicknessSelect,{disabled:0===d.value(),color:i,transparency:a,thickness:s}),n.createElement(K,{id:(0,x.createDomId)(e,"plot-type-select"),disabled:0===d.value(),className:ee.smallStyleControl,plotType:o.value(),priceLine:c.value(),plotTypeChange:this._onPlotTypeChange,priceLineChange:this._onPriceLineChange}))}_getInputForRgb(){const{id:e,showLineWidth:t,property:l}=this.props,{linewidth:r,display:i}=l;return r&&t?n.createElement(de.LineWidthSelect,{id:(0,x.createDomId)(e,"line-width-select"),property:r,disabled:0===i.value()}):null}}he.contextType=I.StylePropertyContext;const me=n.createContext(null);class ve extends n.PureComponent{render(){const{id:e,isRGB:t,title:l,visible:r,color:i,transparency:o,thickness:s,children:a,switchable:c=!0,offset:d,grouped:p,disabled:u}=this.props;return n.createElement(B.InputRow,{label:c?n.createElement(V,{id:e,title:l,visible:r,disabled:u}):l,offset:d,grouped:p},t?null:n.createElement(Q.ColorWithThicknessSelect,{disabled:u||r&&!(Array.isArray(r)?r[0].value():r.value()),color:i,transparency:o,thickness:s}),a)}}ve.contextType=I.StylePropertyContext;class ye extends n.PureComponent{constructor(e){super(e),this._visible=new se.StudyPlotVisibleProperty(e.display)}render(){const{id:e,isRGB:t,title:l,color:r,transparency:i,thickness:o,children:s,switchable:a=!0,offset:c,grouped:d}=this.props;return n.createElement(ve,{id:e,isRGB:t,title:l,color:r,transparency:i,thickness:o,children:s,switchable:a,offset:c,grouped:d,visible:this._visible})}componentWillUnmount(){this._visible.destroy()}}ye.contextType=I.StylePropertyContext;class ge extends n.PureComponent{render(){const{id:e,isRGB:t,property:{colorup:l,colordown:i,transparency:o,display:s}}=this.props;return n.createElement(me.Consumer,null,(a=>n.createElement(n.Fragment,null,n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2,grouped:!0},n.createElement(ce,{id:e,title:it((0,r.ensureNotNull)(a),e),display:s}))),!t&&n.createElement(n.Fragment,null,n.createElement(ye,{id:e,title:Qe,color:l,transparency:o,display:s,switchable:!1,offset:!0,grouped:!0}),n.createElement(ye,{id:e,title:et,color:i,transparency:o,display:s,switchable:!1,offset:!0,grouped:!0
})),n.createElement(re.PropertyTable.GroupSeparator,null))))}}ge.contextType=I.StylePropertyContext;var be=l(87795),fe=l.n(be),we=l(97754),Ce=l.n(we),Pe=l(31261),Se=l(90405),Ee=l(85904);const Te={[Ee.MarkLocation.AboveBar]:{value:Ee.MarkLocation.AboveBar,content:i.t(null,void 0,l(8305)),order:0},[Ee.MarkLocation.BelowBar]:{value:Ee.MarkLocation.BelowBar,content:i.t(null,void 0,l(9417)),order:1},[Ee.MarkLocation.Top]:{value:Ee.MarkLocation.Top,content:i.t(null,void 0,l(97118)),order:2},[Ee.MarkLocation.Bottom]:{value:Ee.MarkLocation.Bottom,content:i.t(null,void 0,l(27567)),order:3},[Ee.MarkLocation.Absolute]:{value:Ee.MarkLocation.Absolute,content:i.t(null,void 0,l(69758)),order:4}},xe=Object.values(Te).sort(((e,t)=>e.order-t.order));class ke extends n.PureComponent{render(){const{id:e,shapeLocation:t,className:l,menuItemClassName:r,shapeLocationChange:i,disabled:o}=this.props;return n.createElement(Se.Select,{id:e,disabled:o,className:l,menuItemClassName:r,items:xe,value:t,onChange:i})}}const _e=new o.TranslatedString("change char",i.t(null,void 0,l(86955))),Ie=new o.TranslatedString("change location",i.t(null,void 0,l(6834)));class Re extends n.PureComponent{constructor(){super(...arguments),this._onCharChange=e=>{const{setValue:t}=this.context,l=e.currentTarget.value.trim(),n=fe()(l),i=0===n.length?"":n[n.length-1];t((0,r.ensureDefined)(this.props.property.childs().char),i,_e)},this._onLocationChange=e=>{const{setValue:t}=this.context;t(this.props.property.childs().location,e,Ie)}}render(){var e;const{id:t,title:l,char:i,isRGB:o,property:s,hasPalette:a}=this.props,{color:c,transparency:d,char:p,location:u,display:h}=s.childs();return n.createElement(B.InputRow,{grouped:a,label:n.createElement(ce,{id:t,title:l,display:h})},!a&&!o&&n.createElement(Q.ColorWithThicknessSelect,{disabled:0===h.value(),color:c,transparency:d}),n.createElement(Pe.InputControl,{disabled:void 0===p||0===h.value(),className:ee.smallStyleControl,value:(0,r.ensureDefined)(null!==(e=null==p?void 0:p.value())&&void 0!==e?e:i),onChange:this._onCharChange}),n.createElement(ke,{id:(0,x.createDomId)(t,"shape-style-select"),disabled:0===h.value(),className:we(ee.defaultSelect,ee.additionalSelect),menuItemClassName:ee.defaultSelectItem,shapeLocation:u.value(),shapeLocationChange:this._onLocationChange}))}}Re.contextType=I.StylePropertyContext;var De,Le=l(31785),Ve=l(69151),Be=l(67211),Ne=l(83786),ze=l(50858),Ae=l(13201),Me=l(59058),We=l(8537),Fe=l(2309),He=l(78240),Oe=l(41683),Ue=l(63798),Ge=l(23223);!function(e){e.ArrowDown="arrow_down",e.ArrowUp="arrow_up",e.Circle="circle",e.Cross="cross",e.Diamond="diamond",e.Flag="flag",e.LabelDown="label_down",e.LabelUp="label_up",e.Square="square",e.TriangleDown="triangle_down",e.TriangleUp="triangle_up",e.XCross="x_cross"}(De||(De={}));const Ye={arrow_down:Ve,arrow_up:Be,circle:Ne,cross:ze,diamond:Ae,flag:Me,label_down:We,label_up:Fe,square:He,triangle_down:Oe,triangle_up:Ue,x_cross:Ge};function je(e){return Ye[e]}const Xe=[];Object.keys(Le.plotShapesData).forEach((e=>{const t=Le.plotShapesData[e];Xe.push({
id:t.id,value:t.id,selectedContent:n.createElement(A.DisplayItem,{icon:je(t.icon)}),content:n.createElement(A.DropItem,{icon:je(t.icon),label:t.guiName})})}));class qe extends n.PureComponent{render(){const{id:e,shapeStyleId:t,className:l,shapeStyleChange:r,disabled:i}=this.props;return n.createElement(A.IconDropdown,{id:e,disabled:i,className:l,hideArrowButton:!0,items:Xe,value:t,onChange:r})}}const Je=new o.TranslatedString("change shape",i.t(null,void 0,l(83468))),$e=new o.TranslatedString("change location",i.t(null,void 0,l(6834)));class Ze extends n.PureComponent{constructor(){super(...arguments),this._onPlotTypeChange=e=>{const{setValue:t}=this.context;t(this.props.property.childs().plottype,e,Je)},this._onLocationChange=e=>{const{setValue:t}=this.context;t(this.props.property.childs().location,e,$e)}}render(){const{id:e,title:t,isRGB:l,hasPalette:r,property:i}=this.props,{color:o,transparency:s,plottype:a,location:c,display:d}=i.childs();return n.createElement(B.InputRow,{grouped:r,label:n.createElement(ce,{id:e,title:t,display:d})},!r&&!l&&n.createElement(Q.ColorWithThicknessSelect,{disabled:0===d.value(),color:o,transparency:s}),n.createElement(qe,{id:(0,x.createDomId)(e,"shape-style-select"),disabled:0===d.value(),className:ee.smallStyleControl,shapeStyleId:a.value(),shapeStyleChange:this._onPlotTypeChange}),n.createElement(ke,{id:(0,x.createDomId)(e,"shape-location-select"),disabled:0===d.value(),className:we(ee.defaultSelect,ee.additionalSelect),menuItemClassName:ee.defaultSelectItem,shapeLocation:c.value(),shapeLocationChange:this._onLocationChange}))}}Ze.contextType=I.StylePropertyContext;const Ke=(0,l(31955).getLogger)("Chart.Study.PropertyPage"),Qe=i.t(null,void 0,l(22691)),et=i.t(null,void 0,l(71776)),tt=i.t(null,void 0,l(74406)),lt=i.t(null,void 0,l(32163)),nt=i.t(null,void 0,l(38408));class rt extends n.PureComponent{render(){var e,t,l;const{plot:i,palettes:o,study:s}=this.props,a=i.id,c=s.properties().styles,d=s.metaInfo().styles,p=c[a],u=i.type,h=o.main,m=!!s.metaInfo().isRGB;if("line"===u||"bar_colorer"===u||"bg_colorer"===u)return h&&h.palette&&h.paletteProps?n.createElement(ae,{title:null!==(t=null===(e=null==d?void 0:d[a])||void 0===e?void 0:e.title)&&void 0!==t?t:a,plot:i,palette:h.palette,paletteProps:h.paletteProps,styleProp:p}):n.createElement(he,{id:a,title:(0,r.ensureDefined)(null===(l=null==d?void 0:d[a])||void 0===l?void 0:l.title),property:p,isRGB:m,isFundamental:false,showLineWidth:"line"===u});if("arrows"===u){const e=this._getPlotSwitch(a,it(s,a),p.display);if(m)return e;const t=o.up,l=o.down;return t||l?n.createElement(n.Fragment,null,e,t&&t.palette&&t.paletteProps?n.createElement(ae,{title:Qe,plot:i,palette:t.palette,paletteProps:t.paletteProps,styleProp:p,showSeparator:!1,showOnlyTitle:!0,offset:!0}):n.createElement(ye,{id:a,isRGB:m,title:Qe,color:p.colorup,display:p.display,transparency:p.transparency,switchable:!1,grouped:!0,offset:!0}),l&&l.palette&&l.paletteProps?n.createElement(ae,{title:et,plot:i,palette:l.palette,paletteProps:l.paletteProps,styleProp:p,showSeparator:!1,
showOnlyTitle:!0,offset:!0}):n.createElement(ye,{id:a,isRGB:m,title:et,color:p.colordown,display:p.display,transparency:p.transparency,switchable:!1,grouped:!0,offset:!0}),n.createElement(re.PropertyTable.GroupSeparator,null)):n.createElement(ge,{id:a,property:p,isRGB:m,plot:i,palettes:o,styleProp:p})}if("chars"===u||"shapes"===u){const e=(0,r.ensureDefined)(null==d?void 0:d[a]),t=e.title;return n.createElement(n.Fragment,null,"chars"===u?n.createElement(Re,{id:a,title:t,char:e.char,property:p,hasPalette:Boolean(h&&h.palette),isRGB:m}):n.createElement(Ze,{id:a,title:t,property:p,hasPalette:Boolean(h&&h.palette),isRGB:m}),h&&h.palette&&h.paletteProps&&n.createElement(ae,{title:t,plot:i,palette:h.palette,paletteProps:h.paletteProps,hideVisibilitySwitch:!0,styleProp:p}))}if((0,k.isOhlcPlot)(i)){const e=i.target,t=s.properties().ohlcPlots[e],l=(0,r.ensureDefined)((0,r.ensureDefined)(s.metaInfo().ohlcPlots)[e]),c=this._getPlotSwitch(a,l.title,t.display);if(m)return c;const d=o.wick&&o.wick.palette&&o.wick.paletteProps,p=o.border&&o.border.palette&&o.border.paletteProps;return n.createElement(n.Fragment,null,c,h&&h.palette&&h.paletteProps?n.createElement(ae,{title:tt,plot:i,palette:h.palette,paletteProps:h.paletteProps,styleProp:t,showSeparator:!1,showOnlyTitle:!0,offset:!0}):n.createElement(ye,{id:a,isRGB:m,title:tt,display:t.display,color:t.color,transparency:t.transparency,switchable:!1,grouped:!0,offset:!0}),o.wick&&o.wick.palette&&o.wick.paletteProps&&n.createElement(ae,{title:lt,plot:i,palette:o.wick.palette,paletteProps:o.wick.paletteProps,styleProp:t,showSeparator:!1,showOnlyTitle:!0,offset:!0}),Boolean(!d&&t.wickColor)&&n.createElement(ye,{id:a,isRGB:m,title:lt,display:t.display,color:t.wickColor,transparency:t.transparency,switchable:!1,grouped:!0,offset:!0}),o.border&&o.border.palette&&o.border.paletteProps&&n.createElement(ae,{title:nt,plot:i,palette:o.border.palette,paletteProps:o.border.paletteProps,styleProp:t,showSeparator:!1,showOnlyTitle:!0,offset:!0}),Boolean(!p&&t.borderColor)&&n.createElement(ye,{id:a,isRGB:m,title:nt,display:t.display,color:t.borderColor,transparency:t.transparency,switchable:!1,grouped:!0,offset:!0}),n.createElement(re.PropertyTable.GroupSeparator,null))}return Ke.logError("Unknown plot type: "+u),null}_getPlotSwitch(e,t,l){return n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2},n.createElement(ce,{id:e,title:t,display:l})))}}function it(e,t){const l=(0,r.ensureDefined)(e.metaInfo().styles),{title:n}=(0,r.ensureDefined)(l[t]);return(0,r.ensureDefined)(n)}var ot=l(91699),st=l(53598);const at=new o.TranslatedString("change line style",i.t(null,void 0,l(28818)));class ct extends n.PureComponent{constructor(){super(...arguments),this._onLineStyleChange=e=>{const{setValue:t}=this.context,{lineStyle:l}=this.props;t(l,e,at)}}render(){const{lineStyle:e,...t}=this.props;return n.createElement(st.LineStyleSelect,{...t,lineStyle:(0,D.getPropertyValue)(e),lineStyleChange:this._onLineStyleChange})}}ct.contextType=I.StylePropertyContext
;const dt=new o.TranslatedString("change value",i.t(null,void 0,l(21333)));class pt extends n.PureComponent{constructor(){super(...arguments),this._onValueChange=e=>{const{setValue:t}=this.context,{value:l}=this.props.property;t(l,e,dt)}}render(){const{id:e,name:t,property:{color:l,linestyle:r,linewidth:i,transparency:o,value:s,visible:a}}=this.props;return n.createElement(B.InputRow,{labelAlign:"adaptive",label:n.createElement(V,{id:e,title:t,visible:a})},n.createElement("div",{className:ee.block},n.createElement("div",{className:ee.group},n.createElement(Q.ColorWithThicknessSelect,{disabled:!a.value(),color:l,transparency:o,thickness:i}),n.createElement(ct,{id:(0,x.createDomId)(e,"line-style-select"),disabled:!a.value(),className:ee.smallStyleControl,lineStyle:r})),n.createElement("div",{className:we(ee.wrapGroup,ee.defaultSelect,ee.additionalSelect)},n.createElement(ot.FloatInputComponent,{input:{id:"",name:"",type:"float",defval:0},value:s.value(),disabled:!a.value(),onChange:this._onValueChange}))))}}pt.contextType=I.StylePropertyContext;class ut extends n.PureComponent{render(){const{orders:{visible:e,showLabels:t,showQty:r}}=this.props;return n.createElement(n.Fragment,null,n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2},n.createElement(V,{id:"chart-orders-switch",title:i.t(null,void 0,l(6532)),visible:e}))),n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2},n.createElement(V,{id:"chart-orders-labels-switch",title:i.t(null,void 0,l(38712)),visible:t}))),n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2},n.createElement(V,{id:"chart-orders-qty-switch",title:i.t(null,void 0,l(98721)),visible:r}))))}}ut.contextType=I.StylePropertyContext;var ht=l(9233),mt=l(55297);const vt=new o.TranslatedString("change percent width",i.t(null,void 0,l(62294))),yt=new o.TranslatedString("change placement",i.t(null,void 0,l(81891))),gt=new o.TranslatedString("change values visibility",i.t(null,void 0,l(9344))),bt=[{value:ht.HHistDirection.LeftToRight,content:i.t(null,void 0,l(11626))},{value:ht.HHistDirection.RightToLeft,content:i.t(null,void 0,l(50421))}],ft=i.t(null,void 0,l(4622)),wt=i.t(null,void 0,l(10783)),Ct=i.t(null,void 0,l(60092)),Pt=i.t(null,void 0,l(77753));class St extends n.PureComponent{constructor(){super(...arguments),this._onPercentWidthChange=e=>{const{setValue:t}=this.context,{percentWidth:l}=this.props.property.childs();t(l,e,vt)},this._onPlacementChange=e=>{const{setValue:t}=this.context,{direction:l}=this.props.property.childs();t(l,e,yt)},this._onShowValuesChange=e=>{const{setValue:t}=this.context,{showValues:l}=this.props.property.childs();t(l,e,gt)}}render(){const{hHistInfo:e,property:t}=this.props,{percentWidth:l,direction:r,showValues:i,valuesColor:o,visible:s}=t.childs(),{title:a}=e;return n.createElement(n.Fragment,null,n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first",
colSpan:2,grouped:!0},n.createElement(V,{id:a,title:a,visible:s}))),n.createElement(B.InputRow,{label:n.createElement("div",{className:ee.childRowContainer},ft),grouped:!0},n.createElement(mt.IntegerInputComponent,{input:{id:"",name:"",type:"integer",defval:0},value:l.value(),disabled:!s.value(),onChange:this._onPercentWidthChange})),n.createElement(B.InputRow,{label:n.createElement("div",{className:ee.childRowContainer},wt),grouped:!0},n.createElement(Se.Select,{id:"hhist-graphic-placement-select",disabled:!s.value(),className:ee.defaultSelect,menuItemClassName:ee.defaultSelectItem,items:bt,value:r.value(),onChange:this._onPlacementChange})),n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{className:ee.childRowContainer,placement:"first",colSpan:2,grouped:!0},n.createElement(R.BoolInputComponent,{label:Ct,input:{id:a+"_showValues",type:"bool",defval:!0,name:"visible"},value:!i||i.value(),disabled:!s.value(),onChange:this._onShowValuesChange}))),n.createElement(B.InputRow,{label:n.createElement("div",{className:ee.childRowContainer},Pt),grouped:!0},n.createElement(Q.ColorWithThicknessSelect,{disabled:s&&!s.value(),color:o})),this._renderColors(),n.createElement(re.PropertyTable.GroupSeparator,null))}_renderColors(){const{property:e,hHistInfo:t}=this.props,{colors:l,transparencies:r,visible:i}=e.childs(),{titles:o}=t;return l.childNames().map((e=>n.createElement(B.InputRow,{key:e,grouped:!0,label:n.createElement("div",{className:ee.childRowContainer},o[+e])},n.createElement(Q.ColorWithThicknessSelect,{disabled:!i.value(),color:l[+e],transparency:r[+e]}))))}}St.contextType=I.StylePropertyContext;class Et extends n.PureComponent{render(){const{title:e,property:t}=this.props,{color:l,width:r,style:i,visible:o}=t.childs();return n.createElement(B.InputRow,{label:n.createElement(V,{id:e,title:e,visible:o})},n.createElement(Q.ColorWithThicknessSelect,{disabled:!o.value(),color:l,transparency:t.child("transparency"),thickness:r}),n.createElement(ct,{id:(0,x.createDomId)(e,"line-style-select"),disabled:!o.value(),className:ee.smallStyleControl,lineStyle:i}))}}var Tt,xt;Et.contextType=I.StylePropertyContext,function(e){e.Triangle="triangle",e.Rectangle="rectangle"}(Tt||(Tt={})),function(e){e.Verdana="Verdana",e.CourierNew="Courier New",e.TimesNewRoman="Times New Roman",e.Arial="Arial"}(xt||(xt={}));class kt extends n.PureComponent{render(){const{graphicType:e,study:t}=this.props,l=t.metaInfo(),i=l.graphics,o=t.properties().graphics.childs(),s=(0,r.ensureDefined)(i[e]);return Object.keys(s).map(((t,i)=>{var s,a,c,d;const p=(0,r.ensureDefined)(null===(s=o[e])||void 0===s?void 0:s.childs()[t]);return"horizlines"===e||"vertlines"===e?n.createElement(Et,{key:t,title:(0,r.ensureDefined)(null===(a=l.graphics[e])||void 0===a?void 0:a[t]).name,property:p}):"lines"===e?n.createElement(Et,{key:t,title:(0,r.ensureDefined)(null===(c=l.graphics.lines)||void 0===c?void 0:c[t]).title,property:p}):"hhists"===e?n.createElement(St,{key:t,hHistInfo:(0,
r.ensureDefined)(null===(d=l.graphics.hhists)||void 0===d?void 0:d[t]),property:p}):null}))}}var _t=l(66045);const It=new o.TranslatedString("change font size",i.t(null,void 0,l(27745))),Rt=[10,11,12,14,16,20,24,28,32,40].map((e=>({value:e,title:e.toString()})));class Dt extends n.PureComponent{constructor(){super(...arguments),this._onFontSizeChange=e=>{const{setValue:t}=this.context,{fontSize:l}=this.props;t(l,e,It)}}render(){const{fontSize:e,...t}=this.props;return n.createElement(_t.FontSizeSelect,{...t,fontSizes:Rt,fontSize:e.value(),fontSizeChange:this._onFontSizeChange})}}Dt.contextType=I.StylePropertyContext;const Lt=new o.TranslatedString("change visibility",i.t(null,void 0,l(1924))),Vt=i.t(null,void 0,l(62791)),Bt=i.t(null,void 0,l(5119)),Nt={Traditional:new Set(["S5/R5","S4/R4","S3/R3","S2/R2","S1/R1","P"]),Fibonacci:new Set(["S3/R3","S2/R2","S1/R1","P"]),Woodie:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"]),Classic:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"]),DM:new Set(["S1/R1","P"]),DeMark:new Set(["S1/R1","P"]),Camarilla:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"]),Floor:new Set(["S3/R3","S2/R2","S1/R1","P"])};class zt extends n.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{setValue:t}=this.context,{levelsStyle:l}=this.props.property.childs(),{showLabels:n}=l.childs();t(n,e,Lt)}}render(){const{fontsize:e,levelsStyle:t}=this.props.property.childs();return n.createElement(n.Fragment,null,n.createElement(B.InputRow,{labelAlign:"adaptive",label:n.createElement("span",null,Vt)},n.createElement("div",{className:ee.block},n.createElement("div",{className:we(ee.wrapGroup,ee.additionalSelect)},n.createElement(Dt,{id:"pivot-points-standard-font-size-select",fontSize:e})))),n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2},n.createElement(R.BoolInputComponent,{label:Bt,input:{id:"ShowLabels",type:"bool",defval:!0,name:"visible"},value:t.childs().showLabels.value(),onChange:this._onChange}))),this._renderColors())}_renderColors(){const{levelsStyle:e,inputs:t}=this.props.property.childs(),{colors:l,widths:i,visibility:o}=e.childs(),{kind:s}=t.childs(),a=(0,r.ensureDefined)(Nt[s.value()]);return l.childNames().filter((e=>a.has(e))).map((e=>n.createElement(ve,{key:e,id:e,title:e,color:l.childs()[e],visible:o.childs()[e],thickness:i.childs()[e]})))}}zt.contextType=I.StylePropertyContext;const At=i.t(null,void 0,l(10783)),Mt=[{value:ht.HHistDirection.RightToLeft,content:i.t(null,void 0,l(50421))},{value:ht.HHistDirection.LeftToRight,content:i.t(null,void 0,l(11626))}],Wt=new o.TranslatedString("change visibility",i.t(null,void 0,l(1924))),Ft=i.t(null,void 0,l(67972)),Ht=i.t(null,void 0,l(60092)),Ot=i.t(null,void 0,l(4622)),Ut=i.t(null,void 0,l(73033)),Gt=i.t(null,{context:"input"},l(49191)),Yt=i.t(null,{context:"input"},l(76542));class jt extends n.PureComponent{constructor(){super(...arguments),this._onChange=e=>{this._setHhistsProperty("visible",e)},this._onShowValuesChange=e=>{this._setHhistsProperty("showValues",e)},
this._onValueChange=e=>{this._setHhistsProperty("percentWidth",e)},this._onDirectionChange=e=>{this._setHhistsProperty("direction",e)}}render(){var e,t,o,s,a,c;const{metaInfo:d}=this.props,{graphics:p,styles:u,showLabelsOnPriceScale:h,showLegendValues:m}=this.props.property.childs(),{hhists:v,horizlines:y,polygons:g}=p.childs(),b=(0,r.ensureDefined)(d.graphics.hhists),f=Object.keys(b),w=v.childs()[f[0]],C=w.childs().visible,P=f.map((e=>v.childs()[e].childs().showValues)),S=w.childs().percentWidth,E=w.childs().direction,T=f.map((e=>v.childs()[e].childs().valuesColor)),x=null===(e=y.childs())||void 0===e?void 0:e.vahLines,k=null===(t=d.graphics.horizlines)||void 0===t?void 0:t.vahLines,_=null===(o=y.childs())||void 0===o?void 0:o.valLines,I=null===(s=d.graphics.horizlines)||void 0===s?void 0:s.valLines,D=y.childs().pocLines,L=(0,r.ensureDefined)(null===(a=d.graphics.horizlines)||void 0===a?void 0:a.pocLines),N=u.childs().developingPoc,z=new se.StudyPlotVisibleProperty(N.childs().display),A=(0,r.ensureDefined)(null===(c=d.styles)||void 0===c?void 0:c.developingPoc),M=u.childs().developingVAHigh,W=new se.StudyPlotVisibleProperty(M.childs().display),F=u.childs().developingVALow,H=new se.StudyPlotVisibleProperty(F.childs().display),O=d.graphics.polygons&&d.graphics.polygons.histBoxBg;return n.createElement(n.Fragment,null,n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2},n.createElement(R.BoolInputComponent,{label:Ft,input:{id:"VolumeProfile",type:"bool",defval:!0,name:"visible"},value:C.value(),onChange:this._onChange}))),n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first"},n.createElement("div",{className:ee.childRowContainer},n.createElement(R.BoolInputComponent,{disabled:!C.value(),label:Ht,input:{id:"ShowValues",type:"bool",defval:!0,name:"visible"},value:P[0].value(),onChange:this._onShowValuesChange}))),n.createElement(re.PropertyTable.Cell,{placement:"last"},n.createElement(Q.ColorWithThicknessSelect,{disabled:!C.value()||!P[0].value(),color:T}))),n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first"},n.createElement("div",{className:ee.childRowContainer},Ot)),n.createElement(re.PropertyTable.Cell,{placement:"last"},n.createElement(mt.IntegerInputComponent,{disabled:!C.value(),input:{id:"",name:"",type:"integer",defval:0},value:S.value(),onChange:this._onValueChange}))),n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.Cell,{placement:"first"},n.createElement("div",{className:ee.childRowContainer},At)),n.createElement(re.PropertyTable.Cell,{placement:"last"},n.createElement(Se.Select,{id:"hhist-direction-select",disabled:!C.value(),className:ee.defaultSelect,menuItemClassName:ee.defaultSelectItem,items:Mt,value:E.value(),onChange:this._onDirectionChange}))),f.map((e=>n.createElement(n.Fragment,{key:e},v.childs()[e].childs().colors.childNames().map(((t,r)=>{const o=b[e];return n.createElement(B.InputRow,{key:r,label:n.createElement("div",{
className:ee.childRowContainer},o&&i.t(o.titles[r],{context:"input"},l(32856))||"")},n.createElement(Q.ColorWithThicknessSelect,{disabled:!C.value(),color:v.childs()[e].childs().colors.childs()[r],transparency:v.childs()[e].childs().transparencies.childs()[r]}))}))))),k&&x&&n.createElement(ve,{id:"vahLines",title:k.name,color:x.childs().color,visible:x.childs().visible,thickness:x.childs().width},n.createElement(ct,{id:"vah-lines-line-style-select",disabled:!x.childs().visible.value(),className:ee.smallStyleControl,lineStyle:x.childs().style})),I&&_&&n.createElement(ve,{id:"valLines",title:I.name,color:_.childs().color,visible:_.childs().visible,thickness:_.childs().width},n.createElement(ct,{id:"val-lines-line-style-select",disabled:!_.childs().visible.value(),className:ee.smallStyleControl,lineStyle:_.childs().style})),n.createElement(ve,{id:"pocLines",title:L.name,color:D.childs().color,visible:D.childs().visible,thickness:D.childs().width},n.createElement(ct,{id:"poc-lines-line-style-select",disabled:!D.childs().visible.value(),className:ee.smallStyleControl,lineStyle:D.childs().style})),N&&n.createElement(ve,{id:"developingPoc",title:A.title&&i.t(A.title,{context:"input"},l(32856))||"",color:N.childs().color,visible:z,thickness:N.childs().linewidth},n.createElement(ct,{id:"developing-poc-line-style-select",disabled:!z.value(),className:ee.smallStyleControl,lineStyle:N.childs().linestyle})),M&&F&&n.createElement(ve,{id:"developingPoc",title:Ut,color:[M.childs().color,F.childs().color],visible:[W,H],thickness:[M.childs().linewidth,F.childs().linewidth]},n.createElement(ct,{id:"developing-VA-line-style-select",disabled:!W.value()&&!H.value(),className:ee.smallStyleControl,lineStyle:[M.childs().linestyle,F.childs().linestyle]})),g&&n.createElement(B.InputRow,{label:n.createElement("div",null,O&&i.t(O.name,{context:"input"},l(32856))||"")},n.createElement(Q.ColorWithThicknessSelect,{color:g.childs().histBoxBg.childs().color,transparency:g.childs().histBoxBg.childs().transparency})),(h||m)&&"VbPFixed"!==d.shortId&&n.createElement(n.Fragment,null,h&&n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2},n.createElement(V,{id:"showLabelsOnPriceScale",title:Yt,visible:h})),m&&n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2},n.createElement(V,{id:"showLegendValues",title:Gt,visible:m}))))}_setHhistsProperty(e,t){const{setValue:l}=this.context,{metaInfo:n,property:i}=this.props,o=i.childs().graphics.childs().hhists,s=Object.keys((0,r.ensureDefined)(n.graphics.hhists)),a=o.childs();l(s.map((t=>(0,r.ensureDefined)(a[t].child(e)))),t,Wt)}}function Xt(){const e=(0,r.ensureNotNull)((0,n.useContext)(me)),t=e.metaInfo(),l=e.properties();return n.createElement(jt,{metaInfo:t,property:l})}jt.contextType=I.StylePropertyContext;var qt=l(28117);const Jt={VbPFixed:Xt,PivotPointsStandard:function(){const e=(0,r.ensureNotNull)((0,n.useContext)(me)).properties();return n.createElement(zt,{property:e})},VbPVisible:Xt,VbPAnchored:Xt};class $t extends n.PureComponent{render(){const e=(0,
r.ensureNotNull)(this.context);return n.createElement(me.Consumer,null,(t=>n.createElement(I.StylePropertyContainer,{property:(0,r.ensureNotNull)(t).properties(),model:e},n.createElement(re.PropertyTable,null,this._renderCustomContent((0,r.ensureNotNull)(t).metaInfo().shortId)))))}_renderCustomContent(e){if(e in Jt){const t=Jt[e];return n.createElement(t,null)}return null}}$t.contextType=qt.ModelContext;var Zt=l(65388);const Kt=new o.TranslatedString("change precision",i.t(null,void 0,l(61863))),Qt=i.t(null,void 0,l(16564)),el=i.t(null,void 0,l(59766)),tl=[{value:"default",content:Qt}];for(let e=0;e<=8;e++)tl.push({value:e,content:e.toString()});class ll extends n.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{setValue:t}=this.context,{precision:l}=this.props;t(l,e,Kt)}}render(){const{id:e,precision:t}=this.props;return n.createElement(B.InputRow,{label:el},n.createElement(Se.Select,{id:e,className:ee.defaultSelect,menuItemClassName:ee.defaultSelectItem,items:tl,value:t.value(),onChange:this._onChange}))}}ll.contextType=I.StylePropertyContext;const nl=new o.TranslatedString("change min tick",i.t(null,void 0,l(26476))),rl=i.t(null,void 0,l(16564)),il=i.t(null,void 0,l(64075)),ol=[{priceScale:1,minMove:1,frac:!1},{priceScale:10,minMove:1,frac:!1},{priceScale:100,minMove:1,frac:!1},{priceScale:1e3,minMove:1,frac:!1},{priceScale:1e4,minMove:1,frac:!1},{priceScale:1e5,minMove:1,frac:!1},{priceScale:1e6,minMove:1,frac:!1},{priceScale:1e7,minMove:1,frac:!1},{priceScale:1e8,minMove:1,frac:!1},{priceScale:2,minMove:1,frac:!0},{priceScale:4,minMove:1,frac:!0},{priceScale:8,minMove:1,frac:!0},{priceScale:16,minMove:1,frac:!0},{priceScale:32,minMove:1,frac:!0},{priceScale:64,minMove:1,frac:!0},{priceScale:128,minMove:1,frac:!0},{priceScale:320,minMove:1,frac:!0}],sl=[{id:"tick-default",value:"default",content:rl}];for(let e=0;e<ol.length;e++){const t=ol[e];sl.push({value:t.priceScale+","+t.minMove+","+t.frac,content:t.minMove+"/"+t.priceScale})}class al extends n.PureComponent{constructor(){super(...arguments),this._onChange=e=>{const{setValue:t}=this.context,{minTick:l}=this.props;t(l,e,nl)}}render(){const{id:e,minTick:t}=this.props;return n.createElement(B.InputRow,{label:il},n.createElement(Se.Select,{id:e,className:ee.defaultSelect,menuItemClassName:ee.defaultSelectItem,items:sl,value:t.value(),onChange:this._onChange}))}}al.contextType=I.StylePropertyContext;var cl=l(73146),dl=l(86067);class pl extends n.PureComponent{render(){const{id:e,isRGB:t,title:l,visible:r,bottomColor:i,topColor:o,transparency:s,children:a,switchable:c=!0,offset:d,grouped:p}=this.props;return n.createElement(B.InputRow,{label:c?n.createElement(V,{id:e,title:l,visible:r}):l,offset:d,grouped:p},t?null:n.createElement(n.Fragment,null,o&&n.createElement(Q.ColorWithThicknessSelect,{disabled:r&&!(Array.isArray(r)?r[0].value():r.value()),color:o,transparency:s}),i&&n.createElement("div",{className:Ce()(i&&o&&ee.additionalSelect)},n.createElement(Q.ColorWithThicknessSelect,{disabled:r&&!(Array.isArray(r)?r[0].value():r.value()),
color:i,transparency:s}))),a)}}pl.contextType=I.StylePropertyContext;const ul=i.t(null,void 0,l(79468)),hl=i.t(null,{context:"input"},l(49191)),ml=i.t(null,{context:"input"},l(76542)),vl=i.t(null,void 0,l(89702));class yl extends n.PureComponent{constructor(){super(...arguments),this._findPlotPalettes=e=>{const{study:t}=this.props,l=t.metaInfo(),n=(0,r.ensureDefined)(l.palettes);return(0,k.isBarColorerPlot)(e)||(0,k.isBgColorerPlot)(e)?{main:{palette:n[e.palette],paletteProps:t.properties().palettes[e.palette]}}:this._findPalettesByTargetId(e.id)}}render(){const{study:e}=this.props,t=e.metaInfo();if((0,Zt.isCustomStudy)(t.shortId))return n.createElement($t,null);const l=e.properties(),{precision:r,strategy:i,minTick:o,showLabelsOnPriceScale:s,showLegendValues:a}=l,c=t.plots.length>0,d=t.plots.some((e=>!(0,k.isPlotWithTechnicalValues)(e))),p=c||t.inputs.some((e=>"price"===e.type)),u=(0,cl.createAdapter)(e).canOverrideMinTick();return n.createElement(re.PropertyTable,null,this._plotsElement(),this._bandsElement(),this._bandsBackgroundsElement(),this._areasBackgroundsElement(),this._filledAreasElement(),this._graphicsElement(),u&&n.createElement(al,{id:(0,x.createDomId)(t.id,"min-tick-select"),minTick:o}),_.StudyMetaInfo.isScriptStrategy(t)&&n.createElement(ut,{orders:i.orders}),(p||d)&&n.createElement(re.PropertyTable.Row,null,n.createElement(re.PropertyTable.GroupSeparator,{size:1}),n.createElement(dl.GroupTitleSection,{title:vl,name:vl}),p&&n.createElement(ll,{id:(0,x.createDomId)(t.id,"precision-select"),precision:r}),d&&n.createElement(n.Fragment,null,n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2},n.createElement(V,{id:"showLabelsOnPriceScale",title:ml,visible:s})),n.createElement(re.PropertyTable.Cell,{placement:"first",colSpan:2},n.createElement(V,{id:"showLegendValues",title:hl,visible:a})))))}_plotsElement(){const{study:e}=this.props,t=e.metaInfo();return new E.MetaInfoHelper(t).getUserEditablePlots().filter((e=>!((0,k.isUpColorerPlot)(e)||(0,k.isDownColorerPlot)(e)||(0,k.isCandleBorderColorerPlot)(e)||(0,k.isCandleWickColorerPlot)(e)))).map((t=>{const l=(0,k.isOhlcPlot)(t)?{...t,id:t.target}:t,r=this._findPlotPalettes(l);return n.createElement(rt,{key:t.id,plot:t,palettes:r,study:e})}))}_bandsElement(){const{study:e}=this.props,t=e.metaInfo().bands,l=e.properties().childs().bands;return t&&l&&t.map(((e,t)=>{if(!e.isHidden)return n.createElement(pt,{key:t,id:e.name,name:e.name,property:l[t]})}))}_bandsBackgroundsElement(){const{study:e}=this.props,t=e.properties(),{bandsBackground:l}=t;return l&&n.createElement(ve,{id:"bandsBackground",title:ul,visible:l.fillBackground,color:l.backgroundColor,transparency:l.transparency})}_areasBackgroundsElement(){const{study:e}=this.props,t=e.metaInfo(),l=e.properties(),{areaBackground:r}=l;return t.isRGB?null:r&&n.createElement(ve,{id:"areaBackground",title:ul,visible:r.fillBackground,color:r.backgroundColor,transparency:r.transparency})}_filledAreasElement(){const{study:e}=this.props,t=e.metaInfo(),l=t.filledAreas;return!l||t.isRGB?[]:l.map((t=>{
if(t.isHidden)return null;const l=e.properties().filledAreasStyle[t.id],i=t.title||ul;if(l.hasChild("fillType")&&"gradient"===l.childs().fillType.value()){if(l.topColor||l.bottomColor)return n.createElement(pl,{key:t.id,id:t.id,title:i,bottomColor:l.bottomColor,topColor:l.topColor,visible:l.visible,transparency:l.transparency});if(t.palette){const e=this._findPalettesByTargetId(t.id),i=(0,r.ensureDefined)(e.main),o=e.secondary;return n.createElement(oe,{key:t.id,title:t.title,area:t,palette:(0,r.ensureDefined)(i.palette),paletteProps:(0,r.ensureDefined)(i.paletteProps),secondaryPalette:null==o?void 0:o.palette,secondaryPaletteProps:null==o?void 0:o.paletteProps,styleProp:l})}return null}if(t.palette){const e=this._findPalettesByTargetId(t.id),i=(0,r.ensureDefined)(e.main);return n.createElement(oe,{key:t.id,title:t.title,area:t,palette:(0,r.ensureDefined)(i.palette),paletteProps:(0,r.ensureDefined)(i.paletteProps),styleProp:l})}return n.createElement(ve,{key:t.id,id:t.id,title:i,color:l.color,visible:l.visible,transparency:l.transparency})}))}_graphicsElement(){const{study:e}=this.props,t=e.metaInfo().graphics;return t&&Object.keys(t).map(((t,l)=>n.createElement(kt,{key:t,graphicType:t,study:e})))}_findPalettesByTargetId(e){const{study:t}=this.props,l=t.metaInfo(),n=l.plots,i=(0,r.ensureDefined)(l.palettes),o={};for(const l of n){if(((0,k.isPaletteColorerPlot)(l)||(0,k.isOhlcColorerPlot)(l))&&l.target===e){if(o.main){o.secondary={palette:i[l.palette],paletteProps:t.properties().palettes[l.palette]};continue}o.main={palette:i[l.palette],paletteProps:t.properties().palettes[l.palette]}}(0,k.isUpColorerPlot)(l)&&l.target===e&&(o.up={palette:i[l.palette],paletteProps:t.properties().palettes[l.palette]}),(0,k.isDownColorerPlot)(l)&&l.target===e&&(o.down={palette:i[l.palette],paletteProps:t.properties().palettes[l.palette]}),(0,k.isCandleWickColorerPlot)(l)&&l.target===e&&(o.wick={palette:i[l.palette],paletteProps:t.properties().palettes[l.palette]}),(0,k.isCandleBorderColorerPlot)(l)&&l.target===e&&(o.border={palette:i[l.palette],paletteProps:t.properties().palettes[l.palette]})}return o}}function gl(e){return(0,I.bindPropertyContext)(yl,{...e,property:e.study.properties()})}class bl extends n.PureComponent{render(){return n.createElement(qt.ModelContext.Provider,{value:this.props.model},n.createElement(me.Provider,{value:this.props.source},n.createElement(gl,{study:this.props.source})))}}var fl=l(57717),wl=l(29280),Cl=l(26434),Pl=l(19466),Sl=l(28124);class El extends wl.DialogRenderer{constructor(e,t,l,n){super(),this._timeout=null,this._handleClose=()=>{var e;null===(e=this._rootInstance)||void 0===e||e.unmount(),this._setVisibility(!1),this._subscription.unsubscribe(this,this._handleCollectionChanged)},this._handleCancel=()=>{this._model.undoToCheckpoint(this._checkpoint)},this._handleSubmit=()=>{},this._handleActiveTabChanged=e=>{a.setValue(this._activeTabSettingsName(),e)},this._source=e,this._model=t,this._propertyPages=n,this._checkpoint=this._ensureCheckpoint(l),
this._subscription=this._model.model().dataSourceCollectionChanged(),this._subscription.subscribe(this,this._handleCollectionChanged)}hide(e){e?this._handleCancel():this._handleSubmit(),this._handleClose()}isVisible(){return this.visible().value()}show(e={}){var t;if(!c.enabled("property_pages")||this.isVisible())return;const r=this._source.metaInfo();if((0,g.isLineTool)(this._source)&&(0,p.trackEvent)("GUI","Drawing Properties",this._source.name()),(0,v.isStudy)(this._source)){const e=!this._source.isPine()||this._source.isStandardPine()?r.description:"Custom Pine";(0,p.trackEvent)("GUI","Study Properties",e)}let o=[];const u=new E.MetaInfoHelper(r);u.hasUserEditableInputs()&&o.push({id:"inputs",label:i.t(null,void 0,l(21429)),Component:T}),u.hasUserEditableProperties(),u.hasUserEditableStyles()&&o.push({id:"style",label:i.t(null,void 0,l(92516)),Component:bl}),this._propertyPages||o.push({id:"visibilities",label:i.t(null,void 0,l(40091)),page:this._createVisibilitiesPropertyPage()}),o=this._getPagesForStudyLineTool(o);const h=e.initialTab||a.getValue(this._activeTabSettingsName())||"inputs";let m=(0,s.clean)(r.shortDescription,!0);const y=null!==(t=o.find((e=>e.id===h)))&&void 0!==t?t:o[0];this._rootInstance=(0,Sl.createReactRoot)(n.createElement(P,{title:m,model:this._model,source:this._source,initialActiveTab:y.id,pages:o,shouldReturnFocus:e.shouldReturnFocus,onSubmit:this._handleSubmit,onCancel:this._handleCancel,onClose:this._handleClose,onActiveTabChanged:this._handleActiveTabChanged}),this._container),this._setVisibility(!0),d.emit("edit_object_dialog",{objectType:"study",scriptTitle:this._source.title(Pl.TitleDisplayTarget.StatusLine)})}_createVisibilitiesPropertyPage(){const e=this._source.properties().childs().intervalsVisibilities.childs();return(0,fl.createPropertyPage)((0,Cl.getIntervalsVisibilitiesPropertiesDefinitions)(this._model,e,new o.TranslatedString(this._source.name(!0),this._source.title(Pl.TitleDisplayTarget.StatusLine,!0))),"visibility",i.t(null,void 0,l(40091)))}_activeTabSettingsName(){return"properties_dialog.active_tab.study"}_ensureCheckpoint(e){return void 0===e&&(e=this._model.createUndoCheckpoint()),e}_getPagesForStudyLineTool(e){if(this._propertyPages){const t=this._propertyPages.filter((e=>"coordinates"===e.id||"visibility"===e.id));return[...e,...t.map((e=>({id:e.id,label:e.title,page:e})))]}return e}_handleCollectionChanged(){null===this._timeout&&(this._timeout=setTimeout((()=>{this._closeDialogIfSourceIsDeleted(),this._timeout=null})))}_closeDialogIfSourceIsDeleted(){null===this._model.model().dataSourceForId(this._source.id())&&this._handleClose()}}},78890:(e,t,l)=>{"use strict";l.d(t,{PropertyActions:()=>m});var n=l(50959),r=l(97754),i=l.n(r),o=l(9745),s=l(11542),a=l(95276),c=l(16396),d=l(44996),p=l(87075);const u={reset:s.t(null,void 0,l(33533)),saveAsDefault:s.t(null,void 0,l(99687)),defaults:s.t(null,void 0,l(48572))};var h;!function(e){e.Normal="normal",e.Compact="compact"}(h||(h={}));class m extends n.PureComponent{render(){
const{mode:e,saveAsDefaults:t,resetToDefaults:l}=this.props;return n.createElement(a.ControlDisclosure,{id:"property-actions",className:i()("normal"===e&&p.defaultsButtonText),hideArrowButton:"compact"===e,buttonChildren:this._getPlaceHolderItem("compact"===e)},n.createElement(c.PopupMenuItem,{className:p.defaultsButtonItem,isActive:!1,label:u.reset,onClick:l}),n.createElement(c.PopupMenuItem,{className:p.defaultsButtonItem,isActive:!1,label:u.saveAsDefault,onClick:t}))}_getPlaceHolderItem(e){return e?n.createElement(o.Icon,{className:p.defaultsButtonIcon,icon:d}):u.defaults}}},48531:(e,t,l)=>{"use strict";l.d(t,{FooterMenu:()=>f});var n=l(50959),r=l(11542),i=l(9745),o=l(95276),s=l(90692),a=l(87509),c=l(44996);function d(e){return e.isTabletWidth?n.createElement(i.Icon,{className:a.themesButtonIcon,icon:c}):n.createElement(n.Fragment,null,r.t(null,void 0,l(93553)))}function p(e){return n.createElement(s.MatchMedia,{rule:"screen and (max-width: 768px)"},(t=>n.createElement(o.ControlDisclosure,{className:!t&&a.themesButtonText,hideArrowButton:t,buttonChildren:n.createElement(d,{isTabletWidth:t})},e.children)))}var u=l(16396),h=l(96040),m=l(70412),v=l(32563),y=l(60925);function g(e){const{name:t,onRemove:l,onClick:r}=e,[i,o]=(0,m.useHover)(),s=n.useCallback((()=>r(t)),[r,t]),c=n.useCallback((()=>{l&&l(t)}),[l,t]);return n.createElement("div",{...o},n.createElement(u.PopupMenuItem,{className:a.defaultsButtonItem,isActive:!1,label:t,onClick:s,toolbox:l&&n.createElement(h.RemoveButton,{hidden:!v.mobiletouch&&!i,onClick:c,icon:y})}))}function b(e){return n.createElement(p,null,n.createElement(g,{onClick:function(){const{sources:t,chartUndoModel:l}=e;l.restoreLineToolsFactoryDefaults(t)},name:r.t(null,void 0,l(62511))}))}function f(e){return n.createElement(b,{...e})}},37289:(e,t,l)=>{"use strict";l.d(t,{PropertiesEditorTab:()=>c});var n=l(50959),r=l(66849);const i={"Elliott Impulse Wave (12345)Degree":"normal","Elliott Triangle Wave (ABCDE)Degree":"normal","Elliott Triple Combo Wave (WXYXZ)Degree":"normal","Elliott Correction Wave (ABC)Degree":"normal","Elliott Double Combo Wave (WXY)Degree":"normal",BarsPatternMode:"normal",StudyInputSource:"normal"},o={TextText:"big",AnchoredTextText:"big",NoteText:"big",AnchoredNoteText:"big",CalloutText:"big",BalloonText:"big"};var s=l(71891),a=l(5978);function c(e){const{page:t,pageRef:l,tableKey:c}=e;return n.createElement(r.ControlCustomHeightContext.Provider,{value:o},n.createElement(r.ControlCustomWidthContext.Provider,{value:i},t&&n.createElement(s.PropertyTable,{reference:l,key:c},t.definitions.value().map((e=>n.createElement(a.Section,{key:e.id,definition:e}))))))}},11684:(e,t,l)=>{"use strict";l.d(t,{PopupMenuSeparator:()=>a});var n,r=l(50959),i=l(97754),o=l.n(i),s=l(71150);function a(e){const{size:t="normal",className:l,ariaHidden:n=!1}=e;return r.createElement("div",{className:o()(s.separator,"small"===t&&s.small,"normal"===t&&s.normal,"large"===t&&s.large,l),role:"separator","aria-hidden":n})}!function(e){e.Small="small",e.Large="large",e.Normal="normal"}(n||(n={}))},
93081:(e,t,l)=>{"use strict";l.d(t,{UnderlineButtonTabs:()=>j});var n,r=l(50959),i=l(97754),o=l.n(i),s=l(11542),a=l(95854),c=l(38528),d=l(47201),p=l(73775),u=l(16212),h=l(26597);!function(e){e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge"}(n||(n={}));const m=(0,r.createContext)({size:"small",overflowBehaviour:void 0});var v=l(17946),y=l(24554);function g(e){const{size:t="xsmall",active:l,fake:n,enableActiveStateStyles:r,anchor:o=!1,hideFocusOutline:s=!1,equalTabSize:a,className:c,overflowBehaviour:d}=e;return i(y["underline-tab"],y[`size-${t}`],l&&y.selected,!r&&y["disable-active-state-styles"],s&&y["disable-focus-outline"],n&&y.fake,o&&y["enable-cursor-pointer"],a&&y[`margin-${t}`],"collapse"===d&&y.collapse,c)}const b=(0,r.forwardRef)(((e,t)=>{const{size:l,overflowBehaviour:n}=(0,r.useContext)(m),i=(0,r.useContext)(v.CustomBehaviourContext),{active:s,fake:a,className:c,enableActiveStateStyles:d=i.enableActiveStateStyles,hideFocusOutline:p=!1,equalTabSize:u,children:h,...b}=e;return r.createElement("button",{...b,ref:t,className:g({size:l,active:s,fake:a,enableActiveStateStyles:d,hideFocusOutline:p,equalTabSize:u,className:c,overflowBehaviour:n})},u&&"string"==typeof h?r.createElement("span",{className:o()(y["ellipsis-children"],"apply-overflow-tooltip")},h):h)}));b.displayName="UnderlineTabsBaseButton";const f=(0,r.forwardRef)(((e,t)=>{const{item:l,highlighted:n,handleItemRef:i,onClick:o,"aria-disabled":s,...a}=e,c=(0,r.useCallback)((()=>{o&&o(l)}),[o,l]),d=(0,r.useCallback)((e=>{i&&i(l,e),t&&"object"==typeof t?t.current=e:"function"==typeof t&&t(e)}),[l,i,t]);return r.createElement(b,{...a,id:l.id,onClick:c,ref:d},l.label)}));f.displayName="UnderlineButtonTab";var w=l(50151),C=l(16396),P=l(4523),S=l(9745),E=l(47531),T=l(2948),x=l(63509),k=l(68874),_=l(10399);function I(e){switch(e){case"xsmall":return E;case"small":return T;case"medium":case"large":return x;case"xlarge":return k}}function R(e){const{size:t,isDropped:l=!1}=e;return r.createElement(S.Icon,{icon:I(t),className:i(_["arrow-icon"],_[`size-${t}`],l&&_.dropped)})}var D=l(456);function L(e){const{size:t,disabled:l,isOpened:n,enableActiveStateStyles:i,hideFocusOutline:o,fake:s,items:a,buttonContent:d,buttonRef:p,isAnchorTabs:u,isHighlighted:h,onButtonClick:m,onItemClick:v,onClose:y}=e,g=(0,r.useRef)(null),f=(0,c.useMergedRefs)([p,g]),S=function(e,t){const l=(0,r.useRef)(B);return(0,r.useEffect)((()=>{const e=getComputedStyle((0,w.ensureNotNull)(t.current));l.current={xsmall:V(e,"xsmall"),small:V(e,"small"),medium:V(e,"medium"),large:V(e,"large"),xlarge:V(e,"xlarge")}}),[t]),(0,r.useCallback)((()=>{const n=(0,w.ensureNotNull)(t.current).getBoundingClientRect(),r=l.current[e];return{x:n.left,y:n.top+n.height+r+4,indentFromWindow:{top:4,bottom:4,left:4,right:4}}}),[t,e])}(t,g);return r.createElement(P.PopupMenuDisclosureView,{buttonRef:g,listboxTabIndex:-1,isOpened:n,onClose:y,listboxAria:{"aria-hidden":!0},popupPosition:S,button:r.createElement(b,{"aria-hidden":!0,disabled:l,active:n,onClick:m,ref:f,tabIndex:-1,
enableActiveStateStyles:i,hideFocusOutline:o,fake:s},d,r.createElement(R,{size:t,isDropped:n})),popupChildren:a.map((e=>r.createElement(C.PopupMenuItem,{key:e.id,className:u?D["link-item"]:void 0,onClick:v,onClickArg:e,isActive:h(e),label:e.label,isDisabled:e.disabled,link:"href"in e?e.href:void 0,rel:"rel"in e?e.rel:void 0,target:"target"in e?e.target:void 0,renderComponent:"renderComponent"in e?e.renderComponent:void 0,dontClosePopup:!0})))})}function V(e,t){return parseInt(e.getPropertyValue(`--ui-lib-underline-tabs-tab-margin-bottom-${t}`),10)}const B={xsmall:0,small:0,medium:0,large:0,xlarge:0};var N=l(5325),z=l(42707),A=l(86240),M=l(7633);function W(e){const{size:t,overflowBehaviour:l,className:n}=e;return i(M["scroll-wrap"],M[`size-${t}`],"scroll"===l&&M["enable-scroll"],n)}function F(){const[e,t]=(0,r.useState)(!1);return(0,r.useEffect)((()=>{t(N.mobiletouch)}),[]),e}var H=l(12481),O=l(63273),U=l(29662),G=l.n(U);function Y(e){const{disabled:t,translateX:l,transitionDuration:n}=e,o=e.scale/100;return r.createElement("div",{className:i(G().underline,t&&G().disabled),style:{transform:`translateX(${l}px) scaleX(${o})`,transitionDuration:`${n}ms`}},r.createElement("div",{className:G().corner,style:{transform:`scaleX(${1/o})`}}),r.createElement("div",{className:G().center,style:{transform:`scaleX(${1-30/e.scale})`}}),r.createElement("div",{className:G().corner,style:{transform:`scaleX(${1/o})`}}))}function j(e){const{id:t,items:n,activationType:i,orientation:v,disabled:y,moreButtonContent:g=s.t(null,void 0,l(37117)),size:b="small",onActivate:w,isActive:C,className:P,style:S,overflowBehaviour:E,enableActiveStateStyles:T,tablistLabelId:x,tablistLabel:k,"data-name":_="underline-tabs-buttons",stretchTabs:I,equalTabSize:R}=e,D=F(),V=function(e){const t=(0,z.useSafeMatchMedia)(A["media-mf-phone-landscape"],!0),l=F();return null!=e?e:l||!t?"scroll":"collapse"}(E),B=(0,r.useRef)(!1),N=(0,r.useCallback)((e=>e.id),[]),U="none"===V&&I,G="none"===V&&R,j=null!=T?T:!D,{visibleItems:X,hiddenItems:q,containerRefCallback:J,innerContainerRefCallback:$,moreButtonRef:Z,setItemRef:K}=(0,a.useCollapsible)(n,N,C),Q="collapse"===V?X:n,ee="collapse"===V?q:[],te=(0,r.useCallback)((e=>ee.includes(e)),[ee]),le=(0,r.useRef)(new Map),{isOpened:ne,open:re,close:ie,onButtonClick:oe}=(0,p.useDisclosure)({id:t,disabled:y}),se=function(e="xsmall"){switch(e){case"xsmall":case"small":return 12;case"medium":return 16;case"large":case"xlarge":return 20}}(b),{tabsBindings:ae,tablistBinding:ce,scrollWrapBinding:de,onActivate:pe,onHighlight:ue,isHighlighted:he}=(0,u.useTabs)({id:t,items:[...Q,...ee],activationType:i,orientation:v,disabled:y,tablistLabelId:x,tablistLabel:k,onActivate:w,isActive:C,isCollapsed:te,isRtl:O.isRtl,itemsRefs:le,isDisclosureOpened:ne,scrollIntoViewOptions:{additionalScroll:se}}),me=n.find(C),ve=ee.find(he),ye=(0,r.useCallback)((()=>{me&&ue(me)}),[ue,me]),ge=(0,r.useCallback)((e=>{var t;return null!==(t=ae.find((t=>t.id===e.id)))&&void 0!==t?t:{}}),[ae]),be=(0,r.useCallback)((()=>{ie(),ye(),B.current=!0}),[ie,ye]),fe=(0,
r.useCallback)((()=>{ve&&(pe(ve),ue(ve,200))}),[pe,ue,ve]);de.ref=(0,c.useMergedRefs)([de.ref,J]),ce.ref=(0,c.useMergedRefs)([ce.ref,$]),ce.onKeyDown=(0,d.createSafeMulticastEventHandler)((0,h.useKeyboardEventHandler)([(0,h.useKeyboardClose)(ne,be),(0,h.useKeyboardActionHandler)([13,32],fe,(0,r.useCallback)((()=>Boolean(ve)),[ve]))]),ce.onKeyDown);const we=(0,r.useCallback)((e=>{B.current=!0,oe(e)}),[B,oe]),Ce=(0,r.useCallback)((e=>{e&&pe(e)}),[pe]);(0,r.useEffect)((()=>{B.current?B.current=!1:(ve&&!ne&&re(),!ve&&ne&&ie())}),[ve,ne,re,ie]);const Pe=function(e,t,l=[]){const[n,i]=(0,r.useState)(),o=(0,r.useRef)(),s=(0,r.useRef)(),a=e=>{var t;const l=null!==(t=e.parentElement)&&void 0!==t?t:void 0;if(void 0===l)return;const n=void 0===s.current||s.current===e?0:100;s.current=e;const{left:r,right:o,width:a}=e.getBoundingClientRect(),{left:c,right:d}=l.getBoundingClientRect(),p=(0,O.isRtl)()?o-d:r-c;i({translateX:p,scale:a,transitionDuration:n})};return(0,r.useEffect)((()=>{const e=(0,H.default)((e=>{const t=e[0].target;void 0!==t&&a(t)}),50);o.current=new ResizeObserver(e)}),[]),(0,r.useEffect)((()=>{var l;if(void 0===t)return;const n=e.get(t);return void 0!==n?(a(n),null===(l=o.current)||void 0===l||l.observe(n),()=>{var e;return null===(e=o.current)||void 0===e?void 0:e.disconnect()}):void 0}),l),n}(le.current,null!=me?me:ve,[null!=me?me:ve,Q,b,U,V]);return r.createElement(m.Provider,{value:{size:b,overflowBehaviour:V}},r.createElement("div",{...de,className:W({size:b,overflowBehaviour:V,className:P}),style:S,"data-name":_},r.createElement("div",{...ce,className:o()(M["underline-tabs"],{[M["make-grid-column"]]:U||G,[M["stretch-tabs"]]:U,[M["equal-tab-size"]]:G})},Q.map((e=>r.createElement(f,{...ge(e),key:e.id,item:e,onClick:pe,enableActiveStateStyles:j,hideFocusOutline:D,ref:K(N(e)),...e.dataId&&{"data-id":e.dataId},equalTabSize:G}))),ee.map((e=>r.createElement(f,{...ge(e),ref:K(N(e)),key:e.id,item:e,fake:!0}))),"collapse"===V&&r.createElement(L,{size:b,disabled:y,isOpened:ne,items:ee,buttonContent:g,buttonRef:Z,isHighlighted:he,onButtonClick:we,onItemClick:Ce,onClose:ie,enableActiveStateStyles:j,hideFocusOutline:D,fake:0===ee.length}),Pe?r.createElement(Y,{...Pe,disabled:y}):r.createElement("div",null))))}var X=l(38952);function q(e){return r.createElement("a",{...(0,X.renameRef)(e)})}(0,r.forwardRef)(((e,t)=>{var l;const{size:n,overflowBehaviour:i}=(0,r.useContext)(m),o=(0,r.useContext)(v.CustomBehaviourContext),{item:s,highlighted:a,handleItemRef:c,onClick:d,active:p,fake:u,className:h,enableActiveStateStyles:y=o.enableActiveStateStyles,hideFocusOutline:b=!1,disabled:f,"aria-disabled":w,...C}=e,P=(0,r.useCallback)((e=>{w?e.preventDefault():d&&d(s)}),[d,w,s]),S=(0,r.useCallback)((e=>{c&&c(s,e),t&&"object"==typeof t?t.current=e:"function"==typeof t&&t(e)}),[s,c,t]),E=null!==(l=s.renderComponent)&&void 0!==l?l:q;return r.createElement(E,{...C,id:s.id,"aria-disabled":w,onClick:P,reference:S,href:s.href,rel:s.rel,target:s.target,className:g({size:n,active:p,fake:u,enableActiveStateStyles:y,anchor:!0,hideFocusOutline:b,
className:h,overflowBehaviour:i})},s.label)})).displayName="UnderlineAnchorTab"},73546:e=>{e.exports={wrapper:"wrapper-bl9AR3Gv",hovered:"hovered-bl9AR3Gv",switchWrap:"switchWrap-bl9AR3Gv",withIcon:"withIcon-bl9AR3Gv",labelRow:"labelRow-bl9AR3Gv",label:"label-bl9AR3Gv",icon:"icon-bl9AR3Gv",labelHint:"labelHint-bl9AR3Gv",labelOn:"labelOn-bl9AR3Gv",accessible:"accessible-bl9AR3Gv"}},47159:e=>{e.exports={smallStyleControl:"smallStyleControl-l5f4IL9k",additionalSelect:"additionalSelect-l5f4IL9k",childRowContainer:"childRowContainer-l5f4IL9k",defaultSelect:"defaultSelect-l5f4IL9k",defaultSelectItem:"defaultSelectItem-l5f4IL9k",block:"block-l5f4IL9k",group:"group-l5f4IL9k",wrapGroup:"wrapGroup-l5f4IL9k",textMarkGraphicBlock:"textMarkGraphicBlock-l5f4IL9k",textMarkGraphicWrapGroup:"textMarkGraphicWrapGroup-l5f4IL9k",transparency:"transparency-l5f4IL9k",color:"color-l5f4IL9k"}},10428:(e,t,l)=>{"use strict";l.d(t,{DEFAULT_MENU_ITEM_SWITCHER_THEME:()=>f,MenuItemSwitcher:()=>w});var n,r=l(50959),i=l(97754),o=l.n(i),s=l(17946),a=l(83072);function c(e){const{size:t="small",checked:l,disabled:n}=e;return r.createElement("span",{className:o()(a.switchView,a[t],n&&a.disabled,l&&a.checked)},r.createElement("span",{className:a.track}),r.createElement("span",{className:a.thumb}))}!function(e){e.Small="small",e.Medium="medium",e.Large="large"}(n||(n={}));var d,p=l(3343),u=l(12006),h=l.n(u);function m(e){const t=(0,r.useContext)(s.CustomBehaviourContext),{size:l,intent:n="default",checked:o,className:a,enableActiveStateStyles:d=t.enableActiveStateStyles,disabled:u,onChange:m,title:v,id:y,name:g,value:b,tabIndex:f,role:w="switch",ariaDisabled:C,reference:P,ariaLabelledBy:S,ariaLabel:E,...T}=e;return r.createElement("span",{className:i(a,o&&h().checked,h().switcher)},r.createElement("input",{...T,type:"checkbox",className:i(h().input,d&&h().activeStylesEnabled),role:w,"aria-checked":o,checked:o,onKeyDown:e=>{var t;13===(0,p.hashFromEvent)(e)&&(null===(t=e.currentTarget)||void 0===t||t.click())},onChange:m,disabled:u,"aria-disabled":C,title:v,id:y,name:g,value:b,ref:P,"aria-label":E,"aria-labelledby":S}),r.createElement("span",{className:i(h().thumbWrapper,h()[n])},r.createElement(c,{checked:o,size:l,disabled:u})))}!function(e){e.Default="default",e.Select="select"}(d||(d={}));var v=l(9745),y=l(50238),g=l(90186),b=l(73546);const f=b;function w(e){const{className:t,checked:l,id:n,label:i,labelDescription:s,value:a,preventLabelHighlight:c,reference:d,switchReference:u,theme:h=b,disabled:f,icon:w}=e,[C,P]=(0,y.useRovingTabindexElement)(null),S=o()(h.label,l&&!c&&h.labelOn),E=o()(t,h.wrapper,l&&h.wrapperWithOnLabel,s&&h.wrapperWithDescription);return r.createElement("label",{className:o()(E,w&&h.withIcon,b.accessible),htmlFor:n,ref:d,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,p.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),C.current instanceof HTMLElement&&C.current.click())},tabIndex:P,"data-role":"menuitem","aria-disabled":e.disabled||void 0},void 0!==w&&r.createElement(v.Icon,{className:h.icon,icon:w
}),r.createElement("div",{className:h.labelRow},r.createElement("div",{className:S},i),s&&r.createElement("div",{className:h.labelHint},s)),r.createElement("div",{className:b.switchWrap},r.createElement(m,{disabled:f,className:h.switch,reference:function(e){C(e),null==u||u(e)},checked:l,onChange:function(t){const l=t.target.checked;void 0!==e.onChange&&e.onChange(l)},value:a,tabIndex:-1,id:n,role:e.switchRole,ariaDisabled:!0,...(0,g.filterDataProps)(e)})))}},57717:(e,t,l)=>{"use strict";l.r(t),l.d(t,{createPropertyPage:()=>r});var n=l(64147);function r(e,t,l,r=null){var i;const o={id:t,title:l,definitions:new n.WatchedValue(e.definitions),visible:null!==(i=e.visible)&&void 0!==i?i:new n.WatchedValue(!0).readonly()};return null!==r&&(o.icon=r),o}},26434:(e,t,l)=>{"use strict";l.r(t),l.d(t,{getIntervalsVisibilitiesPropertiesDefinitions:()=>ae,getSelectionIntervalsVisibilitiesPropertiesDefinition:()=>ce});var n=l(11542),r=l(45126),i=l(56570),o=l(4116),s=l(64147),a=l(10074),c=l(73305),d=l(46112);const p=new r.TranslatedString("change {title} visibility on ticks",n.t(null,void 0,l(98596))),u=new r.TranslatedString("change {title} visibility on seconds",n.t(null,void 0,l(41315))),h=new r.TranslatedString("change {title} seconds from",n.t(null,void 0,l(86780))),m=new r.TranslatedString("change {title} seconds to",n.t(null,void 0,l(6573))),v=new r.TranslatedString("change {title} visibility on minutes",n.t(null,void 0,l(78219))),y=new r.TranslatedString("change {title} minutes from",n.t(null,void 0,l(59820))),g=new r.TranslatedString("change {title} minutes to",n.t(null,void 0,l(38011))),b=new r.TranslatedString("change {title} visibility on hours",n.t(null,void 0,l(68715))),f=new r.TranslatedString("change {title} hours from",n.t(null,void 0,l(8306))),w=new r.TranslatedString("change {title} hours to",n.t(null,void 0,l(67233))),C=new r.TranslatedString("change {title} visibility on days",n.t(null,void 0,l(56402))),P=new r.TranslatedString("change {title} days from",n.t(null,void 0,l(91201))),S=new r.TranslatedString("change {title} days to",n.t(null,void 0,l(96135))),E=new r.TranslatedString("change {title} visibility on weeks",n.t(null,void 0,l(71084))),T=new r.TranslatedString("change {title} weeks from",n.t(null,void 0,l(32481))),x=new r.TranslatedString("change {title} weeks to",n.t(null,void 0,l(18678))),k=new r.TranslatedString("change {title} visibility on months",n.t(null,void 0,l(67583))),_=new r.TranslatedString("change {title} months from",n.t(null,void 0,l(99122))),I=new r.TranslatedString("change {title} months to",n.t(null,void 0,l(10518))),R=(new r.TranslatedString("change {title} visibility on ranges",n.t(null,{replace:{ranges:"ranges"}},l(55616))),
n.t(null,void 0,l(24821))),D=n.t(null,void 0,l(65188)),L=n.t(null,void 0,l(42562)),V=n.t(null,void 0,l(56796)),B=n.t(null,void 0,l(72942)),N=n.t(null,void 0,l(835)),z=n.t(null,void 0,l(43154)),A=new r.TranslatedString("ticks",n.t(null,void 0,l(3539))),M=new r.TranslatedString("seconds",n.t(null,void 0,l(751))),W=new r.TranslatedString("seconds from",n.t(null,void 0,l(35801))),F=new r.TranslatedString("seconds to",n.t(null,void 0,l(73419))),H=new r.TranslatedString("minutes",n.t(null,void 0,l(18726))),O=new r.TranslatedString("minutes from",n.t(null,void 0,l(22476))),U=new r.TranslatedString("minutes to",n.t(null,void 0,l(67649))),G=new r.TranslatedString("hours",n.t(null,void 0,l(2359))),Y=new r.TranslatedString("hours from",n.t(null,void 0,l(82267))),j=new r.TranslatedString("hours to",n.t(null,void 0,l(15600))),X=new r.TranslatedString("days",n.t(null,void 0,l(35813))),q=new r.TranslatedString("days from",n.t(null,void 0,l(59215))),J=new r.TranslatedString("days to",n.t(null,void 0,l(89919))),$=new r.TranslatedString("weeks",n.t(null,void 0,l(45537))),Z=new r.TranslatedString("weeks from",n.t(null,void 0,l(92859))),K=new r.TranslatedString("weeks to",n.t(null,void 0,l(44127))),Q=new r.TranslatedString("months",n.t(null,void 0,l(95300))),ee=new r.TranslatedString("months from",n.t(null,void 0,l(17250))),te=new r.TranslatedString("months to",n.t(null,void 0,l(2828))),le=(new r.TranslatedString("ranges","ranges"),[1,59]),ne=[1,59],re=[1,24],ie=[1,366],oe=[1,52],se=[1,12];function ae(e,t,l){const n=[];if(i.enabled("tick_resolution")){const r=(0,o.createCheckablePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.ticks,p.format({title:l}))},{id:"IntervalsVisibilitiesTicks",title:R});n.push(r)}if((0,a.isSecondsEnabled)()){const r=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.seconds,u.format({title:l})),from:(0,o.convertToDefinitionProperty)(e,t.secondsFrom,h.format({title:l})),to:(0,o.convertToDefinitionProperty)(e,t.secondsTo,m.format({title:l}))},{id:"IntervalsVisibilitiesSecond",title:D,min:new s.WatchedValue(le[0]),max:new s.WatchedValue(le[1])});n.push(r)}const r=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.minutes,v.format({title:l})),from:(0,o.convertToDefinitionProperty)(e,t.minutesFrom,y.format({title:l})),to:(0,o.convertToDefinitionProperty)(e,t.minutesTo,g.format({title:l}))},{id:"IntervalsVisibilitiesMinutes",title:L,min:new s.WatchedValue(ne[0]),max:new s.WatchedValue(ne[1])}),c=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.hours,b.format({title:l})),from:(0,o.convertToDefinitionProperty)(e,t.hoursFrom,f.format({title:l})),to:(0,o.convertToDefinitionProperty)(e,t.hoursTo,w.format({title:l}))},{id:"IntervalsVisibilitiesHours",title:V,min:new s.WatchedValue(re[0]),max:new s.WatchedValue(re[1])}),d=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.days,C.format({title:l})),from:(0,o.convertToDefinitionProperty)(e,t.daysFrom,P.format({title:l})),to:(0,
o.convertToDefinitionProperty)(e,t.daysTo,S.format({title:l}))},{id:"IntervalsVisibilitiesDays",title:B,min:new s.WatchedValue(ie[0]),max:new s.WatchedValue(ie[1])});n.push(r,c,d);const A=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.weeks,E.format({title:l})),from:(0,o.convertToDefinitionProperty)(e,t.weeksFrom,T.format({title:l})),to:(0,o.convertToDefinitionProperty)(e,t.weeksTo,x.format({title:l}))},{id:"IntervalsVisibilitiesWeeks",title:N,min:new s.WatchedValue(oe[0]),max:new s.WatchedValue(oe[1])}),M=(0,o.createRangePropertyDefinition)({checked:(0,o.convertToDefinitionProperty)(e,t.months,k.format({title:l})),from:(0,o.convertToDefinitionProperty)(e,t.monthsFrom,_.format({title:l})),to:(0,o.convertToDefinitionProperty)(e,t.monthsTo,I.format({title:l}))},{id:"IntervalsVisibilitiesMonths",title:z,min:new s.WatchedValue(se[0]),max:new s.WatchedValue(se[1])});return n.push(A,M),{definitions:n}}function ce(e,t){const l=[];if(i.enabled("tick_resolution")){const n=(0,o.createCheckablePropertyDefinition)({checked:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.ticks),A,t)},{id:"IntervalsVisibilitiesTicks",title:R});l.push(n)}if((0,a.isSecondsEnabled)()){const n=(0,o.createRangePropertyDefinition)({checked:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.seconds),M,t),from:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.secondsFrom),W,t),to:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.secondsTo),F,t)},{id:"IntervalsVisibilitiesSecond",title:D,min:new s.WatchedValue(le[0]),max:new s.WatchedValue(le[1])});l.push(n)}const n=(0,o.createRangePropertyDefinition)({checked:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.minutes),H,t),from:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.minutesFrom),O,t),to:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.minutesTo),U,t)},{id:"IntervalsVisibilitiesMinutes",title:L,min:new s.WatchedValue(ne[0]),max:new s.WatchedValue(ne[1])}),r=(0,o.createRangePropertyDefinition)({checked:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.hours),G,t),from:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.hoursFrom),Y,t),to:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.hoursTo),j,t)},{id:"IntervalsVisibilitiesHours",title:V,min:new s.WatchedValue(re[0]),max:new s.WatchedValue(re[1])}),p=(0,o.createRangePropertyDefinition)({checked:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.days),X,t),from:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.daysFrom),q,t),to:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.daysTo),J,t)},{id:"IntervalsVisibilitiesDays",title:B,min:new s.WatchedValue(ie[0]),max:new s.WatchedValue(ie[1])});l.push(n,r,p);const u=(0,o.createRangePropertyDefinition)({checked:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.weeks),$,t),
from:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.weeksFrom),Z,t),to:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.weeksTo),K,t)},{id:"IntervalsVisibilitiesWeeks",title:N,min:new s.WatchedValue(oe[0]),max:new s.WatchedValue(oe[1])}),h=(0,o.createRangePropertyDefinition)({checked:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.months),Q,t),from:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.monthsFrom),ee,t),to:new d.CollectiblePropertyUndoWrapper(new c.LineToolCollectedProperty(e.monthsTo),te,t)},{id:"IntervalsVisibilitiesMonths",title:z,min:new s.WatchedValue(se[0]),max:new s.WatchedValue(se[1])});return l.push(u,h),{definitions:l}}},46112:(e,t,l)=>{"use strict";l.d(t,{CollectiblePropertyUndoWrapper:()=>a});var n=l(50151),r=l(11542),i=l(45126),o=l(12988);const s=new i.TranslatedString("change {propertyName} property",r.t(null,void 0,l(25167)));class a extends o.Property{constructor(e,t,l){super(),this._isProcess=!1,this._listenersMappers=[],this._valueApplier={applyValue:(e,t)=>{this._propertyApplier.setProperty(e,t,s)}},this._baseProperty=e,this._propertyApplier=l,this._propertyName=t}destroy(){this._baseProperty.destroy(),super.destroy()}value(){return this._baseProperty.value()}setValue(e,t){this._propertyApplier.beginUndoMacro(s.format({propertyName:this._propertyName})),this._isProcess=!0,this._baseProperty.setValue(e,void 0,this._valueApplier),this._isProcess=!1,this._propertyApplier.endUndoMacro(),this._listenersMappers.forEach((e=>{e.method.call(e.obj,this,"")}))}subscribe(e,t){const l=()=>{this._isProcess||t.call(e,this,"")};this._listenersMappers.push({obj:e,method:t,callback:l}),this._baseProperty.subscribe(e,l)}unsubscribe(e,t){var l;const r=(0,n.ensureDefined)(null===(l=this._listenersMappers.find((l=>l.obj===e&&l.method===t)))||void 0===l?void 0:l.callback);this._baseProperty.unsubscribe(e,r)}unsubscribeAll(e){this._baseProperty.unsubscribeAll(e)}}},1183:(e,t,l)=>{"use strict";l.d(t,{StudyPlotVisibleProperty:()=>i});var n=l(34776),r=l(52033);class i{constructor(e){this._subscribers=new r.Delegate,this._displayProperty=e,this._displayProperty.subscribe(this,this._displayPropertyValueChanged)}destroy(){this._displayProperty.unsubscribe(this,this._displayPropertyValueChanged),this._subscribers.destroy()}value(){return 0!==this._displayProperty.value()}setValue(e,t){this._displayProperty.setValue(e?15:0)}subscribe(e,t){this._subscribers.subscribe(e,t,!1)}unsubscribe(e,t){this._subscribers.unsubscribe(e,t)}unsubscribeAll(e){this._subscribers.unsubscribeAll(e)}storeStateIfUndefined(){return!1}weakReference(){return(0,n.weakReference)(this)}ownership(){return(0,n.ownership)(this)}_displayPropertyValueChanged(){this._subscribers.fire(this)}}},47531:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="m4.67 7.38.66-.76L9 9.84l3.67-3.22.66.76L9 11.16 4.67 7.38Z"/></svg>'},63509:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.57 7.85 9 12.62l5.43-4.77-1.32-1.5L9 9.95l-4.11-3.6-1.32 1.5Z"/></svg>'},68874:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m14 18.41-6.7-6.7 1.4-1.42 5.3 5.3 5.3-5.3 1.4 1.41-6.7 6.71Z"/></svg>'},69151:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14 21l7.424-6.114a.5.5 0 0 0-.318-.886H18.5V7h-9v7H6.894a.5.5 0 0 0-.318.886L14 21z"/></svg>'},67211:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14 7l7.424 6.114a.5.5 0 0 1-.318.886H18.5v7h-9v-7H6.894a.5.5 0 0 1-.318-.886L14 7z"/></svg>'},83786:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><circle stroke="currentColor" cx="14" cy="14" r="6.5"/></svg>'},50858:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M9 14.5h11M14.5 20V9"/></svg>'},13201:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14.354 6.646L14 6.293l-.354.353-7 7-.353.354.353.354 7 7 .354.353.354-.353 7-7 .353-.354-.353-.354-7-7z"/></svg>'},59058:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8.5 22v-5.5m0 0v-8L12 7l4 2.5 3.5-1v8l-3.5 1-4-2.5-3.5 1.5z"/></svg>'},8537:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M11 8.5h-.5v9.707l.146.147 3 3 .354.353.354-.353 3-3 .146-.147V8.5H11z"/></svg>'},2309:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M11 18.5h-.5V8.793l.146-.147 3-3L14 5.293l.354.353 3 3 .146.147V18.5H11z"/></svg>'},78240:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M7.5 7.5h13v13h-13z"/></svg>'},41683:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M19.424 11.265l.478-.765H8.098l.478.765 5 8 .424.678.424-.678 5-8z"/></svg>'},63798:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M19.424 16.735l.478.765H8.098l.478-.765 5-8L14 8.057l.424.678 5 8z"/></svg>'},23223:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M9 9l11 11M9 20L20 9"/></svg>'},93976:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M13 11.5l-1.915-1.532a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82V18.5a1 1 0 0 0 1 1H13m3.5-7l4.293-4.293c.63-.63 1.707-.184 1.707.707V18.5a1 1 0 0 1-1 1H16"/><path fill="currentColor" d="M14 6h1v2h-1zM14 11h1v2h-1zM14 16h1v2h-1zM14 21h1v2h-1z"/></svg>'},91512:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 13.52v4.98a1 1 0 0 0 1 1h15a1 1 0 0 0 1-1V8.914c0-.89-1.077-1.337-1.707-.707l-4.66 4.66a1 1 0 0 1-1.332.074l-3.716-2.973a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82z"/></svg>'},21579:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M10.5 13a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM16.5 19a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM22.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/></svg>'},72914:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M6.5 12.5v8h3v-8h-3zM12.5 7.5v13h3v-13h-3zM18.5 15.5v5h3v-5h-3z"/></svg>'},98450:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M17 8.5h7M20.5 12V5M10 19.5h7M13.5 23v-7M3 12.5h7M6.5 16V9"/></svg>'},18621:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4.5 20v-7m3 7V10m3 10V8m3 12V10m3 10v-8m3 8V10m3 10V8"/></svg>'},18819:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l5-5a1.414 1.414 0 0 1 2 0m11-1l-5 5a1.414 1.414 0 0 1-2 0"/><path fill="currentColor" d="M14 5h1v2h-1zM14 10h1v2h-1zM14 15h1v2h-1zM14 20h1v2h-1z"/></svg>'},94152:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l4.586-4.586a2 2 0 0 1 2.828 0l3.172 3.172a2 2 0 0 0 2.828 0L23.5 10.5"/></svg>'},46464:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14 3h1v2h-1V3Zm1 5h-1v2h1V8Zm-1 5h1v2h-1v-2Zm0 5h1v2h-1v-2Zm0 5h1v2h-1v-2ZM10 5h2V4H9v18H6v-5H5v6h5V5Zm11 16h1V7h-5v10h1V8h3v13Z"/></svg>'},96298:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M9.8 2.7l.7-.7.7.7 2.1 2.1.2.2H18v9.5l.2.2 2.1 2.1.2.2H24v1h-3.5l-.2.2-2.1 2.1-.7.7-.7-.7-2.1-2.1-.7-.7.7-.7 2.1-2.1.2-.2V6h-3.5l-.2.2-2.1 2.1-.2.2V24H5.5v-1H10V8.5l-.2-.2-2.1-2.1-.7-.7.7-.7 2.1-2.1zM8.4 5.5l2.09 2.09 2.09-2.09-2.09-2.09L8.41 5.5zm9.09 14.09l-2.09-2.09 2.09-2.09 2.09 2.09-2.09 2.09z"/></svg>'},14643:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 17v5.5h4v-18h4v12h4v-9h4V21"/></svg>'}}]);