(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4862],{74581:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},81329:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},55315:e=>{e.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF","with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},4914:e=>{e.exports={itemRow:"itemRow-BadjY5sX",active:"active-BadjY5sX",selected:"selected-BadjY5sX",mobile:"mobile-BadjY5sX",itemInfo:"itemInfo-BadjY5sX",title:"title-BadjY5sX",details:"details-BadjY5sX",itemInfoWithPadding:"itemInfoWithPadding-BadjY5sX",favoriteButton:"favoriteButton-BadjY5sX",favorite:"favorite-BadjY5sX",removeButton:"removeButton-BadjY5sX"}},91504:e=>{e.exports={scrollWrap:"scrollWrap-FaOvTD2r"}},60591:e=>{e.exports={wrap:"wrap-vSb6C0Bj","wrap--horizontal":"wrap--horizontal-vSb6C0Bj",bar:"bar-vSb6C0Bj",barInner:"barInner-vSb6C0Bj","barInner--horizontal":"barInner--horizontal-vSb6C0Bj","bar--horizontal":"bar--horizontal-vSb6C0Bj"}},59385:e=>{e.exports={dropTargetInside:"dropTargetInside-e_nPSSdZ",dropTarget:"dropTarget-e_nPSSdZ",before:"before-e_nPSSdZ",after:"after-e_nPSSdZ"}},12356:e=>{e.exports={sticky:"sticky-U0YaDVkl",hideSticky:"hideSticky-U0YaDVkl"}},72045:e=>{e.exports={wrap:"wrap-IEe5qpW4",selected:"selected-IEe5qpW4",
childOfSelected:"childOfSelected-IEe5qpW4",disabled:"disabled-IEe5qpW4",expandHandle:"expandHandle-IEe5qpW4",expanded:"expanded-IEe5qpW4"}},89791:e=>{e.exports={separator:"separator-MgF6KBas",sticky:"sticky-MgF6KBas",tree:"tree-MgF6KBas",overlayScrollWrap:"overlayScrollWrap-MgF6KBas",listContainer:"listContainer-MgF6KBas"}},68016:e=>{e.exports={title:"title-QPktCwTY",container:"container-QPktCwTY",mobile:"mobile-QPktCwTY",empty:"empty-QPktCwTY",image:"image-QPktCwTY",spinner:"spinner-QPktCwTY",contentList:"contentList-QPktCwTY",item:"item-QPktCwTY"}},90684:e=>{e.exports={dialog:"dialog-VUnQLSMH",buttons:"buttons-VUnQLSMH",button:"button-VUnQLSMH",disabled:"disabled-VUnQLSMH"}},91439:e=>{e.exports={title:"title-uNZ8yW1y",withoutIcon:"withoutIcon-uNZ8yW1y",buttons:"buttons-uNZ8yW1y",button:"button-uNZ8yW1y",disabled:"disabled-uNZ8yW1y",spacing:"spacing-uNZ8yW1y",toolbar:"toolbar-uNZ8yW1y"}},63770:e=>{e.exports={wrap:"wrap-C8ln3wvp",dialog:"dialog-C8ln3wvp",mobile:"mobile-C8ln3wvp",offset:"offset-C8ln3wvp",title:"title-C8ln3wvp",main:"main-C8ln3wvp",disabled:"disabled-C8ln3wvp",icon:"icon-C8ln3wvp",pathIcon:"pathIcon-C8ln3wvp",syncIconWrap:"syncIconWrap-C8ln3wvp",syncIcon:"syncIcon-C8ln3wvp",rightButtons:"rightButtons-C8ln3wvp",hover:"hover-C8ln3wvp",expandHandle:"expandHandle-C8ln3wvp",button:"button-C8ln3wvp",selected:"selected-C8ln3wvp",childOfSelected:"childOfSelected-C8ln3wvp",renameInput:"renameInput-C8ln3wvp",warn:"warn-C8ln3wvp",visible:"visible-C8ln3wvp"}},64083:e=>{e.exports={wrap:"wrap-ukH4sVzT",space:"space-ukH4sVzT",tree:"tree-ukH4sVzT"}},69658:e=>{e.exports={"default-drawer-min-top-distance":"100px",wrap:"wrap-_HnK0UIN",positionBottom:"positionBottom-_HnK0UIN",backdrop:"backdrop-_HnK0UIN",drawer:"drawer-_HnK0UIN",positionLeft:"positionLeft-_HnK0UIN"}},98992:e=>{e.exports={favorite:"favorite-_FRQhM5Y",hovered:"hovered-_FRQhM5Y",disabled:"disabled-_FRQhM5Y",active:"active-_FRQhM5Y",checked:"checked-_FRQhM5Y"}},20723:e=>{e.exports={button:"button-w6lVe_oI",hovered:"hovered-w6lVe_oI",disabled:"disabled-w6lVe_oI"}},11772:e=>{e.exports={button:"button-iLKiGOdQ",hovered:"hovered-iLKiGOdQ",disabled:"disabled-iLKiGOdQ",active:"active-iLKiGOdQ",hidden:"hidden-iLKiGOdQ"}},95604:(e,t,n)=>{"use strict";function o(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>o})},67029:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>b,InputClasses:()=>m});var o=n(50959),r=n(97754),i=n(50151),s=n(38528),l=n(90186),a=n(86332),c=n(95604);var u=n(74581),d=n.n(u);function h(e){let t="";return 0!==e&&(1&e&&(t=r(t,d()["no-corner-top-left"])),2&e&&(t=r(t,d()["no-corner-top-right"])),4&e&&(t=r(t,d()["no-corner-bottom-right"])),8&e&&(t=r(t,d()["no-corner-bottom-left"]))),t}function p(e,t,n,o){const{removeRoundBorder:i,className:s,intent:l="default",borderStyle:a="thin",size:u,highlight:p,disabled:f,readonly:m,stretch:v,noReadonlyStyles:g,isFocused:b}=e,S=h(null!=i?i:(0,c.getGroupCellRemoveRoundBorders)(n))
;return r(d().container,d()[`container-${u}`],d()[`intent-${l}`],d()[`border-${a}`],u&&d()[`size-${u}`],S,p&&d()["with-highlight"],f&&d().disabled,m&&!g&&d().readonly,b&&d().focused,v&&d().stretch,t&&d().grouped,!o&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],s)}function f(e,t,n){const{highlight:o,highlightRemoveRoundBorder:i}=e;if(!o)return d().highlight;const s=h(null!=i?i:(0,c.getGroupCellRemoveRoundBorders)(t));return r(d().highlight,d().shown,d()[`size-${n}`],s)}const m={FontSizeMedium:(0,i.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,i.ensureDefined)(d()["font-size-large"])},v={passive:!1};function g(e,t){const{style:n,id:r,role:i,onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:m,onMouseUp:g,onKeyDown:b,onClick:S,tabIndex:y,startSlot:_,middleSlot:C,endSlot:w,onWheel:E,onWheelNoPassive:T=null,size:M}=e,{isGrouped:I,cellState:D,disablePositionAdjustment:N=!1}=(0,o.useContext)(a.ControlGroupContext),k=function(e,t=null,n){const r=(0,o.useRef)(null),i=(0,o.useRef)(null),s=(0,o.useCallback)((()=>{if(null===r.current||null===i.current)return;const[e,t,n]=i.current;null!==t&&r.current.addEventListener(e,t,n)}),[]),l=(0,o.useCallback)((()=>{if(null===r.current||null===i.current)return;const[e,t,n]=i.current;null!==t&&r.current.removeEventListener(e,t,n)}),[]),a=(0,o.useCallback)((e=>{l(),r.current=e,s()}),[]);return(0,o.useEffect)((()=>(i.current=[e,t,n],s(),l)),[e,t,n]),a}("wheel",T,v);return o.createElement("span",{style:n,id:r,role:i,className:p(e,I,D,N),tabIndex:y,ref:(0,s.useMergedRefs)([t,k]),onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:m,onMouseUp:g,onKeyDown:b,onClick:S,onWheel:E,...(0,l.filterDataProps)(e),...(0,l.filterAriaProps)(e)},_,C,w,o.createElement("span",{className:f(e,D,M)}))}g.displayName="ControlSkeleton";const b=o.forwardRef(g)},78274:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>u,EndSlot:()=>c,MiddleSlot:()=>a,StartSlot:()=>l});var o=n(50959),r=n(97754),i=n(81329),s=n.n(i);function l(e){const{className:t,interactive:n=!0,icon:i=!1,children:l}=e;return o.createElement("span",{className:r(s()["inner-slot"],n&&s().interactive,i&&s().icon,t)},l)}function a(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(s()["inner-slot"],s()["inner-middle-slot"],t)},n)}function c(e){const{className:t,interactive:n=!0,icon:i=!1,children:l}=e;return o.createElement("span",{className:r(s()["inner-slot"],n&&s().interactive,i&&s().icon,t)},l)}function u(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(s()["after-slot"],t)},n)}},31261:(e,t,n)=>{"use strict";n.d(t,{InputControl:()=>b});var o=n(50959),r=n(97754),i=n(90186),s=n(47201),l=n(48907),a=n(38528),c=n(48027),u=n(29202),d=n(45812),h=n(67029),p=n(78274),f=n(55315),m=n.n(f);function v(e){return!(0,i.isAriaAttribute)(e)&&!(0,i.isDataAttribute)(e)}function g(e){
const{id:t,title:n,role:s,tabIndex:l,placeholder:a,name:c,type:u,value:d,defaultValue:f,draggable:g,autoComplete:b,autoFocus:S,maxLength:y,min:_,max:C,step:w,pattern:E,inputMode:T,onSelect:M,onFocus:I,onBlur:D,onKeyDown:N,onKeyUp:k,onKeyPress:x,onChange:L,onDragStart:R,size:B="small",className:A,inputClassName:O,disabled:P,readonly:F,containerTabIndex:z,startSlot:W,endSlot:H,reference:j,containerReference:V,onContainerFocus:G,...K}=e,U=(0,i.filterProps)(K,v),Z={...(0,i.filterAriaProps)(K),...(0,i.filterDataProps)(K),id:t,title:n,role:s,tabIndex:l,placeholder:a,name:c,type:u,value:d,defaultValue:f,draggable:g,autoComplete:b,autoFocus:S,maxLength:y,min:_,max:C,step:w,pattern:E,inputMode:T,onSelect:M,onFocus:I,onBlur:D,onKeyDown:N,onKeyUp:k,onKeyPress:x,onChange:L,onDragStart:R};return o.createElement(h.ControlSkeleton,{...U,disabled:P,readonly:F,tabIndex:z,className:r(m().container,A),size:B,ref:V,onFocus:G,startSlot:W,middleSlot:o.createElement(p.MiddleSlot,null,o.createElement("input",{...Z,className:r(m().input,m()[`size-${B}`],O,W&&m()["with-start-slot"],H&&m()["with-end-slot"]),disabled:P,readOnly:F,ref:j})),endSlot:H})}function b(e){e=(0,c.useControl)(e);const{disabled:t,autoSelectOnFocus:n,tabIndex:r=0,onFocus:i,onBlur:h,reference:p,containerReference:f=null}=e,m=(0,o.useRef)(null),v=(0,o.useRef)(null),[b,S]=(0,u.useFocus)(),y=t?void 0:b?-1:r,_=t?void 0:b?r:-1,{isMouseDown:C,handleMouseDown:w,handleMouseUp:E}=(0,d.useIsMouseDown)(),T=(0,s.createSafeMulticastEventHandler)(S.onFocus,(function(e){n&&!C.current&&(0,l.selectAllContent)(e.currentTarget)}),i),M=(0,s.createSafeMulticastEventHandler)(S.onBlur,h),I=(0,o.useCallback)((e=>{m.current=e,p&&("function"==typeof p&&p(e),"object"==typeof p&&(p.current=e))}),[m,p]);return o.createElement(g,{...e,isFocused:b,containerTabIndex:y,tabIndex:_,onContainerFocus:function(e){v.current===e.target&&null!==m.current&&m.current.focus()},onFocus:T,onBlur:M,reference:I,containerReference:(0,a.useMergedRefs)([v,f]),onMouseDown:w,onMouseUp:E})}},48027:(e,t,n)=>{"use strict";n.d(t,{useControl:()=>i});var o=n(47201),r=n(29202);function i(e){const{onFocus:t,onBlur:n,intent:i,highlight:s,disabled:l}=e,[a,c]=(0,r.useFocus)(void 0,l),u=(0,o.createSafeMulticastEventHandler)(l?void 0:c.onFocus,t),d=(0,o.createSafeMulticastEventHandler)(l?void 0:c.onBlur,n);return{...e,intent:i||(a?"primary":"default"),highlight:null!=s?s:a,onFocus:u,onBlur:d}}},76974:(e,t,n)=>{"use strict";n.d(t,{useIsMounted:()=>r});var o=n(50959);const r=()=>{const e=(0,o.useRef)(!1);return(0,o.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}},45812:(e,t,n)=>{"use strict";n.d(t,{useIsMouseDown:()=>r});var o=n(50959);function r(){const e=(0,o.useRef)(!1),t=(0,o.useCallback)((()=>{e.current=!0}),[e]),n=(0,o.useCallback)((()=>{e.current=!1}),[e]);return{isMouseDown:e,handleMouseDown:t,handleMouseUp:n}}},48907:(e,t,n)=>{"use strict";function o(e){null!==e&&e.setSelectionRange(0,e.value.length)}n.d(t,{selectAllContent:()=>o})},64530:(e,t,n)=>{"use strict";n.d(t,{DialogContentItem:()=>h})
;var o=n(50959),r=n(97754),i=n.n(r),s=n(49483),l=n(36189),a=n(96040);function c(e){const{url:t,...n}=e;return t?o.createElement("a",{...n,href:t}):o.createElement("div",{...n})}var u=n(60925),d=n(4914);function h(e){const{title:t,subtitle:n,removeBtnLabel:r,onClick:h,onClickFavorite:f,onClickRemove:m,isActive:v,isSelected:g,isFavorite:b,isMobile:S=!1,showFavorite:y=!0,className:_,...C}=e;return o.createElement(c,{...C,className:i()(d.itemRow,v&&!g&&d.active,S&&d.mobile,g&&d.selected,_),onClick:p.bind(null,h),"data-role":"list-item","data-active":v},y&&f&&o.createElement(l.FavoriteButton,{className:i()(d.favoriteButton,b&&d.favorite,s.CheckMobile.any()&&d.mobile),isActive:v&&!g,isFilled:b,onClick:p.bind(null,f),"data-name":"list-item-favorite-button","data-favorite":b}),o.createElement("div",{className:i()(d.itemInfo,!y&&d.itemInfoWithPadding)},o.createElement("div",{className:i()(d.title,v&&!g&&d.active,S&&d.mobile),"data-name":"list-item-title"},t),o.createElement("div",{className:i()(d.details,v&&!g&&d.active,S&&d.mobile)},n)),o.createElement(a.RemoveButton,{className:d.removeButton,isActive:v&&!g,onClick:p.bind(null,m),"data-name":"list-item-remove-button",title:r,icon:u}))}function p(e,t){t.defaultPrevented||(t.preventDefault(),e(t))}},3085:(e,t,n)=>{"use strict";n.d(t,{OverlayScrollContainer:()=>v});var o=n(50959),r=n(97754),i=n.n(r),s=n(63273),l=n(50151),a=n(9859);const c=n(60591);var u;!function(e){e[e.Vertical=0]="Vertical",e[e.Horizontal=1]="Horizontal",e[e.HorizontalRtl=2]="HorizontalRtl"}(u||(u={}));const d={0:{isHorizontal:!1,isNegative:!1,sizePropName:"height",minSizePropName:"minHeight",startPointPropName:"top",currentMousePointPropName:"clientY",progressBarTransform:"translateY"},1:{isHorizontal:!0,isNegative:!1,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"left",currentMousePointPropName:"clientX",progressBarTransform:"translateX"},2:{isHorizontal:!0,isNegative:!0,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"right",currentMousePointPropName:"clientX",progressBarTransform:"translateX"}},h=40;function p(e){const{size:t,scrollSize:n,clientSize:r,scrollProgress:s,onScrollProgressChange:u,scrollMode:p,theme:f=c,onDragStart:m,onDragEnd:v,minBarSize:g=h}=e,b=(0,o.useRef)(null),S=(0,o.useRef)(null),[y,_]=(0,o.useState)(!1),C=(0,o.useRef)(0),{isHorizontal:w,isNegative:E,sizePropName:T,minSizePropName:M,startPointPropName:I,currentMousePointPropName:D,progressBarTransform:N}=d[p];(0,o.useEffect)((()=>{const e=(0,l.ensureNotNull)(b.current).ownerDocument;return y?(m&&m(),e&&(e.addEventListener("mousemove",F),e.addEventListener("mouseup",z))):v&&v(),()=>{e&&(e.removeEventListener("mousemove",F),e.removeEventListener("mouseup",z))}}),[y]);const k=t/n||0,x=r*k||0,L=Math.max(x,g),R=(t-L)/(t-x),B=n-t,A=E?-B:0,O=E?0:B,P=H((0,a.clamp)(s,A,O))||0;return o.createElement("div",{ref:b,className:i()(f.wrap,w&&f["wrap--horizontal"]),style:{[T]:t},onMouseDown:function(e){if(e.isDefaultPrevented())return;e.preventDefault();const t=W(e.nativeEvent,(0,
l.ensureNotNull)(b.current)),n=Math.sign(t),o=(0,l.ensureNotNull)(S.current).getBoundingClientRect();C.current=n*o[T]/2;let r=Math.abs(t)-Math.abs(C.current);const i=H(B);r<0?(r=0,C.current=t):r>i&&(r=i,C.current=t-n*i);u(j(n*r)),_(!0)}},o.createElement("div",{ref:S,className:i()(f.bar,w&&f["bar--horizontal"]),style:{[M]:g,[T]:L,transform:`${N}(${P}px)`},onMouseDown:function(e){e.preventDefault(),C.current=W(e.nativeEvent,(0,l.ensureNotNull)(S.current)),_(!0)}},o.createElement("div",{className:i()(f.barInner,w&&f["barInner--horizontal"])})));function F(e){const t=W(e,(0,l.ensureNotNull)(b.current))-C.current;u(j(t))}function z(){_(!1)}function W(e,t){const n=t.getBoundingClientRect()[I];return e[D]-n}function H(e){return e*k*R}function j(e){return e/k/R}}var f=n(70412),m=n(91504);function v(e){const{reference:t,className:n,containerHeight:i=0,containerWidth:l=0,contentHeight:a=0,contentWidth:c=0,scrollPosTop:u=0,scrollPosLeft:d=0,onVerticalChange:h,onHorizontalChange:v,visible:g}=e,[b,S]=(0,f.useHover)(),[y,_]=(0,o.useState)(!1),C=i<a,w=l<c,E=C&&w?8:0;return o.createElement("div",{...S,ref:t,className:r(n,m.scrollWrap),style:{visibility:g||b||y?"visible":"hidden"}},C&&o.createElement(p,{size:i-E,scrollSize:a-E,clientSize:i-E,scrollProgress:u,onScrollProgressChange:function(e){h&&h(e)},onDragStart:T,onDragEnd:M,scrollMode:0}),w&&o.createElement(p,{size:l-E,scrollSize:c-E,clientSize:l-E,scrollProgress:d,onScrollProgressChange:function(e){v&&v(e)},onDragStart:T,onDragEnd:M,scrollMode:(0,s.isRtl)()?2:1}));function T(){_(!0)}function M(){_(!1)}}},50238:(e,t,n)=>{"use strict";n.d(t,{useRovingTabindexElement:()=>i});var o=n(50959),r=n(39416);function i(e,t=[]){const[n,i]=(0,o.useState)(!1),s=(0,r.useFunctionalRefObject)(e);return(0,o.useLayoutEffect)((()=>{const e=s.current;if(null===e)return;const t=e=>{switch(e.type){case"roving-tabindex:main-element":i(!0);break;case"roving-tabindex:secondary-element":i(!1)}};return e.addEventListener("roving-tabindex:main-element",t),e.addEventListener("roving-tabindex:secondary-element",t),()=>{e.removeEventListener("roving-tabindex:main-element",t),e.removeEventListener("roving-tabindex:secondary-element",t)}}),t),[s,n?0:-1]}},37404:(e,t,n)=>{"use strict";n.d(t,{showManageDrawingsDialog:()=>r});let o=null;function r(e){return Promise.all([n.e(4166),n.e(8316),n.e(6220),n.e(291),n.e(1702)]).then(n.bind(n,41662)).then((t=>{const n=new(0,t.ManageDrawingsDialogRenderer)(e);return null!==o&&o.hide(),n.show(),o=n,n}))}},11386:(e,t,n)=>{"use strict";n.d(t,{ManageDrawings:()=>k});var o=n(50959),r=n(43370),i=n(97754),s=n.n(i),l=n(9745),a=n(11542),c=n(45126),u=n(64147),d=n(31955),h=n(630),p=n(64530),f=n(37265),m=n(63932),v=n(89846);const g=a.t(null,void 0,n(92931)),b=a.t(null,void 0,n(41870)),S=a.t(null,void 0,n(80996));function y(e){const{sharingMode:t,onTabClick:n}=e,r=o.useMemo((()=>[{children:g,id:"2"},{children:b,id:"1"},{children:S,id:"0"}]),[]);return o.createElement(v.RoundButtonTabs,{id:"manage-drawings-tabs",isActive:e=>parseInt(e.id)===t,onActivate:function(e){n(parseInt(e.id))},
overflowBehaviour:"scroll",items:r})}var _=n(29540),C=n(68016);const w=(0,d.getLogger)("Chart.ManageDrawings"),E=new Map;function T(e){let t=E.get(e);return void 0===t&&(t=new u.WatchedValue([]),E.set(e,t)),t}const M=new c.TranslatedString("remove all line tools for {symbol}",a.t(null,void 0,n(58407))),I=a.t(null,void 0,n(8182)),D=a.t(null,void 0,n(84212));function N(e){const[t,n]=o.useState(null),[i,s]=o.useState(null),[l,a]=o.useState(null),[c,u]=(o.useRef(null),o.useState([]));return o.useEffect((()=>{let t;const o=()=>{t&&s(t.mainSeries().proSymbol())};return e.withModel(null,(()=>{t=e.model(),n(t),o(),t.mainSeries().symbolResolved().subscribe(null,o)})),()=>{null==t||t.mainSeries().symbolResolved().unsubscribe(null,o),n(null)}}),[e]),o.useEffect((()=>{if(null!==t){const e={},n=(0,r.default)(v,250,{leading:!1});return v(),t.model().dataSourceCollectionChanged().subscribe(e,n),()=>{t.model().dataSourceCollectionChanged().unsubscribe(e,n)}}}),[t]),o.useEffect((()=>{if(null!==t){const e=T(t.model().id()).spawn();return u([...e.value()]),e.subscribe((()=>u([...e.value()]))),()=>null==e?void 0:e.destroy()}}),[t]),o.useMemo((()=>({currentSymbol:i,symbolDrawingsMaps:l,removeSymbolDrawings:d,changeSymbol:p,hiddenSymbols:c})),[i,l,d,p,c]);async function d(e,n){if(t&&l){const o=l[n].get(e);if(o){const n=Array.from(o).map((e=>t.model().dataSourceForId(e))).filter(f.notNull);n.length>0&&t.removeSources(n,!1,M.format({symbol:e}));const r=T(t.model().id());r.setValue([...r.value(),e]);try{await v()}catch(e){w.logError(`Error removing line tools: ${e}`)}r.setValue(r.value().filter((t=>t!==e)))}}}function p(n){e.setSymbol(n),null!==t&&s(n)}async function m(e){const t=function(e){const t=[new Map,new Map,new Map];return e.forEach((e=>{var n;if((0,h.isLineTool)(e)&&e.showInObjectTree()){const o=null!==(n=e.symbol())&&void 0!==n?n:"",r=e.sharingMode().value();t[r].set(o,(t[r].get(o)||new Set).add(e.id()))}})),t}(e);return(await async function(){return[new Map,new Map,new Map]}()).forEach(((e,n)=>{const o=t[n];e.forEach(((e,t)=>{const n=o.get(t)||new Set;e.forEach((e=>n.add(e))),o.set(t,n)}))})),t}async function v(){null!==t&&a(await m(t.dataSources()))}}function k(e){const{isMobile:t,isSmallWidth:r,chartWidget:i,onClose:c,onInitialized:u}=e,{currentSymbol:d,symbolDrawingsMaps:h,removeSymbolDrawings:f,changeSymbol:v,hiddenSymbols:g}=N(i),[b,S]=o.useState(null),[w,E]=o.useMemo((()=>{var e;if(null!==d&&null!==h){const t=[];let n=b;if(null===n)for(n=2;n>0&&!(((null===(e=h[n].get(d))||void 0===e?void 0:e.size)||0)>0);)n--;return h[n].forEach(((e,n)=>{g.includes(n)||t.push({symbol:n,drawingsCount:e.size,onRemove:()=>function(e){f(e,E)}(n),onClick:()=>function(e){""!==e&&(v(e),null==c||c())}(n)})})),t.sort(((e,t)=>e.drawingsCount===t.drawingsCount?e.symbol.localeCompare(t.symbol):e.drawingsCount>t.drawingsCount?-1:1)),[t,n]}return[[],0]}),[d,b,h,g]);return o.useEffect((()=>{null!==h&&(null==u||u())}),[h]),o.createElement(o.Fragment,null,o.createElement("div",{className:s()(C.container,(r||t)&&C.mobile)},o.createElement(y,{sharingMode:E,
onTabClick:S})),0===w.length?null===h?o.createElement(m.Spinner,{className:C.spinner}):o.createElement("div",{className:C.empty},o.createElement(l.Icon,{className:C.image,icon:_}),o.createElement("span",null,D)):w.map((({symbol:e,drawingsCount:r,onRemove:i,onClick:s})=>{return o.createElement(p.DialogContentItem,{key:e,title:e,subtitle:(l=r,a.t(null,{plural:"{drawingsCount} drawings",count:l},n(90755)).format({drawingsCount:l.toString()})),removeBtnLabel:I,isActive:e===d,isMobile:t,onClick:s,onClickRemove:i,showFavorite:!1,className:C.item});var l})))}},34489:(e,t,n)=>{"use strict";n.r(t),n.d(t,{ObjectTreeDialogRenderer:()=>mr});var o=n(50959);async function r(e,t,n){let o;for(let r=0;r<t;++r)try{return await e(o)}catch(e){o=e,await n(r)}throw o}async function i(e,t){return r(e,t,(()=>Promise.resolve()))}var s=n(31955);const l=(0,s.getLogger)("DataSourcesIcons");let a=null;function c(){const e=n.c[98626];return e?Promise.resolve(e.exports.lineToolsIcons):n.e(1890).then(n.bind(n,98626)).then((e=>e.lineToolsIcons))}function u(){const e=n.c[23076];return e?Promise.resolve(e.exports.SERIES_ICONS):n.e(9685).then(n.bind(n,23076)).then((e=>e.SERIES_ICONS))}let d=null;function h(){return null===d&&(d=function(){const e=i(c,2).then((e=>e)).catch((e=>(l.logWarn(e),{}))),t=i(u,2).then((e=>e)).catch((e=>(l.logWarn(e),{})));return Promise.all([e,t])}()),d.then((e=>(a={linetool:e[0],series:e[1]},a)))}var p=n(11542),f=n(97754),m=n.n(f),v=n(125),g=n(76974),b=n(41590),S=n(37558),y=n(90692),_=n(79418),C=n(24437),w=n(11386),E=(n(37404),n(29885)),T=n(32563),M=n(68335),I=n(50151),D=n(9745),N=n(10838),k=n(88811),x=n(36947);const L=o.createContext(null);var R=n(98945),B=n(6190),A=n(19291),O=n(36296),P=n(74059),F=n(80465),z=n(91439),W=n(51768);function H(e){const{hideTitle:t}=e,{viewModel:r}=(0,I.ensureNotNull)((0,o.useContext)(L)),i=(0,x.useForceUpdate)(),s=r.selection();(0,o.useEffect)((()=>{const e={};return r.onChange().subscribe(e,(()=>i())),()=>{r.onChange().unsubscribeAll(e)}}),[r]),(0,o.useEffect)((()=>{const e={};return s.onChange().subscribe(e,(()=>i())),()=>{s.onChange().unsubscribeAll(e)}}),[s]),(0,o.useEffect)((()=>{(0,A.updateTabIndexes)()}),[]);const l=!r.canSelectionBeUnmerged(),a=r.isSelectionCopiable(),c=r.isSelectionCloneable(),u=!a&&!c,d=r.canSelectionBeGrouped(),h=!1;return o.createElement(B.Toolbar,{orientation:"horizontal",className:z.toolbar},!t&&o.createElement("div",{className:f(z.title,z.withoutIcon)},p.t(null,void 0,n(88616)),h),o.createElement("div",{className:z.buttons},o.createElement(R.ToolbarIconButton,{className:f(z.button,!d&&z.disabled),icon:F,onClick:function(){r.createGroupFromSelection()},isDisabled:!d,tooltip:p.t(null,void 0,n(83390)),"data-name":"group-button"}),o.createElement(k.ToolbarMenuButton,{className:f(z.button,u&&z.disabled),isDisabled:u,content:o.createElement(D.Icon,{icon:O}),tooltip:p.t(null,void 0,n(92389)),arrow:!1,isShowTooltip:!0,"data-name":"copy-clone-button"},a&&o.createElement(N.AccessibleMenuItem,{"data-name":"copy",label:p.t(null,void 0,n(49680)),onClick:function(){
r.copySelection()}}),c&&o.createElement(N.AccessibleMenuItem,{"data-name":"clone",label:p.t(null,void 0,n(12537)),onClick:function(){r.cloneSelection()}})),o.createElement(k.ToolbarMenuButton,{className:f(z.button,l&&z.disabled),isDisabled:l,content:o.createElement(D.Icon,{icon:P}),tooltip:p.t(null,void 0,n(35049)),arrow:!1,isShowTooltip:!0,"data-name":"move-to-button"},o.createElement(N.AccessibleMenuItem,{"aria-label":p.t(null,void 0,n(15512)),"data-name":"new-pane-above",label:p.t(null,void 0,n(15512)),onClick:function(){r.unmergeSelectionUp()}}),o.createElement(N.AccessibleMenuItem,{"aria-label":p.t(null,void 0,n(52160)),"data-name":"new-pane-below",label:p.t(null,void 0,n(52160)),onClick:function(){r.unmergeSelectionDown()}})),t&&o.createElement(o.Fragment,null,o.createElement("div",{className:z.spacing}),o.createElement(R.ToolbarIconButton,{className:z.button,icon:manageDrawingsIcon,tooltip:p.t(null,void 0,n(81031)),"data-name":"manage-drawings-button",onClick:m}))));function m(){h}}var j=n(46212),V=n(43766),G=n(10170),K=n(7809),U=n(94212);const Z=(Y="OBJECT_TREE",e=>Y+"__"+e);var Y;const Q=Z("SET_NODES"),$=Z("SYNC_NODES"),q=Z("UPDATE_NODE"),X=Z("UPDATE_NODES"),J=Z("RESET_TREE"),ee=Z("SET_SELECTED_IDS"),te=Z("DROP_SELECTION"),ne=Z("SELECT_PREVIOUS"),oe=Z("SELECT_NEXT"),re=Z("MULTI_SELECT_PREVIOUS"),ie=Z("MULTI_SELECT_NEXT"),se=Z("PROCESS_DROP_TARGET"),le=Z("UPDATE_DROP_TARGET"),ae=Z("HIDE_DROP_TARGET"),ce=Z("START_MULTI_SELECT"),ue=Z("STOP_MULTI_SELECT"),de=(Z("REMOVE_NODE"),Z("SET_FOCUSED_NODE")),he=Z("SCROLL_TO_ID"),pe=Z("SET_IS_SELECTED"),fe=Z("SET_IS_EXPANDED"),me=Z("SET_DISABLED_NODES"),ve=Z("MOVE_NODES"),ge=(Z("START_DRAG"),Z("END_DRAG")),be=()=>({type:ne}),Se=()=>({type:oe}),ye=()=>({type:re}),_e=()=>({type:ie}),Ce=(e,t,n,o,r)=>({type:se,dropTarget:e,dropType:t,isHoveredLeft:n,boundBox:o,isLastChild:r}),we=()=>({type:te}),Ee=e=>({type:ee,ids:e}),Te=(e,t,n)=>({type:ve,ids:e,targetId:t,dropType:n}),Me=()=>({type:ce}),Ie=()=>({type:ue}),De=e=>({type:de,nodeId:e}),Ne=e=>({type:he,nodeId:e}),ke=(e,t,n=0)=>({type:pe,nodeId:e,isSelected:t,mode:n}),xe=(e,t)=>({type:fe,nodeId:e,isExpanded:t}),Le=e=>({type:me,ids:e}),Re=()=>({type:ge});var Be=n(77145);const Ae=e=>e.nodes,Oe=e=>e.selection,Pe=e=>e.dropTarget,Fe=e=>e.expanded,ze=e=>e.scrollToId,We=(e,t)=>t,He=(0,Be.createSelector)([Ae,We],((e,t)=>e[t])),je=(0,Be.createSelector)([Oe,We],((e,t)=>e.ids.includes(t))),Ve=(0,Be.createSelector)([Fe,We],((e,t)=>e.includes(t))),Ge=(0,Be.createSelector)([e=>e.disabled,Oe,We],((e,t,n)=>!t.ids.includes(n)&&e.includes(n))),Ke=(0,Be.createSelector)(Ae,(e=>Object.keys(e))),Ue=(0,Be.createSelector)(Oe,(({ids:e})=>e)),Ze=(0,Be.createSelector)(Oe,(({lastFocusedNodeId:e})=>e)),Ye=(0,Be.createSelector)(Oe,(({isMultiSelecting:e})=>e)),Qe=(0,Be.createSelector)([Ae,Ue],((e,t)=>t.map((t=>e[t])))),$e=(0,Be.createSelector)(Ae,(e=>Object.values(e).filter((e=>0===e.level)))),qe=(0,Be.createSelector)([Ae,$e],((e,t)=>t.reduce(((t,n)=>[...t,...Xe(e,(0,I.ensureDefined)(n))]),[])));function Xe(e,t){const n=[];for(const o of t.children)n.push(e[o]),
n.push(...Xe(e,e[o]));return n}const Je=(0,Be.createSelector)([Ae,$e,Fe],((e,t,n)=>{const o=new Set(n);return t.reduce(((t,n)=>[...t,...tt(e,(0,I.ensureDefined)(n),o)]),[])})),et=(0,Be.createSelector)([Ae,Ue,Fe],((e,t,n)=>{const o=new Set(n);return[{id:"drag-list",level:-1,children:t}].reduce(((t,n)=>[...t,...tt(e,(0,I.ensureDefined)(n),o)]),[])}));function tt(e,t,n){const o=[];for(const r of t.children){const t=e[r];void 0!==t&&(o.push(t),n.has(r)&&o.push(...tt(e,t,n)))}return o}function*nt(e){const{selectedIds:t,nodes:n}=yield(0,E.call)(e),o={};for(let e=0;e<n.length;++e){const t=n[e];o[t.id]=t}yield(0,E.put)((e=>({type:Q,nodes:e}))(o)),yield(0,E.put)(Ee(t));!Ze(yield(0,E.select)())&&t.length>0&&(yield(0,E.put)(De(t[0])),yield(0,E.put)(Ne(t[0])))}function*ot(e){for(;;){if((yield(0,E.take)([ce,ue])).type===ce){const t=Ke(yield(0,E.select)()).filter((t=>!e(t)));yield(0,E.put)(Le(t))}else yield(0,E.put)(Le([]))}}function*rt(){for(;;){const{type:e}=yield(0,E.take)([ie,re]),t=yield(0,E.select)(),n=qe(t),o=n.length,r=Ze(t),i=[...Ue(t)],s=1===i.length&&i[0]!==r,l=n.findIndex((e=>e.id===(s?i[0]:r)));if(e===re&&0===l||e===ie&&l===o-1)continue;const a=dt(t,e===ie?"next":"previous",n,l),{id:c}=a;i.includes(c)&&r?(yield(0,E.put)(ke(r,!1,1)),yield(0,E.put)(De(c))):yield(0,E.put)(ke(c,!0,1)),yield(0,E.put)(Ne(c))}}function*it(e,t){for(;;){const{type:n}=yield(0,E.take)([oe,ne]),o=yield(0,E.select)(),r=qe(o),i=Qe(o),s=Ze(o);if(1===i.length&&i[0].id!==s&&!s){if(n===oe){yield(0,E.put)(De(i[0].id));continue}if(n===ne){const e=r.findIndex((e=>e.id===i[0].id)),t=dt(o,"previous",r,e);yield(0,E.put)(De(t.id));continue}}const l=r.findIndex((e=>e.id===s)),a=n===oe?"next":"previous",c=dt(o,a,r,l),{id:u}=c;e?e([u],a):yield(0,E.put)(Ee([u])),t&&t(u),yield(0,E.put)(De(u))}}function*st(e,t=(()=>!0)){for(;;){const{mode:n,nodeId:o,isSelected:r}=yield(0,E.take)(pe);let i=[...Ue(yield(0,E.select)())];const s=qe(yield(0,E.select)());if(1===n)r?i.push(o):i.splice(i.indexOf(o),1);else if(2===n&&i.length>0){const e=Ze(yield(0,E.select)());let n=s.findIndex((t=>t.id===e));-1===n&&(n=s.reduce(((e,t,n)=>i.includes(t.id)?n:e),-1));const r=s.findIndex((e=>e.id===o));if(n!==r)for(let e=Math.min(n,r);e<=Math.max(n,r);e++){const n=s[e].id;!i.includes(n)&&t(n)&&i.push(n)}}else i=o?[o]:[];const l=new Set(i);i=s.reduce(((e,t)=>(l.has(t.id)&&e.push(t.id),e)),[]),e?e(i):yield(0,E.put)(Ee(i)),yield(0,E.put)(De(o))}}function*lt(e=(()=>!0),t){const{dropTarget:n,dropType:o,isHoveredLeft:r,boundBox:i,isLastChild:s}=t,l=Pe(yield(0,E.select)()),a=He(yield(0,E.select)(),(0,I.ensureDefined)(n.parentId)),c=s&&"after"===o,u=Qe(yield(0,E.select)()),d=!c||!r&&e(u,n,o)?n:a,h=l.node&&l.node.id!==d.id||l.dropType!==o;u.map((e=>e.id)).includes(d.id)?yield(0,E.put)({type:ae}):h&&e(u,d,o)&&(yield(0,E.put)(((e,t,n)=>({type:le,node:e,dropType:t,boundBox:n}))(d,o,i)))}function*at(e){yield(0,E.throttle)(0,se,lt,e)}function*ct(e){for(;;){yield(0,E.take)(te);const t=Qe(yield(0,E.select)()),{node:n,dropType:o}=Pe(yield(0,E.select)());if(n&&o){const r=new CustomEvent("tree-node-drop",{
detail:{nodes:t,target:n.id,type:o}});if(e&&e(r),!r.defaultPrevented){const e=Ue(yield(0,E.select)());yield(0,E.put)(Te(e,n.id,o))}}}}function*ut(e){for(;;){yield(0,E.take)(ve);e(Ae(yield(0,E.select)()))}}function dt(e,t,n,o){const r=n.length;let i;-1===o&&"previous"===t&&(o=r);let s=0;for(;!i||Math.abs(s)<r&&((l=i).level>1&&!Ve(e,(0,I.ensureDefined)(l.parentId)));)s+="next"===t?1:-1,i=n[(o+s+r)%r];var l;return i}function*ht(e={}){const{saga:t,onDrop:n,canMove:o,onMove:r,onSelect:i,onKeyboardSelect:s,initState:l,canBeAddedToSelection:a}=e,c=[(0,E.fork)(at,o),(0,E.fork)(ct,n),(0,E.fork)(st,i,a),(0,E.fork)(it,i,s),(0,E.fork)(rt)];for(t&&c.push((0,E.fork)(t)),r&&c.push((0,E.fork)(ut,r)),a&&c.push((0,E.fork)(ot,a));;){l&&(yield(0,E.call)(nt,l));const e=yield(0,E.all)(c);yield(0,E.take)(J);for(const t of e)yield(0,E.cancel)(t)}}var pt=n(6047),ft=n(37265);const mt=(0,s.getLogger)("Platform.GUI.ObjectTree.CallApi");const vt={ids:[],lastFocusedNodeId:void 0,isMultiSelecting:!1};const gt={node:void 0,dropType:void 0,boundBox:void 0};const bt=(0,pt.combineReducers)({nodes:function(e={},t){switch(t.type){case Q:return t.nodes;case $:{const{nodes:n}=t,o=n.map((e=>e.id)),r={...e};for(const t of Object.keys(e))if(!o.includes(t)){const{parentId:e}=r[t];e&&(r[e]={...r[e],children:r[e].children.filter((e=>e!==t))}),delete r[t]}for(const e of n){const t=e.id;if(r.hasOwnProperty(t)){!(0,ft.deepEquals)(r[t].children,e.children)[0]&&(r[t]={...r[t],children:[...e.children]})}else{r[t]=e;const{parentId:n}=e;if(n&&!r[n].children.includes(t))throw new Error("Not implemented")}}return r}case q:{const{type:n,nodeId:o,...r}=t;return{...e,[o]:{...e[o],...r}}}case X:{const{nodes:n}=t,o={...e};return Object.keys(n).forEach((e=>{o[e]={...o[e],...n[e]}})),{...e,...o}}case ve:{const{ids:n,targetId:o,dropType:r}=t,i=(0,I.ensureDefined)(e[o].parentId),s=e[i],l={};for(const t of n){const n=e[t];if(n.parentId){const o=l[n.parentId]||e[n.parentId];l[n.parentId]={...o,children:o.children.filter((e=>e!==t))}}l[t]={...n,parentId:i,level:s.level+1}}const a=s.children.filter((e=>!n.includes(e)));return a.splice(((e,t,n)=>{switch(n){case"before":return e.indexOf((0,I.ensureDefined)(t));case"inside":return e.length;case"after":return e.indexOf((0,I.ensureDefined)(t))+1;default:return 0}})(a,o,r),0,...n),l[i]={...e[i],children:a,isExpanded:!0},{...e,...l}}default:return e}},selection:function(e=vt,t){switch(t.type){case ee:{const{ids:n}=t;return{...e,ids:n,lastFocusedNodeId:n.length>0?e.lastFocusedNodeId:void 0}}case ce:return{...e,isMultiSelecting:!0};case ue:return{...e,isMultiSelecting:!1};case de:return{...e,lastFocusedNodeId:t.nodeId};case $:{const n=new Set(t.nodes.map((e=>e.id)));return e.lastFocusedNodeId&&!n.has(e.lastFocusedNodeId)&&delete e.lastFocusedNodeId,{...e,ids:e.ids.filter((e=>n.has(e)))}}default:return e}},dropTarget:function(e=gt,t){switch(t.type){case le:{const{node:n,dropType:o,boundBox:r}=t;return{...e,node:n,dropType:o,boundBox:r}}case ae:case ge:case J:return{...gt};default:return e}},expanded:function(e=[],t){if(t.type===fe){
const{nodeId:n,isExpanded:o}=t;if(o)return[...e,n];const r=[...e];return r.splice(e.indexOf(n),1),r}return e},disabled:function(e=[],t){return t.type===me?[...t.ids]:e},scrollToId:function(e=null,t){return t.type===he?null===t.nodeId?null:{id:t.nodeId}:e}});var St=n(68788),yt=n(69690),_t=n(84952),Ct=n(49483);var wt,Et,Tt=n(16812),Mt=n(98314),It=n(47201),Dt=n(70412);!function(e){e[e.Normal=0]="Normal",e[e.Small=1]="Small"}(wt||(wt={})),function(e){e[e.Select=0]="Select",e[e.Click=1]="Click"}(Et||(Et={}));const Nt=o.createContext({size:0,smallSizeTreeNodeAction:1}),kt={[M.Modifiers.Mod]:1,[M.Modifiers.Shift]:2};var xt=n(69533),Lt=n(72045);const Rt=()=>{};class Bt extends o.PureComponent{constructor(){super(...arguments),this._ref=null,this._handleRef=e=>{this._ref=e;const{connectDragSource:t,connectDropTarget:n,connectDragPreview:o}=this.props;(0,I.ensureDefined)(n)(this._ref),(0,I.ensureDefined)(t)(this._ref),(0,I.ensureDefined)(o)((0,Mt.getEmptyImage)(),{captureDraggingState:!0})},this._handleTouchStart=e=>{const t=(e,t)=>{const n=function(e,t){try{const n=document.createEvent("TouchEvent");return n.initTouchEvent(e,!0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,t.touches,t.targetTouches,t.changedTouches),n}catch(e){return null}}(e,t);if(n)return n;const o=Array.from(t.changedTouches),r=Array.from(t.touches),i=Array.from(t.targetTouches);return new TouchEvent(e,{bubbles:!0,changedTouches:o,touches:r,targetTouches:i})},n=e.target;if(n instanceof Element){const e=e=>{const o=e;if(!n.isConnected){o.preventDefault();const e=t("touchmove",o);document.body.dispatchEvent(e)}},o=r=>{const i=r;if(!n.isConnected){i.preventDefault();const e=t("touchend",i);document.body.dispatchEvent(e)}n.removeEventListener("touchend",o),n.removeEventListener("touchmove",e)};n.addEventListener("touchend",o),n.addEventListener("touchmove",e)}}}componentDidMount(){var e;null===(e=this._ref)||void 0===e||e.addEventListener("touchstart",this._handleTouchStart)}componentWillUnmount(){var e;null===(e=this._ref)||void 0===e||e.removeEventListener("touchstart",this._handleTouchStart)}render(){return o.createElement(At,{...this.props,reference:this._handleRef})}getNode(){return(0,I.ensureNotNull)(this._ref)}}const At=e=>{const{id:t,isSelected:n,isOffset:r,isExpandable:i,setIsSelected:s,isDisabled:l,isExpanded:a,onClick:c,parentId:u,setIsExpanded:d,reference:h,isFirstListItem:p,isLastListItem:m,nodeRenderer:v,isChildOfSelected:g=!1,isStuckNode:b}=e,{size:S,smallSizeTreeNodeAction:y}=(0,o.useContext)(Nt),_=(0,o.useRef)(null),C=(0,It.createSafeMulticastEventHandler)((e=>_.current=e),h);let[w,E]=(0,Dt.useHover)();return(Ct.CheckMobile.any()||Ct.CheckMobile.isIPad())&&(w=n,E={onMouseOut:Rt,onMouseOver:Rt}),o.createElement("div",{className:f(Lt.wrap,n&&Lt.selected,g&&Lt.childOfSelected,l&&Lt.disabled,i&&Lt.expandable),onClick:1===S&&0===y?T:function(e){if(e.defaultPrevented)return;const o=kt[(0,M.modifiersFromEvent)(e)]||0;!l&&s&&s(t,!n,o);c&&0===o&&c(e,t)},onContextMenu:T,ref:C,...E},i&&o.createElement(D.Icon,{icon:xt,
className:f(Lt.expandHandle,a&&Lt.expanded),onClick:function(e){e.preventDefault(),i&&d(t,!a)},onMouseDown:function(e){e.preventDefault()}}),v({id:t,isOffset:r,parentId:u,isDisabled:l,isSelected:n,isChildOfSelected:g,isHovered:w,isExpanded:a,isFirstListItem:p,isLastListItem:m,isStuckNode:b}));function T(){l||n||!s||s(t,!0)}},Ot=o.createContext({});function Pt(e,t){const{id:n}=t,o=He(e,n),r=je(e,n);let i=!1,s=o.parentId;for(;s&&!i;)i=je(e,s),s=He(e,s).parentId;return{...o,isSelected:r,isChildOfSelected:i,isExpanded:o.children.length>0&&Ve(e,n),isExpandable:o.children.length>0,isDisabled:Ge(e,n)}}function Ft(e){return(0,pt.bindActionCreators)({setIsExpanded:xe,processDropTarget:Ce,dropSelection:we,selectNext:Se,selectPrevious:be,setIsSelected:ke,endDrag:Re},e)}const zt=(0,V.connect)(Pt,Ft,null,{context:Ot})((function(e){const t=(0,o.useRef)(null),[,n,r]=(0,Tt.useDrag)({type:"node",item:t=>{const{id:n,isDisabled:o,isSelected:r}=e;return o||r||e.setIsSelected(n,!0),e},end:e=>{e.endDrag()}}),[,i]=(0,yt.useDrop)({accept:"node",hover:(n,o)=>{const r=t.current;if(!r)return;if(e.isStuckNode)return;const i=r.getNode(),s=i.getBoundingClientRect(),l=s.bottom-s.top,a=o.getClientOffset();if(a){const t=a.y-s.top;let n,o;if(n=0===e.children.length?t<l/2?"before":"after":t<l/3?"before":e.isExpanded||t>=l/3&&t<2*l/3?"inside":"after",void 0!==e.getContainerElement){const t=e.getContainerElement().getBoundingClientRect();o={top:s.top-t.top,left:s.left-t.left,bottom:s.top-t.top+s.height,right:s.left-t.left+s.width,height:s.height,width:s.width}}else o={top:i.offsetTop,left:i.offsetLeft,bottom:i.offsetTop+i.offsetHeight,right:i.offsetLeft+i.offsetWidth,height:i.offsetHeight,width:i.offsetWidth};e.processDropTarget(e,n,a.x-s.left<48,o,e.isLastChild)}}});return o.createElement(Bt,{...e,connectDragSource:n,connectDropTarget:i,connectDragPreview:r,ref:t})})),Wt=(0,V.connect)(Pt,Ft,null,{context:Ot})(At);var Ht=n(35749),jt=n(65982),Vt=n(42842);function Gt(e){const t=e(),n=(0,o.useRef)(t);n.current=t;const[r,i]=(0,o.useState)(n.current),s=(0,o.useRef)(null);return(0,o.useEffect)((()=>{null===s.current&&(s.current=requestAnimationFrame((()=>{s.current=null,i(n.current)})))})),(0,o.useEffect)((()=>()=>{s.current&&cancelAnimationFrame(s.current)}),[]),r}function Kt(e){const{dropTargetOffset:t,mousePosition:n}=e;if(!t)return{display:"none"};const{x:o,y:r}=t,i=n&&t?n.y-t.y:0,s=`translate(${o+(n&&t?n.x-t.x:0)}px, ${r+i}px)`;return{transform:s,WebkitTransform:s}}const Ut={top:0,left:0,position:"fixed",pointerEvents:"none",zIndex:100,opacity:.5,width:300,backgroundColor:"red"};function Zt(e){return{isDragging:e.isDragging()&&"node"===e.getItemType(),mousePosition:e.getClientOffset(),dropTargetOffset:e.getSourceClientOffset()}}const Yt=(0,V.connect)((function(e){return{items:et(e)}}),null,null,{context:Ot})((function(e){const{items:t,isDragging:n,nodeRenderer:r,dragPreviewRenderer:i}=e;return Gt((function(){return n?o.createElement(Vt.Portal,null,o.createElement("div",{style:{...Ut,...Kt(e)}},t.map((e=>{if(i){const t=i;return o.createElement(t,{
key:e.id,...e})}return o.createElement(Wt,{id:e.id,key:e.id,nodeRenderer:r,isDragPreview:!0,isOffset:e.level>1})})))):null}))}));function Qt(e){return o.createElement(Yt,{...e,...(0,jt.useDragLayer)(Zt)})}var $t=n(3085),qt=n(33127);const Xt=o.forwardRef(((e,t)=>{const n=(0,o.useRef)(null);return e.connectDropTarget(n),(0,o.useImperativeHandle)(t,(()=>({getNode:()=>(0,I.ensureNotNull)(n.current)})),[]),o.createElement("div",{ref:n,style:{height:"100%",width:"100%"}})}));function Jt(e){const t=(0,o.useRef)(null),[,n]=(0,yt.useDrop)({accept:"node",hover:(n,o)=>{if(!t.current)return;const r=o.getClientOffset();if(null===r)return;const i=e.getOrderedNodes();if(0===i.length)return;const s=t.current.getNode().getBoundingClientRect(),l=e.getContainerElement().getBoundingClientRect();if("first"===e.type){const t={top:s.top-l.top+s.height,left:s.left-l.left,bottom:s.top-l.top+s.height,right:s.left-l.left+s.width,height:0,width:s.width};e.processDropTarget(i[0],"before",!1,t,!1)}if("last"===e.type){const t=r.x-s.left<48,n=i[i.length-1],o=t&&2===n.level?(0,I.ensureDefined)(i.find((e=>e.id===n.parentId))):n,a={top:s.top-l.top,left:s.left-l.left,bottom:s.top-l.top,right:s.left-l.left+s.width,height:s.height,width:s.width};e.processDropTarget(o,"after",t,a,!1)}}});return o.createElement(Xt,{...e,connectDropTarget:n,ref:t})}const en=o.createContext({isOver:!1,transform:void 0});var tn=n(59385);function nn(e){const{dropType:t,boundBox:n}=e,{top:o,bottom:r,left:i}=(0,I.ensureDefined)(n);return[i,"before"===t||"inside"===t?o:r]}function on(e){return{isDragging:e.isDragging()}}const rn=(0,V.connect)((function(e){const{boundBox:t,dropType:n,node:o}=Pe(e);return{boundBox:t,dropType:n,level:o?o.level:void 0}}),null,null,{context:Ot})((function(e){const{dropType:t,boundBox:n,isDragging:r,level:i,transform:s=nn}=e;return Gt((function(){if(!r||!t||!n)return null;const l={[tn.dropTarget]:"inside"!==t,[tn.dropTargetInside]:"inside"===t},{width:a,height:c}=n,[u,d]=s(e),h=`translate(${u}px, ${"inside"===t?d:d-1}px)`;return o.createElement("div",{className:f(l),style:{position:"absolute",transform:h,WebkitTransform:h,top:0,left:2===i?"46px":0,width:2===i?a-46+"px":a,height:"inside"===t?c:"2px"}})}))}));function sn(e){const{isDragging:t}=(0,jt.useDragLayer)(on);return o.createElement(rn,{...e,isDragging:t})}const ln=o.createContext(null);var an=n(12356),cn=n.n(an);const un=o.forwardRef(((e,t)=>{const n=(0,o.useContext)(en),r=(0,o.useContext)(ln),i=(0,o.useMemo)((()=>(null==r?void 0:r.readOnly)?Wt:zt),[null==r?void 0:r.readOnly]);return o.createElement("div",{...e,ref:t},(null==r?void 0:r.id)?o.createElement("div",{className:m()(cn().sticky,!r.isShowStuck&&cn().hideSticky)},o.createElement(i,{id:r.id,key:r.id,isStuckNode:!0,nodeRenderer:r.nodeRenderer,readOnly:r.readOnly,onClick:r.onClick,getContainerElement:r.getContainerElement})):null,e.children,n.isOver&&o.createElement(sn,{transform:n.transform}))}));var dn=n(63273),hn=n(89791);const pn=38+M.Modifiers.Shift,fn=40+M.Modifiers.Shift;const mn=o.forwardRef((function(e,t){
const{navigationKeys:n,renderList:r,stopMultiSelect:i,startMultiSelect:s,isMultiSelecting:l,nodeRenderer:a,dragPreviewRenderer:c,className:u,connectDropTarget:d,readOnly:h,onClick:p,dropLayerTransform:f,setFocusedNode:v,scrollToId:g,rowHeight:b,onMultiSelectPrevious:S,onMultiSelectNext:y,onMoveCursorToNext:_,onMoveCursorToPrevious:C,onKeyDown:w,outerRef:E,width:T,height:D,isOver:N,processDropTarget:k,autofocus:x}=e,[R,B]=(0,o.useState)(),[A,O]=(0,o.useState)(!0),P=(0,o.useContext)(L),F=(0,o.useRef)(null),z=(0,St.useDragDropManager)();(0,o.useEffect)((()=>{const e=z.getMonitor();e.subscribeToStateChange((()=>O(!e.isDragging())))}),[]),(0,o.useEffect)((()=>{var e;x&&(null===(e=F.current)||void 0===e||e.focus())}),[]),(0,o.useEffect)((()=>{const e=e=>{[M.Modifiers.Mod,M.Modifiers.Shift].includes((0,M.modifiersFromEvent)(e))&&s()},t=e=>{l&&![M.Modifiers.Mod,M.Modifiers.Shift].includes((0,M.modifiersFromEvent)(e))&&i()};return document.addEventListener("keydown",e),document.addEventListener("keyup",t),document.addEventListener("mousemove",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t),document.removeEventListener("mousemove",t)}}),[l]),function(e){(0,o.useEffect)((()=>{if(Ct.isEdge){let t=null;const n=(0,I.ensureNotNull)(e.current),o=e=>{if(e.target instanceof Element){const n=(0,I.ensureNotNull)(e.target.closest("[draggable]"));n instanceof HTMLElement&&(n.style.opacity="0",t=requestAnimationFrame((()=>n.style.opacity="1")))}};return n.addEventListener("dragstart",o),()=>{n.removeEventListener("dragstart",o),null!==t&&cancelAnimationFrame(t)}}return()=>{}}),[])}(F);const W=(0,o.useCallback)((()=>(0,I.ensureNotNull)(ee.current)),[]),H=(0,o.useCallback)((()=>r),[r]),j=(0,o.useMemo)((()=>{const e=h?Wt:zt,t=[];let n,i;t.push({type:"padding",node:o.createElement(Jt,{type:"first",key:"padding-top",getContainerElement:W,getOrderedNodes:H,processDropTarget:k})});for(let s=0;s<r.length;s++){const l=r[s];l.isSticky&&(i=l.id),1===l.level&&(void 0!==n&&n!==l.parentId&&t.push({type:"separator",node:o.createElement("div",{key:n+"_separator",className:hn.separator})}),n=l.parentId),t.push({type:"node",stickyNodeId:i,isSticky:l.isSticky,node:o.createElement(e,{id:l.id,key:l.id,isFirstListItem:0===s,isLastListItem:s===r.length-1,isExpandable:l.children.length>0,nodeRenderer:a,readOnly:h,onClick:p,isOffset:l.level>1,getContainerElement:W})})}return t.push({type:"padding",node:o.createElement(Jt,{type:"last",key:"padding-bottom",getContainerElement:W,getOrderedNodes:H,processDropTarget:k})}),t}),[r]),V=(0,o.useMemo)((()=>{if(R){const e=j[R];return e&&"stickyNodeId"in e?e.stickyNodeId:void 0}}),[R,j]),G=(0,o.useRef)([]);G.current=j;const K=(0,o.useCallback)((e=>{var t,n;let{style:r}=e;const{index:i}=e,s=G.current[i];return i===G.current.length-1&&(r={...r,bottom:0,minHeight:r.height},delete r.height),"isSticky"in s&&s.isSticky&&(r={...r,zIndex:(null===(t=q.current)||void 0===t?void 0:t.scrollTop)&&r.top&&+r.top<=(null===(n=q.current)||void 0===n?void 0:n.scrollTop)?2:5}),o.createElement("div",{
style:r},G.current[i].node)}),[]),U=(0,o.useCallback)((e=>{const t=G.current[e];return"padding"===t.type?6:"function"==typeof b?b(e,t):b}),[b]),Z=(0,o.useCallback)((e=>(0,I.ensure)(G.current[e].node.key)),[]),Y=(0,o.useMemo)((()=>null===g?{index:-1}:{index:G.current.findIndex((e=>e.node.key===g.id))}),[g]);d(F);const[Q,$,q,X]=(0,qt.useOverlayScroll)(),J=(0,o.useRef)(null);(0,o.useEffect)((()=>(0,I.ensureNotNull)(J.current).resetAfterIndex(0,!0)),[j]),(0,o.useEffect)((()=>{let e=Y.index;const t=G.current[Y.index];R&&e<=R&&t&&"stickyNodeId"in t&&t.stickyNodeId&&(e-=1),(0,I.ensureNotNull)(J.current).scrollToItem(e)}),[Y]);const ee=(0,o.useRef)(null),te=(0,o.useMemo)((()=>({isOver:N,transform:f})),[N,f]),ne=(0,o.useMemo)((()=>({id:V,nodeRenderer:a,getContainerElement:W,onClick:p,isShowStuck:A,readOnly:h})),[V,a,W,A,p,h]),oe=(0,o.useRef)(null),re=(0,o.useRef)({startScroll(e){const t=()=>{null!==q.current&&(oe.current=requestAnimationFrame(t),q.current.scrollBy({top:e}))};this.stopScroll(),t()},stopScroll(){null!==oe.current&&(cancelAnimationFrame(oe.current),oe.current=null)},getListElement:()=>q.current});return(0,o.useImperativeHandle)(t,(()=>re.current),[]),(0,o.useEffect)((()=>()=>re.current.stopScroll()),[N]),(0,o.useEffect)((()=>{if(!F.current)return;function e(e){if(!t.matches(":focus-visible"))return;if(!P)return;const{viewModel:n}=P,o=n.selection();e.defaultPrevented||e.currentTarget!==e.target||o.selected().length||_()}const t=F.current;return t.addEventListener("focus",e),()=>{t.removeEventListener("focus",e)}}),[F,_,P]),o.createElement(en.Provider,{value:te},o.createElement(ln.Provider,{value:ne},o.createElement("div",{...$,className:m()(hn.tree,u),ref:F,"data-name":"tree",tabIndex:0,onKeyDown:function(e){const t=(0,M.hashFromEvent)(e);if(e.defaultPrevented||(0,Ht.isNativeUIInteraction)(t,e.target))return;const o=(0,I.ensureDefined)(Q.scrollPosTop),r=(0,I.ensureDefined)(Q.contentHeight),i=(0,I.ensureDefined)(Q.containerHeight);if(i){const n=.875*i,s=o+i===r;switch(t){case 35:s||(e.preventDefault(),ie(r));break;case 36:0!==o&&(e.preventDefault(),ie(0));break;case 33:0!==o&&(e.preventDefault(),ie(Math.max(0,o-n)));break;case 34:s||(e.preventDefault(),ie(Math.min(o+n,r)))}}P||t!==pn||(e.preventDefault(),S());P||t!==fn||(e.preventDefault(),y());(38===t||void 0!==n&&"previous"===n[t])&&(e.preventDefault(),C());(40===t||void 0!==n&&"next"===n[t])&&(e.preventDefault(),_());if((8===t||46===t)&&P){const{viewModel:e}=P,t=e.selection(),n=t.selected();if(1!==n.length)return;const o=e.getNextNodeIdAfterRemove(n[0]);if(null===o)return;e.onChange().subscribe(null,(()=>{if(t.selected().length)return;const n=e.entity(o);n&&(t.set([n]),v(o))}),!0)}null==w||w(e)}},o.createElement($t.OverlayScrollContainer,{...Q,className:hn.overlayScrollWrap}),o.createElement(_t.VariableSizeList,{ref:function(e){J.current=e},className:hn.listContainer,width:T,height:D,itemCount:j.length,itemSize:U,children:K,itemKey:Z,outerRef:function(e){q.current=e,E&&E(e)},innerRef:function(e){ee.current=e},innerElementType:un,
onItemsRendered:function({visibleStartIndex:e}){B(e),X()},overscanCount:20,direction:(0,dn.isRtl)()?"rtl":"ltr"}),o.createElement(Qt,{dragPreviewRenderer:c,nodeRenderer:a}))));function ie(e){var t;null===(t=q.current)||void 0===t||t.scrollTo({left:0,top:e})}}));const vn=(0,V.connect)((function(e){return{renderList:Je(e),orderedNodes:qe(e),isMultiSelecting:Ye(e),selectedIds:Ue(e),scrollToId:ze(e)}}),(function(e){return(0,pt.bindActionCreators)({startMultiSelect:Me,stopMultiSelect:Ie,setFocusedNode:De,processDropTarget:Ce,onMoveCursorToNext:Se,onMoveCursorToPrevious:be,onMultiSelectPrevious:ye,onMultiSelectNext:_e},e)}),null,{context:Ot})((function(e){const t=(0,o.useRef)(null),[{isOver:n},r]=(0,yt.useDrop)({accept:"node",drop:(n,o)=>{var r;("touch"===e.drag||Ct.isFF)&&(null===(r=t.current)||void 0===r||r.stopScroll()),o.getItem().dropSelection()},hover:(n,o)=>{var r,i;if("touch"!==e.drag&&!Ct.isFF)return;const s=o.getClientOffset();if(null===s)return;const l=null!==(i=null===(r=t.current)||void 0===r?void 0:r.getListElement())&&void 0!==i?i:null;if(null===l)return;const a=l.getBoundingClientRect();((n,o,r)=>{var i;const s=Math.abs(n-r),l=Math.abs(n-o);if(l>40&&s>40||s<=40&&l<=40)return void(null===(i=t.current)||void 0===i||i.stopScroll());((n,o,r,i)=>{var s,l,a,c;o||n?"touch"===e.drag?null===(s=t.current)||void 0===s||s.startScroll(o?-5:5):null===(l=t.current)||void 0===l||l.startScroll(o?-2:2):(r||i)&&("touch"===e.drag?null===(a=t.current)||void 0===a||a.startScroll(r?-10:10):null===(c=t.current)||void 0===c||c.startScroll(r?-5:5))})(l>20&&l<=40,s>20&&s<=40,s<=20,l<=20)})(s.y,a.bottom,a.top)},collect:e=>({isOver:e.isOver()})});return o.createElement(mn,{...e,isOver:n,connectDropTarget:r,ref:t})})),gn={delayTouchStart:100};function bn(e){const{canBeAddedToSelection:t,initState:n,onSelect:r,canMove:i,onDrop:s,onMove:l,nodes:a,selectedIds:c,onKeyboardSelect:u,saga:d,lastFocusedNodeObject:h,lastSyncTimestampRef:p,scrollToId:f,...m}=e,[v,g]=(0,o.useState)(null);return(0,o.useEffect)((()=>{const e=(0,j.default)();g(function(e){const t=(0,pt.applyMiddleware)(e);return(0,pt.createStore)(bt,t)}(e));const o=e.run(ht,{initState:n,onKeyboardSelect:u,saga:d,canMove:i,onMove:l,onDrop:s,onSelect:r,canBeAddedToSelection:t});return()=>o.cancel()}),[]),(0,o.useEffect)((()=>(null!==v&&a&&(p&&(p.current=performance.now()),v.dispatch((e=>({type:$,nodes:e}))(a))),()=>{})),[v,a]),(0,o.useEffect)((()=>{null!==v&&c&&v.dispatch(Ee(c))}),[v,c]),(0,o.useEffect)((()=>{null!==v&&(null==h?void 0:h.id)&&v.dispatch(De(h.id))}),[v,h]),null===v?null:o.createElement(Sn,{store:v,scrollToId:f,...m})}const Sn=o.memo((function(e){const{store:t,scrollToId:n,...r}=e,i="touch"===e.drag?K.TouchBackend:G.HTML5Backend;return(0,o.useEffect)((()=>{var e;t.dispatch(Ne(null!==(e=null==n?void 0:n.id)&&void 0!==e?e:null))}),[n]),o.createElement(U.DndProvider,{backend:i,options:gn},o.createElement(V.Provider,{store:t,context:Ot},o.createElement(vn,{...r})))}));function yn(e,t){(0,W.trackEvent)("Object Tree",e,t)}function _n(e){
return e.length>1?"Multi select":e[0].gaLabel()}function Cn(e){return(0,j.eventChannel)((t=>{const n={};return e.onChange().subscribe(n,(()=>t({type:J}))),e.onGroupCreated().subscribe(n,(e=>t(xe(e,!0)))),e.selection().onChange().subscribe(n,(e=>t(Ee(e)))),()=>{e.onChange().unsubscribeAll(n),e.selection().onChange().unsubscribeAll(n),e.onGroupCreated().unsubscribeAll(n)}}),j.buffers.expanding())}function*wn(){for(;;)yield(0,E.take)([oe,ne]),yn("Select","Arrow")}function*En(){for(;;){const{mode:e}=yield(0,E.take)(pe);1===e&&yn("Multi select","Ctrl"),2===e&&yn("Multi select","Shift")}}function*Tn(e){for(;;){yield(0,E.take)(te);const{node:t,dropType:n}=Pe(yield(0,E.select)());if(t){const o=Qe(yield(0,E.select)()),r=o.map((t=>(0,I.ensureNotNull)(e.entity(t.id))));let i="Drag";1===t.level&&"inside"!==n&&o.some((e=>2===e.level))?i="From the group":2!==t.level&&"inside"!==n||!o.some((e=>1===e.level))?1===o.length&&o[0].parentId!==t.parentId&&(i="Existing pane"):i="To the group",yn(i,_n(r))}}}function*Mn(e){yield(0,E.fork)(wn),yield(0,E.fork)(En),yield(0,E.fork)(Tn,e)}function*In(e){yield(0,E.fork)(Mn,e);const t=yield(0,E.call)(Cn,e);mt.logNormal("Opened object tree data source channel");try{for(;;){const e=yield(0,E.take)(t);yield(0,E.put)(e)}}finally{mt.logNormal("Closed object tree data source channel"),t.close()}}const Dn=o.createContext(null);var Nn=n(14729),kn=n(898),xn=n(64083);const Ln=T.mobiletouch?"touch":"native";function Rn(e){const{viewModel:t,showHeader:n=!0,nodeRenderer:r,isDialog:i=!1,hideHeaderTitle:s=!1}=e,l=(0,o.useRef)(null),a=function(e){const[t,n]=(0,o.useState)(e.getChartId()),r=(0,o.useRef)(t);return r.current=t,(0,o.useEffect)((()=>{return e.onChange().subscribe(null,t),()=>{e.onChange().unsubscribe(null,t)};function t(){const t=e.getChartId();r.current!==t&&n(t)}}),[]),t}(t),[c,u]=(0,kn.useDimensions)(),[d,h]=(0,o.useState)(null),p=(0,o.useMemo)((()=>({isTouch:T.touch,isDialog:i})),[i]);return o.createElement(Dn.Provider,{value:p},o.createElement(L.Provider,{value:{viewModel:t}},o.createElement("div",{className:xn.wrap,onContextMenu:Nn.preventDefaultForContextMenu},n&&o.createElement(H,{hideTitle:s}),o.createElement("div",{className:xn.space,onClick:function(e){if(e.defaultPrevented)return;if(!(e.target instanceof Element)||null===l.current)return;e.target===l.current&&t.selection().set([])},ref:c},null!==u&&o.createElement(bn,{key:a,height:u.height,width:u.width,canBeAddedToSelection:function(e){const n=t.entity(e);return t.selection().canBeAddedToSelection(n)},nodeRenderer:r,initState:function(){const{nodes:e,selection:n}=t.getState();return{selectedIds:n,nodes:e}},canMove:function(e,n,o){return t.isSelectionDropable(n.id,o)},drag:Ln,rowHeight:Bn,onSelect:function(e){const n=e.map((e=>t.entity(e))).filter((e=>null!==e));t.selection().set(n)},onDrop:function(e){e.preventDefault();const{detail:{target:n,type:o}}=e;t.insertSelection(n,o)},scrollToId:d,saga:function*(){yield(0,E.fork)(In,t)},onKeyboardSelect:function(e){h({id:e})},outerRef:function(e){l.current=e},onKeyDown:function(e){if(13===(0,
M.hashFromEvent)(e)){e.preventDefault();const n=t.selection().selected(),o=n.length>0?t.entity(n[0]):void 0;o&&t.openProperties(o)}},autofocus:i})))))}function Bn(e,t){switch(t.type){case"node":return 38;case"separator":return 13}}var An,On,Pn=n(56570);!function(e){e.Svg="svg"}(An||(An={})),function(e){e[e.NoSync=0]="NoSync",e[e.SyncInLayout=1]="SyncInLayout",e[e.GlobalSync=2]="GlobalSync"}(On||(On={}));var Fn=n(31261),zn=n(20723);function Wn(e){const{className:t,disabled:n,...r}=e;return o.createElement(D.Icon,{className:m()(zn.button,n&&zn.disabled,t),...r})}var Hn=n(77975);const jn=p.t(null,void 0,n(83390)),Vn=p.t(null,void 0,n(6321)),Gn=p.t(null,void 0,n(99894)),Kn=p.t(null,void 0,n(5837)),Un=p.t(null,void 0,n(27298)),Zn=p.t(null,void 0,n(98334)),Yn=p.t(null,void 0,n(67410));var Qn=n(26023),$n=n(84015),qn=n(60925),Xn=n(52870),Jn=n(49756),eo=n(94007),to=n(62766),no=n(63770);Pn.enabled("saveload_separate_drawings_storage");function oo(e){const{id:t}=e,n=(0,o.useContext)(L),{viewModel:r}=(0,I.ensureNotNull)(n),i=r.entity(t);return null===i?null:o.createElement(ro,{...e,entity:i})}function ro(e){const{id:t,isOffset:r,isDisabled:i,isSelected:s,isChildOfSelected:l,isHovered:a,parentId:c,entity:u,isExpanded:d}=e,h=(0,o.useContext)(L),{viewModel:m}=(0,I.ensureNotNull)(h),v=(0,o.useContext)(Dn),{size:g}=(0,o.useContext)(Nt),[b,S]=(0,o.useState)(!1),y=(0,o.useRef)(null),[_,C]=(0,o.useState)(u.title().value()),[w,E]=(0,o.useState)(u.getIcon()),[T,N]=(0,o.useState)(u.isLocked()),[k,x]=(0,o.useState)(u.isVisible()),[R,B]=(0,o.useState)(u.isActualInterval()),[A,O]=(0,o.useState)(u.getDrawingSyncState()),[P,F]=(0,o.useState)(!1),[z,W]=((0,Hn.useWatchedValueReadonly)({watchedValue:m.getChartLayout()}),(0,o.useState)(!1)),H=(0,o.useRef)(null);(0,o.useEffect)((()=>{const e={};u.onLockChanged().subscribe(e,(()=>N(u.isLocked()))),u.onVisibilityChanged().subscribe(e,(()=>x(u.isVisible())));const t=u.title().spawn();t.subscribe((e=>C(e))),u.onIsActualIntervalChange().subscribe(e,(()=>B(u.isActualInterval()))),u.onSyncStateChanged().subscribe(e,(()=>O(u.getDrawingSyncState())));const n=u.onIconChanged?u.onIconChanged():void 0;return n&&n.subscribe(e,(()=>E(u.getIcon()))),()=>{u.onIsActualIntervalChange().unsubscribeAll(e),u.onLockChanged().unsubscribeAll(e),u.onVisibilityChanged().unsubscribeAll(e),u.onSyncStateChanged().unsubscribeAll(e),t.destroy(),H.current&&clearTimeout(H.current),n&&n.unsubscribeAll(e)}}),[u]),(0,o.useEffect)((()=>{b&&y.current&&(y.current.focus(),y.current.setSelectionRange(0,_.length))}),[b]),(0,o.useEffect)((()=>{const e={};return m.hoveredObjectChanged().subscribe(e,J),()=>{m.hoveredObjectChanged().unsubscribeAll(e)}}),[d]),(0,o.useEffect)((()=>{m.setHoveredObject(a?t:null)}),[a]),(0,o.useEffect)((()=>{!s&&H.current&&(clearTimeout(H.current),H.current=null),S(!1)}),[s]);const j={};if(c){const e=m.entity(c);e&&(j["data-parent-name"]=e.title().value()),j["data-type"]=u.hasChildren()?"group":"data-source"}
const V=Pn.enabled("test_show_object_tree_debug")?`<${u.id()}> (${u.zOrder()}) ${u.title()}`:u.title().value(),G=a||P,K=b&&s,U=!!v&&v.isTouch,Z=!!v&&v.isDialog,Y=R&&k?eo:to,Q=u.hasChildren()?p.t(null,void 0,n(40983)):p.t(null,void 0,n(80014));let $=null;return w&&w.type===An.Svg&&($=o.createElement(D.Icon,{icon:w.content||"",className:no.icon})),o.createElement("span",{className:f(no.wrap,i&&no.disabled,s&&no.selected,r&&no.offset,l&&no.childOfSelected,P&&!i&&!s&&!l&&no.hover,Z&&!i&&!s&&!l&&no.dialog,Z&&!i&&!s&&!l&&(0,$n.isOnMobileAppPage)("any")&&no.mobile),onMouseDown:function(e){b&&!(0,I.ensureNotNull)(y.current).contains(e.target)&&W(!0)},onClick:1===g?q:function(e){if(e.defaultPrevented)return;if(0!==(0,M.modifiersFromEvent)(e))return;if(H.current)e.preventDefault(),clearTimeout(H.current),H.current=null,m.openProperties(u),W(!1);else{const e=m.selection().selected();H.current=setTimeout((()=>{H.current=null,s&&!z&&1===e.length&&m.rename(u,(()=>S(!0))),W(!1)}),500)}},onContextMenu:0===g?q:void 0},!K&&o.createElement(o.Fragment,null,$,!1,o.createElement("span",{className:f(no.title,m.isMain(u)&&no.main,(!u.isVisible()||!R)&&no.disabled),...j},V),o.createElement("span",{className:no.rightButtons},u.canBeLocked()&&o.createElement(Wn,{title:T?Gn:Kn,icon:T?Xn:Jn,className:f(no.button,(G||T)&&no.visible,"apply-common-tooltip"),onClick:function(e){if(e.defaultPrevented)return;e.preventDefault(),m.setIsLocked(t,!u.isLocked())},"data-role":"button","data-name":"lock","data-active":T}),o.createElement(Wn,{icon:Y,className:f(no.button,!R&&no.warn,(G||!k||!R)&&no.visible,"apply-common-tooltip"),onClick:R?function(e){if(e.defaultPrevented)return;e.preventDefault(),m.setIsVisible(t,!u.isVisible())}:function(e){if(e.defaultPrevented)return;e.preventDefault(),m.openProperties(u,Qn.TabNames.visibility)},title:function(){if(!R)return Q;return k?Un:Zn}(),"data-role":"button","data-name":"hide","data-active":!k}),u.canBeRemoved()&&o.createElement(Wn,{title:Yn,icon:qn,className:f(no.button,(U||G)&&no.visible,"apply-common-tooltip"),onClick:function(e){if(e.defaultPrevented)return;e.preventDefault(),e.stopPropagation(),m.remove(t)},"data-role":"button","data-name":"remove"}))),K&&o.createElement(Fn.InputControl,{value:_,onChange:function(e){C(e.currentTarget.value)},onClick:Nn.preventDefault,className:no.renameInput,onKeyDown:function(e){27===(0,M.hashFromEvent)(e)?(e.preventDefault(),C(u.title().value()),S(!1)):13===(0,M.hashFromEvent)(e)&&(e.preventDefault(),X())},reference:function(e){y.current=e},onBlur:X,onDragStart:function(e){e.preventDefault(),e.stopPropagation()},draggable:!0,stretch:!0}));function q(e){e.defaultPrevented||b||!u.fullyConstructed()||(e.preventDefault(),e.persist(),m.openContextMenu(u,(()=>S(!0)),e))}function X(){""!==_&&u.setName(_),C(u.title().value()),S(!1)}function J(e){if(u.hasChildren()&&!d){const t=null!==e&&u.childrenIds().has(e);F(t)}else F(t===e)}}var io=n(56388),so=n(90684);function lo(e){const{viewModel:t,onClose:r,activeChartWidget:i}=e,[s,l]=(0,o.useState)(!1),[a,c]=(0,o.useState)(!1),[u,d]=(0,
o.useState)(!1),h=(0,o.useRef)(null),m=(0,v.useForceUpdate)(),E=((0,g.useIsMounted)(),t.selection()),T=t.canSelectionBeGrouped();return(0,o.useEffect)((()=>{const e=window.matchMedia(C.DialogBreakpoints.TabletSmall);return e.addEventListener("change",M),()=>e.removeEventListener("change",M)}),[]),(0,o.useEffect)((()=>(E.onChange().subscribe(null,(()=>m())),()=>{E.onChange().unsubscribeAll(null)})),[E]),(0,o.useEffect)((()=>{const e=e=>d(e);return t.isContextMenuOpened().subscribe(e),()=>{t.isContextMenuOpened().unsubscribe(e)}}),[t]),o.createElement(o.Fragment,null,o.createElement(y.MatchMedia,{rule:C.DialogBreakpoints.TabletSmall},(e=>o.createElement(_.AdaptivePopupDialog,{additionalElementPos:"after",additionalHeaderElement:o.createElement("div",{className:so.buttons},o.createElement(io.ToolWidgetIconButton,{className:f(so.button,!T&&so.disabled),icon:F,onClick:D,isDisabled:!T,title:p.t(null,void 0,n(83390)),"data-name":"group-button"}),!1),className:so.dialog,dataName:"object-tree-dialog",isOpened:!0,onClickOutside:a||e||u?()=>{}:r,onClose:r,ref:h,render:()=>o.createElement(ao,{isSmallTablet:e,viewModel:t}),title:p.t(null,void 0,n(88616)),showSeparator:!0}))),o.createElement(S.DrawerManager,null,s&&o.createElement(b.Drawer,{onClose:I,position:"Bottom"},o.createElement(w.ManageDrawings,{onClose:I,chartWidget:i,isMobile:!0}))));function M(){s&&!window.matchMedia(C.DialogBreakpoints.TabletSmall).matches&&l(!1)}function I(){l(!1)}function D(){t.createGroupFromSelection()}}function ao(e){const{isSmallTablet:t,viewModel:n}=e,r=(0,o.useMemo)((()=>({size:t?1:0,smallSizeTreeNodeAction:0})),[t]);return o.createElement(Nt.Provider,{value:r},o.createElement(Rn,{nodeRenderer:oo,showHeader:!1,viewModel:n,isDialog:!0}))}var co=n(16216),uo=n(77788),ho=n(52033);var po=n(32112);function fo(e,t){return`${e}:${t}`}function mo(e){const t=e.split(":");return{persistentId:t[0],instanceId:t[1]}}class vo{constructor(e){this._onChange=new ho.Delegate,this._recalculate=()=>{const e=this._groupModel.groups().map((e=>fo(e.id,e.instanceId()))),t=this._selectionApi.allSources();this._selected=this._selected.filter((n=>e.includes(n)||t.includes(n))),this._onChange.fire(this._selected)},this._model=e,this._selectionApi=new po.SelectionApi(this._model),this._groupModel=this._model.lineToolsGroupModel(),this._selected=this._getSelectedIds(),this._selectionApi.onChanged().subscribe(this,(()=>{this._selected=this._getSelectedIds(),this._onChange.fire(this._selected)})),this._groupModel.onChanged().subscribe(this,this._recalculate)}destroy(){this._selectionApi.onChanged().unsubscribeAll(this),this._groupModel.onChanged().unsubscribeAll(this)}set(e){const t=[];let n=e.map((e=>e.id()));for(const o of e)if(o.hasChildren()){const e=o.childrenIds();t.push(...Array.from(e.values())),n=n.filter((t=>!e.has(t)))}else t.push(o.id());this._selectionApi.set(t.map((e=>mo(e).persistentId))),this._selected=n,this._onChange.fire(this._selected)}canBeAddedToSelection(e){return null!==e&&e.canBeAddedToSelection()}onChange(){return this._onChange}selected(){
return this._selected}_getSelectedIds(){return this._selectionApi.allSources().map((e=>this._model.dataSourceForId(e))).filter(ft.notNull).filter((e=>e.showInObjectTree())).map((e=>fo(e.id(),e.instanceId())))}}class go{constructor(e,t){this._controller=e,this._facade=t,this._groupModel=e.model().lineToolsGroupModel()}buildTree(){const e={};for(const t of this._controller.model().panes()){const n=t.sourcesByGroup().allWithoutMultipane().filter((e=>e.showInObjectTree()));e[t.id()]=bo(t.id(),0);for(const n of this._groupModel.groups()){const o=fo(n.id,n.instanceId()),r=(0,I.ensureNotNull)(this._facade.getObjectById(o));if(r.pane()===t){const o=[...n.lineTools()].sort(((e,t)=>e.zorder()>t.zorder()?-1:1)).map((e=>fo(e.id(),e.instanceId())));e[r.id()]=bo(r.id(),1,t.id(),o),e[t.id()].children.push(r.id());for(const t of o)e[t]=bo(t,2,r.id())}}for(const o of n){const n=fo(o.id(),o.instanceId());e[n]||(e[n]=bo(n,1,t.id()),e[t.id()].children.push(n))}e[t.id()].children.sort(((e,t)=>{const n=(0,I.ensureNotNull)(this._facade.getObjectById(e)),o=(0,I.ensureNotNull)(this._facade.getObjectById(t));return(0,I.ensureNotNull)(o.zOrder())-(0,I.ensureNotNull)(n.zOrder())}))}return this._facade.invalidateCache(new Set(Object.keys(e))),e}}function bo(e,t,n,o=[]){return{id:e,level:t,parentId:n,children:o}}var So=n(45126),yo=n(64147),_o=n(630),Co=n(72708),wo=n(29137),Eo=n(85719),To=n(99531),Mo=n(60074),Io=n(51330),Do=n(928),No=n(19466),ko=n(7295),xo=n(28824),Lo=n(57674);const Ro=new So.TranslatedString("show {title}",p.t(null,void 0,n(51382))),Bo=new So.TranslatedString("hide {title}",p.t(null,void 0,n(13017))),Ao=new So.TranslatedString("lock {title}",p.t(null,void 0,n(76104))),Oo=new So.TranslatedString("unlock {title}",p.t(null,void 0,n(12525))),Po=new So.TranslatedString("change {sourceTitle} title to {newSourceTitle}",p.t(null,void 0,n(23687))),Fo=new So.TranslatedString("insert source(s) after",p.t(null,void 0,n(10370)));function zo(e,t){return t.every((t=>!(t.pane()!==e&&!t.allowsMovingbetweenPanes())))}function Wo(e){return e instanceof wo.DataSource&&e.showInObjectTree()?fo(e.id(),e.instanceId()):null}function Ho(e){return new So.TranslatedString(e.name(),e.title(No.TitleDisplayTarget.DataWindow))}const jo=new ho.Delegate,Vo=Pn.enabled("saveload_separate_drawings_storage");function Go(e){return 0===e?0:1===e?1:2}class Ko{constructor(e,t){this._syncStateChanged=new ho.Delegate,this._updateSyncState=()=>{this._syncStateChanged.fire((0,I.ensureNotNull)(this.getDrawingSyncState()))},this._undoModel=e,this._dataSource=t,(0,_o.isLineTool)(t)?(t.linkKey().subscribe(this._updateSyncState),t.sharingMode().subscribe(this._updateSyncState),this._title=(0,To.createWVFromGetterAndSubscription)((()=>t.properties().title.value()||t.translatedType()),t.properties().title)):(0,Mo.isSymbolSource)(t)?this._title=(0,To.createWVFromGetterAndSubscriptions)((()=>t.symbolTitle(No.TitleDisplayTarget.DataWindow,void 0,void 0,(0,Ct.onWidget)()?"exchange":"listed_exchange")),[t.symbolChanged(),t.symbolResolved()]):(0,Co.isStudy)(t)?this._title=(0,
To.createWVFromGetterAndSubscriptions)((()=>t.title(No.TitleDisplayTarget.DataWindow)),[t.properties().childs().inputs,this._undoModel.model().properties().childs().paneProperties.childs().legendProperties.childs().showStudyArguments,t.onParentSourcesChanges(),t.series().symbolResolved()]):this._title=new yo.WatchedValue(t.title(No.TitleDisplayTarget.DataWindow)).spawn();const n=this._undoModel.lineBeingCreated();null!==n&&n===t&&n.isSynchronizable()&&Do.isToolCreatingNow.subscribe(this._updateSyncState)}destroy(){(0,_o.isLineTool)(this._dataSource)&&(this._dataSource.linkKey().unsubscribe(this._updateSyncState),this._dataSource.sharingMode().unsubscribe(this._updateSyncState)),this._title.destroy(),Do.isToolCreatingNow.unsubscribe(this._updateSyncState)}id(){return fo(this._dataSource.id(),this._dataSource.instanceId())}title(){return this._title}gaLabel(){return(0,Co.isStudy)(this._dataSource)?"Study":(0,_o.isLineTool)(this._dataSource)?"Drawing":"Symbol"}canBeLocked(){return(0,_o.isLineTool)(this._dataSource)&&this._dataSource.userEditEnabled()}canBeRemoved(){return this._undoModel.mainSeries()!==this._dataSource&&this._dataSource.isUserDeletable()}canBeHidden(){return this._dataSource.canBeHidden()}canBeRenamed(){return(0,_o.isLineTool)(this._dataSource)}fullyConstructed(){return this._undoModel.lineBeingCreated()!==this._dataSource}isVisible(){return this._dataSource.properties().visible.value()}isActualInterval(){return!(0,_o.isLineTool)(this._dataSource)&&!(0,Co.isStudy)(this._dataSource)||this._dataSource.isActualInterval()}onIsActualIntervalChange(){return(0,_o.isLineTool)(this._dataSource)||(0,Co.isStudy)(this._dataSource)?this._dataSource.onIsActualIntervalChange():jo}isLocked(){return!!(0,_o.isLineTool)(this._dataSource)&&this._dataSource.properties().frozen.value()}onVisibilityChanged(){return this._dataSource.properties().visible.listeners()}onLockChanged(){return(0,_o.isLineTool)(this._dataSource)?this._dataSource.properties().frozen.listeners():jo}getIcon(){const e=a,t=this._dataSource.getSourceIcon(),n=(0,Co.isStudyStrategy)(this._dataSource);let o={type:An.Svg,content:n?ko:xo};if(e&&t)if("loadSvg"===t.type){const[n,r]=t.svgId.split("."),i="linetool"===n?e.linetool[r]:e.series[Number(r)];o={type:An.Svg,content:i||xo}}else"svgContent"===t.type&&(o={type:An.Svg,content:t.content});return o}onIconChanged(){if(this._dataSource.onSourceIconChanged)return this._dataSource.onSourceIconChanged()}setVisible(e){const t=(e?Ro:Bo).format({title:Ho(this._dataSource)});this._undoModel.setProperty(this._dataSource.properties().visible,e,t,Eo.lineToolsDoNotAffectChartInvalidation&&(0,_o.isLineTool)(this._dataSource))}setLocked(e){if((0,_o.isLineTool)(this._dataSource)){const t=(e?Ao:Oo).format({title:Ho(this._dataSource)});this._undoModel.setProperty(this._dataSource.properties().frozen,e,t,Eo.lineToolsDoNotAffectChartInvalidation)}}setName(e){if((0,_o.isLineTool)(this._dataSource)){const t=Po.format({sourceTitle:this._dataSource.properties().title.value()||Ho(this._dataSource),newSourceTitle:e})
;this._undoModel.setProperty(this._dataSource.properties().title,e,t,Eo.lineToolsDoNotAffectChartInvalidation)}}isCopiable(){return this._dataSource.copiable()}isClonable(){return this._dataSource.cloneable()}zOrder(){return this._dataSource.zorder()}remove(){this._undoModel.removeSource(this._dataSource,!1)}canBeAddedToSelection(){return this._undoModel.selection().canBeAddedToSelection(this._dataSource)}setAsSelection(){this._undoModel.model().selectionMacro((e=>{e.clearSelection(),e.addSourceToSelection(this._dataSource)}))}addToSelection(){this._undoModel.model().selectionMacro((e=>{e.addSourceToSelection(this._dataSource)}))}addSourcesToArray(e){return e.push(this._dataSource),e}insertSourcesBeforeThis(e){this._insertSources(e,(e=>this._undoModel.insertBefore(e,this._dataSource)))}insertSourcesAfterThis(e){this._insertSources(e,(e=>this._undoModel.insertAfter(e,this._dataSource)))}childrenIds(){return new Set}hasChildren(){return!1}pane(){return(0,I.ensureNotNull)(this._undoModel.model().paneForSource(this._dataSource))}allowsMovingbetweenPanes(){return!(0,_o.isLineTool)(this._dataSource)}canBeAddedToGroup(){return(0,_o.isLineTool)(this._dataSource)&&this._dataSource.boundToSymbol()}canInsertBeforeThis(e){return this._canInsertBeforeOrAfter(e)}canInsertAfterThis(e){return this._canInsertBeforeOrAfter(e)}detachFromParent(){if((0,_o.isLineTool)(this._dataSource)){const e=this._undoModel.model(),t=this._undoModel.lineToolsGroupController(),n=e.lineToolsGroupModel().groupForLineTool(this._dataSource);null!==n&&t.excludeLineToolFromGroup(n,this._dataSource)}}canBeSyncedInLayout(){return(0,_o.isLineTool)(this._dataSource)&&this._dataSource.isSynchronizable()}onSyncStateChanged(){return this._syncStateChanged}setDrawingSyncState(e){if(!this.canBeSyncedInLayout()||!this.fullyConstructed())return;const t=this._dataSource;switch(e){case 0:if(null===t.linkKey().value())return;this._undoModel.unlinkLines([t]);break;case 1:if(null!==t.linkKey().value())return;this._undoModel.shareLineTools([t],1)}}getDrawingSyncState(){return this.canBeSyncedInLayout()?Vo?this.fullyConstructed()?Go(this._dataSource.sharingMode().value()):0:this.fullyConstructed()&&null!==this._dataSource.linkKey().value()?1:0:null}_canInsertBeforeOrAfter(e){const t=this._undoModel.model();if(!zo(this.pane(),e))return!1;if((0,_o.isLineTool)(this._dataSource)){if(null!==t.lineToolsGroupModel().groupForLineTool(this._dataSource)&&e.some((e=>!e.canBeAddedToGroup())))return!1}return!0}_insertSources(e,t){const n=this._undoModel.model(),o=this._undoModel.lineToolsGroupController();this._undoModel.beginUndoMacro(Fo);const r=()=>{e.forEach((e=>e.detachFromParent()))},i=e.reduce(((e,t)=>t.addSourcesToArray(e)),[]);if((0,_o.isLineTool)(this._dataSource)){const t=n.lineToolsGroupModel().groupForLineTool(this._dataSource);null!==t?((0,I.assert)(!e.some((e=>e.hasChildren()))),i.forEach((e=>{(0,_o.isLineTool)(e)&&(t.containsLineTool(e)||o.addLineToolToGroup(t,e))}))):r()}else r();t(i),this._undoModel.endUndoMacro()}}class Uo{constructor(e,t){
this._onVisibilityChanged=new ho.Delegate,this._onLockChanged=new ho.Delegate,this._onIsActualIntervalChanged=new ho.Delegate,this._syncStateChanged=new ho.Delegate,this._linkKeyChangedBound=this._linkKeyChanged.bind(this),this._undoModel=e,this._group=t,this._lineTools=t.lineTools(),this._paneId=(0,I.ensureNotNull)(e.model().paneForSource(this._lineTools[0])).id();const n=()=>{this._lineTools.forEach((e=>{e.properties().visible.listeners().subscribe(this,(()=>this._onVisibilityChanged.fire())),e.properties().frozen.listeners().subscribe(this,(()=>this._onLockChanged.fire())),e.onIsActualIntervalChange().subscribe(this,(()=>this._onIsActualIntervalChanged.fire())),e.linkKey().subscribe(this._linkKeyChangedBound),e.sharingMode().subscribe(this._linkKeyChangedBound)}))};this._group.onChanged().subscribe(this,(e=>{this._unsubscribeFromAllLineTools(),this._lineTools=this._group.lineTools(),n(),e.lockedChanged&&this._onLockChanged.fire(),e.visibilityChanged&&this._onVisibilityChanged.fire(),e.isActualIntervalChanged&&this._onIsActualIntervalChanged.fire();const t=this.getDrawingSyncState();null!==t&&this._syncStateChanged.fire(t)})),n(),this._lastActualZOrder=this.zOrder(),this._lastIsVisible=this.isVisible(),this._lastIsActualInterval=this.isActualInterval(),this._lastIsLocked=this.isLocked()}destroy(){this._unsubscribeFromAllLineTools(),this._group.onChanged().unsubscribeAll(this)}id(){return fo(this._group.id,this._group.instanceId())}title(){return this._group.name()}gaLabel(){return"Group"}getIcon(){return{type:An.Svg,content:Lo}}canBeRemoved(){return!0}canBeHidden(){return!0}canBeLocked(){return!0}canBeRenamed(){return!0}fullyConstructed(){return!0}isVisible(){return this._group.lineTools().length>0&&(this._lastIsVisible="Invisible"!==this._group.visibility()),this._lastIsVisible}isActualInterval(){return this._group.lineTools().length>0&&(this._lastIsActualInterval=this._group.lineTools().some((e=>e.isActualInterval()))),this._lastIsActualInterval}onIsActualIntervalChange(){return this._onIsActualIntervalChanged}isLocked(){return this._group.lineTools().length>0&&(this._lastIsLocked="Locked"===this._group.locked()),this._lastIsLocked}onVisibilityChanged(){return this._onVisibilityChanged}onLockChanged(){return this._onLockChanged}setVisible(e){this._undoModel.lineToolsGroupController().setGroupVisibility(this._group,e)}setLocked(e){this._undoModel.lineToolsGroupController().setGroupLock(this._group,e)}setName(e){this._undoModel.lineToolsGroupController().setGroupName(this._group,e)}isCopiable(){return!1}isClonable(){return!1}zOrder(){return this._group.lineTools().length>0&&(this._lastActualZOrder=this._group.lineTools()[0].zorder()),this._lastActualZOrder}remove(){this._undoModel.lineToolsGroupController().removeGroup(this._group)}canBeAddedToSelection(){const e=this._undoModel.model();return this._lineTools.every((t=>e.selection().canBeAddedToSelection(t)))}setAsSelection(){this._undoModel.model().selectionMacro((e=>{e.clearSelection(),this._lineTools.forEach((t=>e.addSourceToSelection(t)))}))}addToSelection(){
this._undoModel.model().selectionMacro((e=>{this._lineTools.forEach((t=>e.addSourceToSelection(t)))}))}addSourcesToArray(e){return e.push(...this._lineTools),e}detachFromParent(){}insertSourcesBeforeThis(e){const t=this._insertBeforeTarget();this._insertSources(e,(e=>this._undoModel.insertBefore(e,t)))}insertSourcesAfterThis(e){const t=this._insertAfterTarget();this._insertSources(e,(e=>this._undoModel.insertAfter(e,t)))}childrenIds(){const e=[...this._lineTools];return e.sort(((e,t)=>t.zorder()-e.zorder())),new Set(e.map((e=>fo(e.id(),e.instanceId()))))}hasChildren(){return!0}pane(){return(0,I.ensureDefined)(this._undoModel.model().panes().find((e=>e.id()===this._paneId)))}allowsMovingbetweenPanes(){return!1}canBeAddedToGroup(){return!1}canInsertBeforeThis(e){return this._canInsertBeforeOrAfter(e)}canInsertAfterThis(e){return this._canInsertBeforeOrAfter(e)}canBeSyncedInLayout(){return this._lineTools.length>0&&this._lineTools[0].isSynchronizable()}onSyncStateChanged(){return this._syncStateChanged}setDrawingSyncState(e){if(this.canBeSyncedInLayout())switch(e){case 0:const e=this._lineTools.filter((e=>null!==e.linkKey().value()));e.length>0&&this._undoModel.unlinkLines(e);break;case 1:const t=this._lineTools.filter((e=>null===e.linkKey().value()));t.length>0&&this._undoModel.shareLineTools(t,1)}}getDrawingSyncState(){var e;if(!this.canBeSyncedInLayout())return null;if(Vo){const t=null===(e=this._lineTools[0])||void 0===e?void 0:e.sharingMode().value();if(void 0===t)return null;let n=t;if(0!==n)for(const e of this._lineTools)if(e.sharingMode().value()!==n){n=0;break}return Go(n)}return this._lineTools.every((e=>null!==e.linkKey().value()))?1:0}_linkKeyChanged(){this._syncStateChanged.fire((0,I.ensureNotNull)(this.getDrawingSyncState()))}_canInsertBeforeOrAfter(e){return zo(this.pane(),e)}_insertSources(e,t){this._undoModel.beginUndoMacro(Fo);const n=e.reduce(((e,t)=>t.addSourcesToArray(e)),[]);e.forEach((e=>e.detachFromParent())),t(n),this._undoModel.endUndoMacro()}_insertBeforeTarget(){return(0,I.ensureNotNull)(this._lineTools.reduce(((e,t)=>null===e?t:e.zorder()<t.zorder()?e:t),null))}_insertAfterTarget(){return(0,I.ensureNotNull)(this._lineTools.reduce(((e,t)=>null===e?t:e.zorder()>t.zorder()?e:t),null))}_unsubscribeFromAllLineTools(){this._lineTools.forEach((e=>{e.properties().visible.listeners().unsubscribeAll(this),e.properties().frozen.listeners().unsubscribeAll(this),e.onIsActualIntervalChange().unsubscribeAll(this),e.linkKey().unsubscribe(this._linkKeyChangedBound),e.sharingMode().unsubscribe(this._linkKeyChangedBound)}))}}class Zo{constructor(e){this._hoveredObjectChanged=new ho.Delegate,this._entitiesCache=new Map,this._undoModel=e,this._undoModel.model().hoveredSourceChanged().subscribe(this,this._onModelHoveredSourceChanged)}destroy(){for(const e of this._entitiesCache.values())null==e||e.destroy();this._undoModel.model().hoveredSourceChanged().unsubscribe(this,this._onModelHoveredSourceChanged)}getObjectById(e){if(this._entitiesCache.has(e))return(0,I.ensureDefined)(this._entitiesCache.get(e))
;const t=this._createObjectById(e);return this._entitiesCache.set(e,t),t}invalidateCache(e){Array.from(this._entitiesCache.keys()).forEach((t=>{var n;e.has(t)||(null===(n=this._entitiesCache.get(t))||void 0===n||n.destroy(),this._entitiesCache.delete(t))}))}canBeGroupped(e){if(0===e.length||1===e.length&&e[0].hasChildren())return!1;const t=[];if(e.forEach((e=>e.addSourcesToArray(t))),t.some((e=>!(0,_o.isLineTool)(e)||!e.boundToSymbol())))return!1;const n=this._undoModel.model(),o=t.map((e=>n.paneForSource(e)));if(new Set(o).size>1)return!1;if(!Vo)return!0;const r=t.map((e=>e.sharingMode().value()));return 1===new Set(r).size}contextMenuActions(e,t,n){const o=new Io.ActionsProvider(e,n),r=[];return t.forEach((e=>e.addSourcesToArray(r))),o.contextMenuActionsForSources(r,(0,I.ensureNotNull)(this._undoModel.paneForSource(r[0])))}insertBefore(e,t){t.insertSourcesAfterThis(e)}insertAfter(e,t){t.insertSourcesBeforeThis(e)}setHoveredObject(e){const t=this._undoModel.model();if(null===e)return void t.setHoveredSource(null,null);const n=t.dataSourceForId(e);null!==n&&t.setHoveredSource(n,null)}hoveredObjectId(){return Wo(this._undoModel.model().hoveredSource())}hoveredObjectChanged(){return this._hoveredObjectChanged}_onModelHoveredSourceChanged(e){this._hoveredObjectChanged.fire(Wo(e))}_createObjectById(e){const t=mo(e).persistentId,n=this._undoModel.model(),o=n.dataSourceForId(t);if(null!==o)return new Ko(this._undoModel,o);const r=n.lineToolsGroupModel().groupForId(t);return null!==r?new Uo(this._undoModel,r):null}}Error;var Yo=n(45579),Qo=n(40443),$o=n(97702),qo=n(81199),Xo=n(97874),Jo=n(2872),er=n(84959),tr=n(91730),nr=n(93544),or=n(32549);const rr=(0,s.getLogger)("Platform.GUI.ObjectTree");var ir;!function(e){e[e.Up=0]="Up",e[e.Down=1]="Down"}(ir||(ir={}));const sr=new So.TranslatedString("move objects",p.t(null,void 0,n(36044))),lr=new So.TranslatedString("lock objects",p.t(null,void 0,n(18942))),ar=new So.TranslatedString("unlock objects",p.t(null,void 0,n(23230))),cr=new So.TranslatedString("show objects",p.t(null,void 0,n(23771))),ur=new So.TranslatedString("hide objects",p.t(null,void 0,n(93277))),dr=new So.TranslatedString("remove objects",p.t(null,void 0,n(79688)));class hr{constructor(e){this._nodes={},this._onChange=new ho.Delegate,this._onGroupCreated=new ho.Delegate,this._subscriptions=[],this._removeSourcesPromise=null,this._timeout=null,this._objects=[],this._options={general:!0,mainSeries:!0,mainSeriesTrade:!0,esdStudies:!0,fundamentals:!0,studies:!0,lineTools:!0,publishedCharts:!0,ordersAndPositions:!0,alerts:!1,chartEvents:!0,objectTree:!1,gotoLineTool:!0},this._isContextMenuOpened=new yo.WatchedValue(!1),this._getObjectsToModify=e=>{const t=this.selection().selected();return t.find((t=>t===e))?t.map(this._ensuredEntity):[this._ensuredEntity(e)]},this._onActiveChartChanged=()=>{this._cleanup(),this._init()},this._cleanup=()=>{null!==this._timeout&&(clearTimeout(this._timeout),this._timeout=null),this._subscriptions.forEach((e=>{e.unsubscribeAll(this)})),this._selection.destroy(),
this._chart.unsubscribe(this._onActiveChartChanged),null!==this._removeSourcesPromise&&this._removeSourcesPromise.cancel(),this._facade.destroy()},this._init=()=>{const e=this._chart.value();e.hasModel()&&(this._controller=e.model(),this._groupController=this._controller.lineToolsGroupController(),this._model=this._controller.model(),this._groupModel=this._model.lineToolsGroupModel(),this._facade=new Zo(this._controller),this._subscriptions=[this._model.mainSeries().onStyleChanged(),this._model.mainSeries().dataEvents().symbolResolved(),this._model.mainSeries().onIntervalChanged(),this._model.panesCollectionChanged(),this._model.dataSourceCollectionChanged(),this._groupModel.onChanged()],this._subscriptions.forEach((e=>{e.subscribe(this,this._update)})),this._chart.subscribe(this._onActiveChartChanged),this._selection=new vo(this._model),this._update())},this._update=()=>{null===this._timeout&&(this._timeout=setTimeout((()=>{this._recalculateTree(),this._onChange.fire(),this._timeout=null})))},this._ensuredEntity=e=>(0,I.ensureNotNull)(this._getEntityById(e)),this._chart=e,this._init()}destroy(){this._cleanup()}getState(){return{nodes:Object.values(this._nodes),selection:this._selection.selected()}}getChartId(){return this._chart.value().id()}insertSelection(e,t){const n=this._facade,o=this.selection().selected().map(this._ensuredEntity),[r,i]=this._normalizeTargetAndDropType(e,t);this._controller.withMacro(sr,(()=>{switch(i){case"before":n.insertBefore(o,r);break;case"after":n.insertAfter(o,r)}})),this._update()}entity(e){return this._facade.getObjectById(e)}isMain(e){return mo(e.id()).persistentId===this._controller.mainSeries().id()}selection(){return this._selection}setIsLocked(e,t){const n=this._getObjectsToModify(e),o=t?lr:ar;this._controller.withMacro(o,(()=>{for(const e of n)e.setLocked(t)})),yn("Lock",_n(n))}setIsVisible(e,t){const n=this._getObjectsToModify(e),o=t?cr:ur;this._controller.withMacro(o,(()=>{for(const e of n)e.setVisible(t)})),yn("Hide",_n(n))}remove(e){const t=()=>{this._controller.withMacro(dr,(()=>{for(const e of n)e.remove()})),yn("Delete",_n(n)),this._update()},n=this._getObjectsToModify(e);t()}canSelectionBeGrouped(){const e=this._getSelectedEntities();return this._facade.canBeGroupped(e)}createGroupFromSelection(){const e=this._groupController.createGroupFromSelection();yn("Create Group");const t=fo(e.id,e.instanceId());this.selection().set([this._ensuredEntity(t)]),this._onGroupCreated.fire(t),this._update()}isSelectionDropable(e,t){const n=this.selection().selected().map(this._ensuredEntity),[o,r]=this._normalizeTargetAndDropType(e,t);switch(r){case"after":return o.canInsertAfterThis(n);case"before":return o.canInsertBeforeThis(n)}}onChange(){return this._onChange}onGroupCreated(){return this._onGroupCreated}isSelectionCloneable(){const e=this._getSelectedEntities();return e.length>0&&e.every((e=>e.isClonable()))}isSelectionCopiable(){const e=this._getSelectedEntities();return e.length>0&&e.every((e=>e.isCopiable()))}openProperties(e,t){
const n=this._model.dataSourceForId(mo(e.id()).persistentId);this.selection().selected().length>1&&this.selection().selected().includes(e.id())?this._chart.value().showSelectedSourcesProperties(t):(this.selection().set([e]),null!==n?this._controller.mainSeries()===n?this._chart.value().showGeneralChartProperties(void 0,{shouldReturnFocus:!0}):((0,_o.isLineTool)(n)||(0,Co.isStudy)(n))&&this._chart.value().showChartPropertiesForSource(n,t,{shouldReturnFocus:!0}):this._chart.value().showChartPropertiesForSources({sources:this._chart.value().model().selection().lineDataSources(),title:e.title().value(),tabName:t,renamable:!0}))}canSelectionBeUnmerged(){const e=this._getSelectedEntities();return 1===e.length&&this.canNodeWithIdBeUnmerged(mo(e[0].id()).persistentId)}canNodeWithIdBeUnmerged(e){const t=this._model.dataSourceForId(e);return null!==t&&(0,Yo.isPriceDataSource)(t)&&this._model.isUnmergeAvailableForSource(t)}unmergeSelectionUp(){this._unmergeSelection(0)}unmergeSelectionDown(){this._unmergeSelection(1)}copySelection(){const e=this._getSelectedEntities(),t=e.map((e=>(0,I.ensureNotNull)(this._model.dataSourceForId(mo(e.id()).persistentId))));this._chart.value().chartWidgetCollection().clipboard.uiRequestCopy(t),yn("Copy",_n(e))}cloneSelection(){const e=this._getSelectedEntities(),t=e.map((e=>(0,I.ensureNotNull)(this._model.dataSourceForId(mo(e.id()).persistentId))));t.every(_o.isLineTool)&&(this._controller.cloneLineTools([...t],!1),yn("Clone",_n(e)))}rename(e,t){const n=this._getObjectsToModify(e.id());1===n.length&&n.some((e=>e.canBeRenamed()))&&(t(),yn("Rename",_n(n)))}async openContextMenu(e,t,n){var o;this._objects=this._getObjectsToModify(e.id());const r=this._facade.canBeGroupped(this._objects);let i;if(this._objects.some((e=>e.hasChildren())))i=this._getActionsForGroupItem(e,t,r);else{const e=await this._facade.contextMenuActions(this._chart.value(),this._objects,this._options);if(i=Array.from(e).filter(((e,t,n)=>"separator"!==e.type||!n[t+1]||"separator"!==n[t+1].type)),1===this._objects.length&&this._objects[0].canBeRenamed()){const e=i.findIndex((e=>"Copy"===e.id));i.splice(-1===e?i.length:e+1,0,this._getRenameAction(t))}const n=i.findIndex((e=>"Clone"===e.id)),o=i[n];-1!==n&&"action"===o.type&&o.update({shortcutHint:void 0}),r&&i.splice(-1===n?0:n,0,this._getGroupAction())}if(i.length>0){this._chart.value().updateActions();const t=mo(e.id()).persistentId,r=this._model.dataSourceForId(t),s=r instanceof or.Series,l=0!==e.childrenIds().size;let a;a=s?{menuName:"ObjectTreeContextMenu",detail:{type:"series",id:r.instanceId()}}:(0,_o.isLineTool)(r)?{menuName:"ObjectTreeContextMenu",detail:{type:"shape",id:null!==(o=null==r?void 0:r.id())&&void 0!==o?o:null}}:l?{menuName:"ObjectTreeContextMenu",detail:{type:"groupOfShapes",id:t||null}}:{menuName:"ObjectTreeContextMenu",detail:{type:"study",id:(null==r?void 0:r.id())||null}},Qo.ContextMenuManager.showMenu(i,n,{takeFocus:!0,returnFocus:!0},a,(()=>{this._isContextMenuOpened.setValue(!1)})).then((()=>{this._isContextMenuOpened.setValue(!0)}))}}setHoveredObject(e){
const t=e?mo(e).persistentId:null;this._facade.setHoveredObject(t)}hoveredObjectChanged(){return this._facade.hoveredObjectChanged()}getNextNodeIdAfterRemove(e){var t;const{nodes:n}=this.getState(),o=mo(e).persistentId,r=n.find((t=>t.id===e)),i=this.entity(e);if(!(r&&r.parentId&&i&&i.canBeRemoved()))return null;if((null===(t=i.pane().mainDataSource())||void 0===t?void 0:t.id())===o&&!this.canNodeWithIdBeUnmerged(o)){const e=n.filter((e=>0===e.level)).map((e=>e.id)),t=this._takeNextOrPrevElement(e,r.parentId);return(0,I.ensureDefined)(n.find((e=>e.id===t))).children[0]}const s=(0,I.ensureDefined)(n.find((e=>e.id===r.parentId))).children;return 1===s.length?this.getNextNodeIdAfterRemove(r.parentId):this._takeNextOrPrevElement(s,e)}isContextMenuOpened(){return this._isContextMenuOpened.readonly()}getChartLayout(){return this._chart.value().chartWidgetCollection().layout}_takeNextOrPrevElement(e,t){const n=e.indexOf(t);return e[n===e.length-1?n-1:n+1]}_getGroupAction(){return new qo.ActionWithStandardIcon({actionId:"ObjectsTree.CreateGroup",options:{label:jn,iconId:"ObjectsTree.CreateGroup",onExecute:()=>{this.createGroupFromSelection()}}})}_getRenameAction(e){return new qo.ActionWithStandardIcon({actionId:"ObjectsTree.RenameItem",options:{label:Vn,iconId:"ObjectsTree.RenameItem",onExecute:()=>{e(),yn("Context menu rename",_n(this._objects))}}})}_getActionsForGroupItem(e,t,n){const o=[];this._objects.forEach((e=>e.addSourcesToArray(o)));const r=[];1===this._objects.length&&r.unshift(this._getRenameAction(t),new $o.Separator),n&&r.unshift(this._getGroupAction(),new $o.Separator);const i=(0,Io.createSyncDrawingActions)(this._chart.value(),o.filter(_o.isLineTool));i.length&&r.push(...i,new $o.Separator);const s=this._chart.value().actions().format.getState();return r.push(new $o.Action({actionId:"ObjectsTree.ToggleItemLocked",options:{label:e.isLocked()?Gn:Kn,icon:e.isLocked()?Xo:Jo,onExecute:()=>this.setIsLocked(e.id(),!e.isLocked())}}),new $o.Action({actionId:"ObjectsTree.ToggleItemVisibility",options:{label:e.isVisible()?Un:Zn,icon:e.isVisible()?er:tr,onExecute:()=>this.setIsVisible(e.id(),!e.isVisible())}}),new $o.Action({actionId:"ObjectsTree.RemoveItem",options:{label:Yn,icon:nr,onExecute:()=>this.remove(e.id()),hotkeyHash:M.isMacKeyboard?8:46}}),new $o.Separator,new $o.Action({actionId:s.actionId,options:{label:s.label,icon:s.icon,onExecute:()=>this.openProperties(e)}})),r}_unmergeSelection(e){const t=this._getSelectedEntities();if(1!==t.length)throw new Error("Only one object can be unmerged");const n=t[0],o=(0,I.ensureNotNull)(this._model.dataSourceForId(mo(n.id()).persistentId));if(!(0,Yo.isPriceDataSource)(o))throw new Error("Entity is not IPriceDataSource");(0===e?this._controller.unmergeSourceUp:this._controller.unmergeSourceDown).call(this._controller,o);yn(0===e?"New pane above":"New pane below",_n([n]))}_recalculateTree(){const e=new go(this._controller,this._facade);this._nodes=e.buildTree()}_normalizeTargetAndDropType(e,t){let n=this._ensuredEntity(e);return"inside"===t&&(t="before",n=(0,
I.ensureNotNull)(this.entity([...n.childrenIds()].shift()||""))),[n,t]}_getSelectedEntities(){const{selected:e,removed:t}=this._selection.selected().reduce(((e,t)=>{const n=this._getEntityById(t);return n?(e.selected.push(n),e):(e.removed.push(t),e)}),{selected:[],removed:[]});return t.length&&rr.logWarn(`Detected dangling sources in selection. They will be ignored: ${JSON.stringify(t)}`),e}_getEntityById(e){return this._facade.getObjectById(e)}}var pr=n(29280),fr=n(28124);class mr extends pr.DialogRenderer{constructor(){super(),this._handleClose=()=>{var e;null===(e=this._rootInstance)||void 0===e||e.unmount(),this._setVisibility(!1),null!==this._viewModel&&(this._viewModel.destroy(),this._viewModel=null)};const e=(0,co.service)(uo.CHART_WIDGET_COLLECTION_SERVICE);this._activeChartWidget=e.activeChartWidget.value(),this._viewModel=new hr(e.activeChartWidget)}hide(){this._handleClose()}isVisible(){return this.visible().value()}show(){this.isVisible()||h().then((()=>{null!==this._viewModel&&(this._rootInstance=(0,fr.createReactRoot)(o.createElement(lo,{onClose:this._handleClose,viewModel:this._viewModel,activeChartWidget:this._activeChartWidget}),this._container),this._setVisibility(!0))}))}}},37558:(e,t,n)=>{"use strict";n.d(t,{DrawerContext:()=>s,DrawerManager:()=>i});var o=n(50959),r=n(99054);class i extends o.PureComponent{constructor(e){super(e),this._isBodyFixed=!1,this._addDrawer=e=>{this.setState((t=>({stack:[...t.stack,e]})))},this._removeDrawer=e=>{this.setState((t=>({stack:t.stack.filter((t=>t!==e))})))},this.state={stack:[]}}componentDidUpdate(e,t){!t.stack.length&&this.state.stack.length&&((0,r.setFixedBodyState)(!0),this._isBodyFixed=!0),t.stack.length&&!this.state.stack.length&&this._isBodyFixed&&((0,r.setFixedBodyState)(!1),this._isBodyFixed=!1)}componentWillUnmount(){this.state.stack.length&&this._isBodyFixed&&(0,r.setFixedBodyState)(!1)}render(){return o.createElement(s.Provider,{value:{addDrawer:this._addDrawer,removeDrawer:this._removeDrawer,currentDrawer:this.state.stack.length?this.state.stack[this.state.stack.length-1]:null}},this.props.children)}}const s=o.createContext(null)},41590:(e,t,n)=>{"use strict";n.d(t,{Drawer:()=>p});var o=n(50959),r=n(50151),i=n(97754),s=n(92184),l=n(42842),a=n(37558),c=n(29197),u=n(86656),d=n(69658);var h;function p(e){const{position:t="Bottom",onClose:n,children:u,reference:h,className:p,theme:m=d}=e,v=(0,r.ensureNotNull)((0,o.useContext)(a.DrawerContext)),[g]=(0,o.useState)((()=>(0,s.randomHash)())),b=(0,o.useRef)(null),S=(0,o.useContext)(c.CloseDelegateContext);return(0,o.useLayoutEffect)((()=>((0,r.ensureNotNull)(b.current).focus({preventScroll:!0}),S.subscribe(v,n),v.addDrawer(g),()=>{v.removeDrawer(g),S.unsubscribe(v,n)})),[]),o.createElement(l.Portal,null,o.createElement("div",{ref:h,className:i(d.wrap,d[`position${t}`])},g===v.currentDrawer&&o.createElement("div",{className:d.backdrop,onClick:n}),o.createElement(f,{className:i(m.drawer,d[`position${t}`],p),ref:b,"data-name":e["data-name"]},u)))}!function(e){e.Left="Left",e.Bottom="Bottom"}(h||(h={}))
;const f=(0,o.forwardRef)(((e,t)=>{const{className:n,...r}=e;return o.createElement(u.TouchScrollContainer,{className:i(d.drawer,n),tabIndex:-1,ref:t,...r})}))},36189:(e,t,n)=>{"use strict";n.d(t,{FavoriteButton:()=>d});var o=n(11542),r=n(50959),i=n(97754),s=n(9745),l=n(39146),a=n(48010),c=n(98992);const u={add:o.t(null,void 0,n(69207)),remove:o.t(null,void 0,n(85106))};function d(e){const{className:t,isFilled:n,isActive:o,onClick:d,...h}=e;return r.createElement(s.Icon,{...h,className:i(c.favorite,"apply-common-tooltip",n&&c.checked,o&&c.active,t),icon:n?l:a,onClick:d,title:n?u.remove:u.add})}},898:(e,t,n)=>{"use strict";n.d(t,{useDimensions:()=>i});var o=n(50959),r=n(67842);function i(e){const[t,n]=(0,o.useState)(null),i=(0,o.useCallback)((([e])=>{const o=e.target.getBoundingClientRect();o.width===(null==t?void 0:t.width)&&o.height===t.height||n(o)}),[t]);return[(0,r.useResizeObserver)({callback:i,ref:e}),t]}},70412:(e,t,n)=>{"use strict";n.d(t,{hoverMouseEventFilter:()=>i,useAccurateHover:()=>s,useHover:()=>r});var o=n(50959);function r(){const[e,t]=(0,o.useState)(!1);return[e,{onMouseOver:function(e){i(e)&&t(!0)},onMouseOut:function(e){i(e)&&t(!1)}}]}function i(e){return!e.currentTarget.contains(e.relatedTarget)}function s(e){const[t,n]=(0,o.useState)(!1);return(0,o.useEffect)((()=>{const t=t=>{if(null===e.current)return;const o=e.current.contains(t.target);n(o)};return document.addEventListener("mouseover",t),()=>document.removeEventListener("mouseover",t)}),[]),t}},33127:(e,t,n)=>{"use strict";n.d(t,{useOverlayScroll:()=>a});var o=n(50959),r=n(50151),i=n(70412),s=n(49483);const l={onMouseOver:()=>{},onMouseOut:()=>{}};function a(e,t=s.CheckMobile.any()){const n=(0,o.useRef)(null),a=e||(0,o.useRef)(null),[c,u]=(0,i.useHover)(),[d,h]=(0,o.useState)({reference:n,containerHeight:0,containerWidth:0,contentHeight:0,contentWidth:0,scrollPosTop:0,scrollPosLeft:0,onVerticalChange:function(e){h((t=>({...t,scrollPosTop:e}))),(0,r.ensureNotNull)(a.current).scrollTop=e},onHorizontalChange:function(e){h((t=>({...t,scrollPosLeft:e}))),(0,r.ensureNotNull)(a.current).scrollLeft=e},visible:c}),p=(0,o.useCallback)((()=>{if(!a.current)return;const{clientHeight:e,scrollHeight:t,scrollTop:o,clientWidth:r,scrollWidth:i,scrollLeft:s}=a.current,l=n.current?n.current.offsetTop:0;h((n=>({...n,containerHeight:e-l,contentHeight:t-l,scrollPosTop:o,containerWidth:r,contentWidth:i,scrollPosLeft:s})))}),[]);function f(){h((e=>({...e,scrollPosTop:(0,r.ensureNotNull)(a.current).scrollTop,scrollPosLeft:(0,r.ensureNotNull)(a.current).scrollLeft})))}return(0,o.useEffect)((()=>{c&&p(),h((e=>({...e,visible:c})))}),[c]),(0,o.useEffect)((()=>{const e=a.current;return e&&e.addEventListener("scroll",f),()=>{e&&e.removeEventListener("scroll",f)}}),[a]),[d,t?l:u,a,p]}},96040:(e,t,n)=>{"use strict";n.d(t,{RemoveButton:()=>c});var o=n(11542),r=n(50959),i=n(97754),s=n(9745),l=n(33765),a=n(11772);function c(e){const{className:t,isActive:c,onClick:u,onMouseDown:d,title:h,hidden:p,"data-name":f="remove-button",icon:m,...v}=e;return r.createElement(s.Icon,{...v,
"data-name":f,className:i(a.button,"apply-common-tooltip",c&&a.active,p&&a.hidden,t),icon:m||l,onClick:u,onMouseDown:d,title:h||o.t(null,void 0,n(67410))})}},86656:(e,t,n)=>{"use strict";n.d(t,{TouchScrollContainer:()=>c});var o=n(50959),r=n(59142),i=n(50151),s=n(49483);const l=CSS.supports("overscroll-behavior","none");let a=0;const c=(0,o.forwardRef)(((e,t)=>{const{children:n,...i}=e,c=(0,o.useRef)(null);return(0,o.useImperativeHandle)(t,(()=>c.current)),(0,o.useLayoutEffect)((()=>{if(s.CheckMobile.iOS())return a++,null!==c.current&&(l?1===a&&(document.body.style.overscrollBehavior="none"):(0,r.disableBodyScroll)(c.current,{allowTouchMove:u(c)})),()=>{a--,null!==c.current&&(l?0===a&&(document.body.style.overscrollBehavior=""):(0,r.enableBodyScroll)(c.current))}}),[]),o.createElement("div",{ref:c,...i},n)}));function u(e){return t=>{const n=(0,i.ensureNotNull)(e.current),o=document.activeElement;return!n.contains(t)||null!==o&&n.contains(o)&&o.contains(t)}}},19347:e=>{e.exports={accessible:"accessible-NQERJsv9",active:"active-NQERJsv9"}},52495:e=>{e.exports={button:"button-xNqEcuN2"}},94878:e=>{e.exports={button:"button-merBkM5y",hover:"hover-merBkM5y",clicked:"clicked-merBkM5y",accessible:"accessible-merBkM5y",arrow:"arrow-merBkM5y",arrowWrap:"arrowWrap-merBkM5y",isOpened:"isOpened-merBkM5y"}},20243:(e,t,n)=>{"use strict";n.d(t,{focusFirstMenuItem:()=>c,handleAccessibleMenuFocus:()=>l,handleAccessibleMenuKeyDown:()=>a,queryMenuElements:()=>h});var o=n(19291),r=n(57177),i=n(68335);const s=[37,39,38,40];function l(e,t){var n;if(!e.target)return;const o=null===(n=e.relatedTarget)||void 0===n?void 0:n.getAttribute("aria-activedescendant");if(e.relatedTarget!==t.current){const e=o&&document.getElementById(o);if(!e||e!==t.current)return}c(e.target)}function a(e){var t;if(e.defaultPrevented)return;const n=(0,i.hashFromEvent)(e);if(!s.includes(n))return;const l=document.activeElement;if(!(document.activeElement instanceof HTMLElement))return;const a=h(e.currentTarget).sort(o.navigationOrderComparator);if(0===a.length)return;const c=document.activeElement.closest('[data-role="menuitem"]')||(null===(t=document.activeElement.parentElement)||void 0===t?void 0:t.querySelector('[data-role="menuitem"]'));if(!(c instanceof HTMLElement))return;const f=a.indexOf(c);if(-1===f)return;const m=p(c),v=m.indexOf(document.activeElement),g=-1!==v,b=e=>{l&&(0,r.becomeSecondaryElement)(l),(0,r.becomeMainElement)(e),e.focus()};switch((0,o.mapKeyCodeToDirection)(n)){case"inlinePrev":if(!m.length)return;e.preventDefault(),b(0===v?a[f]:g?u(m,v,-1):m[m.length-1]);break;case"inlineNext":if(!m.length)return;e.preventDefault(),v===m.length-1?b(a[f]):b(g?u(m,v,1):m[0]);break;case"blockPrev":{e.preventDefault();const t=u(a,f,-1);if(g){const e=d(t,v);b(e||t);break}b(t);break}case"blockNext":{e.preventDefault();const t=u(a,f,1);if(g){const e=d(t,v);b(e||t);break}b(t)}}}function c(e){const[t]=h(e);t&&((0,r.becomeMainElement)(t),t.focus())}function u(e,t,n){return e[(t+e.length+n)%e.length]}function d(e,t){const n=p(e)
;return n.length?n[(t+n.length)%n.length]:null}function h(e){return Array.from(e.querySelectorAll('[data-role="menuitem"]:not([disabled]):not([aria-disabled])')).filter((0,o.createScopedVisibleElementFilter)(e))}function p(e){return Array.from(e.querySelectorAll("[tabindex]:not([disabled]):not([aria-disabled])")).filter((0,o.createScopedVisibleElementFilter)(e))}},57177:(e,t,n)=>{"use strict";var o;function r(e){e.dispatchEvent(new CustomEvent("roving-tabindex:main-element"))}function i(e){e.dispatchEvent(new CustomEvent("roving-tabindex:secondary-element"))}n.d(t,{becomeMainElement:()=>r,becomeSecondaryElement:()=>i}),function(e){e.MainElement="roving-tabindex:main-element",e.SecondaryElement="roving-tabindex:secondary-element"}(o||(o={}))},10838:(e,t,n)=>{"use strict";n.d(t,{AccessibleMenuItem:()=>u});var o=n(50959),r=n(97754),i=n.n(r),s=n(3343),l=n(50238),a=n(16396),c=n(19347);function u(e){const{className:t,...n}=e,[r,u]=(0,l.useRovingTabindexElement)(null);return o.createElement(a.PopupMenuItem,{...n,className:i()(c.accessible,e.isActive&&c.active,t),reference:r,tabIndex:u,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,s.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),r.current instanceof HTMLElement&&r.current.click())},"data-role":"menuitem","aria-disabled":e.isDisabled||void 0})}},78135:(e,t,n)=>{"use strict";n.d(t,{HorizontalAttachEdge:()=>r,HorizontalDropDirection:()=>s,VerticalAttachEdge:()=>o,VerticalDropDirection:()=>i,getPopupPositioner:()=>c});var o,r,i,s,l=n(50151);!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom",e[e.AutoStrict=2]="AutoStrict"}(o||(o={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(r||(r={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(i||(i={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(s||(s={}));const a={verticalAttachEdge:o.Bottom,horizontalAttachEdge:r.Left,verticalDropDirection:i.FromTopToBottom,horizontalDropDirection:s.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function c(e,t){return n=>{var c,u;const{contentWidth:d,contentHeight:h,availableHeight:p}=n,f=(0,l.ensureNotNull)(e).getBoundingClientRect(),{horizontalAttachEdge:m=a.horizontalAttachEdge,horizontalDropDirection:v=a.horizontalDropDirection,horizontalMargin:g=a.horizontalMargin,verticalMargin:b=a.verticalMargin,matchButtonAndListboxWidths:S=a.matchButtonAndListboxWidths}=t;let y=null!==(c=t.verticalAttachEdge)&&void 0!==c?c:a.verticalAttachEdge,_=null!==(u=t.verticalDropDirection)&&void 0!==u?u:a.verticalDropDirection;y===o.AutoStrict&&(p<f.y+f.height+b+h?(y=o.Top,_=i.FromBottomToTop):(y=o.Bottom,_=i.FromTopToBottom));const C=y===o.Top?-1*b:b,w=m===r.Right?f.right:f.left,E=y===o.Top?f.top:f.bottom,T={x:w-(v===s.FromRightToLeft?d:0)+g,y:E-(_===i.FromBottomToTop?h:0)+C};return S&&(T.overrideWidth=f.width),T}}},56388:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetIconButton:()=>l});var o=n(50959),r=n(97754),i=n(81348),s=n(52495)
;const l=o.forwardRef((function(e,t){const{className:n,id:l,...a}=e;return o.createElement(i.ToolWidgetButton,{id:l,"data-name":l,...a,ref:t,className:r(n,s.button)})}))},20626:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetMenu:()=>g});var o=n(50959),r=n(97754),i=n(3343),s=n(20520),l=n(10381),a=n(90186),c=n(37558),u=n(41590),d=n(78135),h=n(90692),p=n(56570),f=n(76460),m=n(94878);var v;!function(e){e[e.Vertical=2]="Vertical",e[e.Horizontal=0]="Horizontal"}(v||(v={}));class g extends o.PureComponent{constructor(e){super(e),this._wrapperRef=null,this._controller=o.createRef(),this._onPopupCloseOnClick=e=>{"keyboard"===e.detail.clickType&&this.focus()},this._handleMenuFocus=e=>{var t,n;e.relatedTarget===this._wrapperRef&&this.setState((e=>({...e,isOpenedByButton:!0}))),null===(n=(t=this.props).onMenuFocus)||void 0===n||n.call(t,e)},this._handleWrapperRef=e=>{this._wrapperRef=e,this.props.reference&&this.props.reference(e)},this._handleOpen=()=>{var e,t,n;"div"!==this.props.tag&&(this.setState((e=>({...e,isOpenedByButton:!1}))),null===(t=null===(e=this.props.menuReference)||void 0===e?void 0:e.current)||void 0===t||t.addEventListener("popup-menu-close-event",this._onPopupCloseOnClick),null===(n=this._controller.current)||void 0===n||n.focus())},this._handleClick=e=>{(p.enabled("skip_event_target_check")||e.target instanceof Node)&&e.currentTarget.contains(e.target)&&(this._handleToggleDropdown(void 0,(0,f.isKeyboardClick)(e)),this.props.onClick&&this.props.onClick(e,!this.state.isOpened))},this._handleToggleDropdown=(e,t=!1)=>{const{onClose:n,onOpen:o}=this.props,{isOpened:r}=this.state,i="boolean"==typeof e?e:!r;this.setState({isOpened:i,shouldReturnFocus:!!i&&t}),i&&o&&o(),!i&&n&&n()},this._handleClose=()=>{this.close()},this._handleKeyDown=e=>{var t;const{orientation:n="horizontal"}=this.props;if(e.defaultPrevented)return;if(!(e.target instanceof Node))return;const o=(0,i.hashFromEvent)(e);if(e.currentTarget.contains(e.target))switch(o){case 40:if("div"===this.props.tag||"horizontal"!==n)return;if(this.state.isOpened)return;e.preventDefault(),this._handleToggleDropdown(!0,!0);break;case 27:if(!this.state.isOpened||!this.props.closeOnEsc)return;e.preventDefault(),e.stopPropagation(),this._handleToggleDropdown(!1)}else{if("div"===this.props.tag)return;switch(o){case 27:{e.preventDefault();const{shouldReturnFocus:n,isOpenedByButton:o}=this.state;this._handleToggleDropdown(!1),n&&o&&(null===(t=this._wrapperRef)||void 0===t||t.focus());break}}}},this.state={isOpened:!1,shouldReturnFocus:!1,isOpenedByButton:!1}}render(){const{tag:e="div",id:t,arrow:n,content:i,isDisabled:s,isDrawer:c,isShowTooltip:u,title:d,className:p,hotKey:f,theme:m,drawerBreakpoint:v,tabIndex:g,isClicked:S}=this.props,{isOpened:y}=this.state,_=r(p,m.button,{"apply-common-tooltip":u||!s,[m.isDisabled]:s,[m.isOpened]:y,[m.clicked]:S}),C=b(i)?i({isOpened:y}):i;return"button"===e?o.createElement("button",{type:"button",id:t,className:r(_,m.accessible),disabled:s,onClick:this._handleClick,title:d,"data-tooltip-hotkey":f,ref:this._handleWrapperRef,
onKeyDown:this._handleKeyDown,tabIndex:g,...(0,a.filterDataProps)(this.props),...(0,a.filterAriaProps)(this.props)},C,n&&o.createElement("div",{className:m.arrow},o.createElement("div",{className:m.arrowWrap},o.createElement(l.ToolWidgetCaret,{dropped:y}))),this.state.isOpened&&(v?o.createElement(h.MatchMedia,{rule:v},(e=>this._renderContent(e))):this._renderContent(c))):o.createElement("div",{id:t,className:_,onClick:s?void 0:this._handleClick,title:d,"data-tooltip-hotkey":f,ref:this._handleWrapperRef,"data-role":"button",tabIndex:g,onKeyDown:this._handleKeyDown,...(0,a.filterDataProps)(this.props)},C,n&&o.createElement("div",{className:m.arrow},o.createElement("div",{className:m.arrowWrap},o.createElement(l.ToolWidgetCaret,{dropped:y}))),this.state.isOpened&&this._renderContent(c))}close(){var e,t;null===(t=null===(e=this.props.menuReference)||void 0===e?void 0:e.current)||void 0===t||t.removeEventListener("popup-menu-close-event",this._onPopupCloseOnClick),this._handleToggleDropdown(!1)}focus(){var e;null===(e=this._wrapperRef)||void 0===e||e.focus()}update(){null!==this._controller.current&&this._controller.current.update()}_renderContent(e){const{menuDataName:t,minWidth:n,menuClassName:r,maxHeight:i,drawerPosition:l="Bottom",children:a}=this.props,{isOpened:h}=this.state,p={horizontalMargin:this.props.horizontalMargin||0,verticalMargin:this.props.verticalMargin||2,verticalAttachEdge:this.props.verticalAttachEdge,horizontalAttachEdge:this.props.horizontalAttachEdge,verticalDropDirection:this.props.verticalDropDirection,horizontalDropDirection:this.props.horizontalDropDirection,matchButtonAndListboxWidths:this.props.matchButtonAndListboxWidths},f=Boolean(h&&e&&l),m=b(a)?a({isDrawer:f}):a;return f?o.createElement(c.DrawerManager,null,o.createElement(u.Drawer,{reference:this.props.drawerReference,onClose:this._handleClose,position:l,"data-name":t},m)):o.createElement(s.PopupMenu,{reference:this.props.menuReference,controller:this._controller,closeOnClickOutside:this.props.closeOnClickOutside,doNotCloseOn:this,isOpened:h,minWidth:n,onClose:this._handleClose,position:(0,d.getPopupPositioner)(this._wrapperRef,p),className:r,maxHeight:i,"data-name":t,tabIndex:"div"!==this.props.tag?-1:void 0,onOpen:this._handleOpen,onKeyDown:this.props.onMenuKeyDown,onFocus:this._handleMenuFocus},m)}}function b(e){return"function"==typeof e}g.defaultProps={arrow:!0,closeOnClickOutside:!0,theme:m}},98945:(e,t,n)=>{"use strict";n.d(t,{ToolbarIconButton:()=>s});var o=n(50959),r=n(50238),i=n(56388);const s=(0,o.forwardRef)((function(e,t){const{tooltip:n,...s}=e,[l,a]=(0,r.useRovingTabindexElement)(t);return o.createElement(i.ToolWidgetIconButton,{"aria-label":n,...s,tag:"button",ref:l,tabIndex:a,"data-tooltip":n})}))},88811:(e,t,n)=>{"use strict";n.d(t,{ToolbarMenuButton:()=>u});var o=n(50959),r=n(39416),i=n(50238),s=n(7047),l=n(20626),a=n(20243);const c=(0,o.forwardRef)((function(e,t){const{tooltip:n,tag:i,buttonRef:s,reference:c,...u}=e,d=(0,r.useFunctionalRefObject)(null!=c?c:null);return o.createElement(l.ToolWidgetMenu,{"aria-label":n,
...u,ref:t,tag:null!=i?i:"button",reference:null!=s?s:d,"data-tooltip":n,onMenuKeyDown:a.handleAccessibleMenuKeyDown,onMenuFocus:e=>(0,a.handleAccessibleMenuFocus)(e,null!=s?s:d)})})),u=(0,o.forwardRef)((function(e,t){const{tooltip:n,menuReference:l=null,...a}=e,[u,d]=(0,i.useRovingTabindexElement)(null),h=(0,r.useFunctionalRefObject)(l);return o.createElement(c,{"aria-label":n,...s.MouseClickAutoBlurHandler.attributes(),...a,ref:t,tag:"button",buttonRef:u,tabIndex:d,menuReference:h,tooltip:n})}))},6190:(e,t,n)=>{"use strict";n.d(t,{Toolbar:()=>d});var o=n(50959),r=n(50151),i=n(47201),s=n(3343),l=n(19291),a=n(57177),c=n(39416),u=n(7047);const d=(0,o.forwardRef)((function(e,t){const{onKeyDown:n,orientation:d,blurOnEscKeydown:h=!0,blurOnClick:p=!0,...f}=e,m=(0,c.useFunctionalRefObject)(t);return(0,o.useLayoutEffect)((()=>{const e=(0,r.ensureNotNull)(m.current),t=()=>{const t=(0,l.queryTabbableElements)(e).sort(l.navigationOrderComparator);if(0===t.length){const[t]=(0,l.queryFocusableElements)(e).sort(l.navigationOrderComparator);if(void 0===t)return;(0,a.becomeMainElement)(t)}if(t.length>1){const[,...e]=t;for(const t of e)(0,a.becomeSecondaryElement)(t)}};return window.addEventListener("keyboard-navigation-activation",t),()=>window.removeEventListener("keyboard-navigation-activation",t)}),[]),o.createElement("div",{...u.MouseClickAutoBlurHandler.attributes(p),...f,role:"toolbar","aria-orientation":d,ref:m,onKeyDown:(0,i.createSafeMulticastEventHandler)((function(e){if(e.defaultPrevented)return;if(!(document.activeElement instanceof HTMLElement))return;const t=(0,s.hashFromEvent)(e);if(h&&27===t)return e.preventDefault(),void document.activeElement.blur();if("vertical"!==d&&37!==t&&39!==t)return;if("vertical"===d&&38!==t&&40!==t)return;const n=e.currentTarget,o=(0,l.queryFocusableElements)(n).sort(l.navigationOrderComparator);if(0===o.length)return;const r=o.indexOf(document.activeElement);if(-1===r)return;e.preventDefault();const i=()=>{const e=(r+o.length-1)%o.length;(0,a.becomeSecondaryElement)(o[r]),(0,a.becomeMainElement)(o[e]),o[e].focus()},c=()=>{const e=(r+o.length+1)%o.length;(0,a.becomeSecondaryElement)(o[r]),(0,a.becomeMainElement)(o[e]),o[e].focus()};switch((0,l.mapKeyCodeToDirection)(t)){case"inlinePrev":"vertical"!==d&&i();break;case"inlineNext":"vertical"!==d&&c();break;case"blockPrev":"vertical"===d&&i();break;case"blockNext":"vertical"===d&&c()}}),n)})}))},60925:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12 4h3v1h-1.04l-.88 9.64a1.5 1.5 0 0 1-1.5 1.36H6.42a1.5 1.5 0 0 1-1.5-1.36L4.05 5H3V4h3v-.5C6 2.67 6.67 2 7.5 2h3c.83 0 1.5.67 1.5 1.5V4ZM7.5 3a.5.5 0 0 0-.5.5V4h4v-.5a.5.5 0 0 0-.5-.5h-3ZM5.05 5l.87 9.55a.5.5 0 0 0 .5.45h5.17a.5.5 0 0 0 .5-.45L12.94 5h-7.9Z"/></svg>'},33765:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path fill="currentColor" d="M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z"/></svg>'},
29540:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72" width="72" height="72"><path fill="currentColor" d="M15 24a21 21 0 1 1 42 0v7.41l8.97 5.01 1.08.6-.82.94-7.77 8.82 2.34 2.53-1.47 1.36L57 48.15V69H46v-7h-6v5h-9V56h-6v13H15V48.15l-2.33 2.52-1.47-1.36 2.35-2.53-7.78-8.82-.82-.93 1.08-.6L15 31.4V24Zm0 9.7-6.9 3.87L15 45.4V33.7Zm42 11.7 6.91-7.83-6.9-3.87v11.7ZM36 5a19 19 0 0 0-19 19v43h6V54h10v11h5v-5h10v7h7V24A19 19 0 0 0 36 5Zm-5 19.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0ZM42.5 26a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"/></svg>'},36296:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8 9.5H6.5a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V20m-8-1.5h11a1 1 0 0 0 1-1v-11a1 1 0 0 0-1-1h-11a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1z"/></svg>'},57674:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 11.5v8a1 1 0 0 0 1 1h15a1 1 0 0 0 1-1v-8m-17 0v-4a1 1 0 0 1 1-1h4l2 2h9a1 1 0 0 1 1 1v2m-17 0h17"/></svg>'},80465:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M5.5 6C4.67 6 4 6.67 4 7.5V20.5c0 .83.67 1.5 1.5 1.5H16v-1H5.5a.5.5 0 0 1-.5-.5V12h16v1h1V9.5c0-.83-.67-1.5-1.5-1.5h-8.8L9.86 6.15 9.71 6H5.5zM21 11H5V7.5c0-.28.22-.5.5-.5h3.8l1.85 1.85.14.15h9.21c.28 0 .5.22.5.5V11zm1 11v-3h3v-1h-3v-3h-1v3h-3v1h3v3h1z"/></svg>'},94007:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M2.448 10.124a10.82 10.82 0 0 1-.336-.609L2.105 9.5l.007-.015a12.159 12.159 0 0 1 1.686-2.466C5.002 5.665 6.752 4.373 9.05 4.373c2.297 0 4.047 1.292 5.25 2.646a12.166 12.166 0 0 1 1.687 2.466l.007.015-.007.015a12.163 12.163 0 0 1-1.686 2.466c-1.204 1.354-2.954 2.646-5.251 2.646-2.298 0-4.048-1.292-5.252-2.646a12.16 12.16 0 0 1-1.35-1.857zm14.558-.827l-.456.203.456.203v.002l-.003.005-.006.015-.025.052a11.813 11.813 0 0 1-.461.857 13.163 13.163 0 0 1-1.463 2.011c-1.296 1.46-3.296 2.982-5.998 2.982-2.703 0-4.703-1.522-6-2.982a13.162 13.162 0 0 1-1.83-2.677 7.883 7.883 0 0 1-.118-.243l-.007-.015-.002-.005v-.001l.456-.204-.456-.203v-.002l.002-.005.007-.015a4.66 4.66 0 0 1 .119-.243 13.158 13.158 0 0 1 1.83-2.677c1.296-1.46 3.296-2.982 5.999-2.982 2.702 0 4.702 1.522 5.998 2.981a13.158 13.158 0 0 1 1.83 2.678 8.097 8.097 0 0 1 .119.243l.006.015.003.005v.001zm-.456.203l.456-.203.09.203-.09.203-.456-.203zM1.092 9.297l.457.203-.457.203-.09-.203.09-.203zm9.958.203c0 1.164-.917 2.07-2 2.07-1.084 0-2-.906-2-2.07 0-1.164.916-2.07 2-2.07 1.083 0 2 .906 2 2.07zm1 0c0 1.695-1.344 3.07-3 3.07-1.657 0-3-1.375-3-3.07 0-1.695 1.343-3.07 3-3.07 1.656 0 3 1.375 3 3.07z"/></svg>'},52870:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M7 5.5a2.5 2.5 0 0 1 5 0V7H7V5.5zM6 7V5.5a3.5 3.5 0 1 1 7 0V7a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2zm8 2a1 1 0 0 0-1-1H6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V9zm-3 2.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/></svg>'},74059:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M21.106 12.5H6.894a.5.5 0 0 1-.318-.886L14 5.5l7.424 6.114a.5.5 0 0 1-.318.886zM21.106 16.5H6.894a.5.5 0 0 0-.318.886L14 23.5l7.424-6.114a.5.5 0 0 0-.318-.886z"/></svg>'},91730:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M4.605 14.089A10.052 10.052 0 0 1 4.56 14l.046-.089a17.18 17.18 0 0 1 2.329-3.327C8.58 8.758 10.954 7 14 7c3.046 0 5.421 1.757 7.066 3.585A17.18 17.18 0 0 1 23.44 14l-.046.089a17.18 17.18 0 0 1-2.329 3.327C19.42 19.242 17.046 21 14 21c-3.046 0-5.421-1.757-7.066-3.584a17.18 17.18 0 0 1-2.329-3.327zm19.848-.3L24 14l.453.212-.001.002-.003.005-.009.02a16.32 16.32 0 0 1-.662 1.195c-.44.72-1.1 1.684-1.969 2.65C20.08 20.008 17.454 22 14 22c-3.454 0-6.079-1.993-7.81-3.916a18.185 18.185 0 0 1-2.469-3.528 10.636 10.636 0 0 1-.161-.318l-.01-.019-.002-.005v-.002L4 14a55.06 55.06 0 0 1-.453-.212l.001-.002.003-.005.009-.02.033-.067a16.293 16.293 0 0 1 .629-1.126c.44-.723 1.1-1.686 1.969-2.652C7.92 7.993 10.546 6 14 6c3.454 0 6.079 1.993 7.81 3.916a18.183 18.183 0 0 1 2.469 3.528 10.588 10.588 0 0 1 .161.318l.01.019.002.005v.002zM24 14l.453-.211.099.211-.099.211L24 14zm-20.453-.211L4 14l-.453.211L3.448 14l.099-.211zM11 14a3 3 0 1 1 6 0 3 3 0 0 1-6 0zm3-4a4 4 0 1 0 0 8 4 4 0 0 0 0-8zm0 5a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/></svg>'},7295:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M4.5 12.5l4.59-4.59a2 2 0 0 1 2.83 0l3.17 3.17a2 2 0 0 0 2.83 0L22.5 6.5m-8 9.5v5.5M12 19l2.5 2.5L17 19m4.5 3v-5.5M19 19l2.5-2.5L24 19"/></svg>'},28824:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l4.586-4.586a2 2 0 0 1 2.828 0l3.172 3.172a2 2 0 0 0 2.828 0L23.5 10.5"/></svg>'},49756:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M11.5 4A2.5 2.5 0 0 0 7 5.5V7h6a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2V5.5a3.5 3.5 0 0 1 6.231-2.19c-.231.19-.73.69-.73.69zM13 8H6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1zm-2 3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/></svg>'},62766:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14.692 3.012l-12 12.277.715.699 12-12.277-.715-.699zM9.05 15.627a7.042 7.042 0 0 1-3.144-.741l.742-.76c.72.311 1.52.5 2.402.5 2.297 0 4.047-1.29 5.25-2.645a12.168 12.168 0 0 0 1.687-2.466l.007-.015-.007-.015A12.166 12.166 0 0 0 14.3 7.019c-.11-.124-.225-.247-.344-.37l.699-.715c.137.14.268.28.392.42a13.16 13.16 0 0 1 1.83 2.678 8.117 8.117 0 0 1 .119.243l.006.015.003.005v.001l-.456.204.456.203v.002l-.003.005-.006.015-.025.052a11.762 11.762 0 0 1-.461.857 13.158 13.158 0 0 1-1.463 2.011c-1.296 1.46-3.296 2.982-5.998 2.982zm7.5-6.127l.456-.203.09.203-.09.203-.456-.203zm-7.5 3.07c-.27 0-.53-.037-.778-.105l.879-.899c.999-.052 1.833-.872 1.895-1.938l.902-.923c.**************.102.795 0 1.695-1.344 3.07-3 3.07zM6.15 10.294l.902-.923c.063-1.066.896-1.886 1.895-1.938l.879-.9a2.94 2.94 0 0 0-.777-.103c-1.657 0-3 1.374-3 3.069 0 .275.035.541.101.795zM9.05 4.373c.88 0 1.68.19 2.4.5l.743-.759a7.043 7.043 0 0 0-3.143-.74c-2.703 0-4.703 1.521-6 2.98a13.159 13.159 0 0 0-1.83 2.678 7.886 7.886 0 0 0-.118.243l-.007.015-.002.005v.001l.456.204-.457-.203-.09.203.09.203.457-.203-.456.203v.002l.002.005.007.015a4.5 4.5 0 0 0 .119.243 13.152 13.152 0 0 0 1.83 2.677c.124.14.255.28.392.42l.7-.715c-.12-.122-.235-.245-.345-.369a12.156 12.156 0 0 1-1.686-2.466L2.105 9.5l.007-.015a12.158 12.158 0 0 1 1.686-2.466C5.002 5.665 6.752 4.373 9.05 4.373z"/></svg>'},39146:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" d="M9 1l2.35 4.76 5.26.77-3.8 3.7.9 5.24L9 13l-4.7 2.47.9-5.23-3.8-3.71 5.25-.77L9 1z"/></svg>'},48010:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M9 2.13l1.903 3.855.116.236.26.038 4.255.618-3.079 3.001-.188.184.044.259.727 4.237-3.805-2L9 12.434l-.233.122-3.805 2.001.727-4.237.044-.26-.188-.183-3.079-3.001 4.255-.618.26-.038.116-.236L9 2.13z"/></svg>'}}]);