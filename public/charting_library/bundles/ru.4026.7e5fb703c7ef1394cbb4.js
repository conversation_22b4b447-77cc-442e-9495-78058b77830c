(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4026],{53310:e=>{e.exports={en:["Re"],ru:["Сим."]}},94073:e=>{e.exports={en:["A"],ru:["А"]}},66384:e=>{e.exports={en:["L"],ru:["Л"]}},85119:e=>{e.exports={en:["Dark"],ru:["Тёмная"]}},96870:e=>{e.exports={en:["Light"],ru:["Светлая"]}},85886:e=>{e.exports={en:["d"],ru:["д"]}},44634:e=>{e.exports={en:["h"],ru:["ч"]}},5977:e=>{e.exports={en:["m"],ru:["м"]}},21492:e=>{e.exports={en:["s"],ru:["с"]}},97559:e=>{e.exports={en:["{title} copy"],ru:["Копия {title}"]}},38691:e=>{e.exports={en:["D"],ru:["Д"]}},77995:e=>{e.exports={en:["M"],ru:["Мес"]}},93934:e=>{e.exports={en:["R"],ru:["R"]}},82901:e=>{e.exports={en:["T"],ru:["Т"]}},7408:e=>{e.exports={en:["W"],ru:["Н"]}},38048:e=>{e.exports={en:["h"],ru:["ч"]}},68430:e=>{e.exports={en:["m"],ru:["м"]}},68823:e=>{e.exports={en:["s"],ru:["С"]}},2696:e=>{e.exports={en:["C"],ru:["ЗАКР"]}},43253:e=>{e.exports={en:["H"],ru:["МАКС"]}},61372:e=>{e.exports={en:["HL2"],ru:["Макс-Мин-2"]}},55096:e=>{e.exports={en:["HLC3"],ru:["Макс-Мин-Закр-3"]}},94174:e=>{e.exports={en:["OHLC4"],ru:["Откр-Макс-Мин-Закр-4"]}},89923:e=>{e.exports={en:["L"],ru:["МИН"]}},46728:e=>{e.exports={en:["O"],ru:["ОТКР"]}},32856:e=>{e.exports=Object.create(null),e.exports.Close_input={en:["Close"],ru:["Цена закрытия"]},e.exports.Back_input={en:["Back"],ru:["Назад"]},e.exports.Minimize_input={en:["Minimize"],ru:["Свернуть"]},e.exports["Hull MA_input"]={en:["Hull MA"],ru:["Скользящее среднее Хала"]},e.exports.from_input={en:["from"],ru:["от"]},e.exports.to_input={en:["to"],ru:["до"]},e.exports["{number} item_combobox_input"]={en:["{number} item","{number} items"],ru:["{number} критерий","{number} критерия","{number} критериев","{number} критериев"]},e.exports.Length_input={en:["Length"],ru:["Длина"]},e.exports.Plot_input={en:["Plot"],ru:["Граф.отображение"]},e.exports.Zero_input={en:["Zero"],ru:["Ноль"]},e.exports.Signal_input={en:["Signal"],ru:["Сигнал"]},e.exports.Long_input={en:["Long"],ru:["Длинная"]},e.exports.Short_input={en:["Short"],ru:["Короткая"]},e.exports.UpperLimit_input={en:["UpperLimit"],ru:["Верхн.Лимит"]},e.exports.LowerLimit_input={en:["LowerLimit"],ru:["Нижни.Лимит"]},e.exports.Offset_input={en:["Offset"],ru:["Отступ"]},e.exports.length_input={en:["length"],ru:["длина"]},e.exports.mult_input={en:["mult"],ru:["умнож."]},e.exports.short_input={en:["short"],ru:["короткая"]},e.exports.long_input={en:["long"],ru:["длинная"]},e.exports.Limit_input={en:["Limit"],ru:["Лимит"]},e.exports.Move_input={en:["Move"],ru:["Шаг"]},e.exports.Value_input={en:["Value"],ru:["Значение"]},e.exports.Method_input={en:["Method"],ru:["Метод"]},e.exports["Values in status line_input"]={en:["Values in status line"],ru:["Значения в строке статуса"]},e.exports["Labels on price scale_input"]={en:["Labels on price scale"],ru:["Метки на ценовой шкале"]},e.exports["Accumulation/Distribution_input"]={en:["Accumulation/Distribution"],ru:["Накопление/Распределение"]},e.exports.ADR_B_input={en:["ADR_B"],ru:["ADR_B"]},e.exports["Equality Line_input"]={en:["Equality Line"],
ru:["Линия равенства"]},e.exports["Window Size_input"]={en:["Window Size"],ru:["Размер окна"]},e.exports.Sigma_input={en:["Sigma"],ru:["Сигма"]},e.exports["Aroon Up_input"]={en:["Aroon Up"],ru:["Арун вверх"]},e.exports["Aroon Down_input"]={en:["Aroon Down"],ru:["Арун вниз"]},e.exports.Upper_input={en:["Upper"],ru:["Верхн."]},e.exports.Lower_input={en:["Lower"],ru:["Нижний"]},e.exports.Deviation_input={en:["Deviation"],ru:["Отклонение"]},e.exports["Levels Format_input"]={en:["Levels Format"],ru:["Формат уровней"]},e.exports["Labels Position_input"]={en:["Labels Position"],ru:["Позиция меток"]},e.exports["0 Level Color_input"]={en:["0 Level Color"],ru:["Цвет уровня 0"]},e.exports["0.236 Level Color_input"]={en:["0.236 Level Color"],ru:["Цвет уровня 0.236"]},e.exports["0.382 Level Color_input"]={en:["0.382 Level Color"],ru:["Цвет уровня 0.382"]},e.exports["0.5 Level Color_input"]={en:["0.5 Level Color"],ru:["Цвет уровня 0.5"]},e.exports["0.618 Level Color_input"]={en:["0.618 Level Color"],ru:["Цвет уровня 0.618"]},e.exports["0.65 Level Color_input"]={en:["0.65 Level Color"],ru:["Цвет уровня 0.65"]},e.exports["0.786 Level Color_input"]={en:["0.786 Level Color"],ru:["Цвет уровня 0.786"]},e.exports["1 Level Color_input"]={en:["1 Level Color"],ru:["Цвет уровня 1"]},e.exports["1.272 Level Color_input"]={en:["1.272 Level Color"],ru:["Цвет уровня 1.272"]},e.exports["1.414 Level Color_input"]={en:["1.414 Level Color"],ru:["Цвет уровня 1.414"]},e.exports["1.618 Level Color_input"]={en:["1.618 Level Color"],ru:["Цвет уровня 1.618"]},e.exports["1.65 Level Color_input"]={en:["1.65 Level Color"],ru:["Цвет уровня 1.65"]},e.exports["2.618 Level Color_input"]={en:["2.618 Level Color"],ru:["Цвет уровня 2.618"]},e.exports["2.65 Level Color_input"]={en:["2.65 Level Color"],ru:["Цвет уровня 2.65"]},e.exports["3.618 Level Color_input"]={en:["3.618 Level Color"],ru:["Цвет уровня 3.618"]},e.exports["3.65 Level Color_input"]={en:["3.65 Level Color"],ru:["Цвет уровня 3.65"]},e.exports["4.236 Level Color_input"]={en:["4.236 Level Color"],ru:["Цвет уровня 4.236"]},e.exports["-0.236 Level Color_input"]={en:["-0.236 Level Color"],ru:["Цвет уровня -0.236"]},e.exports["-0.382 Level Color_input"]={en:["-0.382 Level Color"],ru:["Цвет уровня -0.382"]},e.exports["-0.618 Level Color_input"]={en:["-0.618 Level Color"],ru:["Цвет уровня -0.618"]},e.exports["-0.65 Level Color_input"]={en:["-0.65 Level Color"],ru:["Цвет уровня -0.65"]},e.exports.ADX_input={en:["ADX"],ru:["ADX"]},e.exports["ADX Smoothing_input"]={en:["ADX Smoothing"],ru:["Сглаживание ADX"]},e.exports["DI Length_input"]={en:["DI Length"],ru:["DI Длина"]},e.exports.Smoothing_input={en:["Smoothing"],ru:["Сглаживание"]},e.exports.ATR_input={en:["ATR"],ru:["ATR"]},e.exports.Growing_input={en:["Growing"],ru:["Растущий"]},e.exports.Falling_input={en:["Falling"],ru:["Нисходящий"]},e.exports["Color 0_input"]={en:["Color 0"],ru:["Цвет 0"]},e.exports["Color 1_input"]={en:["Color 1"],ru:["Цвет 1"]},e.exports.Source_input={en:["Source"],ru:["Данные"]},e.exports.StdDev_input={en:["StdDev"],
ru:["Станд. отклон."]},e.exports.Basis_input={en:["Basis"],ru:["Базовая линия"]},e.exports.Median_input={en:["Median"],ru:["Средняя линия"]},e.exports["Bollinger Bands %B_input"]={en:["Bollinger Bands %B"],ru:["Полосы Боллинджера %B"]},e.exports.Overbought_input={en:["Overbought"],ru:["Перекуплены"]},e.exports.Oversold_input={en:["Oversold"],ru:["Перепроданы"]},e.exports["Bollinger Bands Width_input"]={en:["Bollinger Bands Width"],ru:["Ширина полос Боллинджера"]},e.exports["RSI Length_input"]={en:["RSI Length"],ru:["Длина RSI"]},e.exports["UpDown Length_input"]={en:["UpDown Length"],ru:["Длина вверх/вниз"]},e.exports["ROC Length_input"]={en:["ROC Length"],ru:["Длина ROC"]},e.exports.MF_input={en:["MF"],ru:["MF"]},e.exports.resolution_input={en:["resolution"],ru:["интервал"]},e.exports["Fast Length_input"]={en:["Fast Length"],ru:["Длина Fast"]},e.exports["Slow Length_input"]={en:["Slow Length"],ru:["Длина Slow"]},e.exports["Chaikin Oscillator_input"]={en:["Chaikin Oscillator"],ru:["Осциллятор Чайкина"]},e.exports.P_input={en:["P"],ru:["P"]},e.exports.X_input={en:["X"],ru:["X"]},e.exports.Q_input={en:["Q"],ru:["Q"]},e.exports.p_input={en:["p"],ru:["p"]},e.exports.x_input={en:["x"],ru:["x"]},e.exports.q_input={en:["q"],ru:["q"]},e.exports.Price_input={en:["Price"],ru:["Цена"]},e.exports["Chande MO_input"]={en:["Chande MO"],ru:["Осциллятор темпа Чанде"]},e.exports["Zero Line_input"]={en:["Zero Line"],ru:["Нулевая линия"]},e.exports["Color 2_input"]={en:["Color 2"],ru:["Цвет 2"]},e.exports["Color 3_input"]={en:["Color 3"],ru:["Цвет 3"]},e.exports["Color 4_input"]={en:["Color 4"],ru:["Цвет 4"]},e.exports["Color 5_input"]={en:["Color 5"],ru:["Цвет 5"]},e.exports["Color 6_input"]={en:["Color 6"],ru:["Цвет 6"]},e.exports["Color 7_input"]={en:["Color 7"],ru:["Цвет 7"]},e.exports["Color 8_input"]={en:["Color 8"],ru:["Цвет 8"]},e.exports.CHOP_input={en:["CHOP"],ru:["CHOP"]},e.exports["Upper Band_input"]={en:["Upper Band"],ru:["Верхняя полоса"]},e.exports["Lower Band_input"]={en:["Lower Band"],ru:["Нижняя полоса"]},e.exports.CCI_input={en:["CCI"],ru:["CCI"]},e.exports["Smoothing Line_input"]={en:["Smoothing Line"],ru:["Линия сглаживания"]},e.exports["Smoothing Length_input"]={en:["Smoothing Length"],ru:["Длина сглаживания"]},e.exports["WMA Length_input"]={en:["WMA Length"],ru:["Длина WMA"]},e.exports["Long RoC Length_input"]={en:["Long RoC Length"],ru:["Длина Long RoC"]},e.exports["Short RoC Length_input"]={en:["Short RoC Length"],ru:["Длина Short RoC"]},e.exports.sym_input={en:["sym"],ru:["sym"]},e.exports.Symbol_input={en:["Symbol"],ru:["Инструмент"]},e.exports.Correlation_input={en:["Correlation"],ru:["корреляция"]},e.exports.Period_input={en:["Period"],ru:["Период"]},e.exports.Centered_input={en:["Centered"],ru:["Центральный"]},e.exports["Detrended Price Oscillator_input"]={en:["Detrended Price Oscillator"],ru:["Детрендовый ценовой осциллятор"]},e.exports.isCentered_input={en:["isCentered"],ru:["По центру"]},e.exports.DPO_input={en:["DPO"],ru:["DPO"]},e.exports["ADX smoothing_input"]={en:["ADX smoothing"],
ru:["Сглаживание ADX"]},e.exports["+DI_input"]={en:["+DI"],ru:["+DI"]},e.exports["-DI_input"]={en:["-DI"],ru:["-DI"]},e.exports.DEMA_input={en:["DEMA"],ru:["DEMA"]},e.exports["Multi timeframe_input"]={en:["Multi timeframe"],ru:["Несколько интервалов"]},e.exports.Timeframe_input={en:["Timeframe"],ru:["Интервал"]},e.exports["Wait for timeframe closes_input"]={en:["Wait for timeframe closes"],ru:["Дождаться закрытия интервала"]},e.exports.Divisor_input={en:["Divisor"],ru:["Разделитель"]},e.exports.EOM_input={en:["EOM"],ru:["EOM"]},e.exports["Elder's Force Index_input"]={en:["Elder's Force Index"],ru:["Индекс силы Элдера"]},e.exports.Percent_input={en:["Percent"],ru:["Процент"]},e.exports.Exponential_input={en:["Exponential"],ru:["Экспоненциальное"]},e.exports.Average_input={en:["Average"],ru:["Среднее"]},e.exports["Upper Percentage_input"]={en:["Upper Percentage"],ru:["Верхний процент"]},e.exports["Lower Percentage_input"]={en:["Lower Percentage"],ru:["Нижний процент"]},e.exports.Fisher_input={en:["Fisher"],ru:["Fisher"]},e.exports.Trigger_input={en:["Trigger"],ru:["Условие срабатывания"]},e.exports.Level_input={en:["Level"],ru:["Уровень"]},e.exports["Trader EMA 1 length_input"]={en:["Trader EMA 1 length"],ru:["Длина EMA 1 трейдера"]},e.exports["Trader EMA 2 length_input"]={en:["Trader EMA 2 length"],ru:["Длина EMA 2 трейдера"]},e.exports["Trader EMA 3 length_input"]={en:["Trader EMA 3 length"],ru:["Длина EMA 3 трейдера"]},e.exports["Trader EMA 4 length_input"]={en:["Trader EMA 4 length"],ru:["Длина EMA 4 трейдера"]},e.exports["Trader EMA 5 length_input"]={en:["Trader EMA 5 length"],ru:["Длина EMA 5 трейдера"]},e.exports["Trader EMA 6 length_input"]={en:["Trader EMA 6 length"],ru:["Длина EMA 6 трейдера"]},e.exports["Investor EMA 1 length_input"]={en:["Investor EMA 1 length"],ru:["Длина EMA 1 инвестора"]},e.exports["Investor EMA 2 length_input"]={en:["Investor EMA 2 length"],ru:["Длина EMA 2 инвестора"]},e.exports["Investor EMA 3 length_input"]={en:["Investor EMA 3 length"],ru:["Длина EMA 3 инвестора"]},e.exports["Investor EMA 4 length_input"]={en:["Investor EMA 4 length"],ru:["Длина EMA 4 инвестора"]},e.exports["Investor EMA 5 length_input"]={en:["Investor EMA 5 length"],ru:["Длина EMA 5 инвестора"]},e.exports["Investor EMA 6 length_input"]={en:["Investor EMA 6 length"],ru:["Длина EMA 6 инвестора"]},e.exports.HV_input={en:["HV"],ru:["HV"]},e.exports["Conversion Line Periods_input"]={en:["Conversion Line Periods"],ru:["Периоды Линии переворота"]},e.exports["Base Line Periods_input"]={en:["Base Line Periods"],ru:["Периоды Линии стандарта"]},e.exports["Lagging Span_input"]={en:["Lagging Span"],ru:["Опаздывающая линия"]},e.exports["Conversion Line_input"]={en:["Conversion Line"],ru:["Линия переворота"]},e.exports["Base Line_input"]={en:["Base Line"],ru:["Линия стандарта"]},e.exports["Leading Span A_input"]={en:["Leading Span A"],ru:["Верховая линия 1"]},e.exports["Leading Span B_input"]={},e.exports["Plots Background_input"]={en:["Plots Background"],ru:["Заливка фона"]},e.exports["yay Color 0_input"]={en:["yay Color 0"],
ru:["yay Цвет 2"]},e.exports["yay Color 1_input"]={en:["yay Color 1"],ru:["yay Цвет 1"]},e.exports.Multiplier_input={en:["Multiplier"],ru:["Множитель"]},e.exports["Bands style_input"]={en:["Bands style"],ru:["Стиль полос"]},e.exports.Middle_input={en:["Middle"],ru:["Центральная линия"]},e.exports.useTrueRange_input={en:["useTrueRange"],ru:["Исп. истинный диапазон"]},e.exports.ROCLen1_input={en:["ROCLen1"],ru:["ROC Длин.1"]},e.exports.ROCLen2_input={en:["ROCLen2"],ru:["ROC Длин.2"]},e.exports.ROCLen3_input={en:["ROCLen3"],ru:["ROC Длин.3"]},e.exports.ROCLen4_input={en:["ROCLen4"],ru:["ROC Длин.4"]},e.exports.SMALen1_input={en:["SMALen1"],ru:["SMA Длин.1"]},e.exports.SMALen2_input={en:["SMALen2"],ru:["SMA Длин.2"]},e.exports.SMALen3_input={en:["SMALen3"],ru:["SMA Длин.3"]},e.exports.SMALen4_input={en:["SMALen4"],ru:["SMA Длин.4"]},e.exports.SigLen_input={en:["SigLen"],ru:["Дл.Сигн."]},e.exports.KST_input={en:["KST"],ru:["KST"]},e.exports.Sig_input={en:["Sig"],ru:["Сиг."]},e.exports.roclen1_input={en:["roclen1"],ru:["Roc длин.1"]},e.exports.roclen2_input={en:["roclen2"],ru:["Roc длин.2"]},e.exports.roclen3_input={en:["roclen3"],ru:["Roc длин.3"]},e.exports.roclen4_input={en:["roclen4"],ru:["Roc длин.4"]},e.exports.smalen1_input={en:["smalen1"],ru:["sma длин.1"]},e.exports.smalen2_input={en:["smalen2"],ru:["sma длин.2"]},e.exports.smalen3_input={en:["smalen3"],ru:["sma длин.3"]},e.exports.smalen4_input={en:["smalen4"],ru:["sma длин.4"]},e.exports.siglen_input={en:["siglen"],ru:["Длин. сигн."]},e.exports["Upper Deviation_input"]={en:["Upper Deviation"],ru:["Верхнее отклонение"]},e.exports["Lower Deviation_input"]={en:["Lower Deviation"],ru:["Нижнее отклонение"]},e.exports["Use Upper Deviation_input"]={en:["Use Upper Deviation"],ru:["Использовать верхнее отклонение"]},e.exports["Use Lower Deviation_input"]={en:["Use Lower Deviation"],ru:["Использовать нижнее отклонение"]},e.exports.Count_input={en:["Count"],ru:["Количество"]},e.exports.Crosses_input={en:["Crosses"],ru:["Пересечения"]},e.exports.MOM_input={en:["MOM"],ru:["MOM"]},e.exports.MA_input={en:["MA"],ru:["MA"]},e.exports["Length EMA_input"]={en:["Length EMA"],ru:["Длина EMA"]},e.exports["Length MA_input"]={en:["Length MA"],ru:["Длина MA"]},e.exports["Fast length_input"]={en:["Fast length"],ru:["Длина Fast"]},e.exports["Slow length_input"]={en:["Slow length"],ru:["Длина Slow"]},e.exports["Signal smoothing_input"]={en:["Signal smoothing"],ru:["Сглаживание сигнала"]},e.exports["Simple ma(oscillator)_input"]={en:["Simple ma(oscillator)"],ru:["Прост. скольз.сред. (осциллятор)"]},e.exports["Simple ma(signal line)_input"]={en:["Simple ma(signal line)"],ru:["Прост. скольз.сред. (линия сигнала)"]},e.exports.Histogram_input={en:["Histogram"],ru:["Гистограмма"]},e.exports.MACD_input={en:["MACD"],ru:["MACD"]},e.exports.fastLength_input={en:["fastLength"],ru:["fastLength"]},e.exports.slowLength_input={en:["slowLength"],ru:["slowLength"]},e.exports.signalLength_input={en:["signalLength"],ru:["Длин. сигнала"]},e.exports.NV_input={en:["NV"],ru:["NV"]},
e.exports.OnBalanceVolume_input={en:["OnBalanceVolume"],ru:["Балансовый объём"]},e.exports.Start_input={en:["Start"],ru:["Начать"]},e.exports.Increment_input={en:["Increment"],ru:["Шаг"]},e.exports["Max value_input"]={en:["Max value"],ru:["Макс. значение"]},e.exports.ParabolicSAR_input={en:["ParabolicSAR"],ru:["Параболическая система SAR"]},e.exports.start_input={en:["start"],ru:["начало"]},e.exports.increment_input={en:["increment"],ru:["шаг"]},e.exports.maximum_input={en:["maximum"],ru:["максимум"]},e.exports["Short length_input"]={en:["Short length"],ru:["Длина Short"]},e.exports["Long length_input"]={en:["Long length"],ru:["Длина Long"]},e.exports.OSC_input={en:["OSC"],ru:["OSC"]},e.exports.shortlen_input={en:["shortlen"],ru:["shortlen"]},e.exports.longlen_input={en:["longlen"],ru:["longlen"]},e.exports.PVT_input={en:["PVT"],ru:["PVT"]},e.exports.ROC_input={en:["ROC"],ru:["ROC"]},e.exports.RSI_input={en:["RSI"],ru:["RSI"]},e.exports.RVGI_input={en:["RVGI"],ru:["RVGI"]},e.exports.RVI_input={en:["RVI"],ru:["RVI"]},e.exports["Long period_input"]={en:["Long period"],ru:["Длинный период"]},e.exports["Short period_input"]={en:["Short period"],ru:["Короткий период"]},e.exports["Signal line period_input"]={en:["Signal line period"],ru:["Период линии сигнала"]},e.exports.SMI_input={en:["SMI"],ru:["SMI"]},e.exports["SMI Ergodic Oscillator_input"]={en:["SMI Ergodic Oscillator"],ru:["Осциллятор SMI Ergodic"]},e.exports.Indicator_input={en:["Indicator"],ru:["Индикатор"]},e.exports.Oscillator_input={en:["Oscillator"],ru:["Осциллятор"]},e.exports.K_input={en:["K"],ru:["K"]},e.exports.D_input={en:["D"],ru:["D"]},e.exports.smoothK_input={en:["smoothK"],ru:["smoothK"]},e.exports.smoothD_input={en:["smoothD"],ru:["smoothD"]},e.exports["%K_input"]={en:["%K"],ru:["%K"]},e.exports["%D_input"]={en:["%D"],ru:["%D"]},e.exports["Stochastic Length_input"]={en:["Stochastic Length"],ru:["Длина Стохастик"]},e.exports["RSI Source_input"]={en:["RSI Source"],ru:["Данные для RSI"]},e.exports.lengthRSI_input={en:["lengthRSI"],ru:["длина RSI"]},e.exports.lengthStoch_input={en:["lengthStoch"],ru:["длина Стох."]},e.exports.TRIX_input={en:["TRIX"],ru:["TRIX"]},e.exports.TEMA_input={en:["TEMA"],ru:["TEMA"]},e.exports["Long Length_input"]={en:["Long Length"],ru:["Длина Long"]},e.exports["Short Length_input"]={en:["Short Length"],ru:["Длина Short"]},e.exports["Signal Length_input"]={en:["Signal Length"],ru:["Длина сигнала"]},e.exports.Length1_input={en:["Length1"],ru:["Длина1"]},e.exports.Length2_input={en:["Length2"],ru:["Длина2"]},e.exports.Length3_input={en:["Length3"],ru:["Длина3"]},e.exports.length7_input={en:["length7"],ru:["длина 7"]},e.exports.length14_input={en:["length14"],ru:["длина 14"]},e.exports.length28_input={en:["length28"],ru:["длина 28"]},e.exports.UO_input={en:["UO"],ru:["UO"]},e.exports.VWMA_input={en:["VWMA"],ru:["VWMA"]},e.exports.len_input={en:["len"],ru:["длина"]},e.exports["VI +_input"]={en:["VI +"],ru:["VI +"]},e.exports["VI -_input"]={en:["VI -"],ru:["VI -"]},e.exports["%R_input"]={en:["%R"],ru:["%R"]},
e.exports["Jaw Length_input"]={en:["Jaw Length"],ru:["Длина Jaw"]},e.exports["Teeth Length_input"]={en:["Teeth Length"],ru:["Длина Teeth"]},e.exports["Lips Length_input"]={en:["Lips Length"],ru:["Длина Lips"]},e.exports.Jaw_input={en:["Jaw"],ru:["Jaw"]},e.exports.Teeth_input={en:["Teeth"],ru:["Teeth"]},e.exports.Lips_input={en:["Lips"],ru:["Lips"]},e.exports["Jaw Offset_input"]={en:["Jaw Offset"],ru:["Смещение Jaw"]},e.exports["Teeth Offset_input"]={en:["Teeth Offset"],ru:["Смещение Teeth"]},e.exports["Lips Offset_input"]={en:["Lips Offset"],ru:["Смещение Lips"]},e.exports["Down fractals_input"]={en:["Down fractals"],ru:["Нижние фракталы"]},e.exports["Up fractals_input"]={en:["Up fractals"],ru:["Верхние фракталы"]},e.exports.Periods_input={en:["Periods"],ru:["Периоды"]},e.exports.Shapes_input={en:["Shapes"],ru:["Фигуры"]},e.exports["show MA_input"]={en:["show MA"],ru:["показать MA"]},e.exports["MA Length_input"]={en:["MA Length"],ru:["Длина MA"]},e.exports["Color based on previous close_input"]={en:["Color based on previous close"],ru:["Цвет зависит от цены предыдущего закрытия"]},e.exports["Rows Layout_input"]={en:["Rows Layout"],ru:["Размещение строк"]},e.exports["Row Size_input"]={en:["Row Size"],ru:["Размер строки"]},e.exports.Volume_input={en:["Volume"],ru:["Объём"]},e.exports["Value Area volume_input"]={en:["Value Area volume"],ru:["Объём зоны стоимости"]},e.exports["Extend Right_input"]={en:["Extend Right"],ru:["Продолжить вправо"]},e.exports["Extend POC Right_input"]={en:["Extend POC Right"],ru:["Продолжить POC вправо"]},e.exports["Extend VAH Right_input"]={en:["Extend VAH Right"],ru:["Продолжить VAH вправо"]},e.exports["Extend VAL Right_input"]={en:["Extend VAL Right"],ru:["Продолжить VAL вправо"]},e.exports["Value Area Volume_input"]={en:["Value Area Volume"],ru:["Объём зоны стоимости"]},e.exports.Placement_input={en:["Placement"],ru:["Расположение"]},e.exports.POC_input={en:["POC"],ru:["POC"]},e.exports["Developing Poc_input"]={en:["Developing Poc"],ru:["Динамическая точка контроля (POC)"]},e.exports["Up Volume_input"]={en:["Up Volume"],ru:["Растущий объём"]},e.exports["Down Volume_input"]={en:["Down Volume"],ru:["Снижающийся объём"]},e.exports["Value Area_input"]={en:["Value Area"],ru:["Зона стоимости"]},e.exports["Histogram Box_input"]={en:["Histogram Box"],ru:["Прямоугольник гистограммы"]},e.exports["Value Area Up_input"]={en:["Value Area Up"],ru:["Восходящ. зона стоимости"]},e.exports["Value Area Down_input"]={en:["Value Area Down"],ru:["Нисходящ. зона стоимости"]},e.exports["Number Of Rows_input"]={en:["Number Of Rows"],ru:["Число строк"]},e.exports["Ticks Per Row_input"]={en:["Ticks Per Row"],ru:["Тики в строке"]},e.exports["Up/Down_input"]={en:["Up/Down"],ru:["Вверх/Вниз"]},e.exports.Total_input={en:["Total"],ru:["Итого"]},e.exports.Delta_input={en:["Delta"],ru:["Дельта"]},e.exports.Bar_input={en:["Bar"],ru:["Бар"]},e.exports.Day_input={en:["Day"],ru:["День"]},e.exports["Deviation (%)_input"]={en:["Deviation (%)"],ru:["Отклонение (%)"]},e.exports.Depth_input={en:["Depth"],ru:["Глубина"]},
e.exports["Extend to last bar_input"]={en:["Extend to last bar"],ru:["Продолжить до последнего бара"]},e.exports.Simple_input={en:["Simple"],ru:["Простое"]},e.exports.Weighted_input={en:["Weighted"],ru:["Взвешенное"]},e.exports["Wilder's Smoothing_input"]={en:["Wilder's Smoothing"],ru:["Сглаживание Уайлдера"]},e.exports["1st Period_input"]={en:["1st Period"],ru:["1-й период"]},e.exports["2nd Period_input"]={en:["2nd Period"],ru:["2-й период"]},e.exports["3rd Period_input"]={en:["3rd Period"],ru:["3-й период"]},e.exports["4th Period_input"]={en:["4th Period"],ru:["4-й период"]},e.exports["5th Period_input"]={en:["5th Period"],ru:["5-й период"]},e.exports["6th Period_input"]={en:["6th Period"],ru:["6-й период"]},e.exports["Rate of Change Lookback_input"]={en:["Rate of Change Lookback"],ru:["Rate of Change Lookback"]},e.exports["Instrument 1_input"]={en:["Instrument 1"],ru:["Инструмент 1"]},e.exports["Instrument 2_input"]={en:["Instrument 2"],ru:["Инструмент 2"]},e.exports["Rolling Period_input"]={en:["Rolling Period"],ru:["Rolling Period"]},e.exports["Standard Errors_input"]={en:["Standard Errors"],ru:["Стандартные ошибки"]},e.exports["Averaging Periods_input"]={en:["Averaging Periods"],ru:["Периоды усреднения"]},e.exports["Days Per Year_input"]={en:["Days Per Year"],ru:["Дней в году"]},e.exports["Market Closed Percentage_input"]={en:["Market Closed Percentage"],ru:["Market Closed Percentage"]},e.exports["ATR Mult_input"]={en:["ATR Mult"],ru:["ATR Множ."]},e.exports.VWAP_input={en:["VWAP"],ru:["VWAP"]},e.exports["Anchor Period_input"]={en:["Anchor Period"],ru:["Временной период"]},e.exports.Session_input={en:["Session"],ru:["Сессия"]},e.exports.Week_input={en:["Week"],ru:["Неделя"]},e.exports.Month_input={en:["Month"],ru:["Месяц"]},e.exports.Year_input={en:["Year"],ru:["Год"]},e.exports.Decade_input={en:["Decade"],ru:["Декада"]},e.exports.Century_input={en:["Century"],ru:["Век"]},e.exports.Sessions_input={en:["Sessions"],ru:["Сессии"]},e.exports["Each (pre-market, market, post-market)_input"]={en:["Each (pre-market, market, post-market)"],ru:["Каждая (премаркет, основная, постмаркет)"]},e.exports["Pre-market only_input"]={en:["Pre-market only"],ru:["Только премаркет"]},e.exports["Market only_input"]={en:["Market only"],ru:["Только торговая сессия"]},e.exports["Post-market only_input"]={en:["Post-market only"],ru:["Только постмаркет"]},e.exports["Main chart symbol_input"]={en:["Main chart symbol"],ru:["Главный символ графика"]},e.exports["Another symbol_input"]={en:["Another symbol"],ru:["Другой символ"]},e.exports.Line_input={en:["Line"],ru:["Линия"]},e.exports["Nothing selected_combobox_input"]={en:["Nothing selected"],ru:["Ничего не выбрано"]},e.exports["All items_combobox_input"]={en:["All items"],ru:["Все критерии"]},e.exports.Cancel_input={en:["Cancel"],ru:["Отменить"]},e.exports.Open_input={en:["Open"],ru:["Открыть"]},e.exports.MM_month_input={en:["MM"],ru:["ММ"]},e.exports.YY_year_input={en:["YY"],ru:["ГГ"]},e.exports.Style_input={en:["Style"],ru:["Стиль"]},e.exports["Box size assignment method_input"]={
en:["Box size assignment method"],ru:["Метод определения размера коробки"]},e.exports["Color bars based on previous close_input"]={en:["Color bars based on previous close"],ru:["Цвет баров основан на цене предыдущего закрытия"]},e.exports.Candles_input={en:["Candles"],ru:["Японские свечи"]},e.exports.Borders_input={en:["Borders"],ru:["Границы"]},e.exports.Wick_input={en:["Wick"],ru:["Фитиль"]},e.exports["HLC bars_input"]={en:["HLC bars"],ru:["Бары HLC"]},e.exports["Price source_input"]={en:["Price source"],ru:["Источник цены"]},e.exports.Type_input={en:["Type"],ru:["Тип"]},e.exports["Show real prices on price scale (instead of Heikin-Ashi price)_input"]={en:["Show real prices on price scale (instead of Heikin-Ashi price)"],ru:["Показывать реальные цены на ценовой шкале (вместо значений Хейкен Аши)"]},e.exports["Up bars_input"]={en:["Up bars"],ru:["Восходящие бары"]},e.exports["Down bars_input"]={en:["Down bars"],ru:["Нисходящие бары"]},e.exports["Projection up bars_input"]={en:["Projection up bars"],ru:["Проекция восходящего бара"]},e.exports["Projection down bars_input"]={en:["Projection down bars"],ru:["Проекция нисходящего бара"]},e.exports["Projection up color_input"]={en:["Projection up color"],ru:["Цвет проекции восходящего бара"]},e.exports["Projection down color_input"]={en:["Projection down color"],ru:["Цвет проекции нисходящего бара"]},e.exports.Fill_input={en:["Fill"],ru:["Заливка"]},e.exports["Up color_input"]={en:["Up color"],ru:["Цвет роста"]},e.exports["Down color_input"]={en:["Down color"],ru:["Цвет падения"]},e.exports.Traditional_input={en:["Traditional"],ru:["Традиционный"]},e.exports.PercentageLTP_input={en:["PercentageLTP"],ru:["PercentageLTP"]},e.exports["Box size_input"]={en:["Box size"],ru:["Размер коробки"]},e.exports["Number of line_input"]={en:["Number of line"],ru:["Количество линий"]},e.exports["ATR length_input"]={en:["ATR length"],ru:["Длина ATR"]},e.exports.Percentage_input={en:["Percentage"],ru:["Проценты"]},e.exports["Reversal amount_input"]={en:["Reversal amount"],ru:["Величина разворота"]},e.exports["Phantom bars_input"]={en:["Phantom bars"],ru:["Фантомные бары"]},e.exports["One step back building_input"]={en:["One step back building"],ru:["Объединять одиночные сегменты с предыдущим баром"]},e.exports.Wicks_input={en:["Wicks"],ru:["Фитили"]},e.exports.Range_input={en:["Range"],ru:["Диапазон"]},e.exports.All_input={en:["All"],ru:["Все"]},e.exports.Custom_input={en:["Custom"],ru:["Другая"]},e.exports["Leading Span Periods_input"]={en:["Leading Span Periods"],ru:["Опаздывающая линия 2 периода"]},e.exports["Leading Shift Periods_input"]={en:["Leading Shift Periods"],ru:["Периоды ведущей линии"]},e.exports["ATR({atrValue})_input"]={en:["ATR({atrValue})"],ru:["ATR({atrValue})"]},e.exports["PercentageLTP({percentageLTPValue}%)_input"]={en:["PercentageLTP({percentageLTPValue}%)"],ru:["PercentageLTP({percentageLTPValue}%)"]}},50873:e=>{e.exports={en:["ATR({atrValue})"],ru:["ATR({atrValue})"]}},28037:e=>{e.exports={en:["PercentageLTP({percentageLTPValue}%)"],
ru:["PercentageLTP({percentageLTPValue}%)"]}},40566:e=>{e.exports={en:["Traditional"],ru:["Традиционный"]}},75163:e=>{e.exports={en:["Invert scale"],ru:["Инвертировать шкалу"]}},35210:e=>{e.exports={en:["Indexed to 100"],ru:["Индексированная на 100"]}},31340:e=>{e.exports={en:["Logarithmic"],ru:["Логарифмическая"]}},19405:e=>{e.exports={en:["No overlapping labels"],ru:["Не перекрывать метки"]}},34954:e=>{e.exports={en:["Percent"],ru:["Процентная"]}},55300:e=>{e.exports={en:["Regular"],ru:["Обычная"]}},8029:e=>{e.exports={en:["ETH"],ru:["Расш."]}},34647:e=>{e.exports={en:["Electronic trading hours"],ru:["Электронная торговая сессия"]}},36862:e=>{e.exports={en:["Extended trading hours"],ru:["Торговые данные вне сессии"]}},7807:e=>{e.exports={en:["POST"],ru:["пост"]}},46273:e=>{e.exports={en:["PRE"],ru:["пре"]}},50434:e=>{e.exports={en:["Postmarket"],ru:["Постмаркет"]}},59330:e=>{e.exports={en:["Premarket"],ru:["Премаркет"]}},35342:e=>{e.exports={en:["RTH"],ru:["рег"]}},84246:e=>{e.exports={en:["Regular trading hours"],ru:["Регулярная торговая сессия"]}},13132:e=>{e.exports={en:["May"],ru:["Май"]}},83477:e=>{e.exports=Object.create(null),e.exports.Technicals_study={en:["Technicals"],ru:["Теханализ"]},e.exports["Average Day Range_study"]={en:["Average Day Range"],ru:["Средний дневной диапазон"]},e.exports["Bull Bear Power_study"]={en:["Bull Bear Power"],ru:["«Сила быков» и «Сила медведей»"]},e.exports["Capital expenditures_study"]={en:["Capital expenditures"],ru:["Капиталовложения"]},e.exports["Cash to debt ratio_study"]={en:["Cash to debt ratio"],ru:["Денежные средства/Задолженность"]},e.exports["Debt to EBITDA ratio_study"]={en:["Debt to EBITDA ratio"],ru:["Задолженность/EBITDA"]},e.exports["Directional Movement Index_study"]={en:["Directional Movement Index"],ru:["Индекс направленного движения (DMI)"]},e.exports.DMI_study={en:["DMI"],ru:["DMI"]},e.exports["Dividend payout ratio %_study"]={en:["Dividend payout ratio %"],ru:["Коэффициент выплаты дивиденда %"]},e.exports["Equity to assets ratio_study"]={en:["Equity to assets ratio"],ru:["Капитал/активы"]},e.exports["Enterprise value to EBIT ratio_study"]={en:["Enterprise value to EBIT ratio"],ru:["Стоимость компании/EBIT"]},e.exports["Enterprise value to EBITDA ratio_study"]={en:["Enterprise value to EBITDA ratio"],ru:["Стоимость компании/EBITDA"]},e.exports["Enterprise value to revenue ratio_study"]={en:["Enterprise value to revenue ratio"],ru:["Стоимость компании/Выручка"]},e.exports["Goodwill, net_study"]={en:["Goodwill, net"],ru:["Деловая репутация, нетто"]},e.exports["Ichimoku Cloud_study"]={en:["Ichimoku Cloud"],ru:["Облако Ишимоку"]},e.exports.Ichimoku_study={en:["Ichimoku"],ru:["Ишимоку"]},e.exports["Moving Average Convergence Divergence_study"]={en:["Moving Average Convergence Divergence"],ru:["Схождение/расхождение скользящих средних"]},e.exports["Operating income_study"]={en:["Operating income"],ru:["Операционные доходы"]},e.exports["Price to book ratio_study"]={en:["Price to book ratio"],ru:["Цена/балансовая стоимость"]},
e.exports["Price to cash flow ratio_study"]={en:["Price to cash flow ratio"],ru:["Цена/Движение денежных средств"]},e.exports["Price to earnings ratio_study"]={en:["Price to earnings ratio"],ru:["Цена/Прибыль"]},e.exports["Price to free cash flow ratio_study"]={en:["Price to free cash flow ratio"],ru:["Цена/Движение свободных денежных средств"]},e.exports["Price to sales ratio_study"]={en:["Price to sales ratio"],ru:["Цена/Продажи"]},e.exports["Float shares outstanding_study"]={en:["Float shares outstanding"],ru:["Количество акций доступных к купле-продаже"]},e.exports["Total common shares outstanding_study"]={en:["Total common shares outstanding"],ru:["Всего выпущено обыкновенных акций"]},e.exports["Volume Weighted Average Price_study"]={en:["Volume Weighted Average Price"],ru:["Средневзвешенная цена объёма"]},e.exports["Volume Weighted Moving Average_study"]={en:["Volume Weighted Moving Average"],ru:["Объёмно-взвешенное скользящее среднее"]},e.exports["Williams Percent Range_study"]={en:["Williams Percent Range"],ru:["Процентный диапазон Вильямса"]},e.exports.Doji_study={en:["Doji"],ru:["Доджи"]},e.exports["Spinning Top Black_study"]={en:["Spinning Top Black"],ru:["Чёрный волчок"]},e.exports["Spinning Top White_study"]={en:["Spinning Top White"],ru:["Белый волчок"]},e.exports["Accounts payable_study"]={en:["Accounts payable"],ru:["Кредиторская задолженность"]},e.exports["Accounts receivables, gross_study"]={en:["Accounts receivables, gross"],ru:["Дебиторская задолженность, валовая"]},e.exports["Accounts receivable - trade, net_study"]={en:["Accounts receivable - trade, net"],ru:["Дебиторская задолженность - торговая, нетто"]},e.exports.Accruals_study={en:["Accruals"],ru:["Начисления"]},e.exports["Accrued payroll_study"]={en:["Accrued payroll"],ru:["Задолженность по заработной плате"]},e.exports["Accumulated depreciation, total_study"]={en:["Accumulated depreciation, total"],ru:["Накопленная амортизация, итого"]},e.exports["Additional paid-in capital/Capital surplus_study"]={en:["Additional paid-in capital/Capital surplus"],ru:["Добавочный капитал"]},e.exports["After tax other income/expense_study"]={en:["After tax other income/expense"],ru:["Прочая прибыль (убыток) после налогообложения"]},e.exports["Altman Z-score_study"]={en:["Altman Z-score"],ru:["Коэффициент Альтмана"]},e.exports.Amortization_study={en:["Amortization"],ru:["Амортизация"]},e.exports["Amortization of intangibles_study"]={en:["Amortization of intangibles"],ru:["Амортизация нематериальных активов"]},e.exports["Amortization of deferred charges_study"]={en:["Amortization of deferred charges"],ru:["Амортизация расходов будущих периодов"]},e.exports["Asset turnover_study"]={en:["Asset turnover"],ru:["Оборачиваемость активов"]},e.exports["Average basic shares outstanding_study"]={en:["Average basic shares outstanding"],ru:["Среднее число акций в обращении"]},e.exports["Bad debt / Doubtful accounts_study"]={en:["Bad debt / Doubtful accounts"],ru:["Безнадёжная задолженность / Сомнительная дебиторская задолженность"]},e.exports["Basic EPS_study"]={
en:["Basic EPS"],ru:["Базовая прибыль на акцию"]},e.exports["Basic earnings per share (Basic EPS)_study"]={en:["Basic earnings per share (Basic EPS)"],ru:["Базовая прибыль на акцию"]},e.exports["Beneish M-score_study"]={en:["Beneish M-score"],ru:["Коэффициент Бениша"]},e.exports["Book value per share_study"]={en:["Book value per share"],ru:["Балансовая стоимость на акцию"]},e.exports["Buyback yield %_study"]={en:["Buyback yield %"],ru:["Доходность обратного выкупа акций, %"]},e.exports["Capital and operating lease obligations_study"]={en:["Capital and operating lease obligations"],ru:["Обязательства по финансовой и операционной аренде"]},e.exports["Capital expenditures - fixed assets_study"]={en:["Capital expenditures - fixed assets"],ru:["Капиталовложения — основные средства"]},e.exports["Capital expenditures - other assets_study"]={en:["Capital expenditures - other assets"],ru:["Капиталовложения — прочие активы"]},e.exports["Capitalized lease obligations_study"]={en:["Capitalized lease obligations"],ru:["Капитализированные арендные обязательства"]},e.exports["Cash and short term investments_study"]={en:["Cash and short term investments"],ru:["Денежные средства и краткосрочные инвестиции"]},e.exports["Cash conversion cycle_study"]={en:["Cash conversion cycle"],ru:["Цикл обращения денежных средств"]},e.exports["Cash & equivalents_study"]={en:["Cash & equivalents"],ru:["Денежные средства и их эквиваленты"]},e.exports["Cash from financing activities_study"]={en:["Cash from financing activities"],ru:["Денежные средства от финансовой деятельности"]},e.exports["Cash from investing activities_study"]={en:["Cash from investing activities"],ru:["Денежные средства от инвестиционной деятельности"]},e.exports["Cash from operating activities_study"]={en:["Cash from operating activities"],ru:["Денежные средства от операционной деятельности"]},e.exports["Change in accounts payable_study"]={en:["Change in accounts payable"],ru:["Изменения в кредиторской задолженности"]},e.exports["Change in accounts receivable_study"]={en:["Change in accounts receivable"],ru:["Изменение дебиторской задолженности"]},e.exports["Change in accrued expenses_study"]={en:["Change in accrued expenses"],ru:["Изменения в начисленных расходах"]},e.exports["Change in inventories_study"]={en:["Change in inventories"],ru:["Изменения в запасах"]},e.exports["Change in other assets/liabilities_study"]={en:["Change in other assets/liabilities"],ru:["Изменения в других активах/обязательствах"]},e.exports["Change in taxes payable_study"]={en:["Change in taxes payable"],ru:["Изменения в налогах к оплате"]},e.exports["Changes in working capital_study"]={en:["Changes in working capital"],ru:["Изменения оборотного капитала"]},e.exports["COGS to revenue ratio_study"]={en:["COGS to revenue ratio"],ru:["Себестоимость реализованной продукции/Выручка"]},e.exports["Common dividends paid_study"]={en:["Common dividends paid"],ru:["Выплачено дивидендов по обыкновенным акциям"]},e.exports["Common equity, total_study"]={en:["Common equity, total"],ru:["Основной капитал, итого"]},
e.exports["Common stock par/Carrying value_study"]={en:["Common stock par/Carrying value"],ru:["Учётная стоимость обыкновенных акций"]},e.exports["Cost of goods_study"]={en:["Cost of goods"],ru:["Себестоимость"]},e.exports["Cost of goods sold_study"]={en:["Cost of goods sold"],ru:["Себестоимость реализованных товаров"]},e.exports["Current portion of LT debt and capital leases_study"]={en:["Current portion of LT debt and capital leases"],ru:["Текущая часть долгосрочной зад-ти и обяз-в по аренде"]},e.exports["Current ratio_study"]={en:["Current ratio"],ru:["Коэффициент текущей ликвидности"]},e.exports["Days inventory_study"]={en:["Days inventory"],ru:["Период оборачиваемости запасов"]},e.exports["Days payable_study"]={en:["Days payable"],ru:["Период погашения кредиторской задолженности"]},e.exports["Days sales outstanding_study"]={en:["Days sales outstanding"],ru:["Период погашения дебиторской задолженности"]},e.exports["Debt to assets ratio_study"]={en:["Debt to assets ratio"],ru:["Задолженность/Активы"]},e.exports["Debt to equity ratio_study"]={en:["Debt to equity ratio"],ru:["Задолженность/Капитал"]},e.exports["Debt to revenue ratio_study"]={en:["Debt to revenue ratio"],ru:["Задолженность/Выручка"]},e.exports["Deferred income, current_study"]={en:["Deferred income, current"],ru:["Доходы будущих периодов, оборотные"]},e.exports["Deferred income, non-current_study"]={en:["Deferred income, non-current"],ru:["Доходы будущих периодов, внеоборотные"]},e.exports["Deferred tax assets_study"]={en:["Deferred tax assets"],ru:["Отложенные налоговые активы"]},e.exports["Deferred taxes (cash flow)_study"]={en:["Deferred taxes (cash flow)"],ru:["Отложенный налог (движение денежных средств)"]},e.exports["Deferred tax liabilities_study"]={en:["Deferred tax liabilities"],ru:["Отложенные налоговые обязательства"]},e.exports.Depreciation_study={en:["Depreciation"],ru:["Амортизация"]},e.exports["Deprecation and amortization_study"]={en:["Deprecation and amortization"],ru:["Износ и амортизация"]},e.exports["Depreciation & amortization (cash flow)_study"]={en:["Depreciation & amortization (cash flow)"],ru:["Амортизация (движение денежных средств)"]},e.exports["Depreciation/depletion_study"]={en:["Depreciation/depletion"],ru:["Амортизация/износ"]},e.exports["Diluted EPS_study"]={en:["Diluted EPS"],ru:["Разводнённая прибыль на акцию"]},e.exports["Diluted earnings per share (Diluted EPS)_study"]={en:["Diluted earnings per share (Diluted EPS)"],ru:["Разводнённая прибыль на акцию"]},e.exports["Diluted net income available to common stockholders_study"]={en:["Diluted net income available to common stockholders"],ru:["Разводн. чистая прибыль доступная владельцам обыкновенных акций"]},e.exports["Diluted shares outstanding_study"]={en:["Diluted shares outstanding"],ru:["Разводнённые акции в обращении"]},e.exports["Dilution adjustment_study"]={en:["Dilution adjustment"],ru:["Корректировка на разводнение"]},e.exports["Discontinued operations_study"]={en:["Discontinued operations"],ru:["Прекращённые операции"]},e.exports["Dividends payable_study"]={
en:["Dividends payable"],ru:["Дивиденды к выплате"]},e.exports["Dividends per share - common stock primary issue_study"]={en:["Dividends per share - common stock primary issue"],ru:["Дивиденды на акцию — первичный выпуск обыкновенных акций"]},e.exports["Dividend yield %_study"]={en:["Dividend yield %"],ru:["Дивидендная доходность, %"]},e.exports["Earnings yield_study"]={en:["Earnings yield"],ru:["Доходность акции по прибыли"]},e.exports.EBIT_study={en:["EBIT"],ru:["EBIT"]},e.exports.EBITDA_study={en:["EBITDA"],ru:["EBITDA (прибыль до вычета процентов, налогов и амортизации)"]},e.exports["EBITDA margin %_study"]={en:["EBITDA margin %"],ru:["Рентабельность по EBITDA %"]},e.exports["Effective interest rate on debt %_study"]={en:["Effective interest rate on debt %"],ru:["Действующая процентная ставка по задолж-ти, %"]},e.exports["Enterprise value_study"]={en:["Enterprise value"],ru:["Стоимость компании"]},e.exports["EPS basic one year growth_study"]={en:["EPS basic one year growth"],ru:["Базовая приб./акцию — годовой рост"]},e.exports["EPS diluted one year growth_study"]={en:["EPS diluted one year growth"],ru:["Разводн. приб./акцию — годовой рост"]},e.exports["EPS estimates_study"]={en:["EPS estimates"],ru:["Оценка приб./акцию"]},e.exports["Equity in earnings_study"]={en:["Equity in earnings"],ru:["Доходы от участия в других организациях"]},e.exports["Financing activities – other sources_study"]={en:["Financing activities – other sources"],ru:["Финансовая деятельность — прочие источники поступлений"]},e.exports["Financing activities – other uses_study"]={en:["Financing activities – other uses"],ru:["Финансовая деятельность — прочие направления расходования"]},e.exports["Free cash flow_study"]={en:["Free cash flow"],ru:["Движение свободных денежных средств"]},e.exports["Free cash flow margin %_study"]={en:["Free cash flow margin %"],ru:["Маржа свободных денежных средств %"]},e.exports["Fulmer H factor_study"]={en:["Fulmer H factor"],ru:["Фактор Fulmer H"]},e.exports["Funds from operations_study"]={en:["Funds from operations"],ru:["Средства от основной деятельности"]},e.exports["Goodwill to assets ratio_study"]={en:["Goodwill to assets ratio"],ru:["Репутация/активы"]},e.exports["Graham's number_study"]={en:["Graham's number"],ru:["Число Грэма"]},e.exports["Gross margin %_study"]={en:["Gross margin %"],ru:["Валовая рентабельность %"]},e.exports["Gross profit_study"]={en:["Gross profit"],ru:["Валовая прибыль"]},e.exports["Gross profit to assets ratio_study"]={en:["Gross profit to assets ratio"],ru:["Валовая прибыль/активы"]},e.exports["Gross property/plant/equipment_study"]={en:["Gross property/plant/equipment"],ru:["Основные средства, брутто"]},e.exports.Impairments_study={en:["Impairments"],ru:["Обесценение активов"]},e.exports["Income Tax Credits_study"]={en:["Income Tax Credits"],ru:["Вычет по налогу на прибыль"]},e.exports["Income tax, current_study"]={en:["Income tax, current"],ru:["Налог на прибыль, текущий"]},e.exports["Income tax, current - domestic_study"]={en:["Income tax, current - domestic"],
ru:["Налог на прибыль, текущий — внутренний"]},e.exports["Income Tax, current - foreign_study"]={en:["Income Tax, current - foreign"],ru:["Налог на прибыль, текущий — иностранный"]},e.exports["Income tax, deferred_study"]={en:["Income tax, deferred"],ru:["Налог на прибыль, отложенный"]},e.exports["Income tax, deferred - domestic_study"]={en:["Income tax, deferred - domestic"],ru:["Налог на прибыль, отложенный — внутренний"]},e.exports["Income tax, deferred - foreign_study"]={en:["Income tax, deferred - foreign"],ru:["Налог на прибыль, отложенный — иностранный"]},e.exports["Income tax payable_study"]={en:["Income tax payable"],ru:["Налог на прибыль к уплате"]},e.exports["Interest capitalized_study"]={en:["Interest capitalized"],ru:["Капитализированные проценты"]},e.exports["Interest coverage_study"]={en:["Interest coverage"],ru:["Процентное покрытие"]},e.exports["Interest expense, net of interest capitalized_study"]={en:["Interest expense, net of interest capitalized"],ru:["Проценты к уплате за вычетом капитализированных процентов"]},e.exports["Interest expense on debt_study"]={en:["Interest expense on debt"],ru:["Проценты к уплате"]},e.exports["Inventories - finished goods_study"]={en:["Inventories - finished goods"],ru:["Запасы — готовая продукция"]},e.exports["Inventories - progress payments & other_study"]={en:["Inventories - progress payments & other"],ru:["Запасы — постепенные платежи и другое"]},e.exports["Inventories - raw materials_study"]={en:["Inventories - raw materials"],ru:["Запасы — сырье и материалы"]},e.exports["Inventories - work in progress_study"]={en:["Inventories - work in progress"],ru:["Запасы — незавершенное производство"]},e.exports["Inventory to revenue ratio_study"]={en:["Inventory to revenue ratio"],ru:["Запасы/Выручка"]},e.exports["Inventory turnover_study"]={en:["Inventory turnover"],ru:["Оборачиваемость запасов"]},e.exports["Investing activities – other sources_study"]={en:["Investing activities – other sources"],ru:["Инвестиционная деятельность — прочие источники"]},e.exports["Investing activities – other uses_study"]={en:["Investing activities – other uses"],ru:["Инвестиционная деятельность — прочие направления"]},e.exports["Investments in unconsolidated subsidiaries_study"]={en:["Investments in unconsolidated subsidiaries"],ru:["Инвестиции в неконсолидированные дочерние компании"]},e.exports["Issuance of long term debt_study"]={en:["Issuance of long term debt"],ru:["Начисление долгосрочной задолж-ти"]},e.exports["Issuance/retirement of debt, net_study"]={en:["Issuance/retirement of debt, net"],ru:["Начисление/погашение задолж-ти, нетто"]},e.exports["Issuance/retirement of long term debt_study"]={en:["Issuance/retirement of long term debt"],ru:["Начисление/погашение долгосрочной задолжен-ти"]},e.exports["Issuance/retirement of other debt_study"]={en:["Issuance/retirement of other debt"],ru:["Начисление/погашение прочей задолжен-ти"]},e.exports["Issuance/retirement of short term debt_study"]={en:["Issuance/retirement of short term debt"],
ru:["Начисление/погашение краткосрочной задолжен-ти"]},e.exports["Issuance/retirement of stock, net_study"]={en:["Issuance/retirement of stock, net"],ru:["Выпуск/выкуп и аннулирование акций, нетто"]},e.exports["KZ index_study"]={en:["KZ index"],ru:["Индекс KZ"]},e.exports["Legal claim expense_study"]={en:["Legal claim expense"],ru:["Расходы по судебным разбирательствам"]},e.exports["Long term debt_study"]={en:["Long term debt"],ru:["Долгосрочная задолженность"]},e.exports["Long term debt excl. lease liabilities_study"]={en:["Long term debt excl. lease liabilities"],ru:["Долгосрочная задолж-ть без обязательств по аренде"]},e.exports["Long term debt to total assets ratio_study"]={en:["Long term debt to total assets ratio"],ru:["Долгосрочная задолж./совокупные активы"]},e.exports["Long term debt to total equity ratio_study"]={en:["Long term debt to total equity ratio"],ru:["Долгосрочная задолж./совокуп. капитал"]},e.exports["Long term investments_study"]={en:["Long term investments"],ru:["Долгосрочные инвестиции"]},e.exports["Market capitalization_study"]={en:["Market capitalization"],ru:["Рыночная капитализация"]},e.exports["Minority interest_study"]={en:["Minority interest"],ru:["Миноритарный пакет акций"]},e.exports["Miscellaneous non-operating expense_study"]={en:["Miscellaneous non-operating expense"],ru:["Прочие внеоперационные расходы"]},e.exports["Net current asset value per share_study"]={en:["Net current asset value per share"],ru:["Чистая стоимость оборотных активов на акцию"]},e.exports["Net debt_study"]={en:["Net debt"],ru:["Чистая задолженность"]},e.exports["Net income_study"]={en:["Net income"],ru:["Чистая прибыль (убыток)"]},e.exports["Net income before discontinued operations_study"]={en:["Net income before discontinued operations"],ru:["Чистая прибыль (убыток) от прекращения деятельности"]},e.exports["Net income (cash flow)_study"]={en:["Net income (cash flow)"],ru:["Чистая прибыль (движение денежных средств)"]},e.exports["Net income per employee_study"]={en:["Net income per employee"],ru:["Чистая прибыль на одного работника"]},e.exports["Net intangible assets_study"]={en:["Net intangible assets"],ru:["Чистые нематериальные активы"]},e.exports["Net margin %_study"]={en:["Net margin %"],ru:["Рентабельность по чистой прибыли, %"]},e.exports["Net property/plant/equipment_study"]={en:["Net property/plant/equipment"],ru:["Основные средства, нетто"]},e.exports["Non-cash items_study"]={en:["Non-cash items"],ru:["Неденежные статьи"]},e.exports["Non-controlling/minority interest_study"]={en:["Non-controlling/minority interest"],ru:["Неконтрольный/миноритарный пакет акций"]},e.exports["Non-operating income, excl. interest expenses_study"]={en:["Non-operating income, excl. interest expenses"],ru:["Внеоперационные доходы за вычетом процентов к уплате"]},e.exports["Non-operating income, total_study"]={en:["Non-operating income, total"],ru:["Внеоперационные доходы, итого"]},e.exports["Non-operating interest income_study"]={en:["Non-operating interest income"],
ru:["Проценты к получению по внеоперационной деятельности"]},e.exports["Note receivable - long term_study"]={en:["Note receivable - long term"],ru:["Вексель к получению — долгосрочный"]},e.exports["Notes payable_study"]={en:["Notes payable"],ru:["Векселя к оплате"]},e.exports["Number of employees_study"]={en:["Number of employees"],ru:["Количество сотрудников"]},e.exports["Number of shareholders_study"]={en:["Number of shareholders"],ru:["Количество акционеров"]},e.exports["Operating earnings yield %_study"]={en:["Operating earnings yield %"],ru:["Доходность акции по операционной прибыли, %"]},e.exports["Operating expenses (excl. COGS)_study"]={en:["Operating expenses (excl. COGS)"],ru:["Операционные расходы (без себестоимости реализованной продукции)"]},e.exports["Operating lease liabilities_study"]={en:["Operating lease liabilities"],ru:["Обязательства по операционной аренде"]},e.exports["Operating margin %_study"]={en:["Operating margin %"],ru:["Операционная рентабельность %"]},e.exports["Other COGS_study"]={en:["Other COGS"],ru:["Прочая себестоимость реализованной продукции"]},e.exports["Other common equity_study"]={en:["Other common equity"],ru:["Прочий основной капитал"]},e.exports["Other current assets, total_study"]={en:["Other current assets, total"],ru:["Прочие оборотные активы, итог"]},e.exports["Other current liabilities_study"]={en:["Other current liabilities"],ru:["Прочие текущие обязательства"]},e.exports["Other cost of goods sold_study"]={en:["Other cost of goods sold"],ru:["Прочая себестоимость реализованных товаров"]},e.exports["Other exceptional charges_study"]={en:["Other exceptional charges"],ru:["Другие исключительные расходы"]},e.exports["Other financing cash flow items, total_study"]={en:["Other financing cash flow items, total"],ru:["Общее движение денежных средств от прочей финансовой деятельности"]},e.exports["Other intangibles, net_study"]={en:["Other intangibles, net"],ru:["Прочие нематериальные активы, нетто"]},e.exports["Other investing cash flow items, total_study"]={en:["Other investing cash flow items, total"],ru:["Общее движение денежных средств от прочей инвестиционной деятельности"]},e.exports["Other investments_study"]={en:["Other investments"],ru:["Прочие инвестиции"]},e.exports["Other liabilities, total_study"]={en:["Other liabilities, total"],ru:["Прочие обязательства, итого"]},e.exports["Other long term assets, total_study"]={en:["Other long term assets, total"],ru:["Другие долгосрочные активы, итого"]},e.exports["Other non-current liabilities, total_study"]={en:["Other non-current liabilities, total"],ru:["Прочие долгосрочные обязательства, итого"]},e.exports["Other operating expenses, total_study"]={en:["Other operating expenses, total"],ru:["Другие операционные расходы, итого"]},e.exports["Other receivables_study"]={en:["Other receivables"],ru:["Прочая дебиторская задолженность"]},e.exports["Other short term debt_study"]={en:["Other short term debt"],ru:["Прочая краткосрочная задолж-ть"]},e.exports["Paid in capital_study"]={en:["Paid in capital"],
ru:["Оплаченная часть акционерного капитала"]},e.exports["PEG ratio_study"]={en:["PEG ratio"],ru:["Коэффициент PEG"]},e.exports["Piotroski F-score_study"]={en:["Piotroski F-score"],ru:["Коэффициент Пиотровски"]},e.exports["Preferred dividends_study"]={en:["Preferred dividends"],ru:["Дивиденды по привилегированным акциям"]},e.exports["Preferred dividends paid_study"]={en:["Preferred dividends paid"],ru:["Выплачено дивидендов по привилегированным акциям"]},e.exports["Preferred stock, carrying value_study"]={en:["Preferred stock, carrying value"],ru:["Привилегированные акции, учётная стоимость"]},e.exports["Prepaid expenses_study"]={en:["Prepaid expenses"],ru:["Авансы полученные"]},e.exports["Pretax equity in earnings_study"]={en:["Pretax equity in earnings"],ru:["Доходы от участия в других организациях до вычета налогов"]},e.exports["Pretax income_study"]={en:["Pretax income"],ru:["Прибыль (убыток) до налогообложения"]},e.exports["Price earnings ratio forward_study"]={en:["Price earnings ratio forward"],ru:["Соотношение цена/прибыль за будущие периоды"]},e.exports["Price sales ratio forward_study"]={en:["Price sales ratio forward"],ru:["Соотношение цена/объем продаж за будущие периоды"]},e.exports["Price to tangible book ratio_study"]={en:["Price to tangible book ratio"],ru:["Цена/материальная балансовая стоимость"]},e.exports["Provision for risks & charge_study"]={en:["Provision for risks & charge"],ru:["Резерв на покрытие рисков и расходов"]},e.exports["Purchase/acquisition of business_study"]={en:["Purchase/acquisition of business"],ru:["Покупка/приобретение бизнеса"]},e.exports["Purchase of investments_study"]={en:["Purchase of investments"],ru:["Приобретение финансовых вложений"]},e.exports["Purchase/sale of business, net_study"]={en:["Purchase/sale of business, net"],ru:["Покупка/продажа бизнеса, нетто"]},e.exports["Purchase/sale of investments, net_study"]={en:["Purchase/sale of investments, net"],ru:["Приобретение/выбытие финансовых вложений, нетто"]},e.exports["Quality ratio_study"]={en:["Quality ratio"],ru:["Коэфф. эффективности использования активов"]},e.exports["Quick ratio_study"]={en:["Quick ratio"],ru:["Коэффициент быстрой ликвидности"]},e.exports["Reduction of long term debt_study"]={en:["Reduction of long term debt"],ru:["Сокращение долгосрочной задолж-ти"]},e.exports["Repurchase of common & preferred stock_study"]={en:["Repurchase of common & preferred stock"],ru:["Выкуп обыкновенных и привилегированных акций"]},e.exports["Research & development_study"]={en:["Research & development"],ru:["Расходы на исследование и разработку"]},e.exports["Research & development to revenue ratio_study"]={en:["Research & development to revenue ratio"],ru:["Расходы на исследование и разработку/выручка"]},e.exports["Restructuring charge_study"]={en:["Restructuring charge"],ru:["Расходы на реструктуризацию"]},e.exports["Retained earnings_study"]={en:["Retained earnings"],ru:["Нераспределённая прибыль"]},e.exports["Return on assets %_study"]={en:["Return on assets %"],ru:["Коэффициент рентабельности активов, %"]},
e.exports["Return on equity %_study"]={en:["Return on equity %"],ru:["Коэфф. рентабельности собственного капитала, %"]},e.exports["Return on equity adjusted to book value %_study"]={en:["Return on equity adjusted to book value %"],ru:["Коэф. рентабельности собственного капитала с поправкой на баланс. стоимость, %"]},e.exports["Return on invested capital %_study"]={en:["Return on invested capital %"],ru:["Доход на инвестированный капитал, %"]},e.exports["Return on tangible assets %_study"]={en:["Return on tangible assets %"],ru:["Прибыль на общ. сумму материальных активов, %"]},e.exports["Return on tangible equity %_study"]={en:["Return on tangible equity %"],ru:["Доход на материальный капитал, %"]},e.exports["Revenue estimates_study"]={en:["Revenue estimates"],ru:["Расчёт выручки"]},e.exports["Revenue one year growth_study"]={en:["Revenue one year growth"],ru:["Годовой рост выручки"]},e.exports["Revenue per employee_study"]={en:["Revenue per employee"],ru:["Выручка на одного работника"]},e.exports["Sale/maturity of investments_study"]={en:["Sale/maturity of investments"],ru:["Продажа/погашение финансовых вложений"]},e.exports["Sale of common & preferred stock_study"]={en:["Sale of common & preferred stock"],ru:["Продажа обыкновенных и привилегированных акций"]},e.exports["Sale of fixed assets & businesses_study"]={en:["Sale of fixed assets & businesses"],ru:["Продажа основных средств и бизнеса"]},e.exports["Selling/general/admin expenses, other_study"]={en:["Selling/general/admin expenses, other"],ru:["Коммерческие, общие и управленческие расходы, прочие"]},e.exports["Selling/general/admin expenses, total_study"]={en:["Selling/general/admin expenses, total"],ru:["Коммерческие, общие и управленческие расходы, итого"]},e.exports["Shareholders' equity_study"]={en:["Shareholders' equity"],ru:["Акционерный капитал"]},e.exports["Shares buyback ratio %_study"]={en:["Shares buyback ratio %"],ru:["Коэффициент выкупа акций %"]},e.exports["Short term debt_study"]={en:["Short term debt"],ru:["Краткосрочная задолж-ть"]},e.exports["Short term debt excl. current portion of LT debt_study"]={en:["Short term debt excl. current portion of LT debt"],ru:["Краткосрочная зад-ть без текущей части долгосрочной зад-ти"]},e.exports["Short term investments_study"]={en:["Short term investments"],ru:["Краткосрочные инвестиции"]},e.exports["Sloan ratio %_study"]={en:["Sloan ratio %"],ru:["Коэффициент Слоуна"]},e.exports["Springate score_study"]={en:["Springate score"],ru:["Показатель Спрингейта"]},e.exports["Sustainable growth rate_study"]={en:["Sustainable growth rate"],ru:["Темп устойчивого роста"]},e.exports["Tangible book value per share_study"]={en:["Tangible book value per share"],ru:["Балансовая стоимость материальных активов на акцию"]},e.exports["Tangible common equity ratio_study"]={en:["Tangible common equity ratio"],ru:["Капитал за вычетом нематериальных активов к общим материальным активам"]},e.exports.Taxes_study={en:["Taxes"],ru:["Налоги"]},e.exports["Tobin's Q (approximate)_study"]={en:["Tobin's Q (approximate)"],
ru:["Коэффициент Тобина (приблизительно)"]},e.exports["Total assets_study"]={en:["Total assets"],ru:["Итого активы"]},e.exports["Total cash dividends paid_study"]={en:["Total cash dividends paid"],ru:["Всего дивидендов, выплаченных наличными"]},e.exports["Total current assets_study"]={en:["Total current assets"],ru:["Итого оборотные активы"]},e.exports["Total current liabilities_study"]={en:["Total current liabilities"],ru:["Общая сумма текущих обязательств"]},e.exports["Total debt_study"]={en:["Total debt"],ru:["Итого задолженность"]},e.exports["Total equity_study"]={en:["Total equity"],ru:["Итого собственный капитал"]},e.exports["Total inventory_study"]={en:["Total inventory"],ru:["Итого товарно-материальные запасы"]},e.exports["Total liabilities_study"]={en:["Total liabilities"],ru:["Общая сумма обязательств"]},e.exports["Total liabilities & shareholders' equities_study"]={en:["Total liabilities & shareholders' equities"],ru:["Общая сумма обязательств и акционерного капитала"]},e.exports["Total non-current assets_study"]={en:["Total non-current assets"],ru:["Итого внеоборотные активы"]},e.exports["Total non-current liabilities_study"]={en:["Total non-current liabilities"],ru:["Итого долгосрочные обязательства"]},e.exports["Total operating expenses_study"]={en:["Total operating expenses"],ru:["Итого операционные расходы"]},e.exports["Total receivables, net_study"]={en:["Total receivables, net"],ru:["Общая сумма дебиторской задолженности, нетто"]},e.exports["Total revenue_study"]={en:["Total revenue"],ru:["Общая выручка"]},e.exports["Treasury stock - common_study"]={en:["Treasury stock - common"],ru:["Выкупленные собственные обыкновенные акции"]},e.exports["Unrealized gain/loss_study"]={en:["Unrealized gain/loss"],ru:["Нереализованная прибыль/убыток"]},e.exports["Unusual income/expense_study"]={en:["Unusual income/expense"],ru:["Нестандартные доходы/расходы"]},e.exports["Zmijewski score_study"]={en:["Zmijewski score"],ru:["Показатель Zmijewski"]},e.exports["Valuation ratios_study"]={en:["Valuation ratios"],ru:["Отношение рыночной цены акции к номиналу"]},e.exports["Profitability ratios_study"]={en:["Profitability ratios"],ru:["Коэффициенты рентабельности"]},e.exports["Liquidity ratios_study"]={en:["Liquidity ratios"],ru:["Коэффициенты ликвидности"]},e.exports["Solvency ratios_study"]={en:["Solvency ratios"],ru:["Коэффициенты платежеспособности"]},e.exports["Key stats_study"]={en:["Key stats"],ru:["Основные данные"]},e.exports["Accumulation/Distribution_study"]={en:["Accumulation/Distribution"],ru:["Накопление/Распределение"]},e.exports["Accumulative Swing Index_study"]={en:["Accumulative Swing Index"],ru:["Кумулятивный индекс колебаний"]},e.exports["Advance/Decline_study"]={en:["Advance/Decline"],ru:["Рост/падение"]},e.exports["All Chart Patterns_study"]={en:["All Chart Patterns"],ru:["Все графические паттерны"]},e.exports["Arnaud Legoux Moving Average_study"]={en:["Arnaud Legoux Moving Average"],ru:["Скользящее среднее Арно Легу"]},e.exports.Aroon_study={en:["Aroon"],ru:["Арун"]},e.exports.ASI_study={en:["ASI"],
ru:["ASI"]},e.exports["Average Directional Index_study"]={en:["Average Directional Index"],ru:["Индикатор среднего направленного движения (ADX)"]},e.exports["Average True Range_study"]={en:["Average True Range"],ru:["Средний истинный диапазон"]},e.exports["Awesome Oscillator_study"]={en:["Awesome Oscillator"],ru:["Чудесный осциллятор Билла Вильямса"]},e.exports["Balance of Power_study"]={en:["Balance of Power"],ru:["Баланс силы"]},e.exports["Bollinger Bands %B_study"]={en:["Bollinger Bands %B"],ru:["Полосы Боллинджера %B"]},e.exports["Bollinger Bands Width_study"]={en:["Bollinger Bands Width"],ru:["Ширина полос Боллинджера"]},e.exports["Bollinger Bands_study"]={en:["Bollinger Bands"],ru:["Полосы Боллинджера"]},e.exports["Chaikin Money Flow_study"]={en:["Chaikin Money Flow"],ru:["Денежный поток Чайкина"]},e.exports["Chaikin Oscillator_study"]={en:["Chaikin Oscillator"],ru:["Осциллятор Чайкина"]},e.exports["Chande Kroll Stop_study"]={en:["Chande Kroll Stop"],ru:["Chande Kroll Stop"]},e.exports["Chande Momentum Oscillator_study"]={en:["Chande Momentum Oscillator"],ru:["Моментум-осциллятор Чанде"]},e.exports["Chop Zone_study"]={en:["Chop Zone"],ru:["Индикатор Chop Zone"]},e.exports["Choppiness Index_study"]={en:["Choppiness Index"],ru:["Индекс переменчивости"]},e.exports["Commodity Channel Index_study"]={en:["Commodity Channel Index"],ru:["Индекс товарного канала"]},e.exports["Connors RSI_study"]={en:["Connors RSI"],ru:["RSI Коннора"]},e.exports["Coppock Curve_study"]={en:["Coppock Curve"],ru:["Кривая Коппока"]},e.exports["Correlation Coefficient_study"]={en:["Correlation Coefficient"],ru:["Коэффициент корреляции"]},e.exports.CRSI_study={en:["CRSI"],ru:["CRSI"]},e.exports["Detrended Price Oscillator_study"]={en:["Detrended Price Oscillator"],ru:["Детрендовый ценовой осциллятор"]},e.exports["Directional Movement_study"]={en:["Directional Movement"],ru:["Индекс направленного движения"]},e.exports["Donchian Channels_study"]={en:["Donchian Channels"],ru:["Канал Дончиана"]},e.exports["Double EMA_study"]={en:["Double EMA"],ru:["Двойное экспоненц. скользящ. средн."]},e.exports["Ease Of Movement_study"]={en:["Ease Of Movement"],ru:["Легкость движения"]},e.exports["Elder Force Index_study"]={en:["Elder Force Index"],ru:["Индекс силы Элдера"]},e.exports["EMA Cross_study"]={en:["EMA Cross"],ru:["Пересечение экспоненц. скользящих средних"]},e.exports.Envelopes_study={en:["Envelopes"],ru:["Конверты"]},e.exports["Fisher Transform_study"]={en:["Fisher Transform"],ru:["Индикатор Fisher Transform"]},e.exports["Fixed Range_study"]={en:["Fixed Range"],ru:["Фиксированный диапазон"]},e.exports["Fixed Range Volume Profile_study"]={en:["Fixed Range Volume Profile"],ru:["Фиксированный профиль объема"]},e.exports["Guppy Multiple Moving Average_study"]={en:["Guppy Multiple Moving Average"],ru:["Множественное скользящее среднее Гуппи"]},e.exports["Historical Volatility_study"]={en:["Historical Volatility"],ru:["Историческая волатильность"]},e.exports["Hull Moving Average_study"]={en:["Hull Moving Average"],ru:["Скользящее среднее Хала"]},
e.exports["Keltner Channels_study"]={en:["Keltner Channels"],ru:["Канал Кельтнера"]},e.exports["Klinger Oscillator_study"]={en:["Klinger Oscillator"],ru:["Осциллятор Клингера"]},e.exports["Know Sure Thing_study"]={en:["Know Sure Thing"],ru:["Знать наверняка"]},e.exports["Least Squares Moving Average_study"]={en:["Least Squares Moving Average"],ru:["Скользящее среднее (наименьшие квадраты)"]},e.exports["Linear Regression Curve_study"]={en:["Linear Regression Curve"],ru:["Кривая линейной регрессии"]},e.exports["MA Cross_study"]={en:["MA Cross"],ru:["Пересечение скользящих средних"]},e.exports["MA with EMA Cross_study"]={en:["MA with EMA Cross"],ru:["MA с пересеч. EMA"]},e.exports["MA/EMA Cross_study"]={en:["MA/EMA Cross"],ru:["Пересечение MA/EMA"]},e.exports.MACD_study={en:["MACD"],ru:["MACD"]},e.exports["Mass Index_study"]={en:["Mass Index"],ru:["Индекс массы"]},e.exports["McGinley Dynamic_study"]={en:["McGinley Dynamic"],ru:["Динамический индикатор МакГинли"]},e.exports.Median_study={en:["Median"],ru:["Средняя линия"]},e.exports.Momentum_study={en:["Momentum"],ru:["Моментум (Momentum)"]},e.exports["Money Flow_study"]={en:["Money Flow"],ru:["Денежный поток"]},e.exports["Moving Average Channel_study"]={en:["Moving Average Channel"],ru:["Канал скользящей средней"]},e.exports["Moving Average Exponential_study"]={en:["Moving Average Exponential"],ru:["Скользящее среднее (эксп.)"]},e.exports["Moving Average Weighted_study"]={en:["Moving Average Weighted"],ru:["Взвешенное скользящее среднее"]},e.exports["Moving Average Simple_study"]={en:["Moving Average Simple"],ru:["Простое скользящее среднее"]},e.exports["Net Volume_study"]={en:["Net Volume"],ru:["Чистый объём"]},e.exports["On Balance Volume_study"]={en:["On Balance Volume"],ru:["Балансовый объём"]},e.exports["Parabolic SAR_study"]={en:["Parabolic SAR"],ru:["Параболическая система времени/цены"]},e.exports["Pivot Points Standard_study"]={en:["Pivot Points Standard"],ru:["Стандартные точки разворота"]},e.exports["Periodic Volume Profile_study"]={en:["Periodic Volume Profile"],ru:["Профиль объёма за период"]},e.exports["Price Channel_study"]={en:["Price Channel"],ru:["Ценовой канал"]},e.exports["Price Oscillator_study"]={en:["Price Oscillator"],ru:["Осциллятор цены"]},e.exports["Price Volume Trend_study"]={en:["Price Volume Trend"],ru:["Тренд цены и объёма"]},e.exports["Rate Of Change_study"]={en:["Rate Of Change"],ru:["Скорость изменения цены"]},e.exports["Relative Strength Index_study"]={en:["Relative Strength Index"],ru:["Индекс относительной силы"]},e.exports["Relative Vigor Index_study"]={en:["Relative Vigor Index"],ru:["Индекс относительной бодрости"]},e.exports["Relative Volatility Index_study"]={en:["Relative Volatility Index"],ru:["Относительный индекс волатильности"]},e.exports["Relative Volume at Time_study"]={en:["Relative Volume at Time"],ru:["Относительный объём в момент времени"]},e.exports["Session Volume_study"]={en:["Session Volume"],ru:["Объём за сессию"]},e.exports["Session Volume HD_study"]={en:["Session Volume HD"],ru:["Объём за сессию HD"]},
e.exports["Session Volume Profile_study"]={en:["Session Volume Profile"],ru:["Профиль объёма за сессию"]},e.exports["Session Volume Profile HD_study"]={en:["Session Volume Profile HD"],ru:["Профиль объёма за сессию HD"]},e.exports["SMI Ergodic Indicator/Oscillator_study"]={en:["SMI Ergodic Indicator/Oscillator"],ru:["Индикатор/Осциллятор SMI Ergodic"]},e.exports["Smoothed Moving Average_study"]={en:["Smoothed Moving Average"],ru:["Сглаженное скользящее среднее"]},e.exports["Stochastic Momentum Index_study"]={en:["Stochastic Momentum Index"],ru:["Индекс стохастического моментума"]},e.exports.Stoch_study={en:["Stoch"],ru:["Стох."]},e.exports["Stochastic RSI_study"]={en:["Stochastic RSI"],ru:["Стохастический индекс относительной силы"]},e.exports.Stochastic_study={en:["Stochastic"],ru:["Стохастический осциллятор"]},e.exports["Time Weighted Average Price_study"]={en:["Time Weighted Average Price"],ru:["Средневзвешенная по времени цена"]},e.exports["Triple EMA_study"]={en:["Triple EMA"],ru:["Скользящее среднее (тройное эксп.)"]},e.exports.TRIX_study={en:["TRIX"],ru:["Скользящее среднее (тройное эксп. сглаженное)"]},e.exports["True Strength Indicator_study"]={en:["True Strength Indicator"],ru:["Индекс истинной силы"]},e.exports["Ultimate Oscillator_study"]={en:["Ultimate Oscillator"],ru:["Окончательный осциллятор"]},e.exports["Visible Range_study"]={en:["Visible Range"],ru:["Видимая область"]},e.exports["Visible Range Volume Profile_study"]={en:["Visible Range Volume Profile"],ru:["Профиль объёма видимой области"]},e.exports["Volume Oscillator_study"]={en:["Volume Oscillator"],ru:["Осциллятор объёма"]},e.exports.Volume_study={en:["Volume"],ru:["Объём"]},e.exports.Vol_study={en:["Vol"],ru:["Объём"]},e.exports["Vortex Indicator_study"]={en:["Vortex Indicator"],ru:["Индикатор Vortex"]},e.exports.VWAP_study={en:["VWAP"],ru:["VWAP"]},e.exports.VWMA_study={en:["VWMA"],ru:["Скользящее среднее, взвешенное по объёму"]},e.exports["Williams %R_study"]={en:["Williams %R"],ru:["Процентный диапазон Вильямса (%R)"]},e.exports["Williams Alligator_study"]={en:["Williams Alligator"],ru:["Аллигатор Билла Вильямса"]},e.exports["Williams Fractal_study"]={en:["Williams Fractal"],ru:["Фракталы Билла Вильямса"]},e.exports["Zig Zag_study"]={en:["Zig Zag"],ru:["ЗигЗаг"]},e.exports["24-hour Volume_study"]={en:["24-hour Volume"],ru:["Объём за 24 часа"]},e.exports["Ease of Movement_study"]={en:["Ease of Movement"],ru:["Легкость движения"]},e.exports["Elders Force Index_study"]={en:["Elders Force Index"],ru:["Индекс Силы Элдера"]},e.exports.Envelope_study={en:["Envelope"],ru:["Конверт"]},e.exports.Gaps_study={en:["Gaps"],ru:["Гэпы"]},e.exports["Linear Regression Channel_study"]={en:["Linear Regression Channel"],ru:["Линейный регрессионный канал"]},e.exports["Moving Average Ribbon_study"]={en:["Moving Average Ribbon"],ru:["Лента скользящего среднего"]},e.exports["Multi-Time Period Charts_study"]={en:["Multi-Time Period Charts"],ru:["Multi-Time Period Charts"]},e.exports["Open Interest_study"]={en:["Open Interest"],ru:["Сумма открытых позиций"]},
e.exports["Rob Booker - Intraday Pivot Points_study"]={en:["Rob Booker - Intraday Pivot Points"],ru:["Роб Букер — Внутридневные точки разворота"]},e.exports["Rob Booker - Knoxville Divergence_study"]={en:["Rob Booker - Knoxville Divergence"],ru:["Роб Букер — Отклонение Ноксвилла"]},e.exports["Rob Booker - Missed Pivot Points_study"]={en:["Rob Booker - Missed Pivot Points"],ru:["Роб Букер — Пропущенные точки разворота"]},e.exports["Rob Booker - Reversal_study"]={en:["Rob Booker - Reversal"],ru:["Роб Букер — Разворот"]},e.exports["Rob Booker - Ziv Ghost Pivots_study"]={en:["Rob Booker - Ziv Ghost Pivots"],ru:["Роб Букер — точки разворота Ziv Ghost"]},e.exports.Supertrend_study={en:["Supertrend"],ru:["Супертренд"]},e.exports["Technical Ratings_study"]={en:["Technical Ratings"],ru:["Tехнический индикатор рынка"]},e.exports["True Strength Index_study"]={en:["True Strength Index"],ru:["Индекс истинной силы"]},e.exports["Up/Down Volume_study"]={en:["Up/Down Volume"],ru:["Предельный объем (по верхней/нижней границе)"]},e.exports["Visible Average Price_study"]={en:["Visible Average Price"],ru:["Видимая средняя цена"]},e.exports["Williams Fractals_study"]={en:["Williams Fractals"],ru:["Фракталы Вильямса"]},e.exports["Keltner Channels Strategy_study"]={en:["Keltner Channels Strategy"],ru:["Канал Кельтнера — стратегия"]},e.exports["Rob Booker - ADX Breakout_study"]={en:["Rob Booker - ADX Breakout"],ru:["Роб Букер — Прорыв ADX"]},e.exports["Supertrend Strategy_study"]={en:["Supertrend Strategy"],ru:["Супертренд — стратегия"]},e.exports["Technical Ratings Strategy_study"]={en:["Technical Ratings Strategy"],ru:["Tехнический индикатор рынка — стратегия"]},e.exports["Auto Anchored Volume Profile_study"]={en:["Auto Anchored Volume Profile"],ru:["Профиль объёма, Auto Anchored"]},e.exports["Auto Fib Extension_study"]={en:["Auto Fib Extension"],ru:["Автокоррекция по Фибоначчи"]},e.exports["Auto Fib Retracement_study"]={en:["Auto Fib Retracement"],ru:["Автокоррекция по Фибоначчи"]},e.exports["Auto Pitchfork_study"]={en:["Auto Pitchfork"],ru:["Вилы (Авто)"]},e.exports["Bearish Flag Chart Pattern_study"]={en:["Bearish Flag Chart Pattern"],ru:["Паттерн Медвежий флаг"]},e.exports["Bullish Flag Chart Pattern_study"]={en:["Bullish Flag Chart Pattern"],ru:["Паттерн Бычий флаг"]},e.exports["Bearish Pennant Chart Pattern_study"]={en:["Bearish Pennant Chart Pattern"],ru:["Паттерн Медвежий вымпел"]},e.exports["Bullish Pennant Chart Pattern_study"]={en:["Bullish Pennant Chart Pattern"],ru:["Паттерн Бычий вымпел"]},e.exports["Double Bottom Chart Pattern_study"]={en:["Double Bottom Chart Pattern"],ru:["Паттерн Двойное дно"]},e.exports["Double Top Chart Pattern_study"]={en:["Double Top Chart Pattern"],ru:["Паттерн Двойная вершина"]},e.exports["Elliott Wave Chart Pattern_study"]={en:["Elliott Wave Chart Pattern"],ru:["Паттерн Волны Эллиотта"]},e.exports["Falling Wedge Chart Pattern_study"]={en:["Falling Wedge Chart Pattern"],ru:["Паттерн Нисходящий клин"]},e.exports["Head And Shoulders Chart Pattern_study"]={},
e.exports["Inverse Head And Shoulders Chart Pattern_study"]={},e.exports["Rectangle Chart Pattern_study"]={en:["Rectangle Chart Pattern"],ru:["Паттерн Прямоугольник"]},e.exports["Rising Wedge Chart Pattern_study"]={en:["Rising Wedge Chart Pattern"],ru:["Паттерн Восходящий клин"]},e.exports["Triangle Chart Pattern_study"]={en:["Triangle Chart Pattern"],ru:["Паттерн Треугольник"]},e.exports["Triple Bottom Chart Pattern_study"]={en:["Triple Bottom Chart Pattern"],ru:["Паттерн Тройное дно"]},e.exports["Triple Top Chart Pattern_study"]={en:["Triple Top Chart Pattern"],ru:["Паттерн Тройная вершина"]},e.exports["VWAP Auto Anchored_study"]={en:["VWAP Auto Anchored"],ru:["VWAP Auto Anchored"]},e.exports["*All Candlestick Patterns*_study"]={en:["*All Candlestick Patterns*"],ru:["*Все паттерны японских свечей*"]},e.exports["Abandoned Baby - Bearish_study"]={en:["Abandoned Baby - Bearish"],ru:["Медвежий Брошенный младенец"]},e.exports["Abandoned Baby - Bullish_study"]={en:["Abandoned Baby - Bullish"],ru:["Бычий Брошенный младенец"]},e.exports["Dark Cloud Cover - Bearish_study"]={en:["Dark Cloud Cover - Bearish"],ru:["Завеса из тёмных облаков — медвежья"]},e.exports["Doji Star - Bearish_study"]={en:["Doji Star - Bearish"],ru:["Медвежий Доджи"]},e.exports["Doji Star - Bullish_study"]={en:["Doji Star - Bullish"],ru:["Бычий Доджи"]},e.exports["Downside Tasuki Gap - Bearish_study"]={en:["Downside Tasuki Gap - Bearish"],ru:["Разрыв Тасуки вниз — медвежий"]},e.exports["Dragonfly Doji - Bullish_study"]={en:["Dragonfly Doji - Bullish"],ru:["Бычья Доджи-стрекоза"]},e.exports["Engulfing - Bearish_study"]={en:["Engulfing - Bearish"],ru:["Медвежье Поглощение"]},e.exports["Engulfing - Bullish_study"]={en:["Engulfing - Bullish"],ru:["Бычье Поглощение"]},e.exports["Evening Doji Star - Bearish_study"]={en:["Evening Doji Star - Bearish"],ru:["Медвежья Вечерняя звезда Доджи"]},e.exports["Evening Star - Bearish_study"]={en:["Evening Star - Bearish"],ru:["Медвежья вечерняя звезда"]},e.exports["Falling Three Methods - Bearish_study"]={en:["Falling Three Methods - Bearish"],ru:["Медвежий Метод трёх нисходящих"]},e.exports["Falling Window - Bearish_study"]={en:["Falling Window - Bearish"],ru:["Медвежье нисходящее окно"]},e.exports["Gravestone Doji - Bearish_study"]={en:["Gravestone Doji - Bearish"],ru:["Могильный камень Доджи — медвежий"]},e.exports["Hammer - Bullish_study"]={en:["Hammer - Bullish"],ru:["Бычий Молот"]},e.exports["Hanging Man - Bearish_study"]={en:["Hanging Man - Bearish"],ru:["Медвежий Повешенный"]},e.exports["Harami - Bearish_study"]={en:["Harami - Bearish"],ru:["Медвежий Харами"]},e.exports["Harami - Bullish_study"]={en:["Harami - Bullish"],ru:["Бычий Харами"]},e.exports["Harami Cross - Bearish_study"]={en:["Harami Cross - Bearish"],ru:["Медвежий Крест Харами"]},e.exports["Harami Cross - Bullish_study"]={en:["Harami Cross - Bullish"],ru:["Бычий Крест Харами"]},e.exports["Inverted Hammer - Bullish_study"]={en:["Inverted Hammer - Bullish"],ru:["Бычий Перевёрнутый молот"]},e.exports["Kicking - Bearish_study"]={en:["Kicking - Bearish"],
ru:["Медвежий Кикер"]},e.exports["Kicking - Bullish_study"]={en:["Kicking - Bullish"],ru:["Бычий Кикер"]},e.exports["Long Lower Shadow - Bullish_study"]={en:["Long Lower Shadow - Bullish"],ru:["Длинная тень снизу — бычья"]},e.exports["Long Upper Shadow - Bearish_study"]={en:["Long Upper Shadow - Bearish"],ru:["Длинная тень сверху — медвежья"]},e.exports["Marubozu Black - Bearish_study"]={en:["Marubozu Black - Bearish"],ru:["Медвежий Чёрный Марубозу"]},e.exports["Marubozu White - Bullish_study"]={en:["Marubozu White - Bullish"],ru:["Бычий Белый Марубозу"]},e.exports["Morning Doji Star - Bullish_study"]={en:["Morning Doji Star - Bullish"],ru:["Утренняя звезда Доджи — бычья"]},e.exports["Morning Star - Bullish_study"]={en:["Morning Star - Bullish"],ru:["Бычья Утренняя звезда"]},e.exports["On Neck - Bearish_study"]={en:["On Neck - Bearish"],ru:["На шее — медвежий"]},e.exports["Piercing - Bullish_study"]={en:["Piercing - Bullish"],ru:["Бычий Просвет в облаках"]},e.exports["Rising Three Methods - Bullish_study"]={en:["Rising Three Methods - Bullish"],ru:["Метод трёх восходящих — бычий"]},e.exports["Rising Window - Bullish_study"]={en:["Rising Window - Bullish"],ru:["Бычье Восходящее окно"]},e.exports["Shooting Star - Bearish_study"]={en:["Shooting Star - Bearish"],ru:["Медвежья падающая звезда"]},e.exports["Three Black Crows - Bearish_study"]={en:["Three Black Crows - Bearish"],ru:["Три черные вороны — медвежьи"]},e.exports["Three White Soldiers - Bullish_study"]={en:["Three White Soldiers - Bullish"],ru:["Три белых солдата — бычьи"]},e.exports["Tri-Star - Bearish_study"]={en:["Tri-Star - Bearish"],ru:["Три звезды — медвежьи"]},e.exports["Tri-Star - Bullish_study"]={en:["Tri-Star - Bullish"],ru:["Три звезды — бычьи"]},e.exports["Tweezer Top - Bearish_study"]={en:["Tweezer Top - Bearish"],ru:["Верхний Пинцет — медвежий"]},e.exports["Upside Tasuki Gap - Bullish_study"]={en:["Upside Tasuki Gap - Bullish"],ru:["Верхний гэп Тасуки — бычий"]},e.exports.SuperTrend_study={en:["SuperTrend"],ru:["SuperTrend"]},e.exports["Average Price_study"]={en:["Average Price"],ru:["Средняя цена"]},e.exports["Typical Price_study"]={en:["Typical Price"],ru:["Типичная цена"]},e.exports["Median Price_study"]={en:["Median Price"],ru:["Медианная цена"]},e.exports["Money Flow Index_study"]={en:["Money Flow Index"],ru:["Индекс денежного потока"]},e.exports["Moving Average Double_study"]={en:["Moving Average Double"],ru:["Двойное скользящее среднее"]},e.exports["Moving Average Triple_study"]={en:["Moving Average Triple"],ru:["Тройное скользящее среднее"]},e.exports["Moving Average Adaptive_study"]={en:["Moving Average Adaptive"],ru:["Адаптивное скользящее среднее"]},e.exports["Moving Average Hamming_study"]={en:["Moving Average Hamming"],ru:["Скользящее среднее Хэмминга"]},e.exports["Moving Average Modified_study"]={en:["Moving Average Modified"],ru:["Модифицированное скользящее среднее"]},e.exports["Moving Average Multiple_study"]={en:["Moving Average Multiple"],ru:["Множественное скользящее среднее"]},e.exports["Linear Regression Slope_study"]={
en:["Linear Regression Slope"],ru:["Наклон линейной регрессии"]},e.exports["Standard Error_study"]={en:["Standard Error"],ru:["Стандартная ошибка"]},e.exports["Standard Error Bands_study"]={en:["Standard Error Bands"],ru:["Полосы стандартных ошибок"]},e.exports["Correlation - Log_study"]={en:["Correlation - Log"],ru:["Корреляция - Лог."]},e.exports["Standard Deviation_study"]={en:["Standard Deviation"],ru:["Стандартное отклонение"]},e.exports["Chaikin Volatility_study"]={en:["Chaikin Volatility"],ru:["Индикатор волатильности Чайкина"]},e.exports["Volatility Close-to-Close_study"]={en:["Volatility Close-to-Close"],ru:["Волатильность Close-to-Close"]},e.exports["Volatility Zero Trend Close-to-Close_study"]={en:["Volatility Zero Trend Close-to-Close"],ru:["Volatility Zero Trend Close-to-Close"]},e.exports["Volatility O-H-L-C_study"]={en:["Volatility O-H-L-C"],ru:["Волатильность O-H-L-C"]},e.exports["Volatility Index_study"]={en:["Volatility Index"],ru:["Индекс волатильности"]},e.exports["Trend Strength Index_study"]={en:["Trend Strength Index"],ru:["Индекс силы тренда"]},e.exports["Majority Rule_study"]={en:["Majority Rule"],ru:["Правило большинства"]},e.exports["Advance Decline Line_study"]={en:["Advance Decline Line"],ru:["Линия роста/падения"]},e.exports["Advance Decline Ratio_study"]={en:["Advance Decline Ratio"],ru:["Коэффициент роста/падения"]},e.exports["Advance/Decline Ratio (Bars)_study"]={en:["Advance/Decline Ratio (Bars)"],ru:["Коэффициент роста/падения (Бары)"]},e.exports["BarUpDn Strategy_study"]={en:["BarUpDn Strategy"],ru:["Стратегия BarUpDn"]},e.exports["Bollinger Bands Strategy directed_study"]={en:["Bollinger Bands Strategy directed"],ru:["Стратегия Полосы Боллинджера направленная"]},e.exports["Bollinger Bands Strategy_study"]={en:["Bollinger Bands Strategy"],ru:["Стратегия Полосы Боллинджера"]},e.exports.ChannelBreakOutStrategy_study={en:["ChannelBreakOutStrategy"],ru:["Стратегия Channel BreakOut"]},e.exports.Compare_study={en:["Compare"],ru:["Сравнить"]},e.exports["Conditional Expressions_study"]={en:["Conditional Expressions"],ru:["Условные выражения"]},e.exports.ConnorsRSI_study={en:["ConnorsRSI"],ru:["RSI Коннора"]},e.exports["Consecutive Up/Down Strategy_study"]={en:["Consecutive Up/Down Strategy"],ru:["Стратегия Consecutive Up/Down"]},e.exports["Cumulative Volume Index_study"]={en:["Cumulative Volume Index"],ru:["Кумулятивный индекс объёма"]},e.exports["Divergence Indicator_study"]={en:["Divergence Indicator"],ru:["Индикатор расхождения"]},e.exports["Greedy Strategy_study"]={en:["Greedy Strategy"],ru:["Стратегия Greedy"]},e.exports["InSide Bar Strategy_study"]={en:["InSide Bar Strategy"],ru:["Стратегия Inside Bar"]},e.exports["Keltner Channel Strategy_study"]={en:["Keltner Channel Strategy"],ru:["Стратегия Канал Кельтнера"]},e.exports["Linear Regression_study"]={en:["Linear Regression"],ru:["Кривая линейной регрессии"]},e.exports["MACD Strategy_study"]={en:["MACD Strategy"],ru:["Стратегия MACD"]},e.exports["Momentum Strategy_study"]={en:["Momentum Strategy"],ru:["Стратегия моментум (Momentum)"]
},e.exports["Moon Phases_study"]={en:["Moon Phases"],ru:["Фазы Луны"]},e.exports["Moving Average Convergence/Divergence_study"]={en:["Moving Average Convergence/Divergence"],ru:["Схождение/расхождение скользящих средних"]},e.exports["MovingAvg Cross_study"]={en:["MovingAvg Cross"],ru:["Пересечение скользящих средних"]},e.exports["MovingAvg2Line Cross_study"]={en:["MovingAvg2Line Cross"],ru:["Пересечение 2 линий скользящих средних"]},e.exports["OutSide Bar Strategy_study"]={en:["OutSide Bar Strategy"],ru:["Стратегия OutSide Bar"]},e.exports.Overlay_study={en:["Overlay"],ru:["Поверх основной серии"]},e.exports["Parabolic SAR Strategy_study"]={en:["Parabolic SAR Strategy"],ru:["Параболическая остановка и разворот (SAR)"]},e.exports["Pivot Extension Strategy_study"]={en:["Pivot Extension Strategy"],ru:["Стратегия Pivot Extension"]},e.exports["Pivot Points High Low_study"]={en:["Pivot Points High Low"],ru:["Контрольные точки разворота"]},e.exports["Pivot Reversal Strategy_study"]={en:["Pivot Reversal Strategy"],ru:["Стратегия контрольной точки разворота"]},e.exports["Price Channel Strategy_study"]={en:["Price Channel Strategy"],ru:["Стратегия ценовых каналов"]},e.exports["RSI Strategy_study"]={en:["RSI Strategy"],ru:["Стратегия RSI"]},e.exports["SMI Ergodic Indicator_study"]={en:["SMI Ergodic Indicator"],ru:["Индикатор SMI Ergodic"]},e.exports["SMI Ergodic Oscillator_study"]={en:["SMI Ergodic Oscillator"],ru:["Осциллятор SMI Ergodic"]},e.exports["Stochastic Slow Strategy_study"]={en:["Stochastic Slow Strategy"],ru:["Стратегия медленный стохастик"]},e.exports["Volatility Stop_study"]={en:["Volatility Stop"],ru:["Стоп по волатильности"]},e.exports["Volty Expan Close Strategy_study"]={en:["Volty Expan Close Strategy"],ru:["Стратегия Volty Expan Close"]},e.exports["Woodies CCI_study"]={en:["Woodies CCI"],ru:["Вуди CCI"]},e.exports["Anchored Volume Profile_study"]={en:["Anchored Volume Profile"],ru:["Профиль объёма, Anchored"]},e.exports["Trading Sessions_study"]={en:["Trading Sessions"],ru:["Торговые сессии"]},e.exports["Cup and Handle Chart Pattern_study"]={en:["Cup and Handle Chart Pattern"],ru:["Паттерн Чашка с ручкой"]},e.exports["Inverted Cup and Handle Chart Pattern_study"]={en:["Inverted Cup and Handle Chart Pattern"],ru:["Паттерн Перевёрнутая чашка с ручкой"]},e.exports["Head and Shoulders Chart Pattern_study"]={en:["Head and Shoulders Chart Pattern"],ru:["Паттерн Голова и плечи"]},e.exports["Inverted Head and Shoulders Chart Pattern_study"]={en:["Inverted Head and Shoulders Chart Pattern"],ru:["Паттерн Перевернутые голова и плечи"]}},89633:e=>{e.exports={en:["Anchored Volume Profile"],ru:["Профиль объёма, Anchored"]}},25705:e=>{e.exports={en:["Fixed Range Volume Profile"],ru:["Фиксированный профиль объема"]}},24261:e=>{e.exports={en:["Vol"],ru:["Объём"]}},51077:e=>{e.exports={en:["Minor"],ru:["Второстепенная"]}},922:e=>{e.exports={en:["Minute"],ru:["Минута"]}},91405:e=>{e.exports={en:["Text"],ru:["Текст"]}},78972:e=>{e.exports={en:["Couldn't copy"],ru:["Не удалось скопировать"]}},10615:e=>{e.exports={
en:["Couldn't cut"],ru:["Не удалось вырезать"]}},81518:e=>{e.exports={en:["Couldn't paste"],ru:["Не удалось вставить"]}},83140:e=>{e.exports={en:["Countdown to bar close"],ru:["Обратный отсчёт до закрытия бара"]}},10871:e=>{e.exports={en:["Colombo"],ru:["Коломбо"]}},55761:e=>{e.exports={en:["Columns"],ru:["Столбцы"]}},9818:e=>{e.exports={en:["Comment"],ru:["Комментарий"]}},53942:e=>{e.exports={en:["Compare or Add Symbol"],ru:["Сравнить/Добавить"]}},12086:e=>{e.exports={en:["Compilation error"],ru:["Ошибка компиляции"]}},48141:e=>{e.exports={en:["Confirm Inputs"],ru:["Подтвердить аргументы"]}},38917:e=>{e.exports={en:["Copenhagen"],ru:["Копенгаген"]}},49680:e=>{e.exports={en:["Copy"],ru:["Копировать"]}},66134:e=>{e.exports={en:["Copy Chart Layout"],ru:["Сохранить график как"]}},63553:e=>{e.exports={en:["Copy price"],ru:["Копировать цену"]}},65736:e=>{e.exports={en:["Cairo"],ru:["Каир"]}},25381:e=>{e.exports={en:["Callout"],ru:["Сноска"]}},45054:e=>{e.exports={en:["Candles"],ru:["Японские свечи"]}},30948:e=>{e.exports={en:["Caracas"],ru:["Каракас"]}},70409:e=>{e.exports={en:["Casablanca"],ru:["Касабланка"]}},37276:e=>{e.exports={en:["Change"],ru:["Изменение"]}},85124:e=>{e.exports={en:["Change Symbol"],ru:["Сменить инструмент"]}},2569:e=>{e.exports={en:["Change interval"],ru:["Изменить интервал"]}},9687:e=>{e.exports={en:["Change interval. Press number or comma"],ru:["Изменить интервал. Нажмите на число или запятую"]}},36332:e=>{e.exports={en:["Change symbol. Start typing symbol name"],ru:["Сменить инструмент. Начните вводить имя инструмента"]}},48566:e=>{e.exports={en:["Change scale currency"],ru:["Сменить валюту шкалы"]}},85110:e=>{e.exports={en:["Change scale unit"],ru:["Сменить единицу шкалы"]}},56275:e=>{e.exports={en:["Chart #{index}"],ru:["График #{index}"]}},39950:e=>{e.exports={en:["Chart Properties"],ru:["Свойства графика"]}},98856:e=>{e.exports={en:["Chart by TradingView"],ru:["График от TradingView"]}},1136:e=>{e.exports={en:["Chart for {symbol}, {interval}"],ru:["График {symbol}, {interval}"]}},69804:e=>{e.exports={en:["Chart image copied to clipboard {emoji}"],ru:["Изображение графика скопировано {emoji}"]}},6655:e=>{e.exports={en:["Chart image embed code copied to clipboard {emoji}"],ru:["Код изображения графика для вставки скопирован в буфер обмена {emoji}"]}},36549:e=>{e.exports={en:["Chatham Islands"],ru:["Чатем"]}},72452:e=>{e.exports={en:["Chicago"],ru:["Чикаго"]}},50349:e=>{e.exports={en:["Chongqing"],ru:["Чунцин"]}},91944:e=>{e.exports={en:["Circle"],ru:["Окружность"]}},14985:e=>{e.exports={en:["Click to set a point"],ru:["Кликните, чтобы установить точку"]}},12537:e=>{e.exports={en:["Clone"],ru:["Клонировать"]}},62578:e=>{e.exports={en:["Close"],ru:["Цена закр."]}},264:e=>{e.exports={en:["Create limit order"],ru:["Создать лимитную заявку"]}},6969:e=>{e.exports={en:["Cross"],ru:["Перекрестие"]}},74334:e=>{e.exports={en:["Cross Line"],ru:["Перекрещенные линии"]}},59396:e=>{e.exports={en:["Currencies"],ru:["Валюты"]}},20177:e=>{e.exports={en:["Current interval and above"],ru:["Текущий и выше"]}},
494:e=>{e.exports={en:["Current interval and below"],ru:["Текущий и ниже"]}},60668:e=>{e.exports={en:["Current interval only"],ru:["Только текущий"]}},78609:e=>{e.exports={en:["Curve"],ru:["Кривая"]}},87380:e=>{e.exports={en:["Cycle"],ru:["Цикл"]}},84031:e=>{e.exports={en:["Cyclic Lines"],ru:["Разделение циклов"]}},93191:e=>{e.exports={en:["Cypher Pattern"],ru:["Паттерн Cypher"]}},7219:e=>{e.exports={en:["A layout with that name already exists"],ru:["График с таким именем уже существует"]}},67635:e=>{e.exports={en:["A layout with that name already exists. Do you want to overwrite it?"],ru:["График с таким именем уже существует. Хотите переименовать?"]}},46712:e=>{e.exports={en:["ABCD Pattern"],ru:["Шаблон ABCD"]}},36485:e=>{e.exports={en:["Amsterdam"],ru:["Амстердам"]}},42630:e=>{e.exports={en:["Anchorage"],ru:["Анкоридж"]}},63209:e=>{e.exports={en:["Anchored Note"],ru:["Заметка на экране"]}},42669:e=>{e.exports={en:["Anchored Text"],ru:["Текст на экране"]}},84541:e=>{e.exports={en:["Anchored VWAP"],ru:["Anchored VWAP"]}},77401:e=>{e.exports={en:["Access error"],ru:["Ошибка доступа"]}},46501:e=>{e.exports={en:["Add Symbol"],ru:["Добавить"]}},69709:e=>{e.exports={en:["Add alert on {title}"],ru:["Добавить оповещение для {title}"]}},89295:e=>{e.exports={en:["Add alert on {title} at {price}"],ru:["Добавить оповещение для {title} на {price}"]}},2439:e=>{e.exports={en:["Add financial metric for {instrumentName}"],ru:["Добавить данные отчётности для {instrumentName}"]}},35088:e=>{e.exports={en:["Add indicator/strategy on {studyTitle}"],ru:["Добавить индикатор/стратегию на {studyTitle}"]}},39873:e=>{e.exports={en:["Add text note for {symbol}"],ru:["Добавить текстовую заметку для {symbol}"]}},35679:e=>{e.exports={en:["Add this financial metric to entire layout"],ru:["Добавить этот фин. показатель на все графики в окне"]}},10996:e=>{e.exports={en:["Add this financial metric to favorites"],ru:["Добавить этот показатель в Избранное"]}},26090:e=>{e.exports={en:["Add this indicator to entire layout"],ru:["Добавить этот индикатор на все графики в окне"]}},92957:e=>{e.exports={en:["Add this indicator to favorites"],ru:["Добавить этот индикатор в Избранное"]}},95754:e=>{e.exports={en:["Add this strategy to entire layout"],ru:["Добавить эту стратегию ко всем графикам в окне"]}},39010:e=>{e.exports={en:["Add this symbol to entire layout"],ru:["Добавить этот символ на все графики в окне"]}},426:e=>{e.exports={en:["Adelaide"],ru:["Аделаида"]}},40452:e=>{e.exports={en:["Always invisible"],ru:["Никогда не отображать"]}},36299:e=>{e.exports={en:["Always visible"],ru:["Отображать всегда"]}},81442:e=>{e.exports={en:["All indicators and drawing tools"],ru:["Все индикаторы и инструменты рисования"]}},58026:e=>{e.exports={en:["All intervals"],ru:["Все интервалы"]}},78358:e=>{e.exports={en:["Apply default"],ru:["Сбросить изменения"]}},22437:e=>{e.exports={en:["Apply these indicators to entire layout"],ru:["Применить эти индикаторы для всех графиков в окне"]}},27072:e=>{e.exports={en:["Apr"],ru:["Апр"]}},59324:e=>{e.exports={en:["Arc"],ru:["Дуга"]
}},34456:e=>{e.exports={en:["Area"],ru:["Область"]}},11858:e=>{e.exports={en:["Arrow"],ru:["Стрелка"]}},34247:e=>{e.exports={en:["Arrow Down"],ru:["Стрелка вниз"]}},36352:e=>{e.exports={en:["Arrow Marker"],ru:["Стрелка-указатель"]}},73193:e=>{e.exports={en:["Arrow Mark Down"],ru:["Стрелка вниз"]}},1949:e=>{e.exports={en:["Arrow Mark Left"],ru:["Стрелка влево"]}},86275:e=>{e.exports={en:["Arrow Mark Right"],ru:["Стрелка вправо"]}},62453:e=>{e.exports={en:["Arrow Mark Up"],ru:["Стрелка вверх"]}},77231:e=>{e.exports={en:["Arrow Up"],ru:["Стрелка вверх"]}},98128:e=>{e.exports={en:["Astana"],ru:["Астана"]}},63627:e=>{e.exports={en:["Ashgabat"],ru:["Ашхабад"]}},72445:e=>{e.exports={en:["At close"],ru:["Цена закрытия"]}},73702:e=>{e.exports={en:["Athens"],ru:["Афины"]}},21469:e=>{e.exports={en:["Auto"],ru:["Авто"]}},24157:e=>{e.exports={en:["Auto (fits data to screen)"],ru:["Авто (бары подстраиваются под экран)"]}},46450:e=>{e.exports={en:["Aug"],ru:["Авг"]}},21841:e=>{e.exports={en:["Average close price label"],ru:["Метка средней цены закрытия"]}},16138:e=>{e.exports={en:["Average close price line"],ru:["Линия средней цены закрытия"]}},73025:e=>{e.exports={en:["Avg"],ru:["Средн."]}},73905:e=>{e.exports={en:["Bogota"],ru:["Богота"]}},90594:e=>{e.exports={en:["Bahrain"],ru:["Бахрейн"]}},70540:e=>{e.exports={en:["Balloon"],ru:["Всплывающий текст"]}},47045:e=>{e.exports={en:["Bangkok"],ru:["Бангкок"]}},76651:e=>{e.exports={en:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"],ru:["Симуляция рынка недоступна для этого типа графика. Хотите выйти из режима симуляции?"]}},68054:e=>{e.exports={en:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"],ru:["Симуляция рынка недоступна для этого временного интервала. Хотите выйти из режима симуляции?"]}},27377:e=>{e.exports={en:["Bars"],ru:["Бары"]}},81994:e=>{e.exports={en:["Bars Pattern"],ru:["Шаблон баров"]}},59213:e=>{e.exports={en:["Baseline"],ru:["Базовая линия"]}},71797:e=>{e.exports={en:["Belgrade"],ru:["Белград"]}},64313:e=>{e.exports={en:["Berlin"],ru:["Берлин"]}},43539:e=>{e.exports={en:["Brush"],ru:["Кисть"]}},91499:e=>{e.exports={en:["Brussels"],ru:["Брюссель"]}},70876:e=>{e.exports={en:["Bratislava"],ru:["Братислава"]}},55481:e=>{e.exports={en:["Bring forward"],ru:["На один слой вперед"]}},17293:e=>{e.exports={en:["Bring to front"],ru:["Перенести поверх"]}},79336:e=>{e.exports={en:["Brisbane"],ru:["Брисбен"]}},33672:e=>{e.exports={en:["Bucharest"],ru:["Бухарест"]}},20313:e=>{e.exports={en:["Budapest"],ru:["Будапешт"]}},25282:e=>{e.exports={en:["Buenos Aires"],ru:["Буэнос-Айрес"]}},46768:e=>{e.exports={en:["By TradingView"],ru:["от TradingView"]}},54280:e=>{e.exports={en:["Go to date"],ru:["Перейти к дате"]}},74975:e=>{e.exports={en:["Go to {lineToolName}"],ru:["Перейти к {lineToolName}"]}},15462:e=>{e.exports={en:["Got it"],ru:["Хорошо"]}},47460:e=>{e.exports={en:["Gann Box"],ru:["Коробка Ганна"]}},48683:e=>{e.exports={en:["Gann Fan"],ru:["Веер Ганна"]}},44763:e=>{e.exports={en:["Gann Square"],
ru:["Квадрат Ганна"]}},60707:e=>{e.exports={en:["Gann Square Fixed"],ru:["Фиксированный Квадрат Ганна"]}},46808:e=>{e.exports={en:["Ghost Feed"],ru:["Проекция цены"]}},57726:e=>{e.exports={en:["Grand supercycle"],ru:["Гранд Суперцикл"]}},34914:e=>{e.exports={en:["Do you really want to delete Study Template '{name}' ?"],ru:['Вы действительно хотите удалить шаблон индикаторов "{name}"?']}},77125:e=>{e.exports={en:["Double Curve"],ru:["Двойная кривая"]}},9430:e=>{e.exports={en:["Double-click any edge to reset layout grid"],ru:["Двойной клик по любому краю графика, чтобы сбросить настройки размеров"]}},75296:e=>{e.exports={en:["Double-click to finish Path"],ru:["Двойной клик, чтобы завершить Траекторию"]}},17409:e=>{e.exports={en:["Double-click to finish Polyline"],ru:["Двойной клик, чтобы завершить Ломаную линию"]}},57131:e=>{e.exports={en:["Data Provided by"],ru:["Данные предоставлены"]}},62154:e=>{e.exports={en:["Date"],ru:["Дата"]}},85444:e=>{e.exports={en:["Date Range"],ru:["Диапазон дат"]}},47017:e=>{e.exports={en:["Date and Price Range"],ru:["Диапазон цены и времени"]}},32084:e=>{e.exports={en:["Dec"],ru:["Дек"]}},23403:e=>{e.exports={en:["Degree"],ru:["Степень"]}},27358:e=>{e.exports={en:["Denver"],ru:["Денвер"]}},24959:e=>{e.exports={en:["Dhaka"],ru:["Дакка"]}},15179:e=>{e.exports={en:["Diamond"],ru:["Diamond"]}},91544:e=>{e.exports={en:["Disjoint Channel"],ru:["Расходящийся канал"]}},70132:e=>{e.exports={en:["Displacement"],ru:["Перемещение"]}},93864:e=>{e.exports={en:["Drawings toolbar"],ru:["Показывать панель инструментов"]}},96890:e=>{e.exports={en:["Draw Horizontal Line at"],ru:["Нарисовать горизонтальную линию на"]}},23650:e=>{e.exports={en:["Dubai"],ru:["Дубай"]}},79716:e=>{e.exports={en:["Dublin"],ru:["Дублин"]}},73456:e=>{e.exports={en:["Emoji"],ru:["Эмодзи"]}},9541:e=>{e.exports={en:["Enter a new chart layout name"],ru:["Укажите новое имя графика"]}},80943:e=>{e.exports={en:["Elliott Correction Wave (ABC)"],ru:["Коррекционная волна Эллиотта (ABC)"]}},75112:e=>{e.exports={en:["Elliott Double Combo Wave (WXY)"],ru:["Двойная комбинация Эллиотта (WXY)"]}},61114:e=>{e.exports={en:["Elliott Impulse Wave (12345)"],ru:["Импульсная волна Эллиотта (12345)"]}},72359:e=>{e.exports={en:["Elliott Triangle Wave (ABCDE)"],ru:["ABCDE волна (треугольник)"]}},76129:e=>{e.exports={en:["Elliott Triple Combo Wave (WXYXZ)"],ru:["Тройная комбинация Эллиотта (WXYXZ)"]}},78996:e=>{e.exports={en:["Ellipse"],ru:["Эллипс"]}},52788:e=>{e.exports={en:["Extended Line"],ru:["Удлинённая линия"]}},86905:e=>{e.exports={en:["Exchange"],ru:["Биржа"]}},19271:e=>{e.exports={en:["Existing pane above"],ru:["Выше"]}},46545:e=>{e.exports={en:["Existing pane below"],ru:["Ниже"]}},20138:e=>{e.exports={en:["Forecast"],ru:["Прогноз"]}},2507:e=>{e.exports={en:["Feb"],ru:["Фев"]}},59005:e=>{e.exports={en:["Fib Channel"],ru:["Каналы по Фибоначчи"]}},82330:e=>{e.exports={en:["Fib Circles"],ru:["Окружности Фибоначчи"]}},55986:e=>{e.exports={en:["Fib Retracement"],ru:["Коррекция по Фибоначчи"]}},33880:e=>{e.exports={en:["Fib Speed Resistance Arcs"],
ru:["Дуги сопротивления по Фибоначчи"]}},2395:e=>{e.exports={en:["Fib Speed Resistance Fan"],ru:["Веерные линии сопротивления по Фибоначчи"]}},39014:e=>{e.exports={en:["Fib Spiral"],ru:["Спираль по Фибоначчи"]}},30622:e=>{e.exports={en:["Fib Time Zone"],ru:["Временные периоды по Фибоначчи"]}},85042:e=>{e.exports={en:["Fib Wedge"],ru:["Клин по Фибоначчи"]}},33885:e=>{e.exports={en:["Flag"],ru:["Флаг"]}},14600:e=>{e.exports={en:["Flag Mark"],ru:["Флаг"]}},45051:e=>{e.exports={en:["Flat Top/Bottom"],ru:["Плоский верх/низ"]}},63271:e=>{e.exports={en:["Flipped"],ru:["Отразить по горизонтали"]}},39643:e=>{e.exports={en:["Fraction part is invalid."],ru:["Дробная часть неверна."]}},24077:e=>{e.exports={en:["Fundamental studies are no longer available on charts"],ru:["Индикаторы фундаментального анализа на графиках более недоступны"]}},31561:e=>{e.exports={en:["Kolkata"],ru:["Калькутта"]}},54533:e=>{e.exports={en:["Kathmandu"],ru:["Катманду"]}},83490:e=>{e.exports={en:["Kagi"],ru:["Каги"]}},70913:e=>{e.exports={en:["Karachi"],ru:["Karachi"]}},76614:e=>{e.exports={en:["Kuwait"],ru:["Кувейт"]}},38561:e=>{e.exports={en:["Kuala Lumpur"],ru:["Куала-Лумпур"]}},99906:e=>{e.exports={en:["HLC area"],ru:["Область HLC"]}},34491:e=>{e.exports={en:["Ho Chi Minh"],ru:["Хошимин"]}},13459:e=>{e.exports={en:["Hollow candles"],ru:["Пустые свечи"]}},48861:e=>{e.exports={en:["Hong Kong"],ru:["Гонконг"]}},79668:e=>{e.exports={en:["Honolulu"],ru:["Гонолулу"]}},21795:e=>{e.exports={en:["Horizontal Line"],ru:["Горизонтальная линия"]}},25487:e=>{e.exports={en:["Horizontal Ray"],ru:["Горизонтальный луч"]}},21928:e=>{e.exports={en:["Head and Shoulders"],ru:["Голова и плечи"]}},63876:e=>{e.exports={en:["Heikin Ashi"],ru:["Хейкен Аши"]}},48203:e=>{e.exports={en:["Helsinki"],ru:["Хельсинки"]}},27298:e=>{e.exports={en:["Hide"],ru:["Скрыть"]}},47074:e=>{e.exports={en:["Hide all"],ru:["Скрыть все"]}},52563:e=>{e.exports={en:["Hide all drawings"],ru:["Скрыть все объекты рисования"]}},90763:e=>{e.exports={en:["Hide all drawings and indicators"],ru:["Скрыть все объекты и индикаторы"]}},18216:e=>{e.exports={en:["Hide all drawings, indicators, positions & orders"],ru:["Скрыть все объекты рисования, индикаторы, позиции и заявки"]}},78525:e=>{e.exports={en:["Hide all indicators"],ru:["Скрыть все индикаторы"]}},42164:e=>{e.exports={en:["Hide all positions & orders"],ru:["Скрыть все позиции и заявки"]}},3217:e=>{e.exports={en:["Hide drawings"],ru:["Скрыть объекты рисования"]}},97878:e=>{e.exports={en:["Hide events on chart"],ru:["Скрыть события на графике"]}},72351:e=>{e.exports={en:["Hide indicators"],ru:["Скрыть индикаторы"]}},28345:e=>{e.exports={en:["Hide marks on bars"],ru:["Скрыть отметки на барах"]}},92226:e=>{e.exports={en:["Hide positions & orders"],ru:["Скрыть позиции и заявки"]}},78254:e=>{e.exports={en:["High"],ru:["Макс."]}},98236:e=>{e.exports={en:["High-low"],ru:["Мин-Макс"]}},99479:e=>{e.exports={en:["High and low price labels"],ru:["Метки макс. и мин. цен"]}},33766:e=>{e.exports={en:["High and low price lines"],ru:["Линии макс. и мин. цен"]}},
69476:e=>{e.exports={en:["Highlighter"],ru:["Маркер"]}},72819:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],ru:["Гистограмма слишком большая, увеличьте {boldHighlightStart}размер строки{boldHighlightEnd} в окне настроек."]}},94966:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."],ru:["Гистограмма слишком большая, увеличьте количество {boldHighlightStart}тиков в строке{boldHighlightEnd} в окне настроек."]}},66751:e=>{e.exports={en:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],ru:["Гистограмма слишком большая, уменьшите {boldHighlightStart}размер строки{boldHighlightEnd} в окне настроек."]}},68065:e=>{e.exports={en:["Image"],ru:["Изображение"]}},80185:e=>{e.exports={en:["Intervals less than {resolution} are not supported for {ticker}."],ru:["Интервалы меньше {resolution} не поддерживаются для {ticker}."]}},10268:e=>{e.exports={en:["Intermediate"],ru:["Промежуточная"]}},14285:e=>{e.exports={en:["Invalid Symbol"],ru:["Неизвестный инструмент"]}},52969:e=>{e.exports={en:["Invalid symbol"],ru:["Неверный инструмент"]}},37189:e=>{e.exports={en:["Invert scale"],ru:["Инвертировать шкалу"]}},89999:e=>{e.exports={en:["Indexed to 100"],ru:["Индексированная на 100"]}},46850:e=>{e.exports={en:["Indicators value labels"],ru:["Метки значений индикаторов"]}},54418:e=>{e.exports={en:["Indicators name labels"],ru:["Метки названий индикаторов"]}},40490:e=>{e.exports={en:["Indicators, Metrics and Strategies. Press slash"],ru:["Индикаторы, показатели и стратегии. Нажмите слэш"]}},15992:e=>{e.exports={en:["Info Line"],ru:["Линия данных"]}},87829:e=>{e.exports={en:["Insert indicator"],ru:["Добавить индикатор"]}},91612:e=>{e.exports={en:["Inside"],ru:["Внутрь"]}},41686:e=>{e.exports={en:["Inside Pitchfork"],ru:["Вилы (внутрь)"]}},37913:e=>{e.exports={en:["Icon"],ru:["Значок"]}},78326:e=>{e.exports={en:["Istanbul"],ru:["Стамбул"]}},39585:e=>{e.exports={en:["Johannesburg"],ru:["Йоханнесбург"]}},14995:e=>{e.exports={en:["Jakarta"],ru:["Джакарта"]}},62310:e=>{e.exports={en:["Jan"],ru:["Янв"]}},36057:e=>{e.exports={en:["Jerusalem"],ru:["Иерусалим"]}},53786:e=>{e.exports={en:["Jul"],ru:["Июл"]}},429:e=>{e.exports={en:["Jun"],ru:["Июн"]}},67560:e=>{e.exports={en:["Juneau"],ru:["Джуно"]}},62329:e=>{e.exports={en:["On the left"],ru:["Влево"]}},55813:e=>{e.exports={en:["On the right"],ru:["Вправо"]}},64818:e=>{e.exports={en:["Only {availableResolutions} intervals are supported for {ticker}."],ru:["Для {ticker} поддерживаются только интервалы {availableResolutions}"]}},21064:e=>{e.exports={en:["Oops!"],ru:["Упс!"]}},51221:e=>{e.exports={en:["Object Tree"],ru:["Дерево объектов"]}},12179:e=>{e.exports={en:["Oct"],ru:["Окт"]}},16610:e=>{e.exports={en:["Open"],ru:["Цена откр."]}},46005:e=>{e.exports={en:["Original"],ru:["Обычные"]}},75722:e=>{e.exports={en:["Oslo"],ru:["Осло"]}},65318:e=>{e.exports={en:["Low"],
ru:["Мин."]}},55382:e=>{e.exports={en:["Load layout. Press period"],ru:["Загрузить график. Нажмите точку"]}},5837:e=>{e.exports={en:["Lock"],ru:["Заблокировать"]}},79777:e=>{e.exports={en:["Lock/unlock"],ru:["Блокировать/разблокировать"]}},66005:e=>{e.exports={en:["Lock vertical cursor line by time"],ru:["Зафиксировать вертикальную линию курсора по времени"]}},14017:e=>{e.exports={en:["Lock price to bar ratio"],ru:["Зафиксировать соотношение цена/бар"]}},16170:e=>{e.exports={en:["Logarithmic"],ru:["Логарифмическая"]}},19439:e=>{e.exports={en:["London"],ru:["Лондон"]}},74832:e=>{e.exports={en:["Long Position"],ru:["Длинная позиция"]}},28733:e=>{e.exports={en:["Los Angeles"],ru:["Лос-Анджелес"]}},85924:e=>{e.exports={en:["Label Down"],ru:["Метка вниз"]}},52402:e=>{e.exports={en:["Label Up"],ru:["Метка вверх"]}},5119:e=>{e.exports={en:["Labels"],ru:["Метки"]}},19931:e=>{e.exports={en:["Lagos"],ru:["Лагос"]}},63815:e=>{e.exports={en:["Last day change"],ru:["Изменение последнего дня"]}},59444:e=>{e.exports={en:["Lima"],ru:["Лима"]}},3554:e=>{e.exports={en:["Line"],ru:["Линия"]}},9394:e=>{e.exports={en:["Line with markers"],ru:["Линия с точками"]}},43588:e=>{e.exports={en:["Line break"],ru:["Линейный прорыв"]}},56982:e=>{e.exports={en:["Lines"],ru:["Линии"]}},37367:e=>{e.exports={en:["Link to the chart image copied to clipboard {emoji}"],ru:["Ссылка на изображение графика скопирована {emoji}"]}},53375:e=>{e.exports={en:["Lisbon"],ru:["Лиссабон"]}},81038:e=>{e.exports={en:["Luxembourg"],ru:["Люксембург"]}},60663:e=>{e.exports={en:["Move the point to position the anchor then tap to place"],ru:["Переместите точку, чтобы установить якорь, затем нажмите, чтобы разместить"]}},35049:e=>{e.exports={en:["Move to"],ru:["Переместить"]}},26493:e=>{e.exports={en:["Move scale to left"],ru:["Переместить шкалу влево"]}},40789:e=>{e.exports={en:["Move scale to right"],ru:["Переместить шкалу вправо"]}},70382:e=>{e.exports={en:["Modified Schiff"],ru:["Измененные Шифа"]}},57681:e=>{e.exports={en:["Modified Schiff Pitchfork"],ru:["Видоизмененные вилы Шифа"]}},93907:e=>{e.exports={en:["More settings"]}},64039:e=>{e.exports={en:["Moscow"],ru:["Москва"]}},52066:e=>{e.exports={en:["Madrid"],ru:["Мадрид"]}},38365:e=>{e.exports={en:["Malta"],ru:["Мальта"]}},48991:e=>{e.exports={en:["Manila"],ru:["Манила"]}},92767:e=>{e.exports={en:["Mar"],ru:["Мар"]}},73332:e=>{e.exports={en:["Mexico City"],ru:["Мехико"]}},88314:e=>{e.exports={en:["Merge all scales into one"],ru:["Объединить все шкалы в одну"]}},54215:e=>{e.exports={en:["Mixed"],ru:["Разные"]}},24866:e=>{e.exports={en:["Micro"],ru:["Микро"]}},87957:e=>{e.exports={en:["Millennium"],ru:["Миллениум"]}},14724:e=>{e.exports={en:["Minuette"],ru:["Минуэт"]}},78273:e=>{e.exports={en:["Minuscule"],ru:["Минускул"]}},28941:e=>{e.exports={en:["Mirrored"],ru:["Отобразить по вертикали"]}},9865:e=>{e.exports={en:["Muscat"],ru:["Маскат"]}},96935:e=>{e.exports={en:["N/A"],ru:["Н/Д"]}},36252:e=>{e.exports={en:["No data here"],ru:["Нет данных"]}},11254:e=>{e.exports={en:["No scale (fullscreen)"],
ru:["Без шкалы (на весь экран)"]}},9140:e=>{e.exports={en:["No sync"],ru:["Не синхронизировать"]}},50910:e=>{e.exports={en:["No volume data"],ru:["Нет данных объёма"]}},94389:e=>{e.exports={en:["Note"],ru:["Заметка"]}},26899:e=>{e.exports={en:["Nov"],ru:["Ноя"]}},67891:e=>{e.exports={en:["Norfolk Island"],ru:["Остров Норфолк"]}},40977:e=>{e.exports={en:["Nairobi"],ru:["Найроби"]}},40544:e=>{e.exports={en:["New York"],ru:["Нью-Йорк"]}},66103:e=>{e.exports={en:["New Zealand"],ru:["Новая Зеландия"]}},15512:e=>{e.exports={en:["New pane above"],ru:["Выше, на новую панель"]}},52160:e=>{e.exports={en:["New pane below"],ru:["Ниже, на новую панель"]}},94600:e=>{e.exports={en:["Nicosia"],ru:["Никосия"]}},73013:e=>{e.exports={en:["Something went wrong"],ru:["Что-то пошло не так"]}},4509:e=>{e.exports={en:["Something went wrong. Please try again later."],ru:["Что-то пошло не так. Попробуйте позже, пожалуйста."]}},43047:e=>{e.exports={en:["Save New Chart Layout"],ru:["Сохранить график"]}},76266:e=>{e.exports={en:["Save as"],ru:["Сохранить как"]}},55502:e=>{e.exports={en:["San Salvador"],ru:["Сан-Сальвадор"]}},30231:e=>{e.exports={en:["Santiago"],ru:["Сантьяго"]}},91912:e=>{e.exports={en:["Sao Paulo"],ru:["Сан-Паулу"]}},43931:e=>{e.exports={en:["Scale currency"],ru:["Валюта шкалы"]}},43758:e=>{e.exports={en:["Scale price chart only"],ru:["Игнорировать шкалу индикаторов"]}},40012:e=>{e.exports={en:["Scale unit"],ru:["Единица шкалы"]}},69904:e=>{e.exports={en:["Schiff"],ru:["Шифа"]}},42608:e=>{e.exports={en:["Schiff Pitchfork"],ru:["Вилы Шифа"]}},76078:e=>{e.exports={en:["Script may be not updated if you leave the page."],ru:["Обновления скрипта могут не сохраниться, если вы закроете страницу."]}},32514:e=>{e.exports={en:["Settings"],ru:["Настройки"]}},70784:e=>{e.exports={en:["Second fraction part is invalid."],ru:["Вторая дробная часть неверна."]}},75594:e=>{e.exports={en:["Security info"],ru:["Информация об инструменте"]}},21973:e=>{e.exports={en:["Send to back"],ru:["Отправить назад"]}},71179:e=>{e.exports={en:["Send backward"],ru:["На один слой назад"]}},26820:e=>{e.exports={en:["Seoul"],ru:["Сеул"]}},6816:e=>{e.exports={en:["Sep"],ru:["Сен"]}},94031:e=>{e.exports={en:["Session"],ru:["Сессия"]}},83298:e=>{e.exports={en:["Session volume profile"],ru:["Профиль объёма за сессию"]}},66707:e=>{e.exports={en:["Session breaks"],ru:["Границы сессий"]}},1852:e=>{e.exports={en:["Shanghai"],ru:["Шанхай"]}},8075:e=>{e.exports={en:["Short Position"],ru:["Короткая позиция"]}},98334:e=>{e.exports={en:["Show"],ru:["Показать"]}},85891:e=>{e.exports={en:["Show all drawings"],ru:["Показать все объекты рисования"]}},25881:e=>{e.exports={en:["Show all drawings and indicators"],ru:["Показать все объекты и индикаторы"]}},86738:e=>{e.exports={en:["Show all drawings, indicators, positions & orders"],ru:["Показать все объекты рисования, индикаторы, позиции и заявки"]}},98753:e=>{e.exports={en:["Show all indicators"],ru:["Показать все индикаторы"]}},55418:e=>{e.exports={en:["Show all ideas"],ru:["Все идеи"]}},20506:e=>{e.exports={
en:["Show all positions & orders"],ru:["Показать все позиции и заявки"]}},33158:e=>{e.exports={en:["Show continuous contract switch"],ru:["Показывать переключение непрерывного контракта"]}},81465:e=>{e.exports={en:["Show contract expiration"],ru:["Показывать срок действия контракта"]}},29449:e=>{e.exports={en:["Show dividends"],ru:["Отображать дивиденды"]}},37113:e=>{e.exports={en:["Show earnings"],ru:["Отображать прибыль на акцию"]}},10261:e=>{e.exports={en:["Show ideas of followed users"],ru:["Идеи тех, на кого подписан"]}},68112:e=>{e.exports={en:["Show latest news and Minds"],ru:["Показывать последние новости"]}},44020:e=>{e.exports={en:["Show my ideas only"],ru:["Только мои идеи"]}},50849:e=>{e.exports={en:["Show splits"],ru:["Отображать сплит акций"]}},67751:e=>{e.exports={en:["Signpost"],ru:["Знак"]}},77377:e=>{e.exports={en:["Singapore"],ru:["Сингапур"]}},39090:e=>{e.exports={en:["Sine Line"],ru:["Синусоида"]}},66205:e=>{e.exports={en:["Square"],ru:["Квадрат"]}},86146:e=>{e.exports={en:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."],ru:["Достигнут лимит индикаторов/сигналов: {number} элементов на шаблон графиков.\nПожалуйста, удалите некоторые инструменты."]}},92516:e=>{e.exports={en:["Style"],ru:["Стиль"]}},61507:e=>{e.exports={en:["Stack on the left"],ru:["Слева"]}},97800:e=>{e.exports={en:["Stack on the right"],ru:["Справа"]}},85166:e=>{e.exports={en:["Start using keyboard navigation mode. Press {shortcut}"],ru:["Для начала работы с навигацией с помощью клавиатуры нажмите {shortcut}"]}},4035:e=>{e.exports={en:["Stay in drawing mode"],ru:["Оставаться в режиме рисования"]}},69217:e=>{e.exports={en:["Step line"],ru:["Ступенчатая линия"]}},43114:e=>{e.exports={en:["Sticker"],ru:["Стикер"]}},86716:e=>{e.exports={en:["Stockholm"],ru:["Стокгольм"]}},1145:e=>{e.exports={en:["Submicro"],ru:["Субмикро"]}},63375:e=>{e.exports={en:["Submillennium"],ru:["Субмиллениум"]}},30585:e=>{e.exports={en:["Subminuette"],ru:["Субминуэт"]}},67948:e=>{e.exports={en:["Supercycle"],ru:["Суперцикл"]}},3348:e=>{e.exports={en:["Supermillennium"],ru:["Супермиллениум"]}},18905:e=>{e.exports={en:["Switch to {resolution}"],ru:["Переключиться на {resolution}"]}},31622:e=>{e.exports={en:["Sydney"],ru:["Сидней"]}},70963:e=>{e.exports={en:["Symbol Error"],ru:["Ошибка символа"]}},32390:e=>{e.exports={en:["Symbol name label"],ru:["Метка имени инструмента"]}},10127:e=>{e.exports={en:["Symbol last price label"],ru:["Метка последнего значения символа"]}},39079:e=>{e.exports={en:["Sync globally"],ru:["Синхр. везде"]}},46607:e=>{e.exports={en:["Sync in layout"],ru:["Синхронизировать на всех графиках"]}},76519:e=>{e.exports={en:["Point & figure"],ru:["Крестики-нолики"]}},39949:e=>{e.exports={en:["Polyline"],ru:["Ломаная линия"]}},371:e=>{e.exports={en:["Path"],ru:["Траектория"]}},59256:e=>{e.exports={en:["Parallel Channel"],ru:["Параллельный канал"]}},61879:e=>{e.exports={en:["Paris"],ru:["Париж"]}},35140:e=>{e.exports={en:["Paste"],ru:["Вставить"]}},6919:e=>{e.exports={en:["Percent"],ru:["Процентная"]}},
24436:e=>{e.exports={en:["Perth"],ru:["Перт"]}},14055:e=>{e.exports={en:["Phoenix"],ru:["Финикс"]}},34156:e=>{e.exports={en:["Pitchfan"],ru:["Наклонный веер"]}},19634:e=>{e.exports={en:["Pitchfork"],ru:["Вилы"]}},33110:e=>{e.exports={en:["Pin to new left scale"],ru:["Закрепить на новой левой шкале"]}},28280:e=>{e.exports={en:["Pin to new right scale"],ru:["Закрепить на новой правой шкале"]}},14115:e=>{e.exports={en:["Pin to left scale"],ru:["Закрепить на левой шкале"]}},72046:e=>{e.exports={en:["Pin to left scale (hidden)"],ru:["Закрепить на левой шкале (скрыто)"]}},81054:e=>{e.exports={en:["Pin to right scale"],ru:["Закрепить на правой шкале"]}},16986:e=>{e.exports={en:["Pin to right scale (hidden)"],ru:["Закрепить на правой шкале (скрыто)"]}},60035:e=>{e.exports={en:["Pin to scale (now left)"],ru:["Закрепить на шкале (сейчас слева)"]}},94210:e=>{e.exports={en:["Pin to scale (now no scale)"],ru:["Закрепить на шкале (сейчас без шкалы)"]}},10761:e=>{e.exports={en:["Pin to scale (now right)"],ru:["Закрепить на шкале (сейчас справа)"]}},76150:e=>{e.exports={en:["Pin to scale (now {label})"],ru:["Закрепить на шкале (сейчас {label})"]}},29436:e=>{e.exports={en:["Pin to scale {label}"],ru:["Закрепить на шкале {label}"]}},2165:e=>{e.exports={en:["Pin to scale {label} (hidden)"],ru:["Закрепить на шкале {label} (скрыто)"]}},90095:e=>{e.exports={en:["Pinned to left scale"],ru:["Закреплено на левой шкале"]}},32538:e=>{e.exports={en:["Pinned to left scale (hidden)"],ru:["Закреплено на левой шкале (скрыто)"]}},44579:e=>{e.exports={en:["Pinned to right scale"],ru:["Закреплено на правой шкале"]}},94559:e=>{e.exports={en:["Pinned to right scale (hidden)"],ru:["Закрепить на правой шкале (скрыто)"]}},12645:e=>{e.exports={en:["Pinned to scale {label}"],ru:["Закреплено на шкале {label}"]}},3564:e=>{e.exports={en:["Pinned to scale {label} (hidden)"],ru:["Закреплено на шкале {label} (скрыто)"]}},71566:e=>{e.exports={en:["Plus button"],ru:["Кнопка Плюс"]}},28298:e=>{e.exports={en:["Please give us a clipboard writing permission in your browser or press {keystroke}"],ru:["Пожалуйста, предоставьте доступ к записи в буфер обмена в вашем браузере или нажмите {keystroke}"]}},81248:e=>{e.exports={en:["Prague"],ru:["Прага"]}},81712:e=>{e.exports={en:["Press and hold {key} while zooming to maintain the chart position"],ru:["Зажмите и держите {key} во время масштабирования, чтобы сохранить позицию графика"]}},91282:e=>{e.exports={en:["Price Label"],ru:["Метка цены"]}},97512:e=>{e.exports={en:["Price Note"],ru:["Ценовая заметка"]}},68941:e=>{e.exports={en:["Price Range"],ru:["Диапазон цен"]}},66123:e=>{e.exports={en:["Price format is invalid."],ru:["Формат цены не поддерживается."]}},72926:e=>{e.exports={en:["Price line"],ru:["Линия цены"]}},59189:e=>{e.exports={en:["Primary"],ru:["Первичная"]}},75747:e=>{e.exports={en:["Projection"],ru:["Проекция"]}},55801:e=>{e.exports={en:["Published on {customer}, {date}"],ru:["Опубликовано на {customer}, {date}"]}},28756:e=>{e.exports={en:["Qatar"],ru:["Катар"]}},57959:e=>{e.exports={
en:["Quick search. Press {shortcut}"],ru:["Быстрый поиск. Нажмите {shortcut}"]}},56820:e=>{e.exports={en:["Rotated Rectangle"],ru:["Вращающийся прямоугольник"]}},52961:e=>{e.exports={en:["Rome"],ru:["Рим"]}},50318:e=>{e.exports={en:["Ray"],ru:["Луч"]}},55169:e=>{e.exports={en:["Range"],ru:["Range"]}},13386:e=>{e.exports={en:["Reykjavik"],ru:["Рейкьявик"]}},26001:e=>{e.exports={en:["Rectangle"],ru:["Прямоугольник"]}},48236:e=>{e.exports={en:["Redo"],ru:["Повторить"]}},2460:e=>{e.exports={en:["Regression Trend"],ru:["Регрессионный тренд"]}},67410:e=>{e.exports={en:["Remove"],ru:["Удалить"]}},96374:e=>{e.exports={en:["Remove drawings"],ru:["Удалить объекты рисования"]}},99984:e=>{e.exports={en:["Remove indicators"],ru:["Удалить индикаторы"]}},3061:e=>{e.exports={en:["Remove this financial metric from favorites"],ru:["Удалить этот показатель из Избранного"]}},58764:e=>{e.exports={en:["Remove this indicator from favorites"],ru:["Удалить этот индикатор из Избранного"]}},22584:e=>{e.exports={en:["Rename Chart Layout"],ru:["Переименовать график"]}},88130:e=>{e.exports={en:["Renko"],ru:["Ренко"]}},75246:e=>{e.exports={en:["Reset chart view"],ru:["Сбросить состояние графика"]}},88853:e=>{e.exports={en:["Reset points"],ru:["Сбросить точки"]}},15332:e=>{e.exports={en:["Reset price scale"],ru:["Сбросить масштаб ценовой шкалы"]}},54170:e=>{e.exports={en:["Reset time scale"],ru:["Сбросить масштаб временной шкалы"]}},37974:e=>{e.exports={en:["Riyadh"],ru:["Эр-Рияд"]}},94022:e=>{e.exports={en:["Riga"],ru:["Рига"]}},60630:e=>{e.exports={en:["Runtime error"],ru:["Ошибка времени выполнения"]}},66719:e=>{e.exports={en:["Warning"],ru:["Предупреждение"]}},5959:e=>{e.exports={en:["Warsaw"],ru:["Варшава"]}},94465:e=>{e.exports={en:["Toggle auto scale"],ru:["Включить/выключить автоматический масштаб"]}},46992:e=>{e.exports={en:["Toggle log scale"],ru:["Включить/выключить логарифмическую шкалу"]}},98549:e=>{e.exports={en:["Tokelau"],ru:["Токелау"]}},69122:e=>{e.exports={en:["Tokyo"],ru:["Токио"]}},10095:e=>{e.exports={en:["Toronto"],ru:["Торонто"]}},11034:e=>{e.exports={en:["Taipei"],ru:["Тайбей"]}},79995:e=>{e.exports={en:["Tallinn"],ru:["Таллин"]}},6686:e=>{e.exports={en:["Tehran"],ru:["Тегеран"]}},93553:e=>{e.exports={en:["Template"],ru:["Шаблон"]}},81657:e=>{e.exports={en:["The data vendor doesn't provide volume data for this symbol."],ru:["Поставщик данных не предоставляет данные об объеме для этого инструмента."]}},49947:e=>{e.exports={en:["The publication preview could not be loaded. Please disable your browser extensions and try again."],ru:["Невозможно загрузить предпросмотр публикации. Отключите расширения браузера и попробуйте снова."]}},59519:e=>{e.exports={en:["This indicator cannot be applied to another indicator."],ru:["Данный индикатор нельзя применить к другому индикатору."]}},18260:e=>{e.exports={en:["This script contains an error. Please contact its author."],ru:["Скрипт содержит ошибку. Свяжитесь с его автором."]}},76989:e=>{e.exports={en:["This script is invite-only. To request access, please contact its author."],
ru:["Доступ к этому скрипту ограничен. Запросите доступ у автора скрипта."]}},47773:e=>{e.exports={en:["This symbol is only available on {linkStart}TradingView{linkEnd}."],ru:["Инструмент доступен только на {linkStart}TradingView{linkEnd}."]}},46982:e=>{e.exports={en:["Three Drives Pattern"],ru:["Паттерн трёх движений"]}},24821:e=>{e.exports={en:["Ticks"],ru:["Тики"]}},80254:e=>{e.exports={en:["Tick-based intervals are not available for {ticker}."],ru:["Тиковые интервалы не поддерживаются для {ticker}."]}},12806:e=>{e.exports={en:["Time"],ru:["Время"]}},20909:e=>{e.exports={en:["Time zone"],ru:["Часовой пояс"]}},46852:e=>{e.exports={en:["Time Cycles"],ru:["Временные циклы"]}},17809:e=>{e.exports={en:["Time Price Opportunity"],ru:["Time Price Opportunity"]}},66823:e=>{e.exports={en:["Trade"],ru:["Торговля"]}},7697:e=>{e.exports={en:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"],ru:["Графики TradingView — интерактивные. Для работы с ними в режиме чтения экрана вы можете использовать команды. Ниже приведён список горячих клавиш, которые помогут взаимодействовать с платформой."]}},35757:e=>{e.exports={en:["Trend Angle"],ru:["Угол тренда"]}},97339:e=>{e.exports={en:["Trend Line"],ru:["Линия тренда"]}},80583:e=>{e.exports={en:["Trend-Based Fib Extension"],ru:["Расширение Фибоначчи, основанное на тренде"]}},72159:e=>{e.exports={en:["Trend-Based Fib Time"],ru:["Периоды Фибоначчи, основанные на тренде"]}},1671:e=>{e.exports={en:["Triangle"],ru:["Треугольник"]}},76152:e=>{e.exports={en:["Triangle Down"],ru:["Треугольник вниз"]}},90148:e=>{e.exports={en:["Triangle Pattern"],ru:['Шаблон "Треугольник"']}},21236:e=>{e.exports={en:["Triangle Up"],ru:["Треугольник вверх"]}},21007:e=>{e.exports={en:["Tunis"],ru:["Тунис"]}},1833:e=>{e.exports={en:["UTC"],ru:["UTC"]}},14804:e=>{e.exports={en:["Undo"],ru:["Отменить"]}},15432:e=>{e.exports={en:["Units"],ru:["Количество"]}},11768:e=>{e.exports={en:["Unknown error"],ru:["Неизвестная ошибка"]}},99894:e=>{e.exports={en:["Unlock"],ru:["Разблокировать"]}},75546:e=>{e.exports={en:["Unsupported interval"],ru:["Интервал не поддерживается"]}},8580:e=>{e.exports={en:["User-defined error"],ru:["Пользовательская ошибка"]}},40693:e=>{e.exports={en:["Volume Profile Fixed Range"],ru:["Фиксированный профиль объема"]}},39903:e=>{e.exports={en:["Volume Profile indicator available only on our upgraded plans."],ru:["Индикатор Профиль объёма доступен только в платных подписках."]}},93722:e=>{e.exports={en:["Volume candles"],ru:["Свечи объёма"]}},69156:e=>{e.exports={en:["Volume data is not provided in BIST MIXED data plan."],ru:["В подписку на данные BIST MIXED не входят данные объёма."]}},92763:e=>{e.exports={en:["Volume footprint"],ru:["Кластерный объём"]}},32838:e=>{e.exports={en:["Vancouver"],ru:["Ванкувер"]}},29535:e=>{e.exports={en:["Vertical Line"],ru:["Вертикальная линия"]}},23160:e=>{e.exports={en:["Vienna"],ru:["Вена"]}},60534:e=>{e.exports={en:["Vilnius"],ru:["Вильнюс"]}},40091:e=>{
e.exports={en:["Visibility"],ru:["Видимость"]}},54853:e=>{e.exports={en:["Visibility on intervals"],ru:["Видимость на интервалах"]}},10309:e=>{e.exports={en:["Visible on mouse over"],ru:["При наведении курсора"]}},4077:e=>{e.exports={en:["Visual order"],ru:["Порядок слоев"]}},11316:e=>{e.exports={en:["X Cross"],ru:["X Пересечение"]}},42231:e=>{e.exports={en:["XABCD Pattern"],ru:["Шаблон XABCD"]}},25059:e=>{e.exports={en:["You cannot see this pivot timeframe on this resolution"],ru:["Нельзя использовать этот временной интервал точек разворота на этом интервале"]}},53168:e=>{e.exports={en:["Yangon"],ru:["Янгон"]}},62859:e=>{e.exports={en:["Zurich"],ru:["Цюрих"]}},47977:e=>{e.exports={en:["change Elliott degree"],ru:["изменение угла Эллиотта"]}},61557:e=>{e.exports={en:["change no overlapping labels"],ru:["изменение: не перекрывать метки"]}},76852:e=>{e.exports={en:["change average close price label visibility"],ru:["изменение видимости метки средней цены закрытия"]}},1022:e=>{e.exports={en:["change average close price line visibility"],ru:["изменение видимости линии средней цены закрытия"]}},69362:e=>{e.exports={en:["change bid and ask labels visibility"],ru:["изменение видимости меток цен покупки и продажи"]}},52919:e=>{e.exports={en:["change bid and ask lines visibility"],ru:["изменение видимости линий цен покупки и продажи"]}},32302:e=>{e.exports={en:["change currency"],ru:["изменение валюты"]}},68846:e=>{e.exports={en:["change chart layout to {title}"],ru:["изменение формата отображения графиков на {title}"]}},18867:e=>{e.exports={en:["change continuous contract switch visibility"],ru:["изменение видимости переключения непрерывного контракта"]}},39383:e=>{e.exports={en:["change countdown to bar close visibility"],ru:["изменение видимости обратного отсчета до закрытия бара"]}},16979:e=>{e.exports={en:["change date range"],ru:["изменение диапазона дат"]}},53929:e=>{e.exports={en:["change dividends visibility"],ru:["изменение отображения дивидендов"]}},6119:e=>{e.exports={en:["change events visibility on chart"],ru:["изменение видимости событий на графике"]}},6819:e=>{e.exports={en:["change earnings visibility"],ru:["изменение отображения прибыли"]}},85532:e=>{e.exports={en:["change futures contract expiration visibility"],ru:["изменение видимости срока действия фьючерсного контракта"]}},24226:e=>{e.exports={en:["change high and low price labels visibility"],ru:["изменение видимости меток макс. и мин. цен"]}},80692:e=>{e.exports={en:["change high and low price lines visibility"],ru:["изменение видимости линий макс. и мин. цен"]}},24893:e=>{e.exports={en:["change indicators name labels visibility"],ru:["изменение видимости меток названий индикаторов"]}},64729:e=>{e.exports={en:["change indicators value labels visibility"],ru:["изменение видимости меток значений индикаторов"]}},50243:e=>{e.exports={en:["change latest news and Minds visibility"],ru:["изменение видимости последних новостей"]}},88849:e=>{e.exports={en:["change linking group"],ru:["изменение группы синхронизации"]}},14691:e=>{e.exports={
en:["change pane height"],ru:["изменение высоты панели"]}},96379:e=>{e.exports={en:["change plus button visibility"],ru:["изменение видимости кнопки Плюс"]}},30870:e=>{e.exports={en:["change pre/post market price label visibility"],ru:["изменение видимости меток цены пре-/постмаркета"]}},11718:e=>{e.exports={en:["change pre/post market price line visibility"],ru:["изменение видимости линии цены пре-/постмаркета"]}},58419:e=>{e.exports={en:["change previous close price line visibility"],ru:["изменение видимости линии цены предыдущего закрытия"]}},8662:e=>{e.exports={en:["change price line visibility"],ru:["изменение видимости линии цены"]}},2509:e=>{e.exports={en:["change price to bar ratio"],ru:["изменение соотношения цена/бар"]}},32829:e=>{e.exports={en:["change resolution"],ru:["изменение разрешения"]}},35400:e=>{e.exports={en:["change symbol"],ru:["смену инструмента"]}},73357:e=>{e.exports={en:["change symbol labels visibility"],ru:["изменение видимости меток инструментов"]}},67453:e=>{e.exports={en:["change symbol last value visibility"],ru:["изменение видимости последнего значения инструмента"]}},4729:e=>{e.exports={en:["change symbol previous close value visibility"],ru:["изменение видимости предыдущего значения закрытия инструмента"]}},87041:e=>{e.exports={en:["change session"],ru:["изменение сессии"]}},38413:e=>{e.exports={en:["change session breaks visibility"],ru:["изменение видимости границ сессии"]}},49965:e=>{e.exports={en:["change series style"],ru:["изменение стиля серии"]}},47474:e=>{e.exports={en:["change splits visibility"],ru:["изменение отображения сплитов"]}},20137:e=>{e.exports={en:["change timezone"],ru:["изменение часового пояса"]}},85975:e=>{e.exports={en:["change unit"],ru:["изменение единиц"]}},1924:e=>{e.exports={en:["change visibility"],ru:["изменение отображения"]}},84331:e=>{e.exports={en:["change visibility at current interval"],ru:["изменение видимости на текущем интервале"]}},45800:e=>{e.exports={en:["change visibility at current interval and above"],ru:["изменение видимости на текущем интервале и выше"]}},75645:e=>{e.exports={en:["change visibility at current interval and below"],ru:["изменение видимости на текущем интервале и ниже"]}},57916:e=>{e.exports={en:["change visibility at all intervals"],ru:["изменение видимости на всех интервалах"]}},98463:e=>{e.exports={en:["change {title} style"],ru:["изменение стиля {title}"]}},57122:e=>{e.exports={en:["change {title} text"],ru:["изменение текста в {title}"]}},63058:e=>{e.exports={en:["change {pointIndex} point"],ru:["изменение точки {pointIndex}"]}},94566:e=>{e.exports={en:["charts by TradingView"],ru:["графики от TradingView"]}},32943:e=>{e.exports={en:["clone line tools"],ru:["клонирование объектов рисования"]}},46219:e=>{e.exports={en:["create line tools group"],ru:["создание группы инструментов рисования"]}},95394:e=>{e.exports={en:["create line tools group from selection"],ru:["создание группы из выбранных инструментов рисования"]}},12898:e=>{e.exports={en:["create {tool}"],ru:["создание {tool}"]}},94227:e=>{e.exports={
en:["cut sources"],ru:["вырезание объектов"]}},11500:e=>{e.exports={en:["cut {title}"],ru:["вырезание {title}"]}},12570:e=>{e.exports={en:["add line tool {lineTool} to group {name}"],ru:["добавление инструмента рисования {lineTool} в группу {name}"]}},21162:e=>{e.exports={en:["add line tool(s) to group {group}"],ru:["добавление инструмента(ов) рисования в группу {group}"]}},67608:e=>{e.exports={en:["add this financial metric to entire layout"],ru:["добавление фин. показателя на все графики в окне"]}},96677:e=>{e.exports={en:["add this indicator to entire layout"],ru:["добавление индикатора на все графики в окне"]}},58156:e=>{e.exports={en:["add this strategy to entire layout"],ru:["добавление стратегии ко всем графикам в окне"]}},79290:e=>{e.exports={en:["add this symbol to entire layout"],ru:["добавление символа на все графики в окне"]}},68231:e=>{e.exports={en:["apply chart theme"],ru:["применение цветовой темы для графика"]}},99551:e=>{e.exports={en:["apply all chart properties"],ru:["применение ко всем свойствам графика"]}},89720:e=>{e.exports={en:["apply drawing template"],ru:["применение шаблона графических объектов"]}},27851:e=>{e.exports={en:["apply factory defaults to selected sources"],ru:["применение настроек по умолчанию к выбранным объектам"]}},70507:e=>{e.exports={en:["apply indicators to entire layout"],ru:["применение индикаторов ко всем графикам в окне"]}},69604:e=>{e.exports={en:["apply study template {template}"],ru:["применение шаблона индикаторов {template}"]}},86708:e=>{e.exports={en:["apply toolbars theme"],ru:["применение темы для панелей инструментов"]}},1979:e=>{e.exports={en:["bring group {title} forward"],ru:["перемещение на один слой вперед группы {title}"]}},53159:e=>{e.exports={en:["bring {title} to front"],ru:["перемещение поверх: {title}"]}},41966:e=>{e.exports={en:["bring {title} forward"],ru:["перемещение вперед: {title}"]}},44676:e=>{e.exports={en:["by TradingView"],ru:["от TradingView"]}},58850:e=>{e.exports={en:["date range lock"],ru:["закрепление диапазона дат"]}},35111:e=>{e.exports={en:["erase level line"],ru:["удаление линии уровня"]}},99395:e=>{e.exports={en:["exclude line tools from group {group}"],ru:["удаление инструменты рисования из группы {group}"]}},82349:e=>{e.exports={en:["flip bars pattern"],ru:["отражение по горизонтали шаблонов баров"]}},13017:e=>{e.exports={en:["hide {title}"],ru:["скрытие: {title}"]}},62249:e=>{e.exports={en:["hide marks on bars"],ru:["скрытие отметок на барах"]}},56558:e=>{e.exports={en:["interval lock"],ru:["закрепление интервала"]}},6830:e=>{e.exports={en:["invert scale"],ru:["инвертирование шкалы"]}},48818:e=>{e.exports={en:["insert {title}"],ru:["добавление {title}"]}},56307:e=>{e.exports={en:["insert {title} after {targetTitle}"],ru:["добавление {title} после {targetTitle}"]}},32960:e=>{e.exports={en:["insert {title} after {target}"],ru:["перемещение {title} после {target}"]}},57106:e=>{e.exports={en:["insert {title} before {target}"],ru:["перемещение {title} перед {target}"]}},46229:e=>{e.exports={
en:["insert {title} before {targetTitle}"],ru:["перемещение {title} перед {targetTitle}"]}},43364:e=>{e.exports={en:["load default drawing template"],ru:["загрузку шаблона по умолчанию"]}},62011:e=>{e.exports={en:["loading..."],ru:["загрузка..."]}},76104:e=>{e.exports={en:["lock {title}"],ru:["блокировку: {title}"]}},20453:e=>{e.exports={en:["lock group {group}"],ru:["закрепление группы {group}"]}},18942:e=>{e.exports={en:["lock objects"],ru:["прикрепление объектов"]}},98277:e=>{e.exports={en:["move"],ru:["перемещение"]}},58228:e=>{e.exports={en:["move {title} to new left scale"],ru:["перемещение на новую левую шкалу: {title}"]}},77482:e=>{e.exports={en:["move {title} to new right scale"],ru:["перемещение на новую правую шкалу: {title}"]}},64077:e=>{e.exports={en:["move all scales to left"],ru:["перемещение всех шкал влево"]}},19013:e=>{e.exports={en:["move all scales to right"],ru:["перемещение всех шкал вправо"]}},52510:e=>{e.exports={en:["move drawing(s)"],ru:["перемещение объекта(ов) рисования"]}},79209:e=>{e.exports={en:["move left"],ru:["перемещение влево"]}},60114:e=>{e.exports={en:["move right"],ru:["перемещение вправо"]}},44854:e=>{e.exports={en:["move scale"],ru:["перемещение шкалы"]}},10625:e=>{e.exports={en:["make {title} no scale (Full screen)"],ru:["режим Без шкалы (на весь экран) для {title}"]}},76709:e=>{e.exports={en:["make group {group} invisible"],ru:["невидимость группы {group}"]}},45987:e=>{e.exports={en:["make group {group} visible"],ru:["видимость группы {group}"]}},78055:e=>{e.exports={en:["merge down"],ru:["перемещение ниже"]}},41866:e=>{e.exports={en:["merge to pane"],ru:["перемещение на панель"]}},52458:e=>{e.exports={en:["merge up"],ru:["перемещение выше"]}},20965:e=>{e.exports={en:["mirror bars pattern"],ru:["отражение по вертикали шаблонов баров"]}},90091:e=>{e.exports={en:["n/a"],ru:["н/д"]}},94981:e=>{e.exports={en:["scale price"],ru:["изменение ценовой шкалы"]}},63796:e=>{e.exports={en:["scale price chart only"],ru:["игнорирование шкалы индикаторов"]}},70771:e=>{e.exports={en:["scale time"],ru:["изменение временной шкалы"]}},42070:e=>{e.exports={en:["scroll"],ru:["прокрутку"]}},87840:e=>{e.exports={en:["scroll time"],ru:["прокрутку временной шкалы"]}},82241:e=>{e.exports={en:["set price scale selection strategy to {title}"],ru:["применение стратегии выбора ценовой шкалы для {title}"]}},40962:e=>{e.exports={en:["send {title} backward"],ru:["перемещение назад: {title}"]}},5005:e=>{e.exports={en:["send {title} to back"],ru:["перемещение назад: {title}"]}},69546:e=>{e.exports={en:["send group {title} backward"],ru:["перемещение на один слой назад группы {title}"]}},63934:e=>{e.exports={en:["share line tools globally"],ru:["возможность делиться объектами рисования везде"]}},90221:e=>{e.exports={en:["share line tools in layout"],ru:["возможность делиться объектами рисования на всех графиках"]}},13336:e=>{e.exports={en:["show all ideas"],ru:["отображение всех идей"]}},91395:e=>{e.exports={en:["show ideas of followed users"],ru:["отображение идей тех, на кого подписан"]}},57460:e=>{
e.exports={en:["show my ideas only"],ru:["отображение только собственных идей"]}},4114:e=>{e.exports={en:["stay in drawing mode"],ru:["нахождение в режиме рисования"]}},3350:e=>{e.exports={en:["stop syncing drawing"],ru:["прекращение синхронизации объектов рисования"]}},49183:e=>{e.exports={en:["stop syncing line tool(s)"],ru:["прекращение синхронизации линий тренда"]}},53278:e=>{e.exports={en:["symbol lock"],ru:["закрепление символа"]}},91677:e=>{e.exports={en:["sync time"],ru:["синхронизацию времени"]}},3140:e=>{e.exports={en:["powered by"],ru:["при поддержке"]}},92800:e=>{e.exports={en:["powered by TradingView"],ru:["технология TradingView"]}},62192:e=>{e.exports={en:["paste drawing"],ru:["вставку объекта рисования"]}},1064:e=>{e.exports={en:["paste indicator"],ru:["вставку индикатора"]}},57010:e=>{e.exports={en:["paste {title}"],ru:["вставку {title}"]}},78690:e=>{e.exports={en:["pin to left scale"],ru:["закрепление на левой шкале"]}},7495:e=>{e.exports={en:["pin to right scale"],ru:["закрепление на правой шкале"]}},81566:e=>{e.exports={en:["pin to scale {label}"],ru:["закрепление на шкале {label}"]}},2618:e=>{e.exports={en:["rearrange panes"],ru:["перестановку панелей"]}},43172:e=>{e.exports={en:["remove all studies"],ru:["удаление всех индикаторов"]}},56253:e=>{e.exports={en:["remove all studies and drawing tools"],ru:["удаление всех объектов рисования и индикаторов"]}},27171:e=>{e.exports={en:["remove deselected empty line tools"],ru:["удаление невыбранных пустых объектов рисования"]}},30538:e=>{e.exports={en:["remove drawings"],ru:["удаление объектов рисования"]}},1193:e=>{e.exports={en:["remove drawings group"],ru:["удаление группы объектов рисования"]}},38199:e=>{e.exports={en:["remove line data sources"],ru:["удаление источников данных линии"]}},93333:e=>{e.exports={en:["remove pane"],ru:["удаление панели"]}},94543:e=>{e.exports={en:["remove {title}"],ru:["удаление {title}"]}},41430:e=>{e.exports={en:["removing line tools group {name}"],ru:["удаление группы инструментов рисования: {name}"]}},80491:e=>{e.exports={en:["rename group {group} to {newName}"],ru:["переименование группы {group} на {newName}"]}},85366:e=>{e.exports={en:["reset layout sizes"],ru:["сброс настроек размера графика"]}},3323:e=>{e.exports={en:["reset scales"],ru:["сброс состояния графика"]}},17336:e=>{e.exports={en:["reset time scale"],ru:["сброс масштабов временной шкалы"]}},47418:e=>{e.exports={en:["resize layout"],ru:["изменение размера графика"]}},85815:e=>{e.exports={en:["restore defaults"],ru:["сброс настроек"]}},96881:e=>{e.exports={en:["restore study defaults"],ru:["восстановление настроек стратегии по умолчанию"]}},42240:e=>{e.exports={en:["toggle auto scale"],ru:["включение/выключение автоматического масштаба"]}},46054:e=>{e.exports={en:["toggle collapsed pane state"],ru:["состояние свёрнутой/развёрнутой панели"]}},24736:e=>{e.exports={en:["toggle indexed to 100 scale"],ru:["включение/выключение индексированной на 100 шкалы"]}},49695:e=>{e.exports={en:["toggle lock scale"],ru:["включение/выключение закрепления шкалы"]}},
49403:e=>{e.exports={en:["toggle log scale"],ru:["включение/выключение логарифмической шкалы"]}},98994:e=>{e.exports={en:["toggle percentage scale"],ru:["включение/выключение процентной шкалы"]}},80688:e=>{e.exports={en:["toggle regular scale"],ru:["включение/выключение равномерной шкалы"]}},46807:e=>{e.exports={en:["track time"],ru:["время отслеживания"]}},8040:e=>{e.exports={en:["turn line tools sharing off"],ru:["отключение возможности делиться объектами рисования"]}},23230:e=>{e.exports={en:["unlock objects"],ru:["открепление объектов"]}},74590:e=>{e.exports={en:["unlock group {group}"],ru:["открепление группы {group}"]}},12525:e=>{e.exports={en:["unlock {title}"],ru:["разблокирование: {title}"]}},81576:e=>{e.exports={en:["unmerge to new bottom pane"],ru:["перемещение на новую нижнюю панель"]}},79443:e=>{e.exports={en:["unmerge up"],ru:["перемещение выше, на новую панель"]}},46453:e=>{e.exports={en:["unmerge down"],ru:["перемещение ниже, на новую панель"]}},94656:e=>{e.exports={en:["{chartStyle} chart type isn't currently available for tick-based intervals."],ru:["В данный момент график {chartStyle} не доступен на тиковых интервалах."]}},41643:e=>{e.exports={en:["{count} bars"],ru:["Бары: {count}"]}},83470:e=>{e.exports={en:["{symbol} financials by TradingView"],ru:["Финансовые показатели {symbol} от TradingView"]}},40947:e=>{e.exports={en:["{userName} published on {customer}, {date}"],ru:["{userName} опубликовал(а) на {customer}, {date}"]}},91084:e=>{e.exports={en:["zoom"],ru:["изменение масштаба"]}},49856:e=>{e.exports={en:["zoom in"],ru:["увеличение масштаба"]}},73638:e=>{e.exports={en:["zoom out"],ru:["уменьшение масштаба"]}},41807:e=>{e.exports={en:["day","days"],ru:["день","дня","дней","дней"]}},42328:e=>{e.exports={en:["hour","hours"],ru:["час","часа","часов","часов"]}},98393:e=>{e.exports={en:["month","months"],ru:["месяц","месяца","месяцев","месяцев"]}},78318:e=>{e.exports={en:["minute","minutes"],ru:["минута","минуты","минут","минут"]}},33232:e=>{e.exports={en:["second","seconds"],ru:["секунда","секунды","секунд","секунд"]}},89937:e=>{e.exports={en:["range","ranges"],ru:["range","range","range","range"]}},48898:e=>{e.exports={en:["week","weeks"],ru:["неделя","недели","недель","недель"]}},11913:e=>{e.exports={en:["tick","ticks"],ru:["тик","тика","тиков","тиков"]}},58590:e=>{e.exports={en:["{count}m","{count}m"],ru:["{count}мес","{count}мес","{count}мес","{count}мес"]}},47801:e=>{e.exports={en:["{count}d","{count}d"],ru:["{count}д","{count}д","{count}д","{count}д"]}},46766:e=>{e.exports={en:["{count}y","{count}y"],ru:["{count}г","{count}г","{count}л","{count}л"]}},56316:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]={en:["Apple Inc"],ru:["Apple Inc"]},e.exports["#AUDCAD-symbol-description"]={en:["Australian Dollar/Canadian Dollar"],ru:["Австралийский доллар / Канадский доллар"]},e.exports["#AUDCHF-symbol-description"]={en:["Australian Dollar / Swiss Franc"],ru:["Австралийский доллар / Швейцарский франк"]},e.exports["#AUDJPY-symbol-description"]={
en:["Australian Dollar / Japanese Yen"],ru:["Австралийский доллар / Японская иена"]},e.exports["#AUDNZD-symbol-description"]={en:["Australian Dollar / New Zealand Dollar"],ru:["Австралийский доллар / Новозеландский доллар"]},e.exports["#AUDRUB-symbol-description"]={en:["Australian Dollar / Russian Ruble"],ru:["Австралийский доллар / Российский рубль"]},e.exports["#AUDUSD-symbol-description"]={en:["Australian Dollar / U.S. Dollar"],ru:["Австралийский доллар / Доллар США"]},e.exports["#BRLJPY-symbol-description"]={en:["Brazilian Real / Japanese Yen"],ru:["Бразильский реал / Японская иена"]},e.exports["#BTCCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],ru:["Биткоин / Канадский доллар"]},e.exports["#BTCCNY-symbol-description"]={en:["Bitcoin / Chinese Yuan"],ru:["Биткоин / Китайский юань"]},e.exports["#BTCEUR-symbol-description"]={en:["Bitcoin / Euro"],ru:["Биткоин / Евро"]},e.exports["#BTCKRW-symbol-description"]={en:["Bitcoin / South Korean Won"],ru:["Биткоин / Южнокорейская вона"]},e.exports["#BTCRUR-symbol-description"]={en:["Bitcoin / Ruble"],ru:["Биткоин / Российский рубль"]},e.exports["#BTCUSD-symbol-description"]={en:["Bitcoin / U.S. Dollar"],ru:["Биткоин / Доллар США"]},e.exports["#BVSP-symbol-description"]={en:["Brazil Bovespa Index"],ru:["Индекс BOVESPA"]},e.exports["#CADJPY-symbol-description"]={en:["Canadian Dollar / Japanese Yen"],ru:["Канадский доллар / Японская иена"]},e.exports["#CHFJPY-symbol-description"]={en:["Swiss Franc / Japanese Yen"],ru:["Швейцарский франк / Японская иена"]},e.exports["#COPPER-symbol-description"]={en:["CFDs on Copper"],ru:["CFD на медь"]},e.exports["#ES1-symbol-description"]={en:["S&P 500 E-Mini Futures"],ru:["Фьючерсы на E-Mini S&P 500"]},e.exports["#ESP35-symbol-description"]={en:["IBEX 35 Index"],ru:["Индекс IBEX 35"]},e.exports["#EUBUND-symbol-description"]={en:["Euro Bund"],ru:["Еврооблигации"]},e.exports["#EURAUD-symbol-description"]={en:["Euro / Australian Dollar"],ru:["Евро / Австралийский доллар"]},e.exports["#EURBRL-symbol-description"]={en:["Euro / Brazilian Real"],ru:["Евро / Бразильский реал"]},e.exports["#EURCAD-symbol-description"]={en:["Euro / Canadian Dollar"],ru:["Евро / Канадский доллар"]},e.exports["#EURCHF-symbol-description"]={en:["Euro / Swiss Franc"],ru:["Евро / Швейцарский франк"]},e.exports["#EURGBP-symbol-description"]={en:["Euro / British Pound"],ru:["Евро / Британский фунт"]},e.exports["#EURJPY-symbol-description"]={en:["Euro / Japanese Yen"],ru:["Евро / Японская иена"]},e.exports["#EURNZD-symbol-description"]={en:["Euro / New Zealand Dollar"],ru:["Евро / Новозеландский доллар"]},e.exports["#EURRUB-symbol-description"]={en:["Euro / Russian Ruble"],ru:["Евро / Российский рубль"]},e.exports["#EURRUB_TOM-symbol-description"]={en:["Euro / Russian Ruble TOM"],ru:["Евро / Российский рубль TOM"]},e.exports["#EURSEK-symbol-description"]={en:["Euro / Swedish Krona"],ru:["Евро / Шведская крона"]},e.exports["#EURTRY-symbol-description"]={en:["Euro / Turkish Lira"],ru:["Евро / Турецкая лира"]},e.exports["#EURUSD-symbol-description"]={
en:["Euro / U.S. Dollar"],ru:["Евро / Доллар США"]},e.exports["#EUSTX50-symbol-description"]={en:["Euro Stoxx 50 Index"],ru:["Индекс Euro Stoxx 50"]},e.exports["#FRA40-symbol-description"]={en:["CAC 40 Index"],ru:["Индекс CAC 40"]},e.exports["#GB10-symbol-description"]={en:["UK Government Bonds 10 yr"],ru:["10-летние облигации Британии"]},e.exports["#GBPAUD-symbol-description"]={en:["British Pound / Australian Dollar"],ru:["Британский фунт / Австралийский доллар"]},e.exports["#GBPCAD-symbol-description"]={en:["British Pound / Canadian Dollar"],ru:["Британский фунт / Канадский доллар"]},e.exports["#GBPCHF-symbol-description"]={en:["British Pound / Swiss Franc"],ru:["Британский фунт / Швейцарский франк"]},e.exports["#GBPEUR-symbol-description"]={en:["British Pound / Euro"],ru:["Британский фунт / Евро"]},e.exports["#GBPJPY-symbol-description"]={en:["British Pound / Japanese Yen"],ru:["Британский фунт / Японская иена"]},e.exports["#GBPNZD-symbol-description"]={en:["British Pound / New Zealand Dollar"],ru:["Британский фунт / Новозеландский доллар"]},e.exports["#GBPRUB-symbol-description"]={en:["British Pound / Russian Ruble"],ru:["Британский фунт / Российский рубль"]},e.exports["#GBPUSD-symbol-description"]={en:["British Pound / U.S. Dollar"],ru:["Британский фунт / Доллар США"]},e.exports["#GER30-symbol-description"]={en:["DAX Index"],ru:["Индекс DAX"]},e.exports["#GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"],ru:["Alphabet Inc (Google) Class A"]},e.exports["#ITA40-symbol-description"]={en:["FTSE MIB Index"],ru:["Индекс FTSE MIB"]},e.exports["#JPN225-symbol-description"]={en:["Nikkei 225 Index"],ru:["Индекс Nikkei 225"]},e.exports["#JPYKRW-symbol-description"]={en:["Japanese Yen / South Korean Won"],ru:["Японская иена / Южнокорейская вона"]},e.exports["#JPYRUB-symbol-description"]={en:["Japanese Yen / Russian Ruble"],ru:["Японская иена / Российский рубль"]},e.exports["#KA1-symbol-description"]={en:["Sugar #11 Futures"],ru:["Фьючерсы на сахар"]},e.exports["#KG1-symbol-description"]={en:["Cotton Futures"],ru:["Фьючерсы на хлопок"]},e.exports["#KT1-symbol-description"]={en:["Key Tronic Corр."],ru:["Key Tronic Corр."]},e.exports["#LKOH-symbol-description"]={en:["LUKOIL"],ru:["ЛУКОЙЛ"]},e.exports["#LTCBTC-symbol-description"]={en:["Litecoin / Bitcoin"],ru:["Лайткоин / Биткоин"]},e.exports["#MGNT-symbol-description"]={en:["Magnit"],ru:["Магнит"]},e.exports["#MICEX-symbol-description"]={en:["MICEX Index"],ru:["Индекс МосБиржи"]},e.exports["#MNOD_ME.EQRP-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],ru:["ОАО ГМК Норильский никель (РЕПО)"]},e.exports["#MSFT-symbol-description"]={en:["Microsoft Corp."],ru:["Microsoft Corporation (Майкрософт)"]},e.exports["#NAS100-symbol-description"]={en:["US 100 Cash CFD"],ru:["US 100 Cash CFD"]},e.exports["#NGAS-symbol-description"]={en:["Natural Gas (Henry Hub)"],ru:["Природный газ (Henry Hub)"]},e.exports["#NKY-symbol-description"]={en:["Nikkei 225 Index"],ru:["Идекс Nikkei 225"]},e.exports["#NZDJPY-symbol-description"]={
en:["New Zealand Dollar / Japanese Yen"],ru:["Новозеландский доллар / Японская иена"]},e.exports["#NZDUSD-symbol-description"]={en:["New Zealand Dollar / U.S. Dollar"],ru:["Новозеландский доллар / Доллар США"]},e.exports["#RB1-symbol-description"]={en:["RBOB Gasoline Futures"],ru:["Фьючерсы на бензин RBOB"]},e.exports["#RTS-symbol-description"]={en:["Russian RTS Index"],ru:["Индекс РТС"]},e.exports["#SBER-symbol-description"]={en:["SBERBANK"],ru:["Сбербанк"]},e.exports["#SPX500-symbol-description"]={en:["S&P 500 Index"],ru:["Индекс S&P 500"]},e.exports["#TWTR-symbol-description"]={en:["Twitter Inc"],ru:["Twitter Inc (Твиттер)"]},e.exports["#UK100-symbol-description"]={en:["FTSE 100 Index"],ru:["Индекс FTSE 100"]},e.exports["#USDBRL-symbol-description"]={en:["U.S. Dollar / Brazilian Real"],ru:["Доллар США / Бразильский реал"]},e.exports["#USDCAD-symbol-description"]={en:["U.S. Dollar / Canadian Dollar"],ru:["Доллар США / Канадский доллар"]},e.exports["#USDCHF-symbol-description"]={en:["U.S. Dollar / Swiss Franc"],ru:["Доллар США / Швейцарский франк"]},e.exports["#USDCNY-symbol-description"]={en:["U.S. Dollar / Chinese Yuan"],ru:["Доллар США / Китайский юань"]},e.exports["#USDDKK-symbol-description"]={en:["U.S. Dollar / Danish Krone"],ru:["Доллар США / Датская крона"]},e.exports["#USDHKD-symbol-description"]={en:["U.S. Dollar / Hong Kong Dollar"],ru:["Доллар США / Гонконгский доллар"]},e.exports["#USDIDR-symbol-description"]={en:["U.S. Dollar / Rupiah"],ru:["Доллар США / Индонезийская рупия"]},e.exports["#USDINR-symbol-description"]={en:["U.S. Dollar / Indian Rupee"],ru:["Доллар США / Индийская рупия"]},e.exports["#USDJPY-symbol-description"]={en:["U.S. Dollar / Japanese Yen"],ru:["Доллар США / Японская иена"]},e.exports["#USDKRW-symbol-description"]={en:["U.S. Dollar / South Korean"],ru:["Доллар США / Южнокорейская вона"]},e.exports["#USDMXN-symbol-description"]={en:["U.S. Dollar / Mexican Peso"],ru:["Доллар США / Мексиканский песо"]},e.exports["#USDPHP-symbol-description"]={en:["U.S. Dollar / Philippine peso"],ru:["Доллар США / Филиппинское песо"]},e.exports["#USDRUB-symbol-description"]={en:["U.S. Dollar / Russian Ruble"],ru:["Доллар США / Российский рубль"]},e.exports["#USDRUB_TOM-symbol-description"]={en:["U.S. Dollar / Russian Ruble TOM"],ru:["Доллар США / Российский рубль TOM"]},e.exports["#USDSEK-symbol-description"]={en:["U.S. Dollar / Swedish Krona"],ru:["Доллар США / Шведская крона"]},e.exports["#USDSGD-symbol-description"]={en:["U.S. Dollar / Singapore Dollar"],ru:["Доллар США / Сингапурский доллар"]},e.exports["#USDTRY-symbol-description"]={en:["U.S. Dollar / Turkish Lira"],ru:["Доллар США / Турецкая лира"]},e.exports["#VTBR-symbol-description"]={en:["VTB"],ru:["Банк ВТБ"]},e.exports["#XAGUSD-symbol-description"]={en:["Silver / U.S. Dollar"],ru:["Серебро / Доллар США"]},e.exports["#XAUUSD-symbol-description"]={en:["Gold Spot / U.S. Dollar"],ru:["Золото / Доллар США"]},e.exports["#XPDUSD-symbol-description"]={en:["CFDs on Palladium"],ru:["CFD на палладий"]},e.exports["#XPTUSD-symbol-description"]={
en:["Platinum / U.S. Dollar"],ru:["Платина / Доллар США"]},e.exports["#ZS1-symbol-description"]={en:["Soybean Futures - ECBT"],ru:["Фьючерсы на соевые бобы - ECBT"]},e.exports["#ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],ru:["Фьючерсы на пшеницу"]},e.exports["#BTCGBP-symbol-description"]={en:["Bitcoin / British Pound"],ru:["Биткоин / Британский фунт"]},e.exports["#MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],ru:["Индекс МосБиржи"]},e.exports["#BTCAUD-symbol-description"]={en:["Bitcoin / Australian Dollar"],ru:["Биткоин / Австралийский доллар"]},e.exports["#BTCJPY-symbol-description"]={en:["Bitcoin / Japanese Yen"],ru:["Биткоин / Японская иена"]},e.exports["#BTCBRL-symbol-description"]={en:["Bitcoin / Brazilian Real"],ru:["Биткоин / Бразильская лира"]},e.exports["#PT10-symbol-description"]={en:["Portugal Government Bonds 10 yr"],ru:["10-летние облигации Португалии"]},e.exports["#TXSX-symbol-description"]={en:["TSX 60 Index"],ru:["Индекс TSX 60"]},e.exports["#VIXC-symbol-description"]={en:["TSX 60 VIX Index"],ru:["Индекс TSX 60 VIX"]},e.exports["#USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],ru:["Доллар США / Польский злотый"]},e.exports["#EURPLN-symbol-description"]={en:["Euro / Polish Zloty"],ru:["Евро / Польский злотый"]},e.exports["#BTCPLN-symbol-description"]={en:["Bitcoin / Polish Zloty"],ru:["Биткоин / Польский злотый"]},e.exports["#CAC40-symbol-description"]={en:["CAC 40 Index"],ru:["Индекс CAC 40"]},e.exports["#XBTCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],ru:["Биткоин / Канадский доллар"]},e.exports["#ITI2!-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIF2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIF2019-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIF2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIG2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIG2019-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIG2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIH2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIH2019-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIH2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIJ2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIJ2019-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIJ2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIK2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIK2019-symbol-description"]={en:["Iron Ore Futures"],
ru:["Фьючерсы на железную руду"]},e.exports["#ITIK2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIM2017-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIM2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIM2019-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIM2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIN2017-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIN2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIN2019-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIN2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIQ2017-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIQ2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIQ2019-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIQ2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIU2017-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIU2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIU2019-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIU2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIV2017-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIV2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIV2019-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIV2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIX2017-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIX2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIX2019-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIX2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIZ2017-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIZ2018-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIZ2019-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#ITIZ2020-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#AMEX:GXF-symbol-description"]={en:["Global x FTSE Nordic Region ETF"],
ru:["Global x FTSE Nordic Region ETF"]},e.exports["#ASX:XAF-symbol-description"]={en:["S&P/ASX All Australian 50 Index"],ru:["Индекс S&P/ASX All Australian 50"]},e.exports["#ASX:XAT-symbol-description"]={en:["S&P/ASX All Australian 200 Index"],ru:["Индекс S&P/ASX All Australian 200"]},e.exports["#BIST:XU100-symbol-description"]={en:["BIST 100 Index"],ru:["Индекс BIST 100"]},e.exports["#GPW:WIG20-symbol-description"]={en:["WIG20 Index"],ru:["Индекс WIG20"]},e.exports["#INDEX:JKSE-symbol-description"]={en:["Jakarta Composite Index"],ru:["Индекс Jakarta Composite"]},e.exports["#INDEX:KLSE-symbol-description"]={en:["Bursa Malaysia KLCI Index"],ru:["Индекс Bursa Malaysia KLCI"]},e.exports["#INDEX:NZD-symbol-description"]={en:["NZX 50 Index"],ru:["Индекс NZX 50"]},e.exports["#INDEX:STI-symbol-description"]={en:["STI Index"],ru:["Индекс STI"]},e.exports["#INDEX:XLY0-symbol-description"]={en:["Shanghai Composite Index"],ru:["Индекс Shanghai Composite"]},e.exports["#MOEX:MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],ru:["Индекс МосБиржи"]},e.exports["#NYMEX:KT1!-symbol-description"]={en:["Coffee Futures"],ru:["Фьючерсы на кофе"]},e.exports["#OANDA:NATGASUSD-symbol-description"]={en:["CFDs on Natural Gas"],ru:["CFD на природный газ"]},e.exports["#OANDA:USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],ru:["Доллар США / Польский злотый"]},e.exports["#TSX:TX60-symbol-description"]={en:["S&P/TSX 60 Index"],ru:["Индекс S&P/TSX 60"]},e.exports["#TSX:VBU-symbol-description"]={en:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"],ru:["Совокупный индекс облигаций США, ETF (CAD-hedged) UN"]},e.exports["#TSX:VIXC-symbol-description"]={en:["S&P/TSX 60 VIX Index"],ru:["Индекс S&P/TSX 60 VIX"]},e.exports["#TVC:CAC40-symbol-description"]={en:["CAC 40 Index"],ru:["Индекс CAC 40"]},e.exports["#TVC:ES10-symbol-description"]={en:["Spain Government Bonds 10 YR"],ru:["10-летние облигации Испании"]},e.exports["#TVC:EUBUND-symbol-description"]={en:["Euro Bund"],ru:["Еврооблигации"]},e.exports["#TVC:GB02-symbol-description"]={en:["UK Government Bonds 2 YR"],ru:["2-летние облигации Британии"]},e.exports["#TVC:GB10-symbol-description"]={en:["UK Government Bonds 10 YR"],ru:["10-летние облигации Британии"]},e.exports["#TVC:GOLD-symbol-description"]={en:["CFDs on Gold (US$ / OZ)"],ru:["CFD на золото (US$ / OZ)"]},e.exports["#TVC:ID03-symbol-description"]={en:["Indonesia Government Bonds 3 YR"],ru:["3-летние облигации Индонезии"]},e.exports["#TVC:ID10-symbol-description"]={en:["Indonesia Government Bonds 10 YR"],ru:["10-летние облигации Индонезии"]},e.exports["#TVC:PALLADIUM-symbol-description"]={en:["CFDs on Palladium (US$ / OZ)"],ru:["CFD на палладий (US$ / OZ)"]},e.exports["#TVC:PT10-symbol-description"]={en:["Portugal Government Bonds 10 YR"],ru:["10-летние облигации Португалии"]},e.exports["#TVC:SILVER-symbol-description"]={en:["CFDs on Silver (US$ / OZ)"],ru:["CFD на серебро (US$ / OZ)"]},e.exports["#TSX:TSX-symbol-description"]={en:["S&P/TSX Composite Index"],ru:["Индекс S&P/TSX Composite"]},
e.exports["#OANDA:CH20CHF-symbol-description"]={en:["Swiss 20 Index"],ru:["Индекс Swiss 20"]},e.exports["#TVC:SHCOMP-symbol-description"]={en:["Shanghai Composite Index"],ru:["Индекс Shanghai Composite"]},e.exports["#NZX:ALLC-symbol-description"]={en:["S&P/NZX All Index (Capital Index)"],ru:["Индекс S&P/NZX ALL (Capital Index)"]},e.exports["#AMEX:SHYG-symbol-description"]={en:["Shares 0-5 YEAR High Yield Corporate Bond ETF"],ru:["Доходность корпоративных облигаций США 0-5 лет"]},e.exports["#TVC:AU10-symbol-description"]={en:["Australia Government Bonds 10 YR"],ru:["10-летние облигации Австралии"]},e.exports["#TVC:CN10-symbol-description"]={en:["China Government Bonds 10 YR"],ru:["10-летние облигации Китая"]},e.exports["#TVC:KR10-symbol-description"]={en:["Korea Government Bonds 10 YR"],ru:["10-летние облигации Кореи"]},e.exports["#NYMEX:RB1!-symbol-description"]={en:["RBOB Gasoline Futures"],ru:["Фьючерсы на бензин RBOB"]},e.exports["#NYMEX:HO1!-symbol-description"]={en:["NY Harbor ULSD Futures"],ru:["Фьючерсы NY Harbor ULSD"]},e.exports["#NYMEX:AEZ1!-symbol-description"]={en:["NY Ethanol Futures"],ru:["Фьючерсы на этанол"]},e.exports["#OANDA:XCUUSD-symbol-description"]={en:["CFDs on Copper (US$ / lb)"],ru:["CFD на медь (US$ / lb)"]},e.exports["#COMEX:ZA1!-symbol-description"]={en:["Zinc Futures"],ru:["Фьючерсы на цинк"]},e.exports["#CBOT:ZW1!-symbol-description"]={en:["Wheat Futures"],ru:["Фьючерсы на пшеницу"]},e.exports["#NYMEX:KA1!-symbol-description"]={en:["Sugar #11 Futures"],ru:["Фьючерсы на сахар США #11"]},e.exports["#CBOT:QBC1!-symbol-description"]={en:["Corn Futures"],ru:["Фьючерсы на кукурузу"]},e.exports["#CME:E61!-symbol-description"]={en:["Euro Futures"],ru:["Фьючерсы на Евро"]},e.exports["#CME:B61!-symbol-description"]={en:["British Pound Futures"],ru:["Фьючерсы на британский фунт"]},e.exports["#CME:QJY1!-symbol-description"]={en:["Japanese Yen Futures"],ru:["Фьючерсы на японскую иену"]},e.exports["#CME:A61!-symbol-description"]={en:["Australian Dollar Futures"],ru:["Фьючерсы на австралийский доллар"]},e.exports["#CME:D61!-symbol-description"]={en:["Canadian Dollar Futures"],ru:["Фьючерсы на канадский доллар"]},e.exports["#CME:SP1!-symbol-description"]={en:["S&P 500 Futures"],ru:["Фьючерсы на S&P 500"]},e.exports["#CME_MINI:NQ1!-symbol-description"]={en:["NASDAQ 100 E-mini Futures"],ru:["Фьючерсы на NASDAQ 100 E-Mini"]},e.exports["#CBOT_MINI:YM1!-symbol-description"]={en:["E-mini Dow Jones ($5) Futures"],ru:["Фьючерсы на E-mini Dow Jones ($5)"]},e.exports["#CME:NY1!-symbol-description"]={en:["NIKKEI 225 Futures"],ru:["Фьючерсы на индекс Nikkei 225"]},e.exports["#EUREX:DY1!-symbol-description"]={en:["DAX Index"],ru:["Индекс DAX"]},e.exports["#CME:IF1!-symbol-description"]={en:["IBOVESPA Index Futures-US$"],ru:["Фьючерсы на индекс IBOVESPA в US$"]},e.exports["#CBOT:TY1!-symbol-description"]={en:["10 Year T-Note Futures"],ru:["Фьючерсы на 10-летние среднесрочные казначейские облигации"]},e.exports["#CBOT:FV1!-symbol-description"]={en:["5 Year T-Note Futures"],
ru:["Фьючерсы на 5-летние среднесрочные казначейские облигации"]},e.exports["#CBOT:ZE1!-symbol-description"]={en:["Treasury Notes - 3 Year Futures"],ru:["Фьючерсы на 3-летние среднесрочные казначейские облигации"]},e.exports["#CBOT:TU1!-symbol-description"]={en:["2 Year T-Note Futures"],ru:["Фьючерсы на 2-летние среднесрочные казначейские облигации"]},e.exports["#CBOT:FF1!-symbol-description"]={en:["30-Day FED Funds Interest Rate Futures"],ru:["Фьючерсы на ставку по федеральным фондам"]},e.exports["#CBOT:US1!-symbol-description"]={en:["T-Bond Futures"],ru:["Фьючерсы на долгосрочные казначейские облигации"]},e.exports["#TVC:EXY-symbol-description"]={en:["Euro Currency Index"],ru:["Индекс евро"]},e.exports["#TVC:JXY-symbol-description"]={en:["Japanese Yen Currency Index"],ru:["Индекс японской иены"]},e.exports["#TVC:BXY-symbol-description"]={en:["British Pound Currency Index"],ru:["Индекс британского фунта"]},e.exports["#TVC:AXY-symbol-description"]={en:["Australian Dollar Currency Index"],ru:["Индекс австралийского доллара"]},e.exports["#TVC:CXY-symbol-description"]={en:["Canadian Dollar Currency Index"],ru:["Индекс канадского доллара"]},e.exports["#FRED:GDP-symbol-description"]={en:["Gross Domestic Product, 1 Decimal"],ru:["Валовой внутренний продукт, 1 десятичный знак"]},e.exports["#FRED:UNRATE-symbol-description"]={en:["Civilian Unemployment Rate"],ru:["Безработица среди гражданского населения"]},e.exports["#FRED:POP-symbol-description"]={en:["Total Population: All Ages Including Armed Forces Overseas"],ru:["Общая численность населения: всех возрастов, включая войска, расположенные за границей"]},e.exports["#ETHUSD-symbol-description"]={en:["Ethereum / U.S. Dollar"],ru:["Эфириум / Доллар США"]},e.exports["#BMFBOVESPA:IBOV-symbol-description"]={en:["Bovespa Index"],ru:["Индекс Bovespa"]},e.exports["#BMFBOVESPA:IBRA-symbol-description"]={en:["IBrasil Index"],ru:["Индекс IBrasil"]},e.exports["#BMFBOVESPA:IBXL-symbol-description"]={en:["Brazil 50 Index"],ru:["Индекс Brazil 50"]},e.exports["#COMEX:HG1!-symbol-description"]={en:["Copper Futures"],ru:["Фьючерсы на медь"]},e.exports["#INDEX:HSCE-symbol-description"]={en:["Hang Seng China Enterprises Index"],ru:["Индекс Hang Seng China Enterprises"]},e.exports["#NYMEX:CL1!-symbol-description"]={en:["Light Crude Oil Futures"],ru:["Фьючерсы на нефть WTI"]},e.exports["#OTC:IHRMF-symbol-description"]={en:["Ishares MSCI Japan SHS"],ru:["Ishares MSCI Japan SHS"]},e.exports["#TVC:DAX-symbol-description"]={en:["DAX Index"],ru:["Индекс DAX"]},e.exports["#TVC:DE10-symbol-description"]={en:["German Government Bonds 10 YR"],ru:["10-летние облигации Германии"]},e.exports["#TVC:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],ru:["Промышленный индекс Доу — Джонса"]},e.exports["#TVC:DXY-symbol-description"]={en:["U.S. Dollar Index"],ru:["Индекс доллара США"]},e.exports["#TVC:FR10-symbol-description"]={en:["France Government Bonds 10 YR"],ru:["10-летние облигации Франции"]},e.exports["#TVC:HSI-symbol-description"]={en:["Hang Seng Index"],ru:["Индекс Hang Seng"]},
e.exports["#TVC:IBEX35-symbol-description"]={en:["IBEX 35 Index"],ru:["Индекс IBEX 35"]},e.exports["#FX:AUS200-symbol-description"]={en:["S&P/ASX Index"],ru:["Индекс S&P/ASX"]},e.exports["#AMEX:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],ru:["Казначейские облигации США 1-3 года (ETF)"]},e.exports["#ASX:XJO-symbol-description"]={en:["S&P/ASX 200 Index"],ru:["Индекс S&P/ASX 200"]},e.exports["#BSE:SENSEX-symbol-description"]={en:["S&P BSE Sensex Index"],ru:["Индекс S&P BSE SENSEX"]},e.exports["#INDEX:MIB-symbol-description"]={en:["MIB Index"],ru:["Индекс MIB"]},e.exports["#INDEX:MOY0-symbol-description"]={en:["Euro Stoxx 50 Index"],ru:["Индекс Euro Stoxx 50"]},e.exports["#MOEX:RTSI-symbol-description"]={en:["RTS Index"],ru:["Индекс РТС"]},e.exports["#NSE:NIFTY-symbol-description"]={en:["Nifty 50 Index"],ru:["Индекс Nifty 50"]},e.exports["#NYMEX:NG1!-symbol-description"]={en:["Natural Gas Futures"],ru:["Фьючерсы на природный газ"]},e.exports["#NYMEX:ZC1!-symbol-description"]={en:["Corn Futures"],ru:["Фьючерсы на кукурузу"]},e.exports["#TVC:IN10-symbol-description"]={en:["India Government Bonds 10 YR"],ru:["10-летние облигации Индии"]},e.exports["#TVC:IT10-symbol-description"]={en:["Italy Government Bonds 10 YR"],ru:["10-летние облигации Италии"]},e.exports["#TVC:JP10-symbol-description"]={en:["Japan Government Bonds 10 YR"],ru:["10-летние облигации Японии"]},e.exports["#TVC:NDX-symbol-description"]={en:["US 100 Index"],ru:["Индекс US 100"]},e.exports["#TVC:NI225-symbol-description"]={en:["Nikkei 225 Index"],ru:["Индекс Nikkei 225"]},e.exports["#TVC:SPX-symbol-description"]={en:["S&P 500 Index"],ru:["Индекс S&P 500"]},e.exports["#TVC:SX5E-symbol-description"]={en:["Euro Stoxx 50 Index"],ru:["Индекс Euro Stoxx 50"]},e.exports["#TVC:TR10-symbol-description"]={en:["Turkey Government Bonds 10 YR"],ru:["10-летние облигации Турции"]},e.exports["#TVC:UKOIL-symbol-description"]={en:["CFDs on Brent Crude Oil"],ru:["Нефть марки Brent"]},e.exports["#TVC:UKX-symbol-description"]={en:["UK 100 Index"],ru:["Индекс UK 100"]},e.exports["#TVC:US02-symbol-description"]={en:["US Government Bonds 2 YR"],ru:["2-летние облигации США"]},e.exports["#TVC:US05-symbol-description"]={en:["US Government Bonds 5 YR"],ru:["5-летние облигации США"]},e.exports["#TVC:US10-symbol-description"]={en:["US Government Bonds 10 YR"],ru:["10-летние облигации США"]},e.exports["#TVC:USOIL-symbol-description"]={en:["CFDs on WTI Crude Oil"],ru:["Нефть марки WTI"]},e.exports["#NYMEX:ITI1!-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#NASDAQ:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],ru:["Казначейские облигации США 1-3 года (ETF)"]},e.exports["#AMEX:ALD-symbol-description"]={en:["WisdomTree Asia Local Debt ETF"],ru:["WisdomTree Asia Local Debt ETF"]},e.exports["#NASDAQ:AMD-symbol-description"]={en:["Advanced Micro Devices Inc"],ru:["AMD (Advanced Micro Devices Inc.)"]},e.exports["#NYSE:BABA-symbol-description"]={en:["Alibaba Group Holdings Ltd."],
ru:["Alibaba Group Holdings Ltd."]},e.exports["#ICEEUR:CB-symbol-description"]={en:["Crude Oil Brent"],ru:["Нефть Brent"]},e.exports["#ICEEUR:CB1!-symbol-description"]={en:["Brent Crude Oil"],ru:["Нефть марки Brent"]},e.exports["#ICEUSA:CC-symbol-description"]={en:["Cocoa"],ru:["Какао"]},e.exports["#NYMEX:CL-symbol-description"]={en:["Crude Oil WTI"],ru:["Нефть марки WTI"]},e.exports["#ICEUSA:CT-symbol-description"]={en:["Cotton #2"],ru:["Хлопок №2"]},e.exports["#NASDAQ:CTRV-symbol-description"]={en:["ContraVir Pharmaceuticals Inc"],ru:["ContraVir Pharmaceuticals Inc"]},e.exports["#CME:DL-symbol-description"]={en:["Class III Milk"],ru:["Молоко класса III"]},e.exports["#NYSE:F-symbol-description"]={en:["FORD MTR CO DEL"],ru:["Ford Motor Company (Форд)"]},e.exports["#MOEX:GAZP-symbol-description"]={en:["GAZPROM"],ru:["Газпром"]},e.exports["#COMEX:GC-symbol-description"]={en:["Gold"],ru:["Золото"]},e.exports["#CME:GF-symbol-description"]={en:["Feeder Cattle"],ru:["Крупный рогатый скот"]},e.exports["#CME:HE-symbol-description"]={en:["Lean Hogs"],ru:["Свинина"]},e.exports["#NASDAQ:IEF-symbol-description"]={en:["Ishares 7-10 Year Treasury Bond ETF"],ru:["Казначейские облигации США 7-10 лет (ETF)"]},e.exports["#NASDAQ:IEI-symbol-description"]={en:["Ishares 3-7 Year Treasury Bond ETF"],ru:["Казначейские облигации США 3-7 лет (ETF)"]},e.exports["#NYMEX:KA1-symbol-description"]={en:["Sugar #11 Futures"],ru:["Фьючерсы на сахар США №11"]},e.exports["#ICEUSA:KC-symbol-description"]={en:["Coffee"],ru:["Кофе"]},e.exports["#NYMEX:KG1-symbol-description"]={en:["Cotton Futures"],ru:["Фьючерсы на хлопок"]},e.exports["#FWB:KT1-symbol-description"]={en:["Key Tronic Corр."],ru:["Key Tronic Corр."]},e.exports["#CME:LE-symbol-description"]={en:["Live Cattle"],ru:["Живой скот"]},e.exports["#ICEEUR:LO-symbol-description"]={en:["ICE Heating Oil"],ru:["Мазут (ICE)"]},e.exports["#CME:LS-symbol-description"]={en:["Lumber"],ru:["Лес (пиломатериалы)"]},e.exports["#MOEX:MGNT-symbol-description"]={en:["MAGNIT"],ru:["Магнит"]},e.exports["#LSIN:MNOD-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],ru:["ОАО ГМК Норильский никель (РЕПО)"]},e.exports["#NYMEX:NG-symbol-description"]={en:["Natural Gas"],ru:["Природный газ"]},e.exports["#ICEUSA:OJ-symbol-description"]={en:["Orange Juice"],ru:["Апельсиновый сок"]},e.exports["#NYMEX:PA-symbol-description"]={en:["Palladium"],ru:["Палладий"]},e.exports["#NYSE:PBR-symbol-description"]={en:["PETROLEO BRASILEIRO SA PETROBR"],ru:["Petroleo Brasileiro Petrobras SA"]},e.exports["#NYMEX:PL-symbol-description"]={en:["Platinum"],ru:["Платина"]},e.exports["#COMEX_MINI:QC-symbol-description"]={en:["E-Mini Copper"],ru:["Медь (E-мини)"]},e.exports["#NYMEX:RB-symbol-description"]={en:["Gasoline RBOB"],ru:["Бензин RBOB"]},e.exports["#NYMEX:RB1-symbol-description"]={en:["RBOB Gasoline Futures"],ru:["Фьючерсы на бензин RBOB"]},e.exports["#MOEX:SBER-symbol-description"]={en:["SBERBANK"],ru:["Сбербанк"]},e.exports["#AMEX:SCHO-symbol-description"]={en:["Schwab Short-Term U.S. Treasury ETF"],
ru:["Краткосрочные облигации США SCHWAB ETF"]},e.exports["#COMEX:SI-symbol-description"]={en:["Silver"],ru:["Серебро"]},e.exports["#NASDAQ:TLT-symbol-description"]={en:["Ishares 20+ Year Treasury Bond ETF"],ru:["Казначейские облигации США 20+ лет (ETF)"]},e.exports["#TVC:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],ru:["Индекс волатильности S&P 500"]},e.exports["#MOEX:VTBR-symbol-description"]={en:["VTB"],ru:["Банк ВТБ"]},e.exports["#COMEX:ZA-symbol-description"]={en:["Zinc"],ru:["Цинк"]},e.exports["#CBOT:ZC-symbol-description"]={en:["Corn"],ru:["Кукуруза"]},e.exports["#CBOT:ZK-symbol-description"]={en:["Ethanol Futures"],ru:["Фьючерсы на этанол"]},e.exports["#CBOT:ZL-symbol-description"]={en:["Soybean Oil"],ru:["Соевое масло"]},e.exports["#CBOT:ZO-symbol-description"]={en:["Oats"],ru:["Овёс"]},e.exports["#CBOT:ZR-symbol-description"]={en:["Rough Rice"],ru:["Грубый рис"]},e.exports["#CBOT:ZS-symbol-description"]={en:["Soybeans"],ru:["Соевые бобы"]},e.exports["#CBOT:ZS1-symbol-description"]={en:["Soybean Futures"],ru:["Фьючерсы на соевые бобы"]},e.exports["#CBOT:ZW-symbol-description"]={en:["Wheat"],ru:["Пшеница"]},e.exports["#CBOT:ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],ru:["Фьючерсы на пшеницу"]},e.exports["#NASDAQ:ITI-symbol-description"]={en:["Iteris Inc"],ru:["Iteris Inc"]},e.exports["#NYMEX:ITI2!-symbol-description"]={en:["Iron Ore Futures"],ru:["Фьючерсы на железную руду"]},e.exports["#CADUSD-symbol-description"]={en:["Canadian Dollar / U.S. Dollar"],ru:["Канадский доллар / Доллар США"]},e.exports["#CHFUSD-symbol-description"]={en:["Swiss Franc / U.S. Dollar"],ru:["Швейцарский франк / Доллар США"]},e.exports["#GPW:ACG-symbol-description"]={en:["Acautogaz"],ru:["Acautogaz"]},e.exports["#JPYUSD-symbol-description"]={en:["Japanese Yen / U.S. Dollar"],ru:["Японская иена / Доллар США"]},e.exports["#USDAUD-symbol-description"]={en:["U.S. Dollar / Australian Dollar"],ru:["Доллар США / Австралийский доллар"]},e.exports["#USDEUR-symbol-description"]={en:["U.S. Dollar / Euro"],ru:["Доллар США / Евро"]},e.exports["#USDGBP-symbol-description"]={en:["U.S. Dollar / Pound Sterling"],ru:["Доллар США / Британский фунт"]},e.exports["#USDNZD-symbol-description"]={en:["U.S. Dollar / New Zealand Dollar"],ru:["Доллар США / Новозеландский доллар"]},e.exports["#UKOIL-symbol-description"]={en:["CFDs on Crude Oil (Brent)"],ru:["Нефть марки Brent"]},e.exports["#USOIL-symbol-description"]={en:["CFDs on Crude Oil (WTI)"],ru:["Нефть марки WTI"]},e.exports["#US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],ru:["Промышленный индекс Доу — Джонса"]},e.exports["#BCHUSD-symbol-description"]={en:["Bitcoin Cash / U.S. Dollar"],ru:["Bitcoin Cash / Доллар США"]},e.exports["#ETCUSD-symbol-description"]={en:["Ethereum Classic / U.S. Dollar"],ru:["Эфириум Классик / Доллар США"]},e.exports["#GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"],ru:["Alphabet Inc (Google) Class C"]},e.exports["#LTCUSD-symbol-description"]={en:["Litecoin / U.S. Dollar"],ru:["Лайткоин / Доллар США"]},
e.exports["#XRPUSD-symbol-description"]={en:["XRP / U.S. Dollar"],ru:["XRP / Доллар США"]},e.exports["#SP:SPX-symbol-description"]={en:["S&P 500 Index"],ru:["Индекс S&P 500"]},e.exports["#ETCBTC-symbol-description"]={en:["Ethereum Classic / Bitcoin"],ru:["Эфириум Классик / Биткоин"]},e.exports["#ETHBTC-symbol-description"]={en:["Ethereum / Bitcoin"],ru:["Эфириум / Биткоин"]},e.exports["#XRPBTC-symbol-description"]={en:["XRP / Bitcoin"],ru:["XRP / Биткоин"]},e.exports["#TVC:US30-symbol-description"]={en:["US Government Bonds 30 YR"],ru:["30-летние облигации США"]},e.exports["#COMEX:SI1!-symbol-description"]={en:["Silver Futures"],ru:["Фьючерсы на серебро"]},e.exports["#BTGUSD-symbol-description"]={en:["Bitcoin Gold / U.S. Dollar"],ru:["Bitcoin Gold / Доллар США"]},e.exports["#IOTUSD-symbol-description"]={en:["IOTA / U.S. Dollar"],ru:["IOTA / Доллар США"]},e.exports["#CME:BTC1!-symbol-description"]={en:["Bitcoin CME Futures"],ru:["Фьючерсы на биткоин (CME)"]},e.exports["#COMEX:GC1!-symbol-description"]={en:["Gold Futures"],ru:["Фьючерсы на золото"]},e.exports["#CORNUSD-symbol-description"]={en:["CFDs on Corn"],ru:["CFD на кукурузу"]},e.exports["#COTUSD-symbol-description"]={en:["CFDs on Cotton"],ru:["CFD на хлопок"]},e.exports["#DJ:DJA-symbol-description"]={en:["Dow Jones Composite Average Index"],ru:["Индекс Dow Jones Composite Average"]},e.exports["#DJ:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],ru:["Промышленный индекс Доу — Джонса"]},e.exports["#ETHEUR-symbol-description"]={en:["Ethereum / Euro"],ru:["Эфириум / Евро"]},e.exports["#ETHGBP-symbol-description"]={en:["Ethereum / British Pound"],ru:["Эфириум / Британский фунт"]},e.exports["#ETHJPY-symbol-description"]={en:["Ethereum / Japanese Yen"],ru:["Эфириум / Японская иена"]},e.exports["#EURNOK-symbol-description"]={en:["Euro / Norwegian Krone"],ru:["Евро / Норвежская крона"]},e.exports["#GBPPLN-symbol-description"]={en:["British Pound / Polish Zloty"],ru:["Британский фунт / Польский злотый"]},e.exports["#MOEX:BR1!-symbol-description"]={en:["Brent Oil Futures"],ru:["Фьючерсы на нефть Brent"]},e.exports["#NYMEX:KG1!-symbol-description"]={en:["Cotton Futures"],ru:["Фьючерсы на хлопок"]},e.exports["#NYMEX:PL1!-symbol-description"]={en:["Platinum Futures"],ru:["Фьючерсы на платину"]},e.exports["#SOYBNUSD-symbol-description"]={en:["CFDs on Soybeans"],ru:["CFD на соевые бобы"]},e.exports["#SUGARUSD-symbol-description"]={en:["CFDs on Sugar"],ru:["CFD на сахар"]},e.exports["#TVC:IXIC-symbol-description"]={en:["US Composite Index"],ru:["Индекс US Composite"]},e.exports["#TVC:RU-symbol-description"]={en:["Russell 1000 Index"],ru:["Индекс Russell 1000"]},e.exports["#USDZAR-symbol-description"]={en:["U.S. Dollar / South African Rand"],ru:["Доллар США / Южноафриканский ранд"]},e.exports["#WHEATUSD-symbol-description"]={en:["CFDs on Wheat"],ru:["CFD на пшеницу"]},e.exports["#XRPEUR-symbol-description"]={en:["XRP / Euro"],ru:["XRP / Евро"]},e.exports["#CBOT:S1!-symbol-description"]={en:["Soybean Futures"],ru:["Фьючерсы на соевые бобы"]},
e.exports["#SP:MID-symbol-description"]={en:["S&P 400 Index"],ru:["Индекс S&P 400"]},e.exports["#TSX:XCUUSD-symbol-description"]={en:["CFDs on Copper"],ru:["CFD на медь"]},e.exports["#TVC:NYA-symbol-description"]={en:["NYSE Composite Index"],ru:["Индекс NYSE Composite"]},e.exports["#TVC:PLATINUM-symbol-description"]={en:["CFDs on Platinum (US$ / OZ)"],ru:["CFD на платину (US$ / OZ)"]},e.exports["#TVC:SSMI-symbol-description"]={en:["Swiss Market Index"],ru:["Индекс Swiss Market"]},e.exports["#TVC:SXY-symbol-description"]={en:["Swiss Franc Currency Index"],ru:["Индекс швейцарского франка"]},e.exports["#MOEX:RI1!-symbol-description"]={en:["RTS Index Futures"],ru:["Фьючерсы на индекс РТС"]},e.exports["#MOEX:MX1!-symbol-description"]={en:["MICEX Index Futures"],ru:["Фьючерсы на индекс МосБиржи"]},e.exports["#CBOE:BG1!-symbol-description"]={en:["Bitcoin CBOE Futures"],ru:["Фьючерсы на биткоин (CBOE)"]},e.exports["#TVC:MY10-symbol-description"]={en:["Malaysia Government Bonds 10 YR"],ru:["10-летние облигации Малайзии"]},e.exports["#CME:S61!-symbol-description"]={en:["Swiss Franc Futures"],ru:["Фьючерсы на швейцарский франк"]},e.exports["#TVC:DEU30-symbol-description"]={en:["DAX Index"],ru:["Индекс DAX"]},e.exports["#BCHEUR-symbol-description"]={en:["Bitcoin Cash / Euro"],ru:["Bitcoin Cash / Евро"]},e.exports["#TVC:ZXY-symbol-description"]={en:["New Zealand Dollar Currency Index"],ru:["Индекс новозеландского доллара"]},e.exports["#MIL:FTSEMIB-symbol-description"]={en:["FTSE MIB Index"],ru:["Индекс FTSE MIB"]},e.exports["#XETR:DAX-symbol-description"]={en:["DAX Index"],ru:["Индекс DAX"]},e.exports["#MOEX:IMOEX-symbol-description"]={en:["MOEX Russia Index"],ru:["Индекс МосБиржи"]},e.exports["#FX:US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],ru:["Промышленный индекс Доу — Джонса"]},e.exports["#MOEX:RUAL-symbol-description"]={en:["United Company RUSAL PLC"],ru:["United Company RUSAL PLC"]},e.exports["#MOEX:MX2!-symbol-description"]={en:["MICEX Index Futures"],ru:["Фьючерсы на индекс МосБиржи"]},e.exports["#NEOUSD-symbol-description"]={en:["NEO / U.S. Dollar"],ru:["NEO / Доллар США"]},e.exports["#XMRUSD-symbol-description"]={en:["Monero / U.S. Dollar"],ru:["Монеро / Доллар США"]},e.exports["#ZECUSD-symbol-description"]={en:["Zcash / U.S. Dollar"],ru:["Zcash / Доллар США"]},e.exports["#TVC:CAC-symbol-description"]={en:["CAC 40 Index"],ru:["Индекс CAC 40"]},e.exports["#NASDAQ:ZS-symbol-description"]={en:["Zscaler Inc"],ru:["Zscaler Inc"]},e.exports["#TVC:GB10Y-symbol-description"]={en:["UK Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Британии"]},e.exports["#TVC:AU10Y-symbol-description"]={en:["Australia Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Австралии"]},e.exports["#TVC:CN10Y-symbol-description"]={en:["China Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Китая"]},e.exports["#TVC:DE10Y-symbol-description"]={en:["German Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Германии"]},e.exports["#TVC:ES10Y-symbol-description"]={
en:["Spain Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Испании"]},e.exports["#TVC:FR10Y-symbol-description"]={en:["France Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Франции"]},e.exports["#TVC:IN10Y-symbol-description"]={en:["India Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Индии"]},e.exports["#TVC:IT10Y-symbol-description"]={en:["Italy Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Италии"]},e.exports["#TVC:JP10Y-symbol-description"]={en:["Japan Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Японии"]},e.exports["#TVC:KR10Y-symbol-description"]={en:["Korea Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Кореи"]},e.exports["#TVC:MY10Y-symbol-description"]={en:["Malaysia Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Малайзии"]},e.exports["#TVC:PT10Y-symbol-description"]={en:["Portugal Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Португалии"]},e.exports["#TVC:TR10Y-symbol-description"]={en:["Turkey Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Турции"]},e.exports["#TVC:US02Y-symbol-description"]={en:["US Government Bonds 2 YR Yield"],ru:["Доходность 2-летних облигаций США"]},e.exports["#TVC:US05Y-symbol-description"]={en:["US Government Bonds 5 YR Yield"],ru:["Доходность 5-летних облигаций США"]},e.exports["#TVC:US10Y-symbol-description"]={en:["US Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций США"]},e.exports["#INDEX:TWII-symbol-description"]={en:["Taiwan Weighted Index"],ru:["Тайваньский взвешенный индекс"]},e.exports["#CME:J61!-symbol-description"]={en:["Japanese Yen Futures"],ru:["Фьючерсы на японскую иену"]},e.exports["#CME_MINI:J71!-symbol-description"]={en:["Japanese Yen E-mini Futures"],ru:["Фьючерсы на японскую иену (E-mini)"]},e.exports["#CME_MINI:WM1!-symbol-description"]={en:["E-micro Japanese Yen / U.S. Dollar Futures"],ru:["Фьючерсы на японскую иену / доллар США (E-micro)"]},e.exports["#CME:M61!-symbol-description"]={en:["Mexican Peso Futures"],ru:["Фьючерсы на мексиканский песо"]},e.exports["#CME:T61!-symbol-description"]={en:["South African Rand Futures"],ru:["Фьючерсы на южноафриканский ранд"]},e.exports["#CME:SK1!-symbol-description"]={en:["Swedish Krona Futures"],ru:["Фьючерсы на шведскую крону"]},e.exports["#CME:QT1!-symbol-description"]={en:["Chinese Renminbi / U.S. Dollar Futures"],ru:["Фьючерсы на китайский юань Жэньминьби / доллар США"]},e.exports["#COMEX:AUP1!-symbol-description"]={en:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"],ru:["Фьючерсы на Aluminum MW U.S. Transaction Premium Platts (25MT)"]},e.exports["#CME:L61!-symbol-description"]={en:["Brazilian Real Futures"],ru:["Фьючерсы на бразильский реал"]},e.exports["#CME:WP1!-symbol-description"]={en:["Polish Zloty Futures"],ru:["Фьючерсы на польский злотый"]},e.exports["#CME:N61!-symbol-description"]={en:["New Zealand Dollar Futures"],ru:["Фьючерсы на новозеландский доллар"]},
e.exports["#CME_MINI:MG1!-symbol-description"]={en:["E-micro Australian Dollar / U.S. Dollar Futures"],ru:["Фьючерсы на австралийский доллар / доллар США (E-micro)"]},e.exports["#CME_MINI:WN1!-symbol-description"]={en:["E-micro Swiss Franc / U.S. Dollar Futures"],ru:["Фьючерсы на швейцарский франк / доллар США (E-micro)"]},e.exports["#CME_MINI:MF1!-symbol-description"]={en:["E-micro Euro / U.S. Dollar Futures"],ru:["Фьючерсы на евро / доллар США (E-micro)"]},e.exports["#CME_MINI:E71!-symbol-description"]={en:["Euro E-mini Futures"],ru:["Фьючерсы на евро (E-mini)"]},e.exports["#CBOT:ZK1!-symbol-description"]={en:["Denatured Fuel Ethanol Futures"],ru:["Фьючерсы на этанол"]},e.exports["#CME_MINI:MB1!-symbol-description"]={en:["E-micro British Pound / U.S. Dollar Futures"],ru:["Фьючерсы на британский фунт / доллар США (E-micro)"]},e.exports["#NYMEX_MINI:QU1!-symbol-description"]={en:["E-mini Gasoline Futures"],ru:["Фьючерсы на бензин (E-mini)"]},e.exports["#NYMEX_MINI:QX1!-symbol-description"]={en:["E-mini Heating Oil Futures"],ru:["Фьючерсы на мазут (E-mini)"]},e.exports["#COMEX_MINI:QC1!-symbol-description"]={en:["E-mini Copper Futures"],ru:["Фьючерсы на медь (E-mini)"]},e.exports["#NYMEX_MINI:QG1!-symbol-description"]={en:["E-mini Natural Gas Futures"],ru:["Фьючерсы на природный газ (E-mini)"]},e.exports["#CME:E41!-symbol-description"]={en:["U.S. Dollar / Turkish Lira Futures"],ru:["Фьючерсы на доллар США / турецкую лиру"]},e.exports["#COMEX_MINI:QI1!-symbol-description"]={en:["Silver (Mini) Futures"],ru:["Фьючерсы на серебро (мини)"]},e.exports["#CME:DL1!-symbol-description"]={en:["Milk, Class III Futures"],ru:["Фьючерсы на молоко класса III"]},e.exports["#NYMEX:UX1!-symbol-description"]={en:["Uranium Futures"],ru:["Фьючерсы на уран"]},e.exports["#CBOT:BO1!-symbol-description"]={en:["Soybean Oil Futures"],ru:["Фьючерсы на соевое масло"]},e.exports["#CME:HE1!-symbol-description"]={en:["Lean Hogs Futures"],ru:["Фьючерсы на свинину"]},e.exports["#NYMEX:IAC1!-symbol-description"]={en:["Newcastle Coal Futures"],ru:["Фьючерсы на Newcastle Coal"]},e.exports["#NYMEX_MINI:QM1!-symbol-description"]={en:["E-mini Light Crude Oil Futures"],ru:["Фьючерсы на E-mini Light Crude Oil"]},e.exports["#NYMEX:JMJ1!-symbol-description"]={en:["Mini Brent Financial Futures"],ru:["Фьючерсы на Mini Brent Financial"]},e.exports["#COMEX:AEP1!-symbol-description"]={en:["Aluminium European Premium Futures"],ru:["Фьючерсы на Aluminium European Premium"]},e.exports["#CBOT:ZQ1!-symbol-description"]={en:["30 Day Federal Funds Interest Rate Futures"],ru:["Фьючерсы на 30-дневную процентную ставку по федеральным фондам"]},e.exports["#CME:LE1!-symbol-description"]={en:["Live Cattle Futures"],ru:["Фьючерсы на живой скот"]},e.exports["#CME:UP1!-symbol-description"]={en:["Swiss Franc / Japanese Yen Futures"],ru:["Фьючерсы на швейцарский франк / японскую иену"]},e.exports["#CBOT:ZN1!-symbol-description"]={en:["10 Year T-Note Futures"],ru:["Фьючерсы на 10-летние среднесрочные казначейские облигации"]},e.exports["#CBOT:ZB1!-symbol-description"]={
en:["T-Bond Futures"],ru:["Фьючерсы на долгосрочные казначейские облигации"]},e.exports["#CME:GF1!-symbol-description"]={en:["Feeder Cattle Futures"],ru:["Фьючерсы на крупный рогатый скот"]},e.exports["#CBOT:UD1!-symbol-description"]={en:["Ultra T-Bond Futures"],ru:["Фьючерсы на Ultra T-Bond"]},e.exports["#CME:I91!-symbol-description"]={en:["CME Housing Futures — Washington DC"],ru:["CME фьючерсы на недвижимость — Washington DC"]},e.exports["#CBOT:ZO1!-symbol-description"]={en:["Oat Futures"],ru:["Фьючерсы на овёс"]},e.exports["#CBOT:ZM1!-symbol-description"]={en:["Soybean Meal Futures"],ru:["Фьючерсы на соевую муку"]},e.exports["#CBOT_MINI:XN1!-symbol-description"]={en:["Corn Mini Futures"],ru:["Фьючерсы на кукурузу (мини)"]},e.exports["#CBOT:ZC1!-symbol-description"]={en:["Corn Futures"],ru:["Фьючерсы на кукурузу"]},e.exports["#CME:LS1!-symbol-description"]={en:["Lumber Futures"],ru:["Фьючерсы на лесоматериалы"]},e.exports["#CBOT_MINI:XW1!-symbol-description"]={en:["Wheat Mini Futures"],ru:["Фьючерсы на пшеницу (мини)"]},e.exports["#CBOT_MINI:XK1!-symbol-description"]={en:["Soybean Mini Futures"],ru:["Фьючерсы на соевые бобы (мини)"]},e.exports["#CBOT:ZS1!-symbol-description"]={en:["Soybean Futures"],ru:["Фьючерсы на соевые бобы"]},e.exports["#NYMEX:PA1!-symbol-description"]={en:["Palladium Futures"],ru:["Фьючерсы на палладий"]},e.exports["#CME:FTU1!-symbol-description"]={en:["E-mini FTSE 100 Index USD Futures"],ru:["Фьючерсы на E-mini FTSE 100 Index USD"]},e.exports["#CBOT:ZR1!-symbol-description"]={en:["Rice Futures"],ru:["Фьючерсы на рис"]},e.exports["#COMEX_MINI:GR1!-symbol-description"]={en:["Gold (E-micro) Futures"],ru:["Фьючерсы на золото (E-micro)"]},e.exports["#COMEX_MINI:QO1!-symbol-description"]={en:["Gold (Mini) Futures"],ru:["Фьючерсы на золото (мини)"]},e.exports["#CME_MINI:RL1!-symbol-description"]={en:["E-mini Russell 1000 Futures"],ru:["Фьючерсы на E-mini Russell 1000"]},e.exports["#CME_MINI:EW1!-symbol-description"]={en:["S&P 400 Midcap E-mini Futures"],ru:["Фьючерсы на S&P 400 Midcap E-mini"]},e.exports["#COMEX:LD1!-symbol-description"]={en:["Lead Futures"],ru:["Фьючерсы на свинец"]},e.exports["#CME_MINI:ES1!-symbol-description"]={en:["S&P 500 E-mini Futures"],ru:["Фьючерсы на E-mini S&P 500"]},e.exports["#TVC:SA40-symbol-description"]={en:["South Africa Top 40 Index"],ru:["Индекс South Africa Top 40"]},e.exports["#BMV:ME-symbol-description"]={en:["S&P/BMV IPC Index"],ru:["Индекс S&P/BMV IPC"]},e.exports["#BCBA:IMV-symbol-description"]={en:["S&P MERVAL Index"],ru:["Индекс S&P MERVAL"]},e.exports["#HSI:HSI-symbol-description"]={en:["Hang Seng Index"],ru:["Индекс Hang Seng"]},e.exports["#BVL:SPBLPGPT-symbol-description"]={en:["S&P / BVL Peru General Index (PEN)"],ru:["Индекс S&P / BVL Peru General (PEN)"]},e.exports["#EGX:EGX30-symbol-description"]={en:["EGX 30 Index"],ru:["Индекс EGX 30"]},e.exports["#BVC:IGBC-symbol-description"]={en:["Indice General de la Bolsa de Valores de Colombia"],ru:["Индекс IGBC"]},e.exports["#TWSE:TAIEX-symbol-description"]={
en:["Taiwan Capitalization Weighted Stock Index"],ru:["Индекс Taiwan Capitalization Weighted Stock"]},e.exports["#QSE:GNRI-symbol-description"]={en:["QE Index"],ru:["Индекс QE"]},e.exports["#BME:IBC-symbol-description"]={en:["IBEX 35 Index"],ru:["Индекс IBEX 35"]},e.exports["#NZX:NZ50G-symbol-description"]={en:["S&P / NZX 50 Index Gross"],ru:["Индекс S&P / NZX 50 Gross"]},e.exports["#SIX:SMI-symbol-description"]={en:["Swiss Market Index"],ru:["Индекс Swiss Market"]},e.exports["#SZSE:399001-symbol-description"]={en:["Shenzhen Component Index"],ru:["Индекс Shenzhen Component"]},e.exports["#TADAWUL:TASI-symbol-description"]={en:["Tadawul All Shares Index"],ru:["Индекс Tadawul All Shares"]},e.exports["#IDX:COMPOSITE-symbol-description"]={en:["IDX Composite Index"],ru:["Индекс IDX Composite"]},e.exports["#EURONEXT:PX1-symbol-description"]={en:["CAC 40 Index"],ru:["Индекс CAC 40"]},e.exports["#OMXHEX:OMXH25-symbol-description"]={en:["OMX Helsinki 25 Index"],ru:["Индекс OMX Helsinki 25"]},e.exports["#EURONEXT:BEL20-symbol-description"]={en:["BEL 20 Index"],ru:["Индекс BEL 20"]},e.exports["#TVC:STI-symbol-description"]={en:["Straits Times Index"],ru:["Индекс Straits Times"]},e.exports["#DFM:DFMGI-symbol-description"]={en:["DFM Index"],ru:["Индекс DFM"]},e.exports["#TVC:KOSPI-symbol-description"]={en:["Korea Composite Stock Price Index"],ru:["Индекс Korea Composite Stock Price"]},e.exports["#FTSEMYX:FBMKLCI-symbol-description"]={en:["FTSE Bursa Malaysia KLCI Index"],ru:["Индекс FTSE Bursa Malaysia KLCI"]},e.exports["#TASE:TA35-symbol-description"]={en:["TA-35 Index"],ru:["Индекс TA-35"]},e.exports["#OMXSTO:OMXS30-symbol-description"]={en:["OMX Stockholm 30 Index"],ru:["Индекс OMX Stockholm 30"]},e.exports["#OMXICE:OMXI8-symbol-description"]={en:["OMX Iceland 8 Index"],ru:["Индекс OMX Iceland 8"]},e.exports["#NSENG:NSE30-symbol-description"]={en:["NSE 30 Index"],ru:["Индекс NSE 30"]},e.exports["#BAHRAIN:BSEX-symbol-description"]={en:["Bahrain All Share Index"],ru:["Индекс Bahrain All Share"]},e.exports["#OMXTSE:OMXTGI-symbol-description"]={en:["OMX Tallinn Gross Index"],ru:["Индекс OMX Tallinn Gross"]},e.exports["#OMXCOP:OMXC25-symbol-description"]={en:["OMX Copenhagen 25 Index"],ru:["Индекс OMX Copenhagen 25"]},e.exports["#OMXRSE:OMXRGI-symbol-description"]={en:["OMX Riga Gross Index"],ru:["Индекс OMX Riga Gross"]},e.exports["#BELEX:BELEX15-symbol-description"]={en:["BELEX 15 Index"],ru:["Индекс BELEX 15"]},e.exports["#OMXVSE:OMXVGI-symbol-description"]={en:["OMX Vilnius Gross Index"],ru:["Индекс OMX Vilnius Gross"]},e.exports["#EURONEXT:AEX-symbol-description"]={en:["AEX Index"],ru:["Индекс AEX"]},e.exports["#CBOE:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],ru:["Индекс волатильности S&P 500"]},e.exports["#NASDAQ:XAU-symbol-description"]={en:["PHLX Gold and Silver Sector Index"],ru:["Индекс PHLX Gold and Silver Sector"]},e.exports["#DJ:DJUSCL-symbol-description"]={en:["Dow Jones U.S. Coal Index"],ru:["Индекс Dow Jones U.S. Coal"]},e.exports["#DJ:DJCIKC-symbol-description"]={
en:["Dow Jones Commodity Index Coffee"],ru:["Индекс Dow Jones Commodity Coffee"]},e.exports["#DJ:DJCIEN-symbol-description"]={en:["Dow Jones Commodity Index Energy"],ru:["Индекс Dow Jones Commodity Energy"]},e.exports["#NASDAQ:OSX-symbol-description"]={en:["PHLX Oil Service Sector Index"],ru:["Индекс PHLX Oil Service Sector"]},e.exports["#DJ:DJCISB-symbol-description"]={en:["Dow Jones Commodity Index Sugar"],ru:["Индекс Dow Jones Commodity Sugar"]},e.exports["#DJ:DJCICC-symbol-description"]={en:["Dow Jones Commodity Index Cocoa"],ru:["Индекс Dow Jones Commodity Cocoa"]},e.exports["#DJ:DJCIGR-symbol-description"]={en:["Dow Jones Commodity Index Grains"],ru:["Индекс Dow Jones Commodity Grains"]},e.exports["#DJ:DJCIAGC-symbol-description"]={en:["Dow Jones Commodity Index Agriculture Capped Component"],ru:["Индекс Dow Jones Commodity Agriculture Capped Component"]},e.exports["#DJ:DJCISI-symbol-description"]={en:["Dow Jones Commodity Index Silver"],ru:["Индекс Dow Jones Commodity Silver"]},e.exports["#DJ:DJCIIK-symbol-description"]={en:["Dow Jones Commodity Index Nickel"],ru:["Индекс Dow Jones Commodity Nickel"]},e.exports["#NASDAQ:HGX-symbol-description"]={en:["PHLX Housing Sector Index"],ru:["Индекс PHLX Housing Sector"]},e.exports["#DJ:DJCIGC-symbol-description"]={en:["Dow Jones Commodity Index Gold"],ru:["Индекс Dow Jones Commodity Gold"]},e.exports["#SP:SPGSCI-symbol-description"]={en:["S&P Goldman Sachs Commodity Index"],ru:["Индекс S&P Goldman Sachs Commodity"]},e.exports["#NASDAQ:UTY-symbol-description"]={en:["PHLX Utility Sector Index"],ru:["Индекс PHLX Utility Sector"]},e.exports["#DJ:DJU-symbol-description"]={en:["Dow Jones Utility Average Index"],ru:["Индекс Dow Jones Utility Average"]},e.exports["#SP:SVX-symbol-description"]={en:["S&P 500 Value Index"],ru:["Индекс S&P 500 Value"]},e.exports["#SP:OEX-symbol-description"]={en:["S&P 100 Index"],ru:["Индекс S&P 100"]},e.exports["#CBOE:OEX-symbol-description"]={en:["S&P 100 Index"],ru:["Индекс S&P 100"]},e.exports["#NASDAQ:SOX-symbol-description"]={en:["Philadelphia Semiconductor Index"],ru:["Индекс Philadelphia Semiconductor"]},e.exports["#RUSSELL:RUI-symbol-description"]={en:["Russell 1000 Index"],ru:["Индекс Russell 1000"]},e.exports["#RUSSELL:RUA-symbol-description"]={en:["Russell 3000 Index"],ru:["Индекс Russell 3000"]},e.exports["#RUSSELL:RUT-symbol-description"]={en:["Russell 2000 Index"],ru:["Индекс Russell 2000"]},e.exports["#NYSE:XMI-symbol-description"]={en:["NYSE ARCA Major Market Index"],ru:["Индекс NYSE ARCA Major Market"]},e.exports["#NYSE:XAX-symbol-description"]={en:["AMEX Composite Index"],ru:["Индекс AMEX Composite"]},e.exports["#NASDAQ:NDX-symbol-description"]={en:["Nasdaq 100 Index"],ru:["Индекс Nasdaq 100"]},e.exports["#NASDAQ:IXIC-symbol-description"]={en:["Nasdaq Composite Index"],ru:["Индекс Nasdaq Composite"]},e.exports["#DJ:DJT-symbol-description"]={en:["Dow Jones Transportation Average Index"],ru:["Индекс Dow Jones Transportation Average"]},e.exports["#NYSE:NYA-symbol-description"]={en:["NYSE Composite Index"],
ru:["Индекс NYSE Composite"]},e.exports["#NYMEX:CJ1!-symbol-description"]={en:["Cocoa Futures"],ru:["Фьючерсы на какао"]},e.exports["#USDILS-symbol-description"]={en:["U.S. Dollar / Israeli Shekel"],ru:["Доллар США / Израильский шекель"]},e.exports["#TSXV:F-symbol-description"]={en:["Fiore Gold Inc"],ru:["Fiore Gold Inc"]},e.exports["#SIX:F-symbol-description"]={en:["Ford Motor Company"],ru:["Ford Motor Company (Форд)"]},e.exports["#BMV:F-symbol-description"]={en:["Ford Motor Company"],ru:["Ford Motor Company (Форд)"]},e.exports["#TWII-symbol-description"]={en:["Taiwan Weighted Index"],ru:["Тайваньский взвешенный индекс"]},e.exports["#TVC:PL10Y-symbol-description"]={en:["Poland Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Польши"]},e.exports["#TVC:PL05Y-symbol-description"]={en:["Poland Government Bonds 5 YR Yield"],ru:["Доходность 5-летних облигаций Польши"]},e.exports["#SET:GC-symbol-description"]={en:["Global Connections Public Company"],ru:["Global Connections Public Company"]},e.exports["#TSX:GC-symbol-description"]={en:["Great Canadian Gaming Corporation"],ru:["Great Canadian Gaming Corporation"]},e.exports["#TVC:FTMIB-symbol-description"]={en:["Milano Italia Borsa Index"],ru:["Индекс MIB"]},e.exports["#OANDA:SPX500USD-symbol-description"]={en:["S&P 500 Index"],ru:["Индекс S&P 500"]},e.exports["#BMV:CT-symbol-description"]={en:["China SX20 RT"],ru:["China SX20 RT"]},e.exports["#TSXV:CT-symbol-description"]={en:["Centenera Mining Corporation"],ru:["Centenera Mining Corporation"]},e.exports["#BYBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Contract"],ru:["ETHUSD Бессрочный контракт"]},e.exports["#BYBIT:XRPUSD-symbol-description"]={en:["XRPUSD Perpetual Contract"],ru:["XRPUSD Бессрочный контракт"]},e.exports["#BYBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Contract"],ru:["BTCUSD Бессрочный контракт"]},e.exports["#BITMEX:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],ru:["ETHUSD Бессрочный контракт"]},e.exports["#DERIBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Futures Contract"],ru:["BTCUSD Бессрочный контракт"]},e.exports["#DERIBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],ru:["ETHUSD Бессрочный контракт"]},e.exports["#USDHUF-symbol-description"]={en:["U.S. Dollar / Hungarian Forint"],ru:["Доллар США / Венгерский форинт"]},e.exports["#USDTHB-symbol-description"]={en:["U.S. Dollar / Thai Baht"],ru:["Доллар США / Тайский бат"]},e.exports["#FOREXCOM:US2000-symbol-description"]={en:["US Small Cap 2000"],ru:["US Small Cap 2000"]},e.exports["#TSXV:PBR-symbol-description"]={en:["Para Resources Inc"],ru:["Para Resources Inc"]},e.exports["#NYSE:SI-symbol-description"]={en:["Silvergate Capital Corporation"],ru:["Silvergate Capital Corporation"]},e.exports["#NASDAQ:LE-symbol-description"]={en:["Lands' End Inc"],ru:["Lands' End Inc"]},e.exports["#CME:CB1!-symbol-description"]={en:["Butter Futures-Cash (Continuous: Current contract in front)"],ru:["Butter Futures-Cash (Continuous: Current contract in front)"]},
e.exports["#LSE:SCHO-symbol-description"]={en:["Scholium Group Plc Ord 1P"],ru:["Scholium Group Plc Ord 1P"]},e.exports["#NEO:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],ru:["Hanwei Energy Services Corp."]},e.exports["#NYSE:HE-symbol-description"]={en:["Hawaiian Electric Industries"],ru:["Hawaiian Electric Industries"]},e.exports["#OMXCOP:SCHO-symbol-description"]={en:["Schouw & Co A/S"],ru:["Schouw & Co A/S"]},e.exports["#TSX:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],ru:["Hanwei Energy Services Corp."]},e.exports["#BSE:ITI-symbol-description"]={en:["ITI Ltd"],ru:["ITI Ltd"]},e.exports["#NSE:ITI-symbol-description"]={en:["Indian Telephone Industries Limited"],ru:["Indian Telephone Industries Limited"]},e.exports["#TSX:LS-symbol-description"]={en:["Middlefield Healthcare & Life Sciences Dividend Fund"],ru:["Middlefield Healthcare & Life Sciences Dividend Fund"]},e.exports["#BITMEX:XBT-symbol-description"]={en:["Bitcoin / U.S. Dollar Index"],ru:["Биткоин / Индекс доллара США"]},e.exports["#CME_MINI:RTY1!-symbol-description"]={en:["E-Mini Russell 2000 Index Futures"],ru:["Фьючерсы на Индекс E-Mini Russell 2000"]},e.exports["#CRYPTOCAP:TOTAL-symbol-description"]={en:["Crypto Total Market Cap, $"],ru:["Общая рыночная капитализация криптовалют, $"]},e.exports["#ICEUS:DX1!-symbol-description"]={en:["U.S. Dollar Index Futures"],ru:["Фьючерсы на Индекс доллара США"]},e.exports["#NYMEX:TT1!-symbol-description"]={en:["Cotton Futures"],ru:["Фьючерсы на хлопок"]},e.exports["#PHEMEX:BTCUSD-symbol-description"]={en:["BTC Perpetual Futures Contract"],ru:["BTC ETHUSD Бессрочный контракт"]},e.exports["#PHEMEX:ETHUSD-symbol-description"]={en:["ETH Perpetual Futures Contract"],ru:["ETH Бессрочный контракт"]},e.exports["#PHEMEX:XRPUSD-symbol-description"]={en:["XRP Perpetual Futures Contract"],ru:["XRP Бессрочный контракт"]},e.exports["#PHEMEX:LTCUSD-symbol-description"]={en:["LTC Perpetual Futures Contract"],ru:["LTC Бессрочный контракт"]},e.exports["#BITCOKE:BCHUSD-symbol-description"]={en:["BCH Quanto Swap"],ru:["BCH своп кванто"]},e.exports["#BITCOKE:BTCUSD-symbol-description"]={en:["BTC Quanto Swap"],ru:["BTC своп кванто"]},e.exports["#BITCOKE:ETHUSD-symbol-description"]={en:["ETH Quanto Swap"],ru:["ETH своп кванто"]},e.exports["#BITCOKE:LTCUSD-symbol-description"]={en:["LTC Quanto Swap"],ru:["LTC своп кванто"]},e.exports["#TVC:CA10-symbol-description"]={en:["Canadian Government Bonds, 10 YR"],ru:["Гособлигации Канады, 10 лет"]},e.exports["#TVC:CA10Y-symbol-description"]={en:["Canadian Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Канады"]},e.exports["#TVC:ID10Y-symbol-description"]={en:["Indonesia Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Индонезии"]},e.exports["#TVC:NL10-symbol-description"]={en:["Netherlands Government Bonds, 10 YR"],ru:["Гособлигации Нидерландов, 10 лет"]},e.exports["#TVC:NL10Y-symbol-description"]={en:["Netherlands Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Нидерландов"]},
e.exports["#TVC:NZ10-symbol-description"]={en:["New Zealand Government Bonds, 10 YR"],ru:["Гособлигации Новой Зеландии, 10 лет"]},e.exports["#TVC:NZ10Y-symbol-description"]={en:["New Zealand Government Bonds 10 YR Yield"],ru:["Доходность 10-летних облигаций Новой Зеландии"]},e.exports["#SOLUSD-symbol-description"]={en:["Solana / U.S. Dollar"],ru:["Solana / Доллар США"]},e.exports["#LUNAUSD-symbol-description"]={en:["Luna / U.S. Dollar"],ru:["Luna / Доллар США"]},e.exports["#UNIUSD-symbol-description"]={en:["Uniswap / U.S. Dollar"],ru:["Uniswap / Доллар США"]},e.exports["#LTCBRL-symbol-description"]={en:["Litecoin / Brazilian Real"],ru:["Лайткоин / Бразильский реал"]},e.exports["#ETCEUR-symbol-description"]={en:["Ethereum Classic / Euro"],ru:["Эфириум Классик / Евро"]},e.exports["#ETHKRW-symbol-description"]={en:["Ethereum / South Korean Won"],ru:["Эфириум / Южнокорейская вона"]},e.exports["#BTCRUB-symbol-description"]={en:["Bitcoin / Russian Ruble"],ru:["Биткоин / Российский рубль"]},e.exports["#BTCTHB-symbol-description"]={en:["Bitcoin / Thai Baht"],ru:["Биткоин / Тайский бат"]},e.exports["#ETHTHB-symbol-description"]={en:["Ethereum / Thai Baht"],ru:["Эфириум / Тайский бат"]},e.exports["#TVC:EU10YY-symbol-description"]={en:["Euro Government Bonds 10 YR Yield"],ru:["Доходность 10-летних еврооблигаций"]},e.exports["#NASDAQ:LCID-symbol-description"]={en:["Lucid Group, Inc."],ru:["Lucid Group, Inc."]},e.exports["#TADAWUL:2370-symbol-description"]={en:["Middle East Specialized Cables Co."],ru:["Middle East Specialized Cables Co."]},e.exports["#NASDAQ:GOOG-symbol-description"]={en:["#NASDAQ:GOOG-symbol-description"],ru:["#NASDAQ:GOOG-symbol-description"]},e.exports["#NASDAQ:GOOGL-symbol-description"]={en:["#NASDAQ:GOOGL-symbol-description"],ru:["Alphabet Inc (Google)"]}}}]);