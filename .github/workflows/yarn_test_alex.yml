name: Build and Deploy to ACK

on:
  push:
    branches: ['alex', 'alex#hotfix', 'feature/alex#*']

# Environment variables available to all jobs and steps in this workflow.
env:
  AWS_REGION: ap-southeast-1 # set this to your preferred AWS region, e.g. us-west-1
  ECR_REPOSITORY: pumpkin-dapp # set this to your Amazon ECR repository name
  ENV: test # test: 测试环境， pro: 正式环境

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      # 1. 设置 Node，并开启 Yarn 缓存
      - name: Setup Node environment
        uses: actions/setup-node@v3
        with:
          node-version: '22.14.0'
          # 开启 Yarn 缓存，GitHub 会自动管理 node_modules、Yarn 缓存等
          cache: 'yarn'

      # 2. 如果你需要固定 Yarn 版本，可考虑使用 Corepack
      # （Corepack 内置在 Node 16.10+ 版本中，可帮你管理正确的 Yarn 版本）
      - name: Set specific Yarn version (optional)
        run: |
          corepack enable
          # 例如锁定到 Yarn 1.22.19
          yarn set version 1.22.19
          yarn --version

      # 3. 针对 HTTPS 证书等额外配置（如果需要）
      - name: Configure Yarn
        run: |
          yarn config set "strict-ssl" false -g
          yarn config list

      # 已经启用 setup-node 的内置缓存后，可移除手动的 yarn cache 步骤
      # - name: Get yarn cache directory path
      # - name: Cache yarn dependencies
      #   uses: actions/cache@v3
      #   ...

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.NEW_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.NEW_AWS_ACCESS_KEY }}
          aws-region: ap-southeast-1

      - name: Update kube config
        run: |
          aws eks update-kubeconfig --name test --region ap-southeast-1

      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.25.3'

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      # 4. 安装依赖并构建
      - name: Install dependencies and build
        run: |
          yarn install --frozen-lockfile --prefer-offline
          NODE_OPTIONS="--max-old-space-size=4096" yarn run build:test

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE: pumpkin-dapp-alex
        run: |
          cp -r ./dist ./docker
          cd docker
          pwd
          export IMAGE_NAME=pumpkin-dapp-stephen
          docker build -f ./Dockerfile -t ${ECR_REGISTRY}/test/${IMAGE}:latest .
          docker push ${ECR_REGISTRY}/test/${IMAGE}:latest
          kubectl rollout restart deployment pumpkin-dapp-alex -n pumpkin
          echo -e ">>>>>>>>>> build and push successfully >>>>>>"
